import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/controllers/font_size_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/pages/font_size_setting_page.dart';
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/widgets/preview_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'font_size_setting_page_test.mocks.dart';

// Helper mock class for GetX lifecycle callbacks
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// 1. Mock the controller
@GenerateNiceMocks([MockSpec<FontSizeSettingController>()])
void main() {
  // 2. Declare the mock controller instance
  late MockFontSizeSettingController mockController;
  late MockInternalFinalCallback<void> mockInternalFinalCallback;

  // 3. setUp and tearDown for a clean test environment
  setUp(() {
    Get.reset();
    mockController = MockFontSizeSettingController();
    mockInternalFinalCallback = MockInternalFinalCallback<void>();

    // Default stubbing for the controller's state
    // This simulates the initial state when the page loads.
    when(mockController.selectedFontSize).thenReturn('normal'.obs);
    when(mockController.expand).thenReturn(true.obs);

    // Stub GetX lifecycle methods to prevent FakeUsedError
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);
    when(mockController.onDelete).thenReturn(mockInternalFinalCallback);

    // Put the mock controller into GetX's dependency management
    Get.put<FontSizeSettingController>(mockController);
  });

  tearDown(() {
    Get.reset();
  });

  // 4. Helper function to build the widget
  Future<void> pumpWidget(WidgetTester tester) async {
    await tester.pumpWidget(const GetMaterialApp(home: FontSizeSettingsPage()));
    await tester.pumpAndSettle();
  }

  // Helper function to setup mock controller with all necessary stubs
  void setupMockController({String? selectedFontSize, bool? expand}) {
    Get.reset();
    mockController = MockFontSizeSettingController();
    when(mockController.selectedFontSize).thenReturn((selectedFontSize ?? 'normal').obs);
    when(mockController.expand).thenReturn((expand ?? true).obs);
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);
    when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
    // 添加所有controller方法的stub
    when(mockController.onFontSizeSelect(any)).thenReturn(null);
    when(mockController.onBack()).thenReturn(null);
    when(mockController.onSave()).thenReturn(null);
    Get.put<FontSizeSettingController>(mockController);
  }

  // Phase 1: UI元素验证测试
  group('Phase 1: UI Elements Verification', () {
    testWidgets('Scaffold structure is correctly rendered', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证基本的Scaffold结构
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(Padding), findsAtLeastNWidgets(1)); // Body中的主要Padding
    });

    testWidgets('AppBar components are correctly positioned', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证AppBar组件
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.backgroundColor, const Color.fromARGB(0x20, 0x20, 0x20, 0x20));

      // 验证标题
      expect(find.text('文字サイズ設定'), findsOneWidget);
      expect(find.descendant(of: find.byType(AppBar), matching: find.text('文字サイズ設定')), findsOneWidget);

      // 验证返回按钮
      expect(find.byKey(const Key('font_size_setting_back_button')), findsOneWidget);
      expect(find.descendant(of: find.byType(AppBar), matching: find.byIcon(Icons.arrow_back)), findsOneWidget);

      // 验证保存按钮
      expect(find.byKey(const Key('font_size_setting_save_button')), findsOneWidget);
      expect(find.descendant(of: find.byType(AppBar), matching: find.text('保存')), findsOneWidget);
    });

    testWidgets('Body instruction text is displayed correctly', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证说明文本
      expect(find.text('文字サイズ設定できます。\n表示例をご確認ください。'), findsOneWidget);

      final instructionText = tester.widget<Text>(find.text('文字サイズ設定できます。\n表示例をご確認ください。'));
      expect(instructionText.style?.color, Colors.white);
    });

    testWidgets('Font size option containers are properly structured', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证字体选项容器
      expect(find.byKey(const Key('font_size_item_normal')), findsOneWidget);
      expect(find.byKey(const Key('font_size_item_big')), findsOneWidget);

      // 验证每个容器都是Container类型
      final normalContainer = tester.widget<Container>(find.byKey(const Key('font_size_item_normal')));
      final bigContainer = tester.widget<Container>(find.byKey(const Key('font_size_item_big')));

      // 验证容器样式
      expect(normalContainer.decoration, isA<BoxDecoration>());
      expect(bigContainer.decoration, isA<BoxDecoration>());

      // 验证每个容器内都有ListTile
      expect(
        find.descendant(of: find.byKey(const Key('font_size_item_normal')), matching: find.byType(ListTile)),
        findsOneWidget,
      );
      expect(
        find.descendant(of: find.byKey(const Key('font_size_item_big')), matching: find.byType(ListTile)),
        findsOneWidget,
      );
    });

    testWidgets('Radio buttons are correctly configured', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证Radio按钮配置
      expect(find.byKey(const Key('font_size_radio_normal')), findsOneWidget);
      expect(find.byKey(const Key('font_size_radio_big')), findsOneWidget);

      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      // 验证Radio按钮的value和groupValue
      expect(normalRadio.value, 'normal');
      expect(bigRadio.value, 'big');
      expect(normalRadio.groupValue, 'normal'); // 初始状态为normal
      expect(bigRadio.groupValue, 'normal');
    });

    testWidgets('Font size option texts are displayed with correct styling', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证选项文本和样式
      expect(find.text('通常の文字'), findsOneWidget);
      expect(find.text('大きい文字'), findsOneWidget);

      // 查找包含在font_size_item_normal中的文本
      final normalText = find.descendant(
        of: find.byKey(const Key('font_size_item_normal')),
        matching: find.text('通常の文字'),
      );
      expect(normalText, findsOneWidget);

      final bigText = find.descendant(of: find.byKey(const Key('font_size_item_big')), matching: find.text('大きい文字'));
      expect(bigText, findsOneWidget);

      // 验证文本样式（直接查找特定文本的样式）
      final normalTextWidget = tester.widget<Text>(find.text('通常の文字'));
      expect(normalTextWidget.style?.color, Colors.black);
      expect(normalTextWidget.style?.fontSize, 16);

      final bigTextWidget = tester.widget<Text>(find.text('大きい文字'));
      expect(bigTextWidget.style?.color, Colors.black);
      expect(bigTextWidget.style?.fontSize, 20);
    });

    testWidgets('PreviewWidget is present and accessible', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证PreviewWidget的存在
      expect(find.byType(PreviewWidget), findsOneWidget);

      // 验证预览区域的基本元素
      expect(find.text(' 表示例'), findsOneWidget);
      expect(find.text('資産情報'), findsOneWidget);

      // 验证展开图标存在（默认应该是展开状态）
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
    });

    testWidgets('Layout spacing and structure are correct', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证布局结构
      expect(find.byType(Column), findsAtLeastNWidgets(1)); // 主体Column
      expect(find.byType(SizedBox), findsAtLeastNWidgets(3)); // 间距控制

      // 验证主要的Padding容器
      final bodyPadding = tester.widget<Padding>(find.byType(Padding).first);
      expect(bodyPadding.padding, const EdgeInsets.all(16.0));
    });
  });

  // Phase 2: 响应式状态测试
  group('Phase 2: Responsive State Tests', () {
    testWidgets('Radio buttons reflect selectedFontSize state - normal selected', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态（确保覆盖setUp中的默认值）
      Get.reset();
      mockController = MockFontSizeSettingController();
      when(mockController.selectedFontSize).thenReturn('normal'.obs);
      when(mockController.expand).thenReturn(true.obs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      // Act
      await pumpWidget(tester);

      // Assert - 验证normal被选中，big未选中
      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.value, 'normal');
      expect(normalRadio.groupValue, 'normal');
      expect(bigRadio.value, 'big');
      expect(bigRadio.groupValue, 'normal');

      // 验证选中状态
      expect(normalRadio.value == normalRadio.groupValue, isTrue); // normal被选中
      expect(bigRadio.value == bigRadio.groupValue, isFalse); // big未被选中
    });

    testWidgets('Radio buttons reflect selectedFontSize state - big selected', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态
      Get.reset();
      mockController = MockFontSizeSettingController();
      when(mockController.selectedFontSize).thenReturn('big'.obs);
      when(mockController.expand).thenReturn(true.obs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      // Act
      await pumpWidget(tester);

      // Assert - 验证big被选中，normal未选中
      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.value, 'normal');
      expect(normalRadio.groupValue, 'big');
      expect(bigRadio.value, 'big');
      expect(bigRadio.groupValue, 'big');

      // 验证选中状态
      expect(normalRadio.value == normalRadio.groupValue, isFalse); // normal未被选中
      expect(bigRadio.value == bigRadio.groupValue, isTrue); // big被选中
    });

    testWidgets('Preview widget responds to expand state - expanded', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态
      Get.reset();
      mockController = MockFontSizeSettingController();
      when(mockController.selectedFontSize).thenReturn('normal'.obs);
      when(mockController.expand).thenReturn(true.obs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      // Act
      await pumpWidget(tester);

      // Assert - 验证展开状态的UI
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.byIcon(Icons.add_circle_outline), findsNothing);

      // 验证展开内容存在（注意：可能在Container中需要更精确的查找）
      expect(find.text('資産数'), findsWidgets);
      expect(find.text('資産種類'), findsWidgets);
    });

    testWidgets('Preview widget responds to expand state - collapsed', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态
      Get.reset();
      mockController = MockFontSizeSettingController();
      when(mockController.selectedFontSize).thenReturn('normal'.obs);
      when(mockController.expand).thenReturn(false.obs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      // Act
      await pumpWidget(tester);

      // Assert - 验证收起状态的UI
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsNothing);

      // 验证展开内容不存在（被隐藏）
      expect(find.text('資産数'), findsNothing);
      expect(find.text('資産種類'), findsNothing);
    });

    testWidgets('State changes trigger UI updates - selectedFontSize changes', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态，使用可变的Observable
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(true.obs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Assert - 验证初始状态
      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // Act - 模拟状态变化为big
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      // Assert - 验证状态变化后的UI
      normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.groupValue, 'big');
      expect(bigRadio.groupValue, 'big');
      expect(bigRadio.value == bigRadio.groupValue, isTrue); // big现在被选中
    });

    testWidgets('State changes trigger UI updates - expand state changes', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态，使用可变的Observable
      Get.reset();
      mockController = MockFontSizeSettingController();
      final expandObs = true.obs;
      when(mockController.selectedFontSize).thenReturn('normal'.obs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Assert - 验证初始展开状态
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.text('資産数'), findsWidgets);

      // Act - 模拟状态变化为收起
      expandObs.value = false;
      await tester.pumpAndSettle();

      // Assert - 验证收起状态
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsNothing);
      expect(find.text('資産数'), findsNothing);
    });

    testWidgets('Multiple state combinations work correctly', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态，测试不同状态组合
      Get.reset();
      mockController = MockFontSizeSettingController();
      when(mockController.selectedFontSize).thenReturn('big'.obs);
      when(mockController.expand).thenReturn(false.obs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      // Act
      await pumpWidget(tester);

      // Assert - 验证组合状态：big选中 + 预览收起
      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      // 字体状态：big被选中
      expect(normalRadio.groupValue, 'big');
      expect(bigRadio.groupValue, 'big');
      expect(bigRadio.value == bigRadio.groupValue, isTrue);

      // 预览状态：收起
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.text('資産数'), findsNothing);
    });

    testWidgets('Obx widgets respond to observable changes', (WidgetTester tester) async {
      // Arrange - 重新设置mock状态，使用真实的Observable对象来测试Obx响应
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 连续多次状态变化
      // 第一次变化：normal -> big
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 第二次变化：展开 -> 收起
      expandObs.value = false;
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);

      // 第三次变化：big -> normal
      fontSizeObs.value = 'normal';
      await tester.pumpAndSettle();

      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // 第四次变化：收起 -> 展开
      expandObs.value = true;
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.text('資産数'), findsWidgets);
    });
  });

  // Phase 3: 用户交互测试
  group('Phase 3: User Interaction Tests', () {
    testWidgets('Back button triggers controller.onBack() call', (WidgetTester tester) async {
      // Arrange
      setupMockController();
      await pumpWidget(tester);

      // Act - 点击返回按钮
      await tester.tap(find.byKey(const Key('font_size_setting_back_button')));
      await tester.pumpAndSettle();

      // Assert - 验证onBack方法被调用
      verify(mockController.onBack()).called(1);
    });

    testWidgets('Save button triggers controller.onSave() call', (WidgetTester tester) async {
      // Arrange
      setupMockController();
      await pumpWidget(tester);

      // Act - 点击保存按钮
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // Assert - 验证onSave方法被调用
      verify(mockController.onSave()).called(1);
    });

    testWidgets('Font option containers have proper structure for interaction', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: 'big');
      await pumpWidget(tester);

      // Assert - 验证字体选项容器的结构支持交互
      expect(find.byKey(const Key('font_size_item_normal')), findsOneWidget);
      expect(find.byKey(const Key('font_size_item_big')), findsOneWidget);

      // 验证容器内有GestureDetector（支持点击）- 使用findsAtLeastNWidgets因为可能有多个
      expect(
        find.descendant(of: find.byKey(const Key('font_size_item_normal')), matching: find.byType(GestureDetector)),
        findsAtLeastNWidgets(1),
      );

      expect(
        find.descendant(of: find.byKey(const Key('font_size_item_big')), matching: find.byType(GestureDetector)),
        findsAtLeastNWidgets(1),
      );
    });

    testWidgets('Radio buttons are interactive and clickable', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: 'big');
      await pumpWidget(tester);

      // Act & Assert - 验证Radio按钮可以找到并且是可点击的
      expect(find.byKey(const Key('font_size_radio_normal')), findsOneWidget);
      expect(find.byKey(const Key('font_size_radio_big')), findsOneWidget);

      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.onChanged, isNotNull);
      expect(bigRadio.onChanged, isNotNull);
    });

    testWidgets('Multiple button taps do not crash the app', (WidgetTester tester) async {
      // Arrange
      setupMockController();
      await pumpWidget(tester);

      // Act - 快速连续点击不同的按钮，确保没有崩溃
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pump(const Duration(milliseconds: 100));

      await tester.tap(find.byKey(const Key('font_size_setting_back_button')));
      await tester.pump(const Duration(milliseconds: 100));

      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // Assert - 验证UI没有崩溃，按钮仍然存在
      expect(find.byKey(const Key('font_size_setting_save_button')), findsOneWidget);
      expect(find.byKey(const Key('font_size_setting_back_button')), findsOneWidget);
    });

    testWidgets('Preview widget is interactive', (WidgetTester tester) async {
      // Arrange
      setupMockController();
      await pumpWidget(tester);

      // Act & Assert - 验证预览区域存在并且可以交互
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.text(' 表示例'), findsOneWidget);
      expect(find.text('資産情報'), findsOneWidget);
    });

    testWidgets('All interactive elements are accessible', (WidgetTester tester) async {
      // Arrange
      setupMockController();
      await pumpWidget(tester);

      // Act & Assert - 验证所有交互元素都可以找到并且是可点击的
      final backButton = find.byKey(const Key('font_size_setting_back_button'));
      final saveButton = find.byKey(const Key('font_size_setting_save_button'));
      final normalOption = find.byKey(const Key('font_size_item_normal'));
      final bigOption = find.byKey(const Key('font_size_item_big'));

      expect(backButton, findsOneWidget);
      expect(saveButton, findsOneWidget);
      expect(normalOption, findsOneWidget);
      expect(bigOption, findsOneWidget);

      // 验证这些元素都是可交互的（具有onPressed或onTap回调）
      final backButtonWidget = tester.widget<IconButton>(backButton);
      final saveButtonWidget = tester.widget<TextButton>(saveButton);

      expect(backButtonWidget.onPressed, isNotNull);
      expect(saveButtonWidget.onPressed, isNotNull);
    });
  });

  // Phase 4: 边界条件和异常情况测试
  group('Phase 4: Boundary Conditions and Edge Cases', () {
    testWidgets('Handles invalid selectedFontSize values gracefully', (WidgetTester tester) async {
      // Arrange - 设置无效的字体大小值
      setupMockController();
      // 重新设置为无效值
      when(mockController.selectedFontSize).thenReturn('invalid_value'.obs);

      await pumpWidget(tester);

      // Assert - 验证UI没有崩溃，仍然正常渲染
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);
      expect(find.byKey(const Key('font_size_item_normal')), findsOneWidget);
      expect(find.byKey(const Key('font_size_item_big')), findsOneWidget);

      // 验证Radio按钮的状态
      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.groupValue, 'invalid_value');
      expect(bigRadio.groupValue, 'invalid_value');
      // 两个Radio都不应该被选中
      expect(normalRadio.value == normalRadio.groupValue, isFalse);
      expect(bigRadio.value == bigRadio.groupValue, isFalse);
    });

    testWidgets('Handles null or empty selectedFontSize values', (WidgetTester tester) async {
      // Arrange - 设置空值
      setupMockController();
      when(mockController.selectedFontSize).thenReturn(''.obs);

      await pumpWidget(tester);

      // Assert - 验证UI仍然稳定
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);

      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      final bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.groupValue, '');
      expect(bigRadio.groupValue, '');
    });

    testWidgets('Handles extreme expand state changes', (WidgetTester tester) async {
      // Arrange - 使用可变的expand状态
      Get.reset();
      mockController = MockFontSizeSettingController();
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn('normal'.obs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 快速切换expand状态多次
      for (int i = 0; i < 5; i++) {
        expandObs.value = !expandObs.value;
        await tester.pumpAndSettle();

        // 验证UI没有崩溃
        expect(find.byType(PreviewWidget), findsOneWidget);

        if (expandObs.value) {
          expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
        } else {
          expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
        }
      }
    });

    testWidgets('Handles simultaneous state changes', (WidgetTester tester) async {
      // Arrange - 使用可变的状态
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act - 同时快速改变多个状态
      fontSizeObs.value = 'big';
      expandObs.value = false;
      await tester.pumpAndSettle();

      fontSizeObs.value = 'normal';
      expandObs.value = true;
      await tester.pumpAndSettle();

      // Assert - 验证最终状态正确
      final normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
    });

    testWidgets('Widget rebuilds correctly after controller replacement', (WidgetTester tester) async {
      // Arrange - 初始设置
      setupMockController(selectedFontSize: 'normal');
      await pumpWidget(tester);

      // 验证初始状态
      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // Act - 替换controller
      Get.reset();
      var newMockController = MockFontSizeSettingController();
      when(newMockController.selectedFontSize).thenReturn('big'.obs);
      when(newMockController.expand).thenReturn(false.obs);
      when(newMockController.onStart).thenReturn(mockInternalFinalCallback);
      when(newMockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(newMockController.onFontSizeSelect(any)).thenReturn(null);
      when(newMockController.onBack()).thenReturn(null);
      when(newMockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(newMockController);

      await tester.pumpWidget(const GetMaterialApp(home: FontSizeSettingsPage()));
      await tester.pumpAndSettle();

      // Assert - 验证新状态
      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    });

    testWidgets('Handles very long text values without overflow', (WidgetTester tester) async {
      // Arrange - 正常设置，测试现有文本不会溢出
      setupMockController();
      await pumpWidget(tester);

      // Act & Assert - 验证文本没有溢出
      expect(find.text('文字サイズ設定できます。\n表示例をご確認ください。'), findsOneWidget);
      expect(find.text('通常の文字'), findsOneWidget);
      expect(find.text('大きい文字'), findsOneWidget);

      // 验证没有RenderFlex overflow错误（通过能够正常渲染来验证）
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);
    });

    testWidgets('Maintains stability during rapid widget rebuilds', (WidgetTester tester) async {
      // Arrange
      setupMockController();
      await pumpWidget(tester);

      // Act - 触发多次重建
      for (int i = 0; i < 10; i++) {
        await tester.pumpWidget(const GetMaterialApp(home: FontSizeSettingsPage()));
        await tester.pump(const Duration(milliseconds: 50));
      }
      await tester.pumpAndSettle();

      // Assert - 验证最终状态正确
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);
      expect(find.byKey(const Key('font_size_setting_back_button')), findsOneWidget);
      expect(find.byKey(const Key('font_size_setting_save_button')), findsOneWidget);
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('GetX controller lifecycle is properly managed', (WidgetTester tester) async {
      // Arrange
      setupMockController();

      // Act - 创建和销毁widget
      await pumpWidget(tester);
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);

      // 切换到空白页面
      await tester.pumpWidget(const GetMaterialApp(home: Scaffold(body: Text('Empty'))));
      await tester.pumpAndSettle();

      // 再次切换回原页面
      await tester.pumpWidget(const GetMaterialApp(home: FontSizeSettingsPage()));
      await tester.pumpAndSettle();

      // Assert - 验证页面正常重新创建
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);
    });
  });

  // Phase 5: 集成和完整工作流测试
  group('Phase 5: Integration and Complete Workflow Tests', () {
    testWidgets('Complete font size selection workflow - normal to big', (WidgetTester tester) async {
      // Arrange - 使用可变状态来模拟完整工作流
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 完整工作流：进入页面 → 选择字体 → 保存

      // 1. 验证初始状态 (normal selected)
      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');
      expect(normalRadio.value == normalRadio.groupValue, isTrue);

      // 2. 模拟选择big字体
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');
      expect(bigRadio.value == bigRadio.groupValue, isTrue);

      // 3. 点击保存按钮
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // 4. 验证保存方法被调用
      verify(mockController.onSave()).called(1);

      // 5. 验证最终状态保持
      bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');
    });

    testWidgets('Complete font size selection workflow - big to normal', (WidgetTester tester) async {
      // Arrange - 初始状态为big
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'big'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 完整工作流：big → normal → 保存

      // 1. 验证初始状态 (big selected)
      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 2. 模拟选择normal字体
      fontSizeObs.value = 'normal';
      await tester.pumpAndSettle();

      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // 3. 点击保存按钮
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // 4. 验证最终状态
      verify(mockController.onSave()).called(1);
      normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');
    });

    testWidgets('Cancel workflow - select font then back without saving', (WidgetTester tester) async {
      // Arrange
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 取消工作流：选择字体 → 返回（不保存）

      // 1. 验证初始状态
      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // 2. 模拟选择big字体
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 3. 点击返回按钮（不保存）
      await tester.tap(find.byKey(const Key('font_size_setting_back_button')));
      await tester.pumpAndSettle();

      // 4. 验证onBack被调用，但onSave没有被调用
      verify(mockController.onBack()).called(1);
      verifyNever(mockController.onSave());
    });

    testWidgets('Preview expand/collapse workflow integration', (WidgetTester tester) async {
      // Arrange
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 预览交互与字体选择的集成工作流

      // 1. 验证初始展开状态
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.text('資産数'), findsWidgets);

      // 2. 改变字体大小
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      // 3. 验证预览仍然展开，字体已改变
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 4. 收起预览
      expandObs.value = false;
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.text('資産数'), findsNothing);

      // 5. 保存设置
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // 6. 验证所有状态保持正确
      verify(mockController.onSave()).called(1);
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');
    });

    testWidgets('Multiple font size changes before saving workflow', (WidgetTester tester) async {
      // Arrange
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 多次更改字体大小的工作流

      // 1. 初始状态: normal
      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // 2. 第一次更改: normal → big
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 3. 第二次更改: big → normal
      fontSizeObs.value = 'normal';
      await tester.pumpAndSettle();

      normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');

      // 4. 第三次更改: normal → big
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 5. 最终保存
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // 6. 验证最终状态为最后选择的值
      verify(mockController.onSave()).called(1);
      bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');
    });

    testWidgets('End-to-end user journey with all interactions', (WidgetTester tester) async {
      // Arrange
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 完整的端到端用户旅程

      // 1. 用户进入页面，查看初始状态
      expect(find.text('文字サイズ設定'), findsOneWidget);
      expect(find.text('文字サイズ設定できます。\n表示例をご確認ください。'), findsOneWidget);

      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);

      // 2. 用户查看预览内容
      expect(find.text(' 表示例'), findsOneWidget);
      expect(find.text('資産情報'), findsOneWidget);
      expect(find.text('資産数'), findsWidgets);

      // 3. 用户收起预览查看更多内容
      expandObs.value = false;
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.text('資産数'), findsNothing);

      // 4. 用户重新展开预览
      expandObs.value = true;
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.text('資産数'), findsWidgets);

      // 5. 用户选择大字体
      fontSizeObs.value = 'big';
      await tester.pumpAndSettle();

      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');

      // 6. 用户再次查看预览效果
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.text('資産情報'), findsOneWidget);

      // 7. 用户满意后保存设置
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      // 8. 验证保存成功
      verify(mockController.onSave()).called(1);

      // 9. 验证最终状态保持正确
      bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));
      expect(bigRadio.groupValue, 'big');
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
    });

    testWidgets('Error recovery workflow - handle unexpected state', (WidgetTester tester) async {
      // Arrange - 模拟一些意外状态
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'unknown'.obs; // 意外的初始值
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 错误恢复工作流

      // 1. 验证UI在意外状态下仍然稳定
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);

      var normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      var bigRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_big')));

      expect(normalRadio.groupValue, 'unknown');
      expect(bigRadio.groupValue, 'unknown');
      // 两个radio都不应该被选中
      expect(normalRadio.value == normalRadio.groupValue, isFalse);
      expect(bigRadio.value == bigRadio.groupValue, isFalse);

      // 2. 用户选择正常值来恢复
      fontSizeObs.value = 'normal';
      await tester.pumpAndSettle();

      normalRadio = tester.widget<Radio<String>>(find.byKey(const Key('font_size_radio_normal')));
      expect(normalRadio.groupValue, 'normal');
      expect(normalRadio.value == normalRadio.groupValue, isTrue);

      // 3. 用户可以正常保存
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      verify(mockController.onSave()).called(1);
    });

    testWidgets('Performance test - rapid state changes and interactions', (WidgetTester tester) async {
      // Arrange
      Get.reset();
      mockController = MockFontSizeSettingController();
      final fontSizeObs = 'normal'.obs;
      final expandObs = true.obs;

      when(mockController.selectedFontSize).thenReturn(fontSizeObs);
      when(mockController.expand).thenReturn(expandObs);
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      when(mockController.onFontSizeSelect(any)).thenReturn(null);
      when(mockController.onBack()).thenReturn(null);
      when(mockController.onSave()).thenReturn(null);
      Get.put<FontSizeSettingController>(mockController);

      await pumpWidget(tester);

      // Act & Assert - 性能测试：快速状态变化和交互

      final stopwatch = Stopwatch()..start();

      // 快速执行多个状态变化
      for (int i = 0; i < 20; i++) {
        // 交替改变字体大小
        fontSizeObs.value = (i % 2 == 0) ? 'big' : 'normal';
        // 交替改变展开状态
        expandObs.value = (i % 3 == 0);

        await tester.pump(const Duration(milliseconds: 16)); // 60fps
      }

      await tester.pumpAndSettle();
      stopwatch.stop();

      // 验证性能合理（应该在合理时间内完成）
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5秒内完成

      // 验证最终状态正确
      expect(find.byKey(const Key('font_size_setting_page_scaffold')), findsOneWidget);
      expect(find.byType(PreviewWidget), findsOneWidget);

      // 验证可以正常保存
      await tester.tap(find.byKey(const Key('font_size_setting_save_button')));
      await tester.pumpAndSettle();

      verify(mockController.onSave()).called(1);
    });
  });
}
