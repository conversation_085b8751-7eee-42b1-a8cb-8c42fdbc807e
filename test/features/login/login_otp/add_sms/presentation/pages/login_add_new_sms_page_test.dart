import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/controllers/login_add_new_sms_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/pages/login_add_new_sms_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/state/login_add_new_sms_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 Mock 类
@GenerateNiceMocks([MockSpec<LoginAddNewSmsController>()])
import 'login_add_new_sms_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockLoginAddNewSmsController mockController;
  late LoginAddNewSmsState mockState;

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: LoginAddNewSMSPage());
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    mockController = MockLoginAddNewSmsController();
    mockState = LoginAddNewSmsState();

    // 设置 Mock Controller 的默认返回值
    when(mockController.state).thenReturn(mockState);
    when(mockController.textEditingController).thenReturn(TextEditingController());
    when(mockController.getIpnImage()).thenReturn('assets/intl_phone_code_flag/jp.png');

    // 设置默认交互方法
    when(mockController.goBackPage()).thenAnswer((_) async {});
    when(mockController.hideKeyboard()).thenAnswer((_) async {});
    when(mockController.onChangePhoneInput(any)).thenAnswer((_) async {});
    when(mockController.clearInput()).thenAnswer((_) async {});
    when(mockController.sureBtnOnClick()).thenAnswer((_) async {});

    // 设置生命周期方法的 stub
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 注入 Mock Controller
    Get.put<LoginAddNewSmsController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
    clearInteractions(mockController);
  });

  // ================================
  // UI ELEMENT TESTS - Phase 1
  // ================================
  group('UI ELEMENT TESTS - Phase 1', () {
    // UI结构验证测试
    group('UI Structure Tests', () {
      testWidgets('Displays correct AppBar with title and back button', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.text('携帯電話番号'), findsNWidgets(2)); // AppBar + form field
        expect(find.byIcon(Icons.chevron_left), findsOneWidget);

        // 更精确地验证AppBar中的标题
        final appBarTitle = find.descendant(of: find.byType(AppBar), matching: find.text('携帯電話番号'));
        expect(appBarTitle, findsOneWidget);
      });

      testWidgets('Displays main container with correct structure', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(Column), findsWidgets);
        expect(find.byType(Container), findsWidgets);
        expect(find.byType(Padding), findsWidgets);
      });

      testWidgets('Displays bottom navigation bar with buttons', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(OverflowBar), findsOneWidget);
        expect(find.byType(FractionallySizedBox), findsNWidgets(2));
        expect(find.byType(ElevatedButton), findsNWidgets(2));
      });

      testWidgets('Displays input field structure', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TextField), findsOneWidget);
        expect(find.byType(Row), findsWidgets);
        expect(find.byType(Expanded), findsWidgets);
      });

      testWidgets('Has correct GestureDetector for keyboard hiding', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证存在GestureDetector（可能有多个）
        expect(find.byType(GestureDetector), findsWidgets);

        // 验证页面中存在GestureDetector用于键盘隐藏功能
        // 由于UI中可能存在多个GestureDetector，我们只验证存在即可
        final gestureDetectors = find.byType(GestureDetector);
        expect(gestureDetectors.evaluate().length, greaterThan(0));
      });
    });

    // 文本和标签测试
    group('Text and Label Tests', () {
      testWidgets('Displays all required text labels', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('国番号'), findsOneWidget);
        expect(find.text('携帯電話番号'), findsNWidgets(2)); // AppBar + form field
        expect(find.text('キャンセル'), findsOneWidget);
        expect(find.text('確定'), findsOneWidget);
      });

      testWidgets('Displays input field hint text', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final textField = find.byType(TextField);
        expect(textField, findsOneWidget);

        final TextField widget = tester.widget(textField);
        expect(widget.decoration?.hintText, '未設定');
      });

      testWidgets('Displays button text with correct styling', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final cancelButton = find.text('キャンセル');
        final confirmButton = find.text('確定');

        expect(cancelButton, findsOneWidget);
        expect(confirmButton, findsOneWidget);
      });

      testWidgets('Displays text with correct font styling', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final countryCodeLabel = find.text('国番号');
        final phoneLabel = find.text('携帯電話番号').first;

        expect(countryCodeLabel, findsOneWidget);
        expect(phoneLabel, findsOneWidget);

        // 验证文本样式
        final Text countryCodeWidget = tester.widget(countryCodeLabel);
        final Text phoneWidget = tester.widget(phoneLabel);

        expect(countryCodeWidget.style?.fontSize, 16);
        expect(phoneWidget.style?.fontSize, 16);
      });
    });

    // 条件渲染测试
    group('Conditional Rendering Tests', () {
      testWidgets('Hides country code section when nationCode is empty', (tester) async {
        // Arrange
        mockState.nationCode.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Image), findsNothing);
        expect(find.textContaining('+'), findsNothing);
      });

      testWidgets('Shows country code section when nationCode has value', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Image), findsOneWidget);
        expect(find.text('+81'), findsOneWidget);
      });

      testWidgets('Shows country flag image when nationCode is present', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Image), findsOneWidget);

        final Image imageWidget = tester.widget(find.byType(Image));
        expect(imageWidget.image, isA<AssetImage>());
      });

      testWidgets('Hides clear button when input is empty', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.cancel), findsNothing);
      });

      testWidgets('Shows clear button when input has value', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Clear button has correct styling and constraints', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final clearButton = find.byIcon(Icons.cancel);
        expect(clearButton, findsOneWidget);

        // 更精确地查找清除按钮的IconButton
        final clearIconButton = find.ancestor(of: find.byIcon(Icons.cancel), matching: find.byType(IconButton));
        expect(clearIconButton, findsOneWidget);

        final IconButton buttonWidget = tester.widget(clearIconButton);
        expect(buttonWidget.iconSize, 15.0);
        expect(buttonWidget.constraints?.minHeight, 15.0);
        expect(buttonWidget.constraints?.minWidth, 15.0);
      });
    });

    // 布局和样式测试
    group('Layout and Styling Tests', () {
      testWidgets('Main container has correct decoration', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final containers = find.byType(Container);
        expect(containers, findsWidgets);

        // 验证主要容器的装饰
        final decoratedContainers = tester
            .widgetList<Container>(containers)
            .where((container) => container.decoration != null);
        expect(decoratedContainers.isNotEmpty, true);
      });

      testWidgets('Displays divider line correctly', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证存在DecoratedBox（可能有多个）
        expect(find.byType(DecoratedBox), findsWidgets);

        // 查找具有分割线样式的DecoratedBox（黑色，高度为1）
        final decoratedBoxes = find.byType(DecoratedBox);
        expect(decoratedBoxes, findsWidgets);

        // 验证至少有一个DecoratedBox存在
        final DecoratedBox firstDivider = tester.widget(decoratedBoxes.first);
        expect(firstDivider.decoration, isA<BoxDecoration>());
      });

      testWidgets('Bottom safe area is handled correctly', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final bottomNav = find.byType(OverflowBar);
        expect(bottomNav, findsOneWidget);

        // 验证底部导航栏的父容器
        final parentContainer = find.ancestor(of: bottomNav, matching: find.byType(Container));
        expect(parentContainer, findsOneWidget);
      });

      testWidgets('Buttons have correct fractional sizing', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final fractionallySizedBoxes = find.byType(FractionallySizedBox);
        expect(fractionallySizedBoxes, findsNWidgets(2));

        final List<FractionallySizedBox> boxes = tester
            .widgetList<FractionallySizedBox>(fractionallySizedBoxes)
            .toList();
        expect(boxes[0].widthFactor, 0.35);
        expect(boxes[1].widthFactor, 0.35);
      });

      testWidgets('Input field has correct keyboard type', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final TextField textField = tester.widget(find.byType(TextField));
        expect(textField.keyboardType, TextInputType.number);
        expect(textField.textInputAction, TextInputAction.next);
      });

      testWidgets('Country code image has correct dimensions', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final imageContainers = find.ancestor(of: find.byType(Image), matching: find.byType(Container));

        // 应该至少有一个容器包含图片
        expect(imageContainers, findsWidgets);

        // 验证第一个图片容器的约束
        final Container container = tester.widget(imageContainers.first);
        // 验证容器有适当的大小约束
        expect(container.constraints?.maxWidth, 40);
        expect(container.constraints?.maxHeight, 30);
      });
    });
  });

  // ================================
  // RESPONSIVE STATE TESTS - Phase 2
  // ================================
  group('RESPONSIVE STATE TESTS - Phase 2', () {
    // 国家代码显示测试（业务逻辑：国家代码固定为日本+81）
    group('Nation Code Display Tests', () {
      testWidgets('Displays Japan country code when nationCode is set to 81', (tester) async {
        // Arrange - 模拟控制器初始化后设置的日本国家代码
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 显示日本国家代码和国旗
        expect(find.byType(Image), findsOneWidget);
        expect(find.text('+81'), findsOneWidget);
        verify(mockController.getIpnImage()).called(greaterThan(0));
      });

      testWidgets('Hides country code display when nationCode is empty (loading state)', (tester) async {
        // Arrange - 模拟加载状态，国家代码还未设置
        mockState.nationCode.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 加载状态不显示国家代码
        expect(find.byType(Image), findsNothing);
        expect(find.textContaining('+'), findsNothing);
      });

      testWidgets('Always displays Japan flag due to legal requirements', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证只显示日本国旗（业务规则）
        expect(find.byType(Image), findsOneWidget);

        final Image imageWidget = tester.widget(find.byType(Image));
        expect(imageWidget.image, isA<AssetImage>());

        // 验证调用了getIpnImage方法获取日本国旗
        verify(mockController.getIpnImage()).called(greaterThan(0));
      });

      testWidgets('Country code format is consistent with Japan calling code', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证国家代码格式正确
        expect(find.text('+81'), findsOneWidget);

        // 验证文本样式
        final countryCodeText = find.text('+81');
        expect(countryCodeText, findsOneWidget);
      });

      testWidgets('Country code section layout is correct when loaded', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证国家代码区域布局
        expect(find.text('国番号'), findsOneWidget);
        expect(find.byType(Image), findsOneWidget);
        expect(find.text('+81'), findsOneWidget);

        // 验证图片和文本在同一行
        expect(find.byType(Row), findsWidgets);
      });
    });

    // 输入框状态管理测试
    group('Input State Management Tests', () {
      testWidgets('Updates clear button visibility when inputPhoneNumber changes', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state - no clear button
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Act - Add input
        mockState.inputPhoneNumber.value = '123';
        await tester.pump();

        // Assert updated state - clear button appears
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Hides clear button when inputPhoneNumber becomes empty', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state - clear button visible
        expect(find.byIcon(Icons.cancel), findsOneWidget);

        // Act - Clear input
        mockState.inputPhoneNumber.value = '';
        await tester.pump();

        // Assert updated state - clear button hidden
        expect(find.byIcon(Icons.cancel), findsNothing);
      });

      testWidgets('Maintains clear button visibility for non-empty input changes', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state
        expect(find.byIcon(Icons.cancel), findsOneWidget);

        // Act - Change input (still non-empty)
        mockState.inputPhoneNumber.value = '1234567890';
        await tester.pump();

        // Assert - clear button still visible
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Handles input state changes during typing simulation', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Simulate typing
        final typingSequence = ['1', '12', '123', '1234', '12345'];

        for (String input in typingSequence) {
          mockState.inputPhoneNumber.value = input;
          await tester.pump();

          // Clear button should be visible for all non-empty inputs
          expect(find.byIcon(Icons.cancel), findsOneWidget);
        }
      });

      testWidgets('Handles backspace simulation correctly', (tester) async {
        // Arrange
        mockState.inputPhoneNumber.value = '12345';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Simulate backspace
        final backspaceSequence = ['1234', '123', '12', '1', ''];

        for (String input in backspaceSequence) {
          mockState.inputPhoneNumber.value = input;
          await tester.pump();

          if (input.isEmpty) {
            expect(find.byIcon(Icons.cancel), findsNothing);
          } else {
            expect(find.byIcon(Icons.cancel), findsOneWidget);
          }
        }
      });
    });

    // 复合状态测试（国家代码固定，只测试输入状态）
    group('Composite State Tests', () {
      testWidgets('Handles nation code loaded state with input changes', (tester) async {
        // Arrange - 模拟控制器初始化完成，国家代码已设置
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state - 国家代码已显示，输入为空
        expect(find.byType(Image), findsOneWidget);
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Act - 添加输入
        mockState.inputPhoneNumber.value = '1234567890';
        await tester.pump();

        // Assert - 国家代码保持，输入相关UI更新
        expect(find.byType(Image), findsOneWidget);
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Maintains nation code display during input changes', (tester) async {
        // Arrange - 国家代码已设置，有输入内容
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);

        // Act - 清空输入
        mockState.inputPhoneNumber.value = '';
        await tester.pump();

        // Assert - 国家代码保持不变，只有输入相关UI变化
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Act - 重新添加输入
        mockState.inputPhoneNumber.value = '456';
        await tester.pump();

        // Assert - 国家代码仍然保持，输入UI更新
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Handles loading to loaded state transition', (tester) async {
        // Arrange - 模拟页面加载状态
        mockState.nationCode.value = '';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert loading state - 两个区域都未显示
        expect(find.byType(Image), findsNothing);
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Act - 模拟控制器初始化完成，设置国家代码
        mockState.nationCode.value = '81';
        await tester.pump();

        // Assert - 国家代码区域显示
        expect(find.text('+81'), findsOneWidget);
        expect(find.byType(Image), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Act - 用户开始输入
        mockState.inputPhoneNumber.value = '123';
        await tester.pump();

        // Assert - 完整状态显示
        expect(find.text('+81'), findsOneWidget);
        expect(find.byType(Image), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });
    });

    // 状态重置测试（主要测试输入状态重置）
    group('State Reset Tests', () {
      testWidgets('Resets input state while maintaining nation code', (tester) async {
        // Arrange - 设置完整状态
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);

        // Act - 只重置输入状态（实际使用场景）
        mockState.inputPhoneNumber.value = '';
        await tester.pump();

        // Assert - 国家代码保持，输入状态重置
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsNothing);
      });

      testWidgets('Handles input field clear and re-input', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert initial state
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);

        // Act - 清空输入
        mockState.inputPhoneNumber.value = '';
        await tester.pump();

        // Assert - 国家代码保持，清除按钮消失
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Act - 重新输入
        mockState.inputPhoneNumber.value = '456';
        await tester.pump();

        // Assert - 国家代码仍然保持，清除按钮重新出现
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Maintains UI consistency during input resets', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Multiple input reset cycles
        for (int i = 0; i < 3; i++) {
          // Reset input
          mockState.inputPhoneNumber.value = '';
          await tester.pump();

          // Assert - 国家代码始终保持，输入重置
          expect(find.text('+81'), findsOneWidget);
          expect(find.byIcon(Icons.cancel), findsNothing);

          // Restore input
          mockState.inputPhoneNumber.value = '123$i';
          await tester.pump();

          // Assert - 国家代码保持，输入恢复
          expect(find.text('+81'), findsOneWidget);
          expect(find.byIcon(Icons.cancel), findsOneWidget);
        }
      });
    });

    // Obx响应性测试（主要测试输入状态响应性）
    group('Obx Reactivity Tests', () {
      testWidgets('Obx widgets respond to input state changes immediately', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Test immediate responsiveness without additional pump
        mockState.inputPhoneNumber.value = '123';

        // Single pump should be enough for reactive updates
        await tester.pump();

        // Assert immediate updates
        expect(find.text('+81'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Input Obx widgets update independently from nation code', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Test that changing input observable doesn't affect nation code display
        mockState.inputPhoneNumber.value = '123';
        await tester.pump();

        // Nation code area should remain unchanged
        expect(find.text('+81'), findsOneWidget);
        // Input area should update
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });

      testWidgets('Obx widgets handle rapid input changes efficiently', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Rapid input changes
        for (int i = 0; i < 10; i++) {
          mockState.inputPhoneNumber.value = i.toString();
          await tester.pump();

          // Should consistently show clear button for non-empty values
          expect(find.byIcon(Icons.cancel), findsOneWidget);
          // Nation code should remain stable
          expect(find.text('+81'), findsOneWidget);
        }

        // Final clear
        mockState.inputPhoneNumber.value = '';
        await tester.pump();
        expect(find.byIcon(Icons.cancel), findsNothing);
        expect(find.text('+81'), findsOneWidget);
      });

      testWidgets('Obx widgets maintain performance with frequent input updates', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Performance test with many input updates
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 50; i++) {
          mockState.inputPhoneNumber.value = i.toString();
          await tester.pump();
        }

        stopwatch.stop();

        // Assert reasonable performance (should complete quickly)
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));

        // Assert final state is correct
        expect(find.text('+81'), findsOneWidget); // Nation code remains
        expect(find.byIcon(Icons.cancel), findsOneWidget); // Has input "49"
      });
    });
  });

  // ================================
  // INTERACTION TESTS - Phase 3
  // ================================
  group('INTERACTION TESTS - Phase 3', () {
    // 导航交互测试
    group('Navigation Interaction Tests', () {
      testWidgets('AppBar back button calls goBackPage', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap the back button in AppBar
        await tester.tap(find.byIcon(Icons.chevron_left));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.goBackPage()).called(1);
      });

      testWidgets('Cancel button calls goBackPage', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap the cancel button
        await tester.tap(find.text('キャンセル'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.goBackPage()).called(1);
      });

      testWidgets('Multiple back button taps are handled correctly', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Multiple taps
        await tester.tap(find.byIcon(Icons.chevron_left));
        await tester.pumpAndSettle();
        await tester.tap(find.byIcon(Icons.chevron_left));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.goBackPage()).called(2);
      });

      testWidgets('Navigation works regardless of input state', (tester) async {
        // Arrange - Test with input
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        await tester.tap(find.text('キャンセル'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.goBackPage()).called(1);

        // Test with empty input
        clearInteractions(mockController);
        mockState.inputPhoneNumber.value = '';
        await tester.pump();

        await tester.tap(find.byIcon(Icons.chevron_left));
        await tester.pumpAndSettle();

        verify(mockController.goBackPage()).called(1);
      });
    });

    // 输入交互测试
    group('Input Interaction Tests', () {
      testWidgets('TextField input calls onChangePhoneInput', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Enter text in the input field
        await tester.enterText(find.byType(TextField), '123456789');
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onChangePhoneInput('123456789')).called(1);
      });

      testWidgets('Clear button calls clearInput when visible', (tester) async {
        // Arrange - Set input to make clear button visible
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Verify clear button is visible and tap it
        final clearButton = find.byIcon(Icons.cancel);
        expect(clearButton, findsOneWidget);

        await tester.tap(clearButton);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.clearInput()).called(1);
      });

      testWidgets('TextField onChanged handles incremental input', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Simulate incremental typing
        final textField = find.byType(TextField);

        await tester.enterText(textField, '1');
        verify(mockController.onChangePhoneInput('1')).called(1);

        await tester.enterText(textField, '12');
        verify(mockController.onChangePhoneInput('12')).called(1);

        await tester.enterText(textField, '123');
        verify(mockController.onChangePhoneInput('123')).called(1);
      });

      testWidgets('Input interactions work with different input lengths', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        final textField = find.byType(TextField);

        // Test short input
        await tester.enterText(textField, '1');
        verify(mockController.onChangePhoneInput('1')).called(1);

        // Test normal length input
        await tester.enterText(textField, '1234567890');
        verify(mockController.onChangePhoneInput('1234567890')).called(1);

        // Test long input
        await tester.enterText(textField, '12345678901234567890');
        verify(mockController.onChangePhoneInput('12345678901234567890')).called(1);
      });

      testWidgets('Clear button interaction only available when input exists', (tester) async {
        // Arrange - Start with empty input
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - No clear button initially
        expect(find.byIcon(Icons.cancel), findsNothing);

        // Add input to make clear button appear
        mockState.inputPhoneNumber.value = '123';
        await tester.pump();

        // Assert - Clear button now visible
        expect(find.byIcon(Icons.cancel), findsOneWidget);

        // Tap clear button
        await tester.tap(find.byIcon(Icons.cancel));
        await tester.pumpAndSettle();

        verify(mockController.clearInput()).called(1);
      });
    });

    // 表单提交测试
    group('Form Submission Tests', () {
      testWidgets('Confirm button calls sureBtnOnClick', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap confirm button
        await tester.tap(find.text('確定'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.sureBtnOnClick()).called(1);
      });

      testWidgets('Confirm button works with empty input', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap confirm button even with empty input
        await tester.tap(find.text('確定'));
        await tester.pumpAndSettle();

        // Assert - Should still call the method (validation handled by controller)
        verify(mockController.sureBtnOnClick()).called(1);
      });

      testWidgets('Confirm button works with various input states', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Test with different input states
        final inputStates = ['', '1', '123', '1234567890', 'abc123'];

        for (String inputState in inputStates) {
          // Reset
          clearInteractions(mockController);
          mockState.inputPhoneNumber.value = inputState;

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          await tester.tap(find.text('確定'));
          await tester.pumpAndSettle();

          // Assert
          verify(mockController.sureBtnOnClick()).called(1);
        }
      });

      testWidgets('Multiple confirm button taps are handled', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '1234567890';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Multiple taps
        await tester.tap(find.text('確定'));
        await tester.pumpAndSettle();
        await tester.tap(find.text('確定'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.sureBtnOnClick()).called(2);
      });
    });

    // 键盘交互测试
    group('Keyboard Interaction Tests', () {
      testWidgets('GestureDetector tap calls hideKeyboard', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap on the GestureDetector (background area)
        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.hideKeyboard()).called(1);
      });

      testWidgets('Background tap works regardless of content state', (tester) async {
        // Test with empty state
        mockState.nationCode.value = '';
        mockState.inputPhoneNumber.value = '';

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();

        verify(mockController.hideKeyboard()).called(1);

        // Test with full state
        clearInteractions(mockController);
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';
        await tester.pump();

        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();

        verify(mockController.hideKeyboard()).called(1);
      });

      testWidgets('Multiple background taps call hideKeyboard multiple times', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Multiple taps
        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();
        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();
        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.hideKeyboard()).called(3);
      });

      testWidgets('GestureDetector tap works at different screen positions', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Test tapping at one position that should trigger hideKeyboard
        await tester.tapAt(const Offset(100, 300)); // Center area
        await tester.pumpAndSettle();
        verify(mockController.hideKeyboard()).called(1);

        // Reset and test again with different approach
        clearInteractions(mockController);

        // Tap directly on the GestureDetector widget
        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();
        verify(mockController.hideKeyboard()).called(1);
      });
    });

    // 边界情况交互测试
    group('Edge Case Interaction Tests', () {
      testWidgets('Rapid sequential interactions are handled correctly', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Rapid sequential interactions
        await tester.tap(find.byIcon(Icons.cancel)); // Clear
        await tester.tap(find.text('確定')); // Confirm
        await tester.tap(find.byIcon(Icons.chevron_left)); // Back
        await tester.tap(find.byType(GestureDetector).first); // Hide keyboard
        await tester.pumpAndSettle();

        // Assert all interactions were captured
        verify(mockController.clearInput()).called(1);
        verify(mockController.sureBtnOnClick()).called(1);
        verify(mockController.goBackPage()).called(1);
        verify(mockController.hideKeyboard()).called(1);
      });

      testWidgets('Interactions work during state transitions', (tester) async {
        // Arrange
        mockState.nationCode.value = '';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Interaction during loading state
        await tester.tap(find.text('確定'));
        verify(mockController.sureBtnOnClick()).called(1);

        // Change state and interact again
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';
        await tester.pump();

        await tester.tap(find.text('キャンセル'));
        verify(mockController.goBackPage()).called(1);
      });

      testWidgets('Button interactions work with different widget states', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';

        // Test confirm button with different input states
        final testStates = ['', '1', '123456789', 'invalid'];

        for (String state in testStates) {
          clearInteractions(mockController);
          mockState.inputPhoneNumber.value = state;

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // All buttons should work regardless of input state
          await tester.tap(find.text('確定'));
          verify(mockController.sureBtnOnClick()).called(1);

          await tester.tap(find.text('キャンセル'));
          verify(mockController.goBackPage()).called(1);
        }
      });

      testWidgets('Sequential rapid interactions are handled gracefully', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '123';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Rapidly execute sequential interactions (as fast as possible)
        await tester.tap(find.text('確定'));
        await tester.tap(find.text('キャンセル'));
        await tester.tap(find.byType(GestureDetector).first);
        await tester.pumpAndSettle();

        // Assert that all interactions were processed
        verify(mockController.sureBtnOnClick()).called(1);
        verify(mockController.goBackPage()).called(1);
        verify(mockController.hideKeyboard()).called(1);
      });

      testWidgets('Input field interactions work during various UI states', (tester) async {
        // Arrange
        mockState.nationCode.value = '81';
        mockState.inputPhoneNumber.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        final textField = find.byType(TextField);

        // Test input during different states
        await tester.enterText(textField, 'test input');
        verify(mockController.onChangePhoneInput('test input')).called(1);

        // Make clear button appear and test it
        mockState.inputPhoneNumber.value = 'test';
        await tester.pump();

        await tester.tap(find.byIcon(Icons.cancel));
        verify(mockController.clearInput()).called(1);

        // Test input again after clear
        await tester.enterText(textField, 'new input');
        verify(mockController.onChangePhoneInput('new input')).called(1);
      });
    });
  });
}
