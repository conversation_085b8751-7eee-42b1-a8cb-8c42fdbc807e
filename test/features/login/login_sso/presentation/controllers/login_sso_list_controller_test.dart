import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_list_controller.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:logger/logger.dart';

import 'login_sso_list_controller_test.mocks.dart';

@GenerateMocks([LoginSsoUseCase, NavigationService, DialogService, IEnvHelper, IStorageUtils])
void main() {
  late MockLoginSsoUseCase mockLoginSsoUseCase;
  late MockNavigationService mockNavigationService;
  late MockDialogService mockDialogService;
  late MockIEnvHelper mockEnvHelper;
  late MockIStorageUtils mockStorageUtils;
  late LoginSsoListController controller;

  setUpAll(() {
    // 初始化Flutter测试绑定 - 这对于使用Get.dialog等UI相关功能是必需的
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    // 初始化LogUtil
    LogUtil.initialize();

    mockLoginSsoUseCase = MockLoginSsoUseCase();
    mockNavigationService = MockNavigationService();
    mockDialogService = MockDialogService();
    mockEnvHelper = MockIEnvHelper();
    mockStorageUtils = MockIStorageUtils();

    controller = LoginSsoListController(
      loginSsoUseCase: mockLoginSsoUseCase,
      navigationService: mockNavigationService,
      dialogService: mockDialogService,
      envHelper: mockEnvHelper,
      storageUtils: mockStorageUtils,
    );
  });

  tearDown(() {
    controller.dispose();
  });

  group('LoginSsoListController - 初始化测试', () {
    test('应该正确初始化所有属性', () {
      // Assert
      expect(controller.loginSsoUseCase, equals(mockLoginSsoUseCase));
      expect(controller.navigationService, equals(mockNavigationService));
      expect(controller.dialogService, equals(mockDialogService));
      expect(controller.envHelper, equals(mockEnvHelper));
      expect(controller.storageUtils, equals(mockStorageUtils));
      expect(controller.ssoItemList, isA<RxList<TenantSsoModel>>());
      expect(controller.isSsoListEdit, isA<RxBool>());
    });

    test('应该初始化空的SSO项目列表', () {
      // Assert
      expect(controller.ssoItemList.value, isEmpty);
    });

    test('应该初始化编辑模式为false', () {
      // Assert
      expect(controller.isSsoListEdit.value, isFalse);
    });

    test('onInit应该从存储加载SSO数据', () async {
      // Arrange
      const testEnvironment = 'test_env';
      const storageKey = testEnvironment + StorageKeys.ssoTenantUrl;
      const jsonString = '{"test": "data"}';
      final mockSsoList = [
        TenantSsoModel(ssoUrl: 'https://test1.com', tenantId: 'tenant1', tenantName: 'Test SSO 1', zoneId: 'zone1'),
        TenantSsoModel(ssoUrl: 'https://test2.com', tenantId: 'tenant2', tenantName: 'Test SSO 2', zoneId: 'zone2'),
      ];

      when(mockEnvHelper.getEnvironment()).thenReturn(testEnvironment);
      when(mockStorageUtils.getValue<String>(storageKey)).thenReturn(jsonString);
      when(mockLoginSsoUseCase.fromJsonList(jsonString: jsonString)).thenReturn(mockSsoList);

      // Act
      controller.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成

      // Assert
      verify(mockEnvHelper.getEnvironment()).called(1);
      verify(mockStorageUtils.getValue<String>(storageKey)).called(1);
      verify(mockLoginSsoUseCase.fromJsonList(jsonString: jsonString)).called(1);
      expect(controller.ssoItemList.value, equals(mockSsoList));
      expect(controller.ssoItemList.length, equals(2));
    });

    test('onInit应该处理空存储数据', () async {
      // Arrange
      const testEnvironment = 'test_env';
      const storageKey = testEnvironment + StorageKeys.ssoTenantUrl;
      final emptySsoList = <TenantSsoModel>[];

      when(mockEnvHelper.getEnvironment()).thenReturn(testEnvironment);
      when(mockStorageUtils.getValue<String>(storageKey)).thenReturn(null);
      when(mockLoginSsoUseCase.fromJsonList(jsonString: null)).thenReturn(emptySsoList);

      // Act
      controller.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成

      // Assert
      verify(mockEnvHelper.getEnvironment()).called(1);
      verify(mockStorageUtils.getValue<String>(storageKey)).called(1);
      verify(mockLoginSsoUseCase.fromJsonList(jsonString: null)).called(1);
      expect(controller.ssoItemList.value, equals(emptySsoList));
      expect(controller.ssoItemList.isEmpty, isTrue);
    });
  });

  group('LoginSsoListController - 编辑模式测试', () {
    test('ssoListEditOnClick应该切换编辑模式状态', () {
      // Arrange
      expect(controller.isSsoListEdit.value, isFalse);

      // Act - 第一次点击，从false变为true
      controller.ssoListEditOnClick();

      // Assert
      expect(controller.isSsoListEdit.value, isTrue);

      // Act - 第二次点击，从true变为false
      controller.ssoListEditOnClick();

      // Assert
      expect(controller.isSsoListEdit.value, isFalse);
    });

    test('编辑模式状态应该是响应式的', () {
      // Arrange
      bool? observedValue;
      controller.isSsoListEdit.listen((value) {
        observedValue = value;
      });

      // Act
      controller.ssoListEditOnClick();

      // Assert
      expect(observedValue, isTrue);
      expect(controller.isSsoListEdit.value, isTrue);

      // Act
      controller.ssoListEditOnClick();

      // Assert
      expect(observedValue, isFalse);
      expect(controller.isSsoListEdit.value, isFalse);
    });
  });

  group('LoginSsoListController - 删除功能测试', () {
    late TenantSsoModel testSsoItem;

    setUp(() async {
      testSsoItem = TenantSsoModel(
        tenantId: 'test-tenant',
        tenantName: 'Test Tenant',
        ssoUrl: 'https://test-sso.com',
        zoneId: 'test-zone',
      );

      // 初始化 _appModel (用于删除操作)
      when(mockEnvHelper.getEnvironment()).thenReturn('test_env');
      when(mockStorageUtils.getValue<String>('test_env' + StorageKeys.ssoTenantUrl)).thenReturn(null);
      when(mockLoginSsoUseCase.fromJsonList(jsonString: null)).thenReturn([]);

      controller.onInit();
      await Future.delayed(Duration.zero);

      // 添加测试项目到列表
      controller.ssoItemList.add(testSsoItem);
    });

    test('ssoListDeleteOnClick应该显示删除确认对话框', () async {
      // Arrange
      when(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((_) async {});

      // Act
      controller.ssoListDeleteOnClick(ssoItem: testSsoItem);
      await Future.delayed(Duration.zero);

      // Assert
      verify(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).called(1);
    });

    test('确认删除应该执行完整的删除流程', () async {
      // Arrange
      const testEnvironment = 'test_env';
      const storageKey = testEnvironment + StorageKeys.ssoTenantUrl;
      const processedData = '{"processed": "data"}';

      when(mockEnvHelper.getEnvironment()).thenReturn(testEnvironment);
      when(mockLoginSsoUseCase.processTenants(tenants: anyNamed('tenants'))).thenReturn(processedData);
      when(mockStorageUtils.setValue<String>(storageKey, processedData)).thenAnswer((_) async {});
      when(mockDialogService.showToast('SSO設定を削除しました')).thenAnswer((_) async {});

      // 模拟确认对话框
      late VoidCallback? onConfirmCallback;
      when(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((invocation) async {
        onConfirmCallback = invocation.namedArguments[Symbol('onConfirm')];
      });

      expect(controller.ssoItemList.length, equals(1));
      expect(controller.ssoItemList.contains(testSsoItem), isTrue);

      // Act - 触发删除操作
      controller.ssoListDeleteOnClick(ssoItem: testSsoItem);
      await Future.delayed(Duration.zero);

      // 执行确认回调
      if (onConfirmCallback != null) {
        onConfirmCallback!();
        await Future.delayed(Duration.zero); // 等待异步操作
      }

      // Assert
      expect(controller.ssoItemList.length, equals(0));
      expect(controller.ssoItemList.contains(testSsoItem), isFalse);
      verify(mockLoginSsoUseCase.processTenants(tenants: anyNamed('tenants'))).called(1);
      verify(mockStorageUtils.setValue<String>(storageKey, processedData)).called(1);
      verify(mockDialogService.showToast('SSO設定を削除しました')).called(1);
    });

    test('取消删除应该不执行删除操作', () async {
      // Arrange
      when(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((_) async {});

      expect(controller.ssoItemList.length, equals(1));
      expect(controller.ssoItemList.contains(testSsoItem), isTrue);

      // Act - 只触发对话框，不执行确认回调
      controller.ssoListDeleteOnClick(ssoItem: testSsoItem);
      await Future.delayed(Duration.zero);

      // Assert - 列表应该保持不变
      expect(controller.ssoItemList.length, equals(1));
      expect(controller.ssoItemList.contains(testSsoItem), isTrue);
      verifyNever(mockLoginSsoUseCase.processTenants(tenants: anyNamed('tenants')));
      verifyNever(mockStorageUtils.setValue<String>(any, any));
      verifyNever(mockDialogService.showToast(any));
    });

    test('删除操作应该正确更新存储数据', () async {
      // Arrange
      const testEnvironment = 'test_env';
      const storageKey = testEnvironment + StorageKeys.ssoTenantUrl;
      final remainingItems = <TenantSsoModel>[];
      const processedData = '{"remaining": "items"}';

      when(mockEnvHelper.getEnvironment()).thenReturn(testEnvironment);
      when(mockLoginSsoUseCase.processTenants(tenants: remainingItems)).thenReturn(processedData);
      when(mockStorageUtils.setValue<String>(storageKey, processedData)).thenAnswer((_) async {});
      when(mockDialogService.showToast('SSO設定を削除しました')).thenAnswer((_) async {});

      // 模拟确认删除
      late VoidCallback? onConfirmCallback;
      when(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((invocation) async {
        onConfirmCallback = invocation.namedArguments[Symbol('onConfirm')];
      });

      // Act
      controller.ssoListDeleteOnClick(ssoItem: testSsoItem);
      await Future.delayed(Duration.zero);

      if (onConfirmCallback != null) {
        onConfirmCallback!();
        await Future.delayed(Duration.zero); // 等待异步操作
      }

      // Assert - 验证存储操作
      verify(mockStorageUtils.setValue<String>(storageKey, processedData)).called(1);
      verify(mockLoginSsoUseCase.processTenants(tenants: argThat(isEmpty, named: 'tenants'))).called(1);
    });
  });

  group('LoginSsoListController - 导航功能测试', () {
    test('goBackPage应该调用导航服务返回上一页', () {
      // Act
      controller.goBackPage();

      // Assert
      verify(mockNavigationService.goBack()).called(1);
    });

    test('ssoListAddOnClick应该导航到SSO添加页面', () {
      // Arrange
      when(mockNavigationService.navigateTo(AutoRoutes.loginSso)).thenAnswer((_) async => null);

      // Act
      controller.ssoListAddOnClick();

      // Assert
      verify(mockNavigationService.navigateTo(AutoRoutes.loginSso)).called(1);
    });

    test('ssoListGoToWebViewOnClick应该调用SSO UseCase', () {
      // Arrange
      final testSsoItem = TenantSsoModel(
        tenantId: 'test-tenant',
        tenantName: 'Test Tenant',
        ssoUrl: 'https://test-sso.com',
        zoneId: 'test-zone',
      );

      when(mockLoginSsoUseCase.call('https://test-sso.com')).thenAnswer((_) async {});

      // Act
      controller.ssoListGoToWebViewOnClick(ssoItem: testSsoItem);

      // Assert
      verify(mockLoginSsoUseCase.call('https://test-sso.com')).called(1);
    });
  });

  group('LoginSsoListController - 状态管理测试', () {
    test('ssoItemList应该是响应式的', () {
      // Arrange
      List<TenantSsoModel>? observedList;
      controller.ssoItemList.listen((list) {
        observedList = list;
      });

      final testItem = TenantSsoModel(
        tenantId: 'test-tenant',
        tenantName: 'Test Tenant',
        ssoUrl: 'https://test-sso.com',
        zoneId: 'test-zone',
      );

      // Act - 添加项目
      controller.ssoItemList.add(testItem);

      // Assert
      expect(observedList, contains(testItem));
      expect(controller.ssoItemList.length, equals(1));

      // Act - 删除项目
      controller.ssoItemList.remove(testItem);

      // Assert
      expect(observedList, isNot(contains(testItem)));
      expect(controller.ssoItemList.length, equals(0));
    });

    test('isSsoListEdit状态变化应该被正确观察', () {
      // Arrange
      List<bool> observedValues = [];
      controller.isSsoListEdit.listen((value) {
        observedValues.add(value);
      });

      // Act - 多次切换编辑模式
      controller.ssoListEditOnClick();
      controller.ssoListEditOnClick();
      controller.ssoListEditOnClick();

      // Assert
      expect(observedValues, equals([true, false, true]));
      expect(controller.isSsoListEdit.value, isTrue);
    });

    test('状态变化应该立即反映在UI中', () {
      // Arrange
      expect(controller.isSsoListEdit.value, isFalse);
      expect(controller.ssoItemList.isEmpty, isTrue);

      final testItem = TenantSsoModel(
        tenantId: 'test-tenant',
        tenantName: 'Test Tenant',
        ssoUrl: 'https://test-sso.com',
        zoneId: 'test-zone',
      );

      // Act & Assert - 状态变化应该立即生效
      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isTrue);

      controller.ssoItemList.add(testItem);
      expect(controller.ssoItemList.contains(testItem), isTrue);

      controller.ssoItemList.clear();
      expect(controller.ssoItemList.isEmpty, isTrue);

      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isFalse);
    });
  });

  group('LoginSsoListController - 边界条件测试', () {
    test('空列表状态下的操作应该正常处理', () {
      // Arrange
      expect(controller.ssoItemList.isEmpty, isTrue);

      // Act & Assert - 编辑模式应该正常切换
      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isTrue);

      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isFalse);

      // Act & Assert - 清空空列表应该不产生错误
      controller.ssoItemList.clear();
      expect(controller.ssoItemList.isEmpty, isTrue);
    });

    test('单项列表的完整操作流程', () async {
      // Arrange
      final testItem = TenantSsoModel(
        tenantId: 'single-tenant',
        tenantName: 'Single Tenant',
        ssoUrl: 'https://single-sso.com',
        zoneId: 'single-zone',
      );

      // 初始化环境
      when(mockEnvHelper.getEnvironment()).thenReturn('test_env');
      when(mockStorageUtils.getValue<String>('test_env' + StorageKeys.ssoTenantUrl)).thenReturn(null);
      when(mockLoginSsoUseCase.fromJsonList(jsonString: null)).thenReturn([]);
      controller.onInit();
      await Future.delayed(Duration.zero);

      // Act - 添加单个项目
      controller.ssoItemList.add(testItem);
      expect(controller.ssoItemList.length, equals(1));
      expect(controller.ssoItemList.first, equals(testItem));

      // Act - 切换编辑模式
      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isTrue);

      // Act - 验证单项删除流程（设置Mock）
      when(mockLoginSsoUseCase.processTenants(tenants: anyNamed('tenants'))).thenReturn('{}');
      when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});
      when(mockDialogService.showToast('SSO設定を削除しました')).thenAnswer((_) async {});

      late VoidCallback? onConfirmCallback;
      when(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((invocation) async {
        onConfirmCallback = invocation.namedArguments[Symbol('onConfirm')];
      });

      controller.ssoListDeleteOnClick(ssoItem: testItem);
      await Future.delayed(Duration.zero);

      if (onConfirmCallback != null) {
        onConfirmCallback!();
        await Future.delayed(Duration.zero);
      }

      // Assert - 列表应该为空
      expect(controller.ssoItemList.isEmpty, isTrue);
    });

    test('多项列表的批量操作处理', () {
      // Arrange
      final multipleItems = List.generate(
        5,
        (index) => TenantSsoModel(
          tenantId: 'tenant-$index',
          tenantName: 'Tenant $index',
          ssoUrl: 'https://sso-$index.com',
          zoneId: 'zone-$index',
        ),
      );

      // Act - 批量添加
      controller.ssoItemList.addAll(multipleItems);
      expect(controller.ssoItemList.length, equals(5));

      // Act - 切换编辑模式
      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isTrue);

      // Act - 移除特定项目
      final itemToRemove = multipleItems[2]; // 移除中间项
      controller.ssoItemList.remove(itemToRemove);
      expect(controller.ssoItemList.length, equals(4));
      expect(controller.ssoItemList.contains(itemToRemove), isFalse);

      // Act - 验证剩余项目
      expect(controller.ssoItemList.contains(multipleItems[0]), isTrue);
      expect(controller.ssoItemList.contains(multipleItems[1]), isTrue);
      expect(controller.ssoItemList.contains(multipleItems[3]), isTrue);
      expect(controller.ssoItemList.contains(multipleItems[4]), isTrue);

      // Act - 清空所有项目
      controller.ssoItemList.clear();
      expect(controller.ssoItemList.isEmpty, isTrue);

      // Act - 验证编辑模式仍然可以切换
      controller.ssoListEditOnClick();
      expect(controller.isSsoListEdit.value, isFalse);
    });
  });

  group('LoginSsoListController - 集成测试', () {
    test('完整的删除流程应该正常工作', () async {
      // Arrange
      final testItem = TenantSsoModel(
        tenantId: 'integration-tenant',
        tenantName: 'Integration Tenant',
        ssoUrl: 'https://integration-sso.com',
        zoneId: 'integration-zone',
      );

      // 初始化环境
      when(mockEnvHelper.getEnvironment()).thenReturn('test_env');
      when(mockStorageUtils.getValue<String>('test_env' + StorageKeys.ssoTenantUrl)).thenReturn(null);
      when(mockLoginSsoUseCase.fromJsonList(jsonString: null)).thenReturn([]);
      controller.onInit();
      await Future.delayed(Duration.zero);

      controller.ssoItemList.add(testItem);
      expect(controller.ssoItemList.contains(testItem), isTrue);

      // 模拟正常操作
      when(mockLoginSsoUseCase.processTenants(tenants: anyNamed('tenants'))).thenReturn('{}');
      when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});
      when(mockDialogService.showToast('SSO設定を削除しました')).thenAnswer((_) async {});

      late VoidCallback? onConfirmCallback;
      when(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((invocation) async {
        onConfirmCallback = invocation.namedArguments[Symbol('onConfirm')];
      });

      // Act
      controller.ssoListDeleteOnClick(ssoItem: testItem);
      await Future.delayed(Duration.zero);

      // 执行确认回调
      if (onConfirmCallback != null) {
        onConfirmCallback!();
        await Future.delayed(Duration.zero);
      }

      // Assert - 验证完整流程
      verify(
        mockDialogService.show(
          content: 'SMFLのシングルサインオン(SSO)設定を削除します',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).called(1);
      verify(mockLoginSsoUseCase.processTenants(tenants: anyNamed('tenants'))).called(1);
      verify(mockStorageUtils.setValue<String>(any, any)).called(1);
      verify(mockDialogService.showToast('SSO設定を削除しました')).called(1);

      // 验证列表状态（已从内存中删除）
      expect(controller.ssoItemList.contains(testItem), isFalse);
    });

    test('完整的导航流程应该正常工作', () async {
      // Arrange
      final testItem = TenantSsoModel(
        tenantId: 'nav-tenant',
        tenantName: 'Navigation Tenant',
        ssoUrl: 'https://nav-sso.com',
        zoneId: 'nav-zone',
      );

      when(mockNavigationService.navigateTo(AutoRoutes.loginSso)).thenAnswer((_) async => null);
      when(mockLoginSsoUseCase.call('https://nav-sso.com')).thenAnswer((_) async {});

      // Act & Assert - 验证导航方法
      controller.ssoListAddOnClick();
      verify(mockNavigationService.navigateTo(AutoRoutes.loginSso)).called(1);

      controller.goBackPage();
      verify(mockNavigationService.goBack()).called(1);

      controller.ssoListGoToWebViewOnClick(ssoItem: testItem);
      verify(mockLoginSsoUseCase.call('https://nav-sso.com')).called(1);
    });
  });
}
