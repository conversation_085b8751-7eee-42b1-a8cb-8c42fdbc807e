import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/asset_type_repository_impl.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

// Import generated mock file
import '../../../core/utils/dio_utils_test.mocks.dart';

void main() {
  late AssetTypeRepositoryImpl repository;
  late MockDioUtil mockDioUtil;

  setUp(() {
    LogUtil.initialize(); // Initialize log utility
    mockDioUtil = MockDioUtil(); // Create mock Dio instance
    repository = AssetTypeRepositoryImpl(dioUtil: mockDioUtil); // Initialize repository
  });

  tearDown(() {
    reset(mockDioUtil); // Reset mock to avoid contamination between tests
  });

  /// ----------------------------
  /// AssetTypeRepositoryImpl Tests
  /// ----------------------------
  /// 测试 getAllAssetTypes 方法
  group('Tests for getAllAssetTypes method', () {
    /// 成功获取资产类型列表
    test('Should return a list of asset types on successful data retrieval', () async {
      // 应在成功获取数据时返回资产类型列表

      // Arrange
      when(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).thenAnswer(
        (_) async => Response(
          data: {
            'code': 0,
            'msg': '',
            'assetTypeList': [
              {'assetTypeId': 1, 'assetTypeName': '类型 A'},
              {'assetTypeId': 2, 'assetTypeName': '类型 B'},
            ],
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: '/fake-endpoint'),
        ),
      );

      // Act
      final AssetTypeResponse result = await repository.getAllAssetTypes();

      // Assert
      expect(result.assetTypeList?.length, 2);
      expect(result.assetTypeList?[0]?.assetTypeId, 1);
      expect(result.assetTypeList?[0]?.assetTypeName, '类型 A');
      verify(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).called(1);
    });

    /// 触发 BusinessException
    test('Should throw BusinessException when assetTypeList key is missing in the response', () async {
      // 当响应数据缺少 assetTypeList 时，应抛出 BusinessException

      // Arrange
      when(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).thenAnswer(
        (_) async => Response(
          data: {'code': 0, 'msg': '', 'wrongKey': []},
          statusCode: 200,
          requestOptions: RequestOptions(path: '/fake-endpoint'),
        ),
      );

      // Act & Assert
      expect(() async => await repository.getAllAssetTypes(), throwsA(isA<BusinessException>()));

      verify(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).called(1);
    });

    /// 触发 SystemException
    test('Should throw SystemException when DioUtil.get throws an exception', () async {
      // 当 DioUtil.get 抛出异常时，应抛出 SystemException

      // Arrange
      when(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).thenThrow(Exception('网络错误'));

      // Act & Assert
      expect(() async => await repository.getAllAssetTypes(), throwsA(isA<SystemException>()));

      verify(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).called(1);
    });
  });

  /// 测试 checkAssetAuthority 方法
  group('Tests for checkAssetAuthority method', () {
    /// 成功返回权限检查结果
    test('Should return 1 when authority exists', () async {
      // 应在权限存在时返回 1

      // Arrange
      when(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).thenAnswer(
        (_) async => Response(
          data: {'count': 1},
          statusCode: 200,
          requestOptions: RequestOptions(path: '/fake-endpoint'),
        ),
      );

      // Act
      final result = await repository.checkAssetAuthority(1, 0);

      // Assert
      expect(result, 1);
      verify(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).called(1);
    });

    /// 触发 SystemException
    test('Should throw SystemException when DioUtil.get throws an exception', () async {
      // 当 DioUtil.get 抛出异常时，应抛出 SystemException

      // Arrange
      when(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).thenThrow(Exception('网络错误'));

      // Act & Assert
      expect(() async => await repository.checkAssetAuthority(1, 0), throwsA(isA<SystemException>()));

      verify(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).called(1);
    });

    /// 触发 BusinessException
    test('Should throw BusinessException when invalid data is returned from authority check', () async {
      // 当权限检查返回无效数据时，应抛出 BusinessException

      // Arrange
      when(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).thenThrow(BusinessException('权限检查失败'));

      // Act & Assert
      expect(() async => await repository.checkAssetAuthority(1, 0), throwsA(isA<BusinessException>()));

      verify(mockDioUtil.get(any, queryParams: anyNamed('queryParams'))).called(1);
    });
  });
}
