// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/home/<USER>/usecase/get_home_page_list_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_model.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_page_list_api_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart'
    as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAPIHomePageListByMobileHomeSettingRoot_0 extends _i1.SmartFake
    implements _i2.APIHomePageListByMobileHomeSettingRoot {
  _FakeAPIHomePageListByMobileHomeSettingRoot_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [HomeRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockHomeRepository extends _i1.Mock implements _i3.HomeRepository {
  MockHomeRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.APIHomePageListByMobileHomeSettingRoot>
  getHomePageListByMobileHomeSetting() =>
      (super.noSuchMethod(
            Invocation.method(#getHomePageListByMobileHomeSetting, []),
            returnValue:
                _i4.Future<_i2.APIHomePageListByMobileHomeSettingRoot>.value(
                  _FakeAPIHomePageListByMobileHomeSettingRoot_0(
                    this,
                    Invocation.method(#getHomePageListByMobileHomeSetting, []),
                  ),
                ),
          )
          as _i4.Future<_i2.APIHomePageListByMobileHomeSettingRoot>);

  @override
  _i4.Future<_i5.HomeModel?> getHomePageProcessSummary() =>
      (super.noSuchMethod(
            Invocation.method(#getHomePageProcessSummary, []),
            returnValue: _i4.Future<_i5.HomeModel?>.value(),
          )
          as _i4.Future<_i5.HomeModel?>);

  @override
  _i4.Future<dynamic> httpGetNotificationAnyNew() =>
      (super.noSuchMethod(
            Invocation.method(#httpGetNotificationAnyNew, []),
            returnValue: _i4.Future<dynamic>.value(),
          )
          as _i4.Future<dynamic>);

  @override
  _i4.Future<List<_i6.SharedRoleModel>> getRoleList() =>
      (super.noSuchMethod(
            Invocation.method(#getRoleList, []),
            returnValue: _i4.Future<List<_i6.SharedRoleModel>>.value(
              <_i6.SharedRoleModel>[],
            ),
          )
          as _i4.Future<List<_i6.SharedRoleModel>>);
}
