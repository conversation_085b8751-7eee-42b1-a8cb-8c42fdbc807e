// Mocks generated by <PERSON>ckito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/font_size_setting/presentation/controllers/font_size_setting_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i9;

import 'package:asset_force_mobile_v2/core/platform/method_channel.dart'
    as _i12;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i8;
import 'package:asset_force_mobile_v2/core/services/font_scale_service.dart'
    as _i4;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i10;
import 'package:flutter/material.dart' as _i11;
import 'package:flutter/services.dart' as _i3;
import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRxDouble_0 extends _i1.SmartFake implements _i2.RxDouble {
  _FakeRxDouble_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_1<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMethodCodec_2 extends _i1.SmartFake implements _i3.MethodCodec {
  _FakeMethodCodec_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBinaryMessenger_3 extends _i1.SmartFake
    implements _i3.BinaryMessenger {
  _FakeBinaryMessenger_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FontScaleService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFontScaleService extends _i1.Mock implements _i4.FontScaleService {
  @override
  String get fontSize =>
      (super.noSuchMethod(
            Invocation.getter(#fontSize),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#fontSize),
            ),
            returnValueForMissingStub: _i5.dummyValue<String>(
              this,
              Invocation.getter(#fontSize),
            ),
          )
          as String);

  @override
  set fontSize(String? _fontSize) => super.noSuchMethod(
    Invocation.setter(#fontSize, _fontSize),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RxDouble get textScaleFactor =>
      (super.noSuchMethod(
            Invocation.getter(#textScaleFactor),
            returnValue: _FakeRxDouble_0(
              this,
              Invocation.getter(#textScaleFactor),
            ),
            returnValueForMissingStub: _FakeRxDouble_0(
              this,
              Invocation.getter(#textScaleFactor),
            ),
          )
          as _i2.RxDouble);

  @override
  set textScaleFactor(_i2.RxDouble? _textScaleFactor) => super.noSuchMethod(
    Invocation.setter(#textScaleFactor, _textScaleFactor),
    returnValueForMissingStub: null,
  );

  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_1<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_1<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_1<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_1<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i6.Future<void> onInit() =>
      (super.noSuchMethod(
            Invocation.method(#onInit, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void toggleFontSize(String? size) => super.noSuchMethod(
    Invocation.method(#toggleFontSize, [size]),
    returnValueForMissingStub: null,
  );

  @override
  bool isBig() =>
      (super.noSuchMethod(
            Invocation.method(#isBig, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i6.Future<void> save() =>
      (super.noSuchMethod(
            Invocation.method(#save, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i7.IStorageUtils {
  @override
  _i6.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> initPersonalStorage({required String? cryptedName}) =>
      (super.noSuchMethod(
            Invocation.method(#initPersonalStorage, [], {
              #cryptedName: cryptedName,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  T? getValue<T>(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getValue, [key], {#personal: personal}),
            returnValueForMissingStub: null,
          )
          as T?);

  @override
  _i6.Future<void> setValue<T>(
    String? key,
    T? value, {
    bool? personal = false,
    int? durationInSeconds,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setValue,
              [key, value],
              {#personal: personal, #durationInSeconds: durationInSeconds},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> removeValue(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#removeValue, [key], {#personal: personal}),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  bool containsKey(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key], {#personal: personal}),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i6.Future<void> clearAll({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, [], {#personal: personal}),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  List<String> getAllKeys({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, [], {#personal: personal}),
            returnValue: <String>[],
            returnValueForMissingStub: <String>[],
          )
          as List<String>);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
            returnValueForMissingStub: _i5.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i6.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i8.DialogService {
  @override
  _i6.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i9.VoidCallback? onConfirm,
    _i9.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i10.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i9.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i11.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i10.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PlatformChannel].
///
/// See the documentation for Mockito's code generation for more information.
class MockPlatformChannel extends _i1.Mock implements _i12.PlatformChannel {
  @override
  String get name =>
      (super.noSuchMethod(
            Invocation.getter(#name),
            returnValue: _i5.dummyValue<String>(this, Invocation.getter(#name)),
            returnValueForMissingStub: _i5.dummyValue<String>(
              this,
              Invocation.getter(#name),
            ),
          )
          as String);

  @override
  _i3.MethodCodec get codec =>
      (super.noSuchMethod(
            Invocation.getter(#codec),
            returnValue: _FakeMethodCodec_2(this, Invocation.getter(#codec)),
            returnValueForMissingStub: _FakeMethodCodec_2(
              this,
              Invocation.getter(#codec),
            ),
          )
          as _i3.MethodCodec);

  @override
  _i3.BinaryMessenger get binaryMessenger =>
      (super.noSuchMethod(
            Invocation.getter(#binaryMessenger),
            returnValue: _FakeBinaryMessenger_3(
              this,
              Invocation.getter(#binaryMessenger),
            ),
            returnValueForMissingStub: _FakeBinaryMessenger_3(
              this,
              Invocation.getter(#binaryMessenger),
            ),
          )
          as _i3.BinaryMessenger);

  @override
  _i6.Future<void> syncStorage(Map<String, Object>? data) =>
      (super.noSuchMethod(
            Invocation.method(#syncStorage, [data]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEventToIonic(Map<String, Object>? data) =>
      (super.noSuchMethod(
            Invocation.method(#sendEventToIonic, [data]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<T?> invokeMethod<T>(String? method, [dynamic arguments]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeMethod, [method, arguments]),
            returnValue: _i6.Future<T?>.value(),
            returnValueForMissingStub: _i6.Future<T?>.value(),
          )
          as _i6.Future<T?>);

  @override
  _i6.Future<List<T>?> invokeListMethod<T>(
    String? method, [
    dynamic arguments,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeListMethod, [method, arguments]),
            returnValue: _i6.Future<List<T>?>.value(),
            returnValueForMissingStub: _i6.Future<List<T>?>.value(),
          )
          as _i6.Future<List<T>?>);

  @override
  _i6.Future<Map<K, V>?> invokeMapMethod<K, V>(
    String? method, [
    dynamic arguments,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeMapMethod, [method, arguments]),
            returnValue: _i6.Future<Map<K, V>?>.value(),
            returnValueForMissingStub: _i6.Future<Map<K, V>?>.value(),
          )
          as _i6.Future<Map<K, V>?>);

  @override
  void setMethodCallHandler(
    _i6.Future<dynamic> Function(_i3.MethodCall)? handler,
  ) => super.noSuchMethod(
    Invocation.method(#setMethodCallHandler, [handler]),
    returnValueForMissingStub: null,
  );
}
