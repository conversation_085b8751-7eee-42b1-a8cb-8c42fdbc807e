// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_category/domain/usecases/get_category_all_list_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_count_processing_service.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_data_preparation_service.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_validation_service.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/models/asset_search_category_ui_model.dart'
    as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [CategoryDataPreparationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCategoryDataPreparationService extends _i1.Mock
    implements _i2.CategoryDataPreparationService {
  MockCategoryDataPreparationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i4.CategoryData>> prepareCategoryList(
    _i5.GetCategoryAllListParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#prepareCategoryList, [params]),
            returnValue: _i3.Future<List<_i4.CategoryData>>.value(
              <_i4.CategoryData>[],
            ),
          )
          as _i3.Future<List<_i4.CategoryData>>);

  @override
  _i3.Future<List<_i4.CategoryData>> handleAssetSearchConditionList(
    List<_i6.AssetSearchCategoryUIModel>? assetSearchConditionList,
    List<_i7.Info>? categoryOriginalList,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#handleAssetSearchConditionList, [
              assetSearchConditionList,
              categoryOriginalList,
            ]),
            returnValue: _i3.Future<List<_i4.CategoryData>>.value(
              <_i4.CategoryData>[],
            ),
          )
          as _i3.Future<List<_i4.CategoryData>>);

  @override
  _i3.Future<List<_i4.CategoryData>> handleBackLogic(
    _i7.Info? info,
    List<_i4.CategoryData>? categoryList,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#handleBackLogic, [info, categoryList]),
            returnValue: _i3.Future<List<_i4.CategoryData>>.value(
              <_i4.CategoryData>[],
            ),
          )
          as _i3.Future<List<_i4.CategoryData>>);

  @override
  _i3.Future<List<_i4.CategoryData>> handleForwardLogic(
    String? lastValue,
    List<_i7.Info>? categoryOriginalList,
    List<_i4.CategoryData>? categoryList,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#handleForwardLogic, [
              lastValue,
              categoryOriginalList,
              categoryList,
            ]),
            returnValue: _i3.Future<List<_i4.CategoryData>>.value(
              <_i4.CategoryData>[],
            ),
          )
          as _i3.Future<List<_i4.CategoryData>>);

  @override
  _i3.Future<List<_i4.CategoryData>> handleInitialLogic(
    List<_i7.Info>? categoryOriginalList,
    List<_i4.CategoryData>? categoryList,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#handleInitialLogic, [
              categoryOriginalList,
              categoryList,
            ]),
            returnValue: _i3.Future<List<_i4.CategoryData>>.value(
              <_i4.CategoryData>[],
            ),
          )
          as _i3.Future<List<_i4.CategoryData>>);

  @override
  bool isClickCurrentLevel(_i4.CategoryModel? beforeInfo, dynamic afterInfo) =>
      (super.noSuchMethod(
            Invocation.method(#isClickCurrentLevel, [beforeInfo, afterInfo]),
            returnValue: false,
          )
          as bool);
}

/// A class which mocks [CategoryCountProcessingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCategoryCountProcessingService extends _i1.Mock
    implements _i8.CategoryCountProcessingService {
  MockCategoryCountProcessingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i9.CategoryItemValueModel>> fetchCategoryCount(
    _i5.GetCategoryAllListParams? params,
    List<_i4.CategoryData>? categoryList,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#fetchCategoryCount, [params, categoryList]),
            returnValue: _i3.Future<List<_i9.CategoryItemValueModel>>.value(
              <_i9.CategoryItemValueModel>[],
            ),
          )
          as _i3.Future<List<_i9.CategoryItemValueModel>>);

  @override
  List<_i9.CategoryItemValueModel> orderCategoryName(
    List<_i9.CategoryItemValueModel>? categoryItemValueList,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#orderCategoryName, [categoryItemValueList]),
            returnValue: <_i9.CategoryItemValueModel>[],
          )
          as List<_i9.CategoryItemValueModel>);
}

/// A class which mocks [CategoryValidationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCategoryValidationService extends _i1.Mock
    implements _i10.CategoryValidationService {
  MockCategoryValidationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool checkCompletionStatus(List<_i4.CategoryData>? categoryList) =>
      (super.noSuchMethod(
            Invocation.method(#checkCompletionStatus, [categoryList]),
            returnValue: false,
          )
          as bool);

  @override
  bool isEmptyCategory(List<_i9.CategoryItemValueModel>? countList) =>
      (super.noSuchMethod(
            Invocation.method(#isEmptyCategory, [countList]),
            returnValue: false,
          )
          as bool);
}
