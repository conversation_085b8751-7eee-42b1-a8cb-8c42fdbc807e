import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/models/asset_search_category_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_history_records_count_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/customize_asset_list_model_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/master_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_relation_list_response.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../../../../core/utils/dio_utils_test.mocks.dart';
import '../../mock/asset_list_mock_data.dart';

void main() {
  late AssetRepository assetRepository;
  late MockDioUtil dioUtil;

  setUp(() {
    LogUtil.initialize();
    dioUtil = MockDioUtil();
    assetRepository = AssetRepositoryImpl(dioUtil: dioUtil);
  });

  group('getCustomizeSearchForMobile', () {
    test('getCustomizeSearchForMobile case1', () async {
      when(
        dioUtil.post(GlobalVariable.customizeSearchCountForMobile, data: anyNamed('data'), useFormUrlEncoded: true),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(),
          data: AssetListMockData.customizeSearchCountForMobileResponse,
          statusCode: 200,
        ),
      );
      when(
        dioUtil.post(GlobalVariable.customizeSearchForMobile, data: anyNamed('data'), useFormUrlEncoded: true),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(),
          data: AssetListMockData.customizeSearchForMobileResponse,
          statusCode: 200,
        ),
      );
      final result = await assetRepository.getCustomizeSearchForMobile(
        searchParams: CustomizeSearchForMobileRequestQuery(
          assetTypeId: 111111855,
          searchId: 2,
          keyword: '123',
          assetSearchConditionList: [
            AssetSearchCategoryUIModel(
              searchData: '-10000.00001',
              itemId: 12493079,
              itemName: '数字',
              searchLogic: 'AND',
              method: 'numberEqu',
            ),
            AssetSearchCategoryUIModel(
              searchData: '1234.008900',
              itemId: 12493080,
              itemName: '通貨',
              searchLogic: 'AND',
              method: 'numberEqu',
            ),
            AssetSearchCategoryUIModel(
              searchData: '-9999.00001',
              itemId: 12493081,
              itemName: '計算',
              searchLogic: 'AND',
              method: 'equeue',
            ),
            AssetSearchCategoryUIModel(
              subItemId: 962060,
              subItemName: '数字666',
              searchData: '1231.4561237',
              itemId: 12493082,
              itemName: 'FXH_Master_All',
              searchLogic: 'AND',
              method: 'listInclude',
            ),
          ],
          skip: 0,
          rows: 100,
        ),
      );
      expect(result, isA<CustomizeAssetListModelResponse>());
    });

    test('getCustomizeSearchForMobile error case1 Unexpected response data format.', () async {
      when(
        dioUtil.post(GlobalVariable.customizeSearchCountForMobile, data: anyNamed('data'), useFormUrlEncoded: true),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: [], statusCode: 200));
      when(
        dioUtil.post(GlobalVariable.customizeSearchForMobile, data: anyNamed('data'), useFormUrlEncoded: true),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: [], statusCode: 200));
      try {
        await assetRepository.getCustomizeSearchForMobile(
          searchParams: CustomizeSearchForMobileRequestQuery(assetTypeId: 111111855, searchId: 2, skip: 0, rows: 100),
        );
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });

    test('getCustomizeSearchForMobile error case2 Failed to fetch data. Status code', () async {
      when(
        dioUtil.post(GlobalVariable.customizeSearchCountForMobile, data: anyNamed('data'), useFormUrlEncoded: true),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 400));
      when(
        dioUtil.post(GlobalVariable.customizeSearchForMobile, data: anyNamed('data'), useFormUrlEncoded: true),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 400));
      try {
        await assetRepository.getCustomizeSearchForMobile(
          searchParams: CustomizeSearchForMobileRequestQuery(assetTypeId: 111111855, searchId: 2, skip: 0, rows: 100),
        );
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });

  group('getAssetMobileSettings', () {
    test('getAssetMobileSettings case1', () async {
      when(dioUtil.get(GlobalVariable.getAssetMobileSettingForMobile, queryParams: anyNamed('queryParams'))).thenAnswer(
        (_) async =>
            Response(requestOptions: RequestOptions(), data: AssetListMockData.assetMobileResponse, statusCode: 200),
      );
      final result = await assetRepository.getAssetMobileSettings(assetTypeId: 111111855);
      expect(result, isA<AssetMobileResponse>());
    });

    test('getAssetMobileSettings error case1 Failed to load AssetMobileSettings', () async {
      when(
        dioUtil.get(GlobalVariable.getAssetMobileSettingForMobile, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 0));
      try {
        await assetRepository.getAssetMobileSettings(assetTypeId: 111111855);
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });

    test('getAssetMobileSettings error case2 Error fetching AssetMobileSettings', () async {
      when(
        dioUtil.get(GlobalVariable.getAssetMobileSettingForMobile, queryParams: anyNamed('queryParams')),
      ).thenThrow(BusinessException('インターネット接続が不安定なようです。少し時間をおいてから再度お試しください。'));
      try {
        await assetRepository.getAssetMobileSettings(assetTypeId: 111111855);
      } catch (e) {
        expect(e, isA<Exception>());
        // 将e转换为BusinessException类型
        final businessException = e as BusinessException;
        expect(businessException.message, contains('インターネット接続が不安定なようです。少し時間をおいてから再度お試しください。'));
      }
    });
  });

  group('getAllMasterLayoutSetting', () {
    test('getAllMasterLayoutSetting case1', () async {
      when(dioUtil.get(GlobalVariable.getAllOrMasterLayoutSetting, queryParams: anyNamed('queryParams'))).thenAnswer(
        (_) async =>
            Response(requestOptions: RequestOptions(), data: AssetListMockData.layoutSettingResults, statusCode: 200),
      );
      final result = await assetRepository.getAllMasterLayoutSetting(typeId: 111111855);
      expect(result, isA<MasterLayoutSettingResponse>());
    });

    test('getAllMasterLayoutSetting error case1 Failed to load Master Layout Settings', () async {
      when(
        dioUtil.get(GlobalVariable.getAllOrMasterLayoutSetting, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 0));
      try {
        await assetRepository.getAllMasterLayoutSetting(typeId: 111111855);
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });

    test('getAllMasterLayoutSetting error case2 Error fetching Master Layout Settings', () async {
      final requestOptions = RequestOptions(path: GlobalVariable.getAllOrMasterLayoutSetting);
      when(dioUtil.get(GlobalVariable.getAllOrMasterLayoutSetting, queryParams: anyNamed('queryParams'))).thenThrow(
        DioException.connectionError(requestOptions: requestOptions, reason: 'インターネット接続が不安定なようです。少し時間をおいてから再度お試しください。'),
      );
      try {
        await assetRepository.getAllMasterLayoutSetting(typeId: 111111855);
      } catch (e) {
        expect(e, isA<BusinessException>());
        // 将e转换为BusinessException类型
        final businessException = e as BusinessException;
        expect(businessException.message, contains('インターネット接続が不安定なようです。少し時間をおいてから再度お試しください。'));
      }
    });
  });

  group('getAssetById', () {
    test('getAssetById success case', () async {
      final dummyResponse = {'assetId': 123, 'assetName': 'Test Asset'};
      when(
        dioUtil.get(GlobalVariable.getAssetById, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      try {
        await assetRepository.getAssetById(assetId: 123);
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });

    test('getAssetById error case - unexpected data format', () async {
      when(
        dioUtil.get(GlobalVariable.getAssetById, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 200));
      try {
        await assetRepository.getAssetById(assetId: 123);
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });

  group('getAssetsPrintAble', () {
    test('returns true when code is "0" and result exists', () async {
      final dummyResponse = {
        'code': '0',
        'result': {'printTemplate': 'templateValue'},
      };
      when(
        dioUtil.get(GlobalVariable.getPrintTemplateMapping, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      final result = await assetRepository.getAssetsPrintAble(assetTypeIdsStr: '123');
      expect(result, isTrue);
    });

    test('returns false when code is not "0"', () async {
      final dummyResponse = {
        'code': '1',
        'result': {'printTemplate': 'templateValue'},
      };
      when(
        dioUtil.get(GlobalVariable.getPrintTemplateMapping, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      final result = await assetRepository.getAssetsPrintAble(assetTypeIdsStr: '123');
      expect(result, isFalse);
    });

    test('getAssetsPrintAble error case - exception thrown', () async {
      when(
        dioUtil.get(GlobalVariable.getPrintTemplateMapping, queryParams: anyNamed('queryParams')),
      ).thenThrow(DioException(requestOptions: RequestOptions(), error: 'Error'));
      try {
        await assetRepository.getAssetsPrintAble(assetTypeIdsStr: '123');
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<BusinessException>());
      }
    });
  });

  group('getAssetItemType', () {
    test('getAssetItemType success case', () async {
      final dummyResponse = {
        'items': [],
        // 根据 AssetItemResponse 的结构补充必要字段
      };
      when(
        dioUtil.get(GlobalVariable.getAssetItem, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      try {
        await assetRepository.getAssetItemType(assetTypeId: '123');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });

    test('getAssetItemType error case - unexpected data format', () async {
      when(
        dioUtil.get(GlobalVariable.getAssetItem, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 200));
      try {
        await assetRepository.getAssetItemType(assetTypeId: '123');
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });

  group('saveAsset', () {
    test('saveAsset success case', () async {
      final dummyResponse = {
        'success': true,
        'message': 'Asset saved successfully',
        // 根据 BaseResponse 的结构补充必要字段
      };
      when(
        dioUtil.post(GlobalVariable.updateAsset, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      final result = await assetRepository.saveAsset(
        assetId: '1',
        assetTypeId: '123',
        assetText: 'Test Asset',
        modifiedDate: '2025-03-13',
        barcode: 'barcode123',
        relationAssetIdList: '2,3',
      );
      expect(result, isA<BaseResponse>());
    });

    test('saveAsset error case - unexpected response data format', () async {
      when(
        dioUtil.post(GlobalVariable.updateAsset, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 200));
      try {
        await assetRepository.saveAsset(assetId: '1', assetTypeId: '123', assetText: 'Test Asset');
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });

  group('getAssetRelationList', () {
    test('getAssetRelationList success case', () async {
      final dummyResponse = {
        'relations': [],
        // 根据 AssetRelationListResponse 的结构补充必要字段
      };
      when(
        dioUtil.get(GlobalVariable.getAssetRelationList, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      final result = await assetRepository.getAssetRelationList(assetId: 123);
      expect(result, isA<AssetRelationListResponse>());
    });

    test('getAssetRelationList error case - unexpected data format', () async {
      when(
        dioUtil.get(GlobalVariable.getAssetRelationList, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 200));
      try {
        await assetRepository.getAssetRelationList(assetId: 123);
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });

  group('getLayoutSetting', () {
    test('getLayoutSetting success case', () async {
      final dummyResponse = {
        'layoutSettingList': [
          {'itemId': 1, 'itemName': 'Test Item', 'itemType': 'input', 'classification': 5, 'typeId': 1},
        ],
        'appurtenanceDeletedItemNames': ['item1', 'item2'],
        'commonJS': 'function test() {}',
        'js': 'console.log("test");',
        'appurJsOnSave': 'function onSave() {}',
      };
      when(
        dioUtil.get(GlobalVariable.getAllOrMasterLayoutSetting, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      final result = await assetRepository.getLayoutSetting(typeId: '1', classification: '5');
      expect(result, isA<AssetLayoutSettingResponse>());
      expect(result.layoutSettingList, isNotEmpty);
      expect(result.layoutSettingList.first.itemId, 1);
      expect(result.layoutSettingList.first.itemName, 'Test Item');
      expect(result.appurtenanceDeletedItemNames, ['item1', 'item2']);
      expect(result.commonJS, 'function test() {}');
    });

    test('getLayoutSetting error case - failed to load layout setting', () async {
      when(
        dioUtil.get(GlobalVariable.getAllOrMasterLayoutSetting, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 0));
      try {
        await assetRepository.getLayoutSetting();
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });

  group('getAssetHistoryRecordsCount', () {
    test('getAssetHistoryRecordsCount success case', () async {
      final dummyResponse = {'code': 0, 'msg': 'Success', 'appurtenancesInformationDataCount': 5};
      when(
        dioUtil.get(GlobalVariable.getAssetHistoryRecordsCount, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: dummyResponse, statusCode: 200));
      final result = await assetRepository.getAssetHistoryRecordsCount(
        assetId: 123,
        appurtenancesInformationTypeId: 'typeA',
      );
      expect(result, isA<AssetHistoryRecordsCountResponse>());
      expect(result.code, 0);
      expect(result.msg, 'Success');
      expect(result.appurtenancesInformationDataCount, 5);
    });

    test('getAssetHistoryRecordsCount error case - unexpected data format', () async {
      when(
        dioUtil.get(GlobalVariable.getAssetHistoryRecordsCount, queryParams: anyNamed('queryParams')),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), data: {}, statusCode: 200));
      try {
        await assetRepository.getAssetHistoryRecordsCount(assetId: 123, appurtenancesInformationTypeId: 'typeA');
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });
}
