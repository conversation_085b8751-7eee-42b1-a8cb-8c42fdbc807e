// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/tenant/presentation/controllers/tenant_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i13;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart'
    as _i12;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i14;
import 'package:flutter/cupertino.dart' as _i15;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTenantRepository_0 extends _i1.SmartFake
    implements _i2.TenantRepository {
  _FakeTenantRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserRepository_1 extends _i1.SmartFake
    implements _i3.UserRepository {
  _FakeUserRepository_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_2 extends _i1.SmartFake implements _i4.IEnvHelper {
  _FakeIEnvHelper_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_3 extends _i1.SmartFake implements _i5.IStorageUtils {
  _FakeIStorageUtils_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetTokenResultModel_4 extends _i1.SmartFake
    implements _i6.GetTokenResultModel {
  _FakeGetTokenResultModel_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TenantUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockTenantUseCase extends _i1.Mock implements _i7.TenantUseCase {
  @override
  _i2.TenantRepository get tenantRepository =>
      (super.noSuchMethod(
            Invocation.getter(#tenantRepository),
            returnValue: _FakeTenantRepository_0(
              this,
              Invocation.getter(#tenantRepository),
            ),
            returnValueForMissingStub: _FakeTenantRepository_0(
              this,
              Invocation.getter(#tenantRepository),
            ),
          )
          as _i2.TenantRepository);

  @override
  _i3.UserRepository get userRepository =>
      (super.noSuchMethod(
            Invocation.getter(#userRepository),
            returnValue: _FakeUserRepository_1(
              this,
              Invocation.getter(#userRepository),
            ),
            returnValueForMissingStub: _FakeUserRepository_1(
              this,
              Invocation.getter(#userRepository),
            ),
          )
          as _i3.UserRepository);

  @override
  _i4.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_2(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_2(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i4.IEnvHelper);

  @override
  _i5.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_3(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_3(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i5.IStorageUtils);

  @override
  _i8.Future<_i6.GetTokenResultModel> getBasicInfo(
    _i9.SelectTenantModel? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getBasicInfo, [params]),
            returnValue: _i8.Future<_i6.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_4(
                this,
                Invocation.method(#getBasicInfo, [params]),
              ),
            ),
            returnValueForMissingStub:
                _i8.Future<_i6.GetTokenResultModel>.value(
                  _FakeGetTokenResultModel_4(
                    this,
                    Invocation.method(#getBasicInfo, [params]),
                  ),
                ),
          )
          as _i8.Future<_i6.GetTokenResultModel>);

  @override
  _i8.Future<_i6.GetTokenResultModel> call(_i9.SelectTenantModel? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i8.Future<_i6.GetTokenResultModel>.value(
              _FakeGetTokenResultModel_4(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
            returnValueForMissingStub:
                _i8.Future<_i6.GetTokenResultModel>.value(
                  _FakeGetTokenResultModel_4(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i8.Future<_i6.GetTokenResultModel>);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i10.NavigationService {
  @override
  _i8.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i8.Future<dynamic>.value(),
            returnValueForMissingStub: _i8.Future<dynamic>.value(),
          )
          as _i8.Future<dynamic>);

  @override
  _i8.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i8.Future<dynamic>.value(),
            returnValueForMissingStub: _i8.Future<dynamic>.value(),
          )
          as _i8.Future<dynamic>);

  @override
  _i8.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i8.Future<bool>.value(false),
            returnValueForMissingStub: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Future<dynamic> toAssetDetail(_i11.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i8.Future<dynamic>.value(),
            returnValueForMissingStub: _i8.Future<dynamic>.value(),
          )
          as _i8.Future<dynamic>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i12.DialogService {
  @override
  _i8.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i13.VoidCallback? onConfirm,
    _i13.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i14.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i13.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i15.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i14.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}
