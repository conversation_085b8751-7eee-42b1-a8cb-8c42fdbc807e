// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_sso/presentation/controllers/login_sso_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:ui' as _i13;

import 'package:asset_force_mobile_v2/core/deeplink/i_deeplink_service.dart'
    as _i3;
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i5;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart'
    as _i12;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i14;
import 'package:asset_force_mobile_v2/features/skill_plugin/i_sso_scan_plugin.dart'
    as _i2;
import 'package:flutter/cupertino.dart' as _i15;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeISsoScanPlugin_0 extends _i1.SmartFake
    implements _i2.ISsoScanPlugin {
  _FakeISsoScanPlugin_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIDeeplinkService_1 extends _i1.SmartFake
    implements _i3.IDeeplinkService {
  _FakeIDeeplinkService_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_2 extends _i1.SmartFake implements _i4.IStorageUtils {
  _FakeIStorageUtils_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_3 extends _i1.SmartFake implements _i5.IEnvHelper {
  _FakeIEnvHelper_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginSsoUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginSsoUseCase extends _i1.Mock implements _i6.LoginSsoUseCase {
  MockLoginSsoUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ISsoScanPlugin get ssoScanPlugin =>
      (super.noSuchMethod(
            Invocation.getter(#ssoScanPlugin),
            returnValue: _FakeISsoScanPlugin_0(
              this,
              Invocation.getter(#ssoScanPlugin),
            ),
          )
          as _i2.ISsoScanPlugin);

  @override
  _i3.IDeeplinkService get deeplinkService =>
      (super.noSuchMethod(
            Invocation.getter(#deeplinkService),
            returnValue: _FakeIDeeplinkService_1(
              this,
              Invocation.getter(#deeplinkService),
            ),
          )
          as _i3.IDeeplinkService);

  @override
  _i4.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_2(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i4.IStorageUtils);

  @override
  _i5.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_3(this, Invocation.getter(#envHelper)),
          )
          as _i5.IEnvHelper);

  @override
  _i7.Future<dynamic> call(String? pragma) =>
      (super.noSuchMethod(
            Invocation.method(#call, [pragma]),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  _i7.Future<String?> openScanBarcode() =>
      (super.noSuchMethod(
            Invocation.method(#openScanBarcode, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  List<_i8.TenantSsoModel> fromJsonList({required String? jsonString}) =>
      (super.noSuchMethod(
            Invocation.method(#fromJsonList, [], {#jsonString: jsonString}),
            returnValue: <_i8.TenantSsoModel>[],
          )
          as List<_i8.TenantSsoModel>);

  @override
  String processTenants({required List<_i8.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processTenants, [], {#tenants: tenants}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#processTenants, [], {#tenants: tenants}),
            ),
          )
          as String);

  @override
  String processOrderTenants({required List<_i8.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            ),
          )
          as String);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i10.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  _i7.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  _i7.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<dynamic> toAssetDetail(_i11.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i12.DialogService {
  MockDialogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i13.VoidCallback? onConfirm,
    _i13.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i14.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i13.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i15.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i14.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}
