import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/stacked_task_card_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateNiceMocks([MockSpec<MyHomeController>()])
import 'stacked_task_card_widget_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  late MockMyHomeController mockController;
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  // Sample data for testing
  const String testTitle = '任务标题';
  const String testSubtitle = '任务类别';
  const String testDate = '2024-03-03';
  const String testCount = '5';
  const String testIconPath = 'assets/icons/task.svg';
  bool tapCalled = false;

  // Helper function to create widget under test
  Widget createTaskCard({int itemCount = 1}) {
    return GetMaterialApp(
      home: Scaffold(
        body: StackedTaskCardWidget(
          itemCount: itemCount,
          title: testTitle,
          subtitle: testSubtitle,
          date: testDate,
          count: testCount,
          iconPath: testIconPath,
          onTap: () => tapCalled = true,
        ),
      ),
    );
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    // Initialize mock controller
    mockController = MockMyHomeController();
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 注意：不需要显式处理 SVG 加载
    // 在较新版本的 flutter_svg 中，可以使用 mock 来处理资源加载
    // 这里我们简化处理，测试框架会处理资源加载

    Get.put<MyHomeController>(mockController);

    // Reset tap tracker
    tapCalled = false;
  });

  tearDown(() {
    Get.reset();
  });

  group('StackedTaskCardWidget UI 测试', () {
    testWidgets('应该正确显示所有文本内容', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard());

      // Allow SVG to load
      await tester.pump(const Duration(milliseconds: 100));

      // Verify all text elements are displayed
      expect(find.text(testTitle), findsOneWidget);
      expect(find.text(testSubtitle), findsOneWidget);
      expect(find.text(testDate), findsOneWidget);
      expect(find.text(testCount), findsOneWidget);
    });

    testWidgets('当 itemCount=1 时，不应显示堆叠层', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard(itemCount: 1));
      await tester.pump(const Duration(milliseconds: 100));

      // Only main card should be visible, no stack layers
      expect(find.byType(GestureDetector), findsOneWidget);

      // Find containers, we should have one main container plus badge container
      // But no stack layer containers
      final containerFinders = find.byType(Container);
      expect(containerFinders, findsNWidgets(2));
    });

    testWidgets('当 itemCount=3 时，应显示两个堆叠层', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard(itemCount: 3));
      await tester.pump(const Duration(milliseconds: 100));

      // We should see a main container, two stack layers, and a badge container
      final containerFinders = find.byType(Container);
      expect(containerFinders, findsNWidgets(4));
    });

    testWidgets('点击卡片应触发 onTap 回调', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard());
      await tester.pump(const Duration(milliseconds: 100));

      // Tap the card
      await tester.tap(find.byType(GestureDetector));

      // Verify callback was triggered
      expect(tapCalled, isTrue);
    });

    testWidgets('卡片应该有正确的样式', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard());
      await tester.pump(const Duration(milliseconds: 100));

      // Find main card container
      final mainCardFinder = find.byType(GestureDetector).first;
      final containerFinder = find.descendant(of: mainCardFinder, matching: find.byType(Container)).first;

      final container = tester.widget<Container>(containerFinder);

      // Verify decoration properties
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.color, equals(StackedTaskCardWidget.cardColor));
      expect(decoration.borderRadius, equals(BorderRadius.circular(12)));
      expect(decoration.boxShadow!.length, equals(1));
    });

    testWidgets('徽章应该有正确的样式', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard());
      await tester.pump(const Duration(milliseconds: 100));

      // Find the badge container
      final countText = find.text(testCount);
      final badgeContainer = find.ancestor(of: countText, matching: find.byType(Container)).first;

      final container = tester.widget<Container>(badgeContainer);

      // Verify badge decoration
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.color, equals(Colors.white));
      expect(decoration.borderRadius, equals(BorderRadius.circular(16)));
      expect(decoration.boxShadow!.length, equals(1));
    });

    testWidgets('标题应该有正确的字体样式', (WidgetTester tester) async {
      await tester.pumpWidget(createTaskCard());
      await tester.pump(const Duration(milliseconds: 100));

      // Find title text widget
      final titleFinder = find.text(testTitle);
      final titleText = tester.widget<Text>(titleFinder);

      // Verify text style
      expect(titleText.style!.fontSize, equals(16));
      expect(titleText.style!.color, equals(StackedTaskCardWidget.textColor));
      expect(titleText.style!.fontWeight, equals(FontWeight.w700));
      expect(titleText.style!.overflow, equals(TextOverflow.ellipsis));
    });
  });
}
