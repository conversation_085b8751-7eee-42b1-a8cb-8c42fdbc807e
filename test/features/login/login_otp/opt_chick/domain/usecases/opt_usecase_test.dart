import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/domain/usecases/opt_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/controllers/login_opt_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../../login_sso/presentation/controllers/login_sso_list_controller_test.mocks.dart';
import 'opt_usecase_test.mocks.dart' hide MockIStorageUtils;

@GenerateMocks([TenantRepository, UserRepository, IStorageUtils])
void main() {
  late OptUsecase useCase;
  late MockTenantRepository mockTenantRepository;
  late MockUserRepository mockUserRepository;
  late MockIStorageUtils mockStorageUtils;

  setUp(() {
    mockTenantRepository = MockTenantRepository();
    mockUserRepository = MockUserRepository();
    mockStorageUtils = MockIStorageUtils();
    useCase = OptUsecase(
      tenantRepository: mockTenantRepository,
      userRepository: mockUserRepository,
      storageUtils: mockStorageUtils,
    );
  });

  group('OptUsecase Tests', () {
    // Helper methods for creating test data
    OptTenantModel createValidOptTenant() {
      return OptTenantModel(
        nationCode: '+81',
        tel: '09012345678',
        ticket: 'valid_ticket',
        reCode: 4,
        userId: 123,
        userName: 'testuser',
        password: 'password123',
        tenantId: 'tenant123',
        email: '<EMAIL>',
        code: '123456',
      );
    }

    SelectTenantModel createValidSelectTenant() {
      return SelectTenantModel(
        zoneId: 'zone123',
        dealTenant: null,
        dealDeeplink: null,
        optTenant: createValidOptTenant(),
      );
    }

    GetTokenResultModel createSuccessTokenResult() {
      return GetTokenResultModel(code: 0, msg: 'Success');
    }

    GetTokenResultModel createFailureTokenResult() {
      return GetTokenResultModel(code: 1, msg: 'Failed', signInResult: 'Login failed');
    }

    // getBasicInfo method tests
    group('getBasicInfo Tests', () {
      group('成功场景', () {
        test('正常情况下成功调用时应返回GetTokenResultModel', () async {
          // Arrange - 准备测试数据
          final params = createValidSelectTenant();
          final expectedResult = createSuccessTokenResult();

          // 设置模拟行为
          when(
            mockTenantRepository.getUserBindPhone(
              ticket: anyNamed('ticket'),
              countryCode: anyNamed('countryCode'),
              phone: anyNamed('phone'),
              userName: anyNamed('userName'),
              password: anyNamed('password'),
              code: anyNamed('code'),
              tenantId: anyNamed('tenantId'),
              userId: anyNamed('userId'),
            ),
          ).thenAnswer((_) async => expectedResult);

          // Act - 执行被测试的方法
          final result = await useCase.getBasicInfo(params);

          // Assert - 验证结果
          expect(result, equals(expectedResult));

          // 验证repository方法被正确调用
          verify(
            mockTenantRepository.getUserBindPhone(
              ticket: 'valid_ticket',
              countryCode: '+81',
              phone: '09012345678',
              userName: 'testuser',
              password: 'password123',
              code: '123456',
              tenantId: 'tenant123',
              userId: 123,
            ),
          ).called(1);
        });
      });

      group('参数验证异常', () {
        test('当optTenant为null时应抛出SystemException', () async {
          // Arrange
          final params = SelectTenantModel(zoneId: 'zone123', dealTenant: null, dealDeeplink: null, optTenant: null);

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当ticket为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: '',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当tel为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当nationCode为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当tenantId为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: '',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当userName为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: '',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当password为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: '',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });

        test('当code为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
        });
      });
    });

    // sendSMSCall method tests
    group('sendSMSCall Tests', () {
      group('成功场景', () {
        test('邮件发送成功时应正常完成', () async {
          // Arrange
          final params = createValidSelectTenant();
          final expectedResult = createSuccessTokenResult();

          // 设置模拟行为
          when(
            mockTenantRepository.getSendMfaMailVerifyCode(ticket: anyNamed('ticket'), email: anyNamed('email')),
          ).thenAnswer((_) async => expectedResult);

          // Act
          await useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.email);

          // Assert - 验证repository方法被正确调用
          verify(
            mockTenantRepository.getSendMfaMailVerifyCode(ticket: 'valid_ticket', email: '<EMAIL>'),
          ).called(1);
        });

        test('SMS发送成功时应正常完成', () async {
          // Arrange
          final params = createValidSelectTenant();
          final expectedResult = createSuccessTokenResult();

          // 设置模拟行为
          when(
            mockTenantRepository.getVerificationCode(
              ticket: anyNamed('ticket'),
              countryCode: anyNamed('countryCode'),
              phone: anyNamed('phone'),
            ),
          ).thenAnswer((_) async => expectedResult);

          // Act
          await useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.sms);

          // Assert
          verify(
            mockTenantRepository.getVerificationCode(ticket: 'valid_ticket', countryCode: '+81', phone: '09012345678'),
          ).called(1);
        });

        test('当showOptWidget为null时应走SMS分支', () async {
          // Arrange
          final params = createValidSelectTenant();
          final expectedResult = createSuccessTokenResult();

          // 设置模拟行为
          when(
            mockTenantRepository.getVerificationCode(
              ticket: anyNamed('ticket'),
              countryCode: anyNamed('countryCode'),
              phone: anyNamed('phone'),
            ),
          ).thenAnswer((_) async => expectedResult);

          // Act
          await useCase.sendSMSCall(params: params, showOptWidget: null);

          // Assert - 验证走的是SMS分支
          verify(
            mockTenantRepository.getVerificationCode(ticket: 'valid_ticket', countryCode: '+81', phone: '09012345678'),
          ).called(1);

          // 验证没有调用邮件方法
          verifyNever(
            mockTenantRepository.getSendMfaMailVerifyCode(ticket: anyNamed('ticket'), email: anyNamed('email')),
          );
        });

        test('当showOptWidget为newSms时应走SMS分支', () async {
          // Arrange
          final params = createValidSelectTenant();
          final expectedResult = createSuccessTokenResult();

          // 设置模拟行为
          when(
            mockTenantRepository.getVerificationCode(
              ticket: anyNamed('ticket'),
              countryCode: anyNamed('countryCode'),
              phone: anyNamed('phone'),
            ),
          ).thenAnswer((_) async => expectedResult);

          // Act
          await useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.newSms);

          // Assert - 验证走的是SMS分支
          verify(
            mockTenantRepository.getVerificationCode(ticket: 'valid_ticket', countryCode: '+81', phone: '09012345678'),
          ).called(1);

          // 验证没有调用邮件方法
          verifyNever(
            mockTenantRepository.getSendMfaMailVerifyCode(ticket: anyNamed('ticket'), email: anyNamed('email')),
          );
        });
      });

      group('参数验证异常', () {
        test('当optTenant为null时应抛出SystemException', () async {
          // Arrange
          final params = SelectTenantModel(zoneId: 'zone123', dealTenant: null, dealDeeplink: null, optTenant: null);

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.email),
            throwsA(isA<SystemException>()),
          );
        });

        test('当ticket为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: '',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.email),
            throwsA(isA<SystemException>()),
          );
        });

        test('邮件场景下当email为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.email),
            throwsA(isA<SystemException>()),
          );
        });

        test('SMS场景下当tel为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.sms),
            throwsA(isA<SystemException>()),
          );
        });

        test('SMS场景下当nationCode为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.sms),
            throwsA(isA<SystemException>()),
          );
        });

        test('null场景下当tel为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.sendSMSCall(params: params, showOptWidget: null), throwsA(isA<SystemException>()));
        });

        test('null场景下当nationCode为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(() => useCase.sendSMSCall(params: params, showOptWidget: null), throwsA(isA<SystemException>()));
        });

        test('newSms场景下当tel为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '+81',
            tel: '',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.newSms),
            throwsA(isA<SystemException>()),
          );
        });

        test('newSms场景下当nationCode为空字符串时应抛出SystemException', () async {
          // Arrange
          final optTenant = OptTenantModel(
            nationCode: '',
            tel: '09012345678',
            ticket: 'valid_ticket',
            reCode: 4,
            userId: 123,
            userName: 'testuser',
            password: 'password123',
            tenantId: 'tenant123',
            email: '<EMAIL>',
            code: '123456',
          );
          final params = SelectTenantModel(
            zoneId: 'zone123',
            dealTenant: null,
            dealDeeplink: null,
            optTenant: optTenant,
          );

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.newSms),
            throwsA(isA<SystemException>()),
          );
        });
      });

      group('业务异常场景', () {
        test('当发送失败且有错误消息时应抛出BusinessException', () async {
          // Arrange
          final params = createValidSelectTenant();
          final failureResult = GetTokenResultModel(code: 1, msg: 'Failed', signInResult: 'Email verification failed');

          // 设置模拟行为
          when(
            mockTenantRepository.getSendMfaMailVerifyCode(ticket: anyNamed('ticket'), email: anyNamed('email')),
          ).thenAnswer((_) async => failureResult);

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.email),
            throwsA(isA<BusinessException>().having((e) => e.message, 'message', 'Email verification failed')),
          );
        });

        test('当发送失败且无错误消息时应抛出默认BusinessException', () async {
          // Arrange
          final params = createValidSelectTenant();
          final failureResult = GetTokenResultModel(code: 1, msg: 'Failed', signInResult: null);

          // 设置模拟行为
          when(
            mockTenantRepository.getVerificationCode(
              ticket: anyNamed('ticket'),
              countryCode: anyNamed('countryCode'),
              phone: anyNamed('phone'),
            ),
          ).thenAnswer((_) async => failureResult);

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.sms),
            throwsA(isA<BusinessException>().having((e) => e.message, 'message', '認証コード送信失敗しました。')),
          );
        });

        test('当发送失败且错误消息为空字符串时应抛出默认BusinessException', () async {
          // Arrange
          final params = createValidSelectTenant();
          final failureResult = GetTokenResultModel(code: 1, msg: 'Failed', signInResult: '');

          // 设置模拟行为
          when(
            mockTenantRepository.getSendMfaMailVerifyCode(ticket: anyNamed('ticket'), email: anyNamed('email')),
          ).thenAnswer((_) async => failureResult);

          // Act & Assert
          expect(
            () => useCase.sendSMSCall(params: params, showOptWidget: ShowOptWidgetEnum.email),
            throwsA(isA<BusinessException>().having((e) => e.message, 'message', '認証コード送信失敗しました。')),
          );
        });
      });
    });
  });
}
