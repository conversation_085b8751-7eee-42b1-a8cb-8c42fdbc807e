import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/models/scan_setting_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/controllers/scan_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/widgets/sound_switch_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'sound_switch_widget_test.mocks.dart';

// ==========================================
// 🧪 SoundSwitchWidget 单元测试
// ==========================================

@GenerateNiceMocks([MockSpec<ScanSettingsController>()])
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  group('🧪 SoundSwitchWidget 单元测试', () {
    final mockInternalFinalCallback = MockInternalFinalCallback<void>();
    late MockScanSettingsController mockController;
    late ScanSettingUIModel testSettingsModel;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    // Set up Mock Controller helper method (moved to top-level scope)
    void setupMockController() {
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onInit()).thenAnswer((_) async {});
      when(mockController.onClose()).thenAnswer((_) async {});
      when(mockController.onReady()).thenAnswer((_) async {});
    }

    setUp(() {
      Get.reset();
      mockController = MockScanSettingsController();
      setupMockController();
      Get.put<ScanSettingsController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    Widget createWidgetUnderTest() {
      return GetMaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(body: const SoundSwitchWidget()),
      );
    }

    // Helper method to create test data model
    ScanSettingUIModel createTestModel({
      bool useSound = true,
      double soundValue = 75.0,
      int scanMusicList = 1,
      bool useVibration = true,
      double vibrationValue = 50.0,
    }) {
      return ScanSettingUIModel(
        useSound: useSound,
        soundValue: soundValue,
        scanMusicList: scanMusicList,
        useVibration: useVibration,
        vibrationValue: vibrationValue,
      );
    }

    // Helper method to update mock controller data
    void updateMockControllerData(ScanSettingUIModel model) {
      when(mockController.model).thenReturn(model.obs);
    }

    // ==========================================
    // 📋 Phase 0: 测试基础设施建设
    // ==========================================

    group('📋 Phase 0: 测试基础设施建设', () {
      group('0.1 Mock Controller 设置验证', () {
        testWidgets('应该成功创建 MockScanSettingsController', (tester) async {
          // Assert
          expect(mockController, isNotNull);
          expect(mockController, isA<MockScanSettingsController>());
        });

        testWidgets('应该正确设置 Mock Controller 的基础方法', (tester) async {
          // Act
          mockController.onInit();
          mockController.onReady();
          mockController.onClose();

          // Assert - 验证生命周期方法可以被调用
          verify(mockController.onInit()).called(1);
          verify(mockController.onReady()).called(1);
          verify(mockController.onClose()).called(1);
        });

        testWidgets('应该正确 Mock Controller 的业务方法', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          mockController.soundEventChange(false);

          // Assert
          verify(mockController.soundEventChange(false)).called(1);
        });

        testWidgets('应该正确处理 GetX 生命周期', (tester) async {
          // Assert
          expect(mockController.onStart, isNotNull);
          expect(mockController.onStart, same(mockInternalFinalCallback));
        });
      });

      group('0.2 测试数据模型验证', () {
        testWidgets('应该能够创建默认测试模型', (tester) async {
          // Act
          final model = createTestModel();

          // Assert
          expect(model.useSound, true);
          expect(model.soundValue, 75.0);
          expect(model.scanMusicList, 1);
          expect(model.useVibration, true);
          expect(model.vibrationValue, 50.0);
        });

        testWidgets('应该能够创建自定义测试模型', (tester) async {
          // Act
          final model = createTestModel(useSound: false, soundValue: 25.0, scanMusicList: 2);

          // Assert
          expect(model.useSound, false);
          expect(model.soundValue, 25.0);
          expect(model.scanMusicList, 2);
        });

        testWidgets('应该能够动态更新 Mock Controller 数据', (tester) async {
          // Arrange
          final model = createTestModel(useSound: false);

          // Act
          updateMockControllerData(model);

          // Trigger a call to model to verify mocking
          final _ = mockController.model;

          // Assert
          verify(mockController.model).called(1);
        });
      });

      group('0.3 GetX 依赖注入验证', () {
        testWidgets('应该成功注入 Mock Controller', (tester) async {
          // Assert
          expect(Get.isRegistered<ScanSettingsController>(), true, reason: 'Controller 应该已注册到 GetX');

          final injectedController = Get.find<ScanSettingsController>();
          expect(injectedController, same(mockController), reason: '注入的 Controller 应该是我们的 Mock');
        });

        testWidgets('setUp 和 tearDown 应该正确管理 GetX 状态', (tester) async {
          // Arrange - 在 setUp 中已经注册了 Controller
          expect(Get.isRegistered<ScanSettingsController>(), true);

          // Act - 模拟 tearDown 行为
          Get.reset();

          // Assert
          expect(Get.isRegistered<ScanSettingsController>(), false, reason: 'tearDown 后 Controller 应该被清除');
        });
      });

      group('0.4 Widget 测试环境验证', () {
        testWidgets('应该能够创建测试 Widget 环境', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          final widget = createWidgetUnderTest();

          // Assert
          expect(widget, isNotNull);
          expect(widget, isA<GetMaterialApp>());
        });

        testWidgets('应该能够渲染测试 Widget 而不报错', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act & Assert - 应该不会抛出异常
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();
        });

        testWidgets('应该包含 SoundSwitchWidget', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSwitchWidget), findsOneWidget, reason: '应该找到 SoundSwitchWidget');
        });

        testWidgets('应该正确应用 AppTheme', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final materialApp = tester.widget<GetMaterialApp>(find.byType(GetMaterialApp));
          expect(materialApp.theme, AppTheme.lightTheme, reason: '应该使用正确的主题');
        });
      });

      group('0.5 基础 Widget 构建验证', () {
        testWidgets('SoundSwitchWidget 应该能够成功构建', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSwitchWidget), findsOneWidget);
        });

        testWidgets('应该显示主要 UI 组件', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget, reason: '应该显示主容器');
          expect(find.byKey(const Key('sound_switch_label')), findsOneWidget, reason: '应该显示标签');
          expect(find.byKey(const Key('sound_switch')), findsOneWidget, reason: '应该显示开关');
        });

        testWidgets('应该正确反映 Controller 数据状态', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, false, reason: 'Switch 应该反映 useSound = false 状态');
        });
      });

      group('0.6 响应式系统验证', () {
        testWidgets('应该能够在不同状态下正确构建 Widget', (tester) async {
          // Arrange & Act & Assert - Test useSound = true
          testSettingsModel = createTestModel(useSound: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget, reason: 'useSound = true 时应该显示容器');

          // Test useSound = false 状态 (在新的测试环境中)
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget, reason: 'useSound = false 时容器依然显示');
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, false, reason: 'Switch 应该显示 false 状态');
        });

        testWidgets('Mock Controller 数据模型应该能够正确响应', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, testSettingsModel.useSound, reason: 'Widget 应该反映模型数据');
        });
      });

      group('0.7 测试隔离和清理验证', () {
        testWidgets('测试之间应该正确隔离状态', (tester) async {
          // Arrange - 确保这是一个干净的测试环境
          expect(Get.isRegistered<ScanSettingsController>(), true, reason: '每个测试开始时应该有注册的 Controller');

          // Act
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSwitchWidget), findsOneWidget);
        });

        testWidgets('应该能够创建独立的测试数据', (tester) async {
          // Arrange - 创建特定的测试数据
          final customModel = createTestModel(useSound: false, soundValue: 25.0, scanMusicList: 2);
          updateMockControllerData(customModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, false);
        });
      });
    });

    // ==========================================
    // 🎨 Phase 1: 静态 UI 组件测试
    // ==========================================

    group('🎨 Phase 1: 静态 UI 组件测试', () {
      group('1.1 容器组件测试', () {
        testWidgets('应该显示主容器', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget, reason: '应该显示主容器');

          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_switch_container')));
          expect(containerWidget, isNotNull, reason: '容器组件应该存在');
        });

        testWidgets('主容器应该具有正确的装饰样式', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_switch_container')));

          expect(containerWidget.decoration, isA<BoxDecoration>(), reason: '主容器应该有BoxDecoration装饰');

          final decoration = containerWidget.decoration as BoxDecoration;
          expect(decoration.borderRadius, isA<BorderRadius>(), reason: '装饰应该有圆角边框');
          expect(decoration.color, isNotNull, reason: '装饰应该有背景颜色');
        });

        testWidgets('主容器装饰应该使用主题颜色', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_switch_container')));
          final decoration = containerWidget.decoration as BoxDecoration;

          expect(decoration.color, AppTheme.lightTheme.customTheme.cardBackgroundColor, reason: '容器背景色应该使用主题卡片背景色');
        });

        testWidgets('主容器应该具有正确的圆角半径', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_switch_container')));
          final decoration = containerWidget.decoration as BoxDecoration;
          final borderRadius = decoration.borderRadius as BorderRadius;

          expect(borderRadius.topLeft.x, equals(8.0), reason: '圆角半径应该是8.0');
          expect(borderRadius.topRight.x, equals(8.0), reason: '圆角半径应该是8.0');
          expect(borderRadius.bottomLeft.x, equals(8.0), reason: '圆角半径应该是8.0');
          expect(borderRadius.bottomRight.x, equals(8.0), reason: '圆角半径应该是8.0');
        });
      });

      group('1.2 文本组件测试', () {
        testWidgets('应该显示正确的标签文本', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('音 ON/OFF'), findsOneWidget, reason: '应该显示音效开关标签');

          // 验证文本通过Key也能找到
          final labelWidget = tester.widget<Text>(find.byKey(const Key('sound_switch_label')));
          expect(labelWidget.data, '音 ON/OFF', reason: '标签内容应该正确');
        });

        testWidgets('文本标签应该在正确的布局位置', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerFinder = find.byKey(const Key('sound_switch_container'));

          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_switch_label'))),
            findsOneWidget,
            reason: '文本标签应该在主容器内',
          );

          // 验证文本标签在Row的左侧
          final rowFinder = find.byKey(const Key('sound_switch_row'));
          expect(
            find.descendant(of: rowFinder, matching: find.byKey(const Key('sound_switch_label'))),
            findsOneWidget,
            reason: '文本标签应该在Row布局内',
          );
        });

        testWidgets('文本标签应该具有正确的默认样式', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final labelWidget = tester.widget<Text>(find.byKey(const Key('sound_switch_label')));

          // 验证文本内容
          expect(labelWidget.data, '音 ON/OFF');

          // Text 组件的 style 默认为 null，由主题决定
          // 这里验证组件可以正常渲染即可
          expect(labelWidget, isNotNull);
        });
      });

      group('1.3 开关组件测试', () {
        testWidgets('应该显示 Switch 开关', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_switch')), findsOneWidget, reason: '应该显示开关组件');

          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget, isNotNull, reason: 'Switch 组件应该存在');
        });

        testWidgets('Switch 应该反映 useSound = true 状态', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, true, reason: 'Switch 应该显示开启状态');
        });

        testWidgets('Switch 应该反映 useSound = false 状态', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, false, reason: 'Switch 应该显示关闭状态');
        });

        testWidgets('Switch 应该能反映不同的状态值', (tester) async {
          // Test 不同状态值
          final testStates = [true, false, true, false];

          for (final testState in testStates) {
            // 为每个测试状态创建新的测试环境
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();

            final testModel = createTestModel(useSound: testState);
            when(mockController.model).thenReturn(testModel.obs);
            Get.put<ScanSettingsController>(mockController);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));

            expect(switchWidget.value, testState, reason: 'Switch 应该显示状态 $testState');
          }
        });

        testWidgets('Switch 应该具有正确的回调配置', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.onChanged, isNotNull, reason: 'Switch 应该有 onChanged 回调');
        });

        testWidgets('Switch 应该在 Obx 组件内', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final obxFinder = find.byType(Obx);
          expect(obxFinder, findsAtLeastNWidgets(1), reason: '应该有至少一个 Obx 组件');

          // 验证 Switch 在 Obx 内部
          expect(
            find.descendant(of: obxFinder.first, matching: find.byKey(const Key('sound_switch'))),
            findsOneWidget,
            reason: 'Switch 应该在 Obx 内部以支持响应式更新',
          );
        });
      });

      group('1.4 布局组件测试', () {
        testWidgets('应该具有正确的 Padding 设置', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerFinder = find.byKey(const Key('sound_switch_container'));
          final paddingFinder = find.descendant(of: containerFinder, matching: find.byType(Padding));

          expect(paddingFinder, findsAtLeastNWidgets(1), reason: '应该有至少一个 Padding 组件');

          // 验证有我们需要的 Padding 设置
          final paddingWidgets = tester.widgetList<Padding>(paddingFinder);
          final mainPadding = paddingWidgets.firstWhere(
            (padding) => padding.padding == const EdgeInsets.all(8.0),
            orElse: () => throw StateError('未找到 EdgeInsets.all(8.0) 的 Padding'),
          );
          expect(mainPadding.padding, const EdgeInsets.all(8.0), reason: 'Padding 应该是 8.0 的全边距');
        });

        testWidgets('Row 组件应该具有正确的主轴对齐', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final rowWidget = tester.widget<Row>(find.byKey(const Key('sound_switch_row')));
          expect(rowWidget.mainAxisAlignment, MainAxisAlignment.spaceBetween, reason: 'Row 的主轴对齐应该是 spaceBetween');
        });

        testWidgets('应该具有正确的组件层次结构', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerFinder = find.byKey(const Key('sound_switch_container'));

          // 验证容器包含 Padding
          expect(
            find.descendant(of: containerFinder, matching: find.byType(Padding)),
            findsAtLeastNWidgets(1),
            reason: '容器应该包含至少一个 Padding',
          );

          // 验证 Padding 包含 Row
          final paddingFinder = find.descendant(of: containerFinder, matching: find.byType(Padding));
          expect(
            find.descendant(of: paddingFinder, matching: find.byKey(const Key('sound_switch_row'))),
            findsOneWidget,
            reason: 'Padding 应该包含 Row',
          );

          // 验证 Row 包含标签和开关
          final rowFinder = find.byKey(const Key('sound_switch_row'));
          expect(
            find.descendant(of: rowFinder, matching: find.byKey(const Key('sound_switch_label'))),
            findsOneWidget,
            reason: 'Row 应该包含文本标签',
          );

          expect(
            find.descendant(of: rowFinder, matching: find.byType(Obx)),
            findsOneWidget,
            reason: 'Row 应该包含 Obx (包装 Switch)',
          );
        });

        testWidgets('Row 应该包含正确数量的子组件', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final rowWidget = tester.widget<Row>(find.byKey(const Key('sound_switch_row')));
          expect(rowWidget.children.length, equals(2), reason: 'Row 应该包含2个子组件：Text 和 Obx');
        });

        testWidgets('布局组件应该在正确的嵌套层次', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          // 验证 SoundSwitchWidget > Container > Padding > Row > [Text, Obx] 的层次结构
          expect(find.byType(SoundSwitchWidget), findsOneWidget);
          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget);
          expect(find.byType(Padding), findsAtLeastNWidgets(1));
          expect(find.byKey(const Key('sound_switch_row')), findsOneWidget);
          expect(find.byKey(const Key('sound_switch_label')), findsOneWidget);
          expect(find.byType(Obx), findsAtLeastNWidgets(1));
          expect(find.byKey(const Key('sound_switch')), findsOneWidget);
        });
      });

      group('1.5 UI 组件综合验证', () {
        testWidgets('所有 UI 组件应该协调工作', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有组件都存在且配置正确

          // 容器组件
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_switch_container')));
          expect(containerWidget.decoration, isA<BoxDecoration>());

          // 文本组件
          final labelWidget = tester.widget<Text>(find.byKey(const Key('sound_switch_label')));
          expect(labelWidget.data, '音 ON/OFF');

          // 开关组件
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(switchWidget.value, true);
          expect(switchWidget.onChanged, isNotNull);

          // 布局组件
          final rowWidget = tester.widget<Row>(find.byKey(const Key('sound_switch_row')));
          expect(rowWidget.mainAxisAlignment, MainAxisAlignment.spaceBetween);
        });

        testWidgets('UI 组件样式应该保持一致性', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          // 容器装饰一致性
          final containerFinder = find.byKey(const Key('sound_switch_container'));
          final containerWidget = tester.widget<Container>(containerFinder);
          expect(containerWidget.decoration, isNotNull, reason: '容器应该有装饰');

          final decoration = containerWidget.decoration as BoxDecoration;
          expect(decoration.color, AppTheme.lightTheme.customTheme.cardBackgroundColor, reason: '容器背景色应该使用主题色');
          expect(decoration.borderRadius, isA<BorderRadius>(), reason: '容器应该有圆角');

          // Padding 一致性
          final paddingFinder = find.descendant(of: containerFinder, matching: find.byType(Padding));
          final paddingWidgets = tester.widgetList<Padding>(paddingFinder);
          final mainPadding = paddingWidgets.firstWhere(
            (padding) => padding.padding == const EdgeInsets.all(8.0),
            orElse: () => throw StateError('未找到主要的 Padding'),
          );
          expect(mainPadding.padding, const EdgeInsets.all(8.0), reason: 'Padding 应该统一使用 8.0');
        });

        testWidgets('UI 组件布局应该具有良好的层次结构', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final keyComponents = [
            const Key('sound_switch_container'),
            const Key('sound_switch_row'),
            const Key('sound_switch_label'),
            const Key('sound_switch'),
          ];

          final containerFinder = find.byKey(const Key('sound_switch_container'));

          for (final key in keyComponents) {
            if (key == const Key('sound_switch_container')) {
              // 容器本身应该存在
              expect(find.byKey(key), findsOneWidget, reason: 'Key $key 的组件应该存在');
            } else {
              // 其他组件应该在容器内
              expect(
                find.descendant(of: containerFinder, matching: find.byKey(key)),
                findsOneWidget,
                reason: 'Key $key 的组件应该在主容器内',
              );
            }
          }
        });
      });
    });

    // ==========================================
    // 🎮 Phase 2: 交互组件功能测试
    // ==========================================

    group('🎮 Phase 2: 交互组件功能测试', () {
      group('2.1 Switch 点击交互测试', () {
        testWidgets('应该能够点击 Switch 开关', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击 Switch
          final switchFinder = find.byKey(const Key('sound_switch'));
          expect(switchFinder, findsOneWidget, reason: 'Switch 应该存在');

          await tester.tap(switchFinder);
          await tester.pumpAndSettle();

          // Assert - 验证 soundEventChange 被调用
          verify(mockController.soundEventChange(any)).called(1);
        });

        testWidgets('点击 Switch 应该传递正确的参数', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击 Switch (从 false 切换到 true)
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          // Assert - 验证传递的参数
          final capturedArgs = verify(mockController.soundEventChange(captureAny)).captured;
          expect(capturedArgs, isNotEmpty, reason: '应该有方法调用');
          expect(capturedArgs.first, true, reason: '从 false 点击应该传递 true');
        });

        testWidgets('从 true 状态点击 Switch 应该传递 false', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击 Switch (从 true 切换到 false)
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          // Assert - 验证传递的参数
          final capturedArgs = verify(mockController.soundEventChange(captureAny)).captured;
          expect(capturedArgs, isNotEmpty, reason: '应该有方法调用');
          expect(capturedArgs.first, false, reason: '从 true 点击应该传递 false');
        });

        testWidgets('Switch 应该能够连续点击', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final switchFinder = find.byKey(const Key('sound_switch'));

          // 连续点击多次
          for (int i = 0; i < 3; i++) {
            await tester.tap(switchFinder);
            await tester.pump(const Duration(milliseconds: 100));
          }
          await tester.pumpAndSettle();

          // Assert - 验证方法被多次调用
          verify(mockController.soundEventChange(any)).called(3);
        });

        testWidgets('Switch 应该能处理快速点击', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final switchFinder = find.byKey(const Key('sound_switch'));

          // 快速连续点击
          for (int i = 0; i < 5; i++) {
            await tester.tap(switchFinder);
            await tester.pump(const Duration(milliseconds: 50)); // 更快的间隔
          }
          await tester.pumpAndSettle();

          // Assert - 验证方法被调用
          verify(mockController.soundEventChange(any)).called(5);
        });
      });

      group('2.2 Controller 方法调用验证', () {
        testWidgets('soundEventChange 应该只在点击时被调用', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act - 仅渲染组件，不点击
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 方法不应该被调用
          verifyNever(mockController.soundEventChange(any));

          // 点击后应该被调用
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          verify(mockController.soundEventChange(any)).called(1);
        });

        testWidgets('应该验证 soundEventChange 的调用序列', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final switchFinder = find.byKey(const Key('sound_switch'));

          // 执行一系列点击操作
          // 注意：每次点击都是基于当前 Widget 显示的状态，而不是实际 Controller 状态
          await tester.tap(switchFinder); // 从 false -> true
          await tester.pump(const Duration(milliseconds: 100));

          await tester.tap(switchFinder); // 仍然从 false -> true (因为 Mock 状态没有真实改变)
          await tester.pump(const Duration(milliseconds: 100));

          await tester.tap(switchFinder); // 仍然从 false -> true
          await tester.pumpAndSettle();

          // Assert - 验证调用序列
          final capturedArgs = verify(mockController.soundEventChange(captureAny)).captured;
          expect(capturedArgs.length, 3, reason: '应该有3次方法调用');
          expect(capturedArgs[0], true, reason: '第一次点击应该传递 true');
          expect(capturedArgs[1], true, reason: '第二次点击应该传递 true (Mock 状态未变)');
          expect(capturedArgs[2], true, reason: '第三次点击应该传递 true (Mock 状态未变)');
        });

        testWidgets('应该正确处理 null 和边界情况', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击 Switch
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          // Assert - 验证传递的参数不是 null
          final capturedArgs = verify(mockController.soundEventChange(captureAny)).captured;
          expect(capturedArgs.first, isNotNull, reason: '传递的参数不应该是 null');
          expect(capturedArgs.first, isA<bool>(), reason: '传递的参数应该是 bool 类型');
        });
      });

      group('2.3 响应式状态更新测试', () {
        testWidgets('Obx 应该响应状态变化', (tester) async {
          // Arrange - 初始状态为 false
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证初始状态
          final initialSwitchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(initialSwitchWidget.value, false, reason: '初始状态应该是 false');

          // 模拟状态变化到 true
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();

          final updatedModel = createTestModel(useSound: true);
          when(mockController.model).thenReturn(updatedModel.obs);
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证更新后状态
          final updatedSwitchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(updatedSwitchWidget.value, true, reason: '更新后状态应该是 true');
        });

        testWidgets('应该能够处理多次状态变化', (tester) async {
          // Test 多个状态变化
          final testStates = [false, true, false, true, false];

          for (int i = 0; i < testStates.length; i++) {
            final currentState = testStates[i];

            // 为每个状态创建新的测试环境
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();

            final testModel = createTestModel(useSound: currentState);
            when(mockController.model).thenReturn(testModel.obs);
            Get.put<ScanSettingsController>(mockController);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final switchWidget = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
            expect(switchWidget.value, currentState, reason: '状态 $i 应该显示 $currentState');
          }
        });

        testWidgets('Obx 包装应该确保响应式更新', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证 Obx 存在且包装了 Switch
          final obxFinder = find.byType(Obx);
          expect(obxFinder, findsAtLeastNWidgets(1), reason: '应该有 Obx 组件');

          final switchInObx = find.descendant(of: obxFinder.first, matching: find.byKey(const Key('sound_switch')));
          expect(switchInObx, findsOneWidget, reason: 'Switch 应该在 Obx 内部');
        });
      });

      group('2.4 交互边界条件测试', () {
        testWidgets('应该能够处理极快的连续点击', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final switchFinder = find.byKey(const Key('sound_switch'));

          // 极快的连续点击（模拟用户误操作）
          for (int i = 0; i < 10; i++) {
            await tester.tap(switchFinder);
            await tester.pump(const Duration(milliseconds: 10)); // 极短间隔
          }
          await tester.pumpAndSettle();

          // Assert - 验证所有点击都被处理
          verify(mockController.soundEventChange(any)).called(10);
        });

        testWidgets('Switch 应该在不同初始状态下都能正常工作', (tester) async {
          // Test 不同初始状态
          final initialStates = [true, false];

          for (final initialState in initialStates) {
            // 为每个初始状态创建新的测试环境
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();

            final testModel = createTestModel(useSound: initialState);
            when(mockController.model).thenReturn(testModel.obs);
            Get.put<ScanSettingsController>(mockController);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // 点击 Switch
            await tester.tap(find.byKey(const Key('sound_switch')));
            await tester.pumpAndSettle();

            // 验证传递的参数是相反值
            final capturedArgs = verify(mockController.soundEventChange(captureAny)).captured;
            expect(capturedArgs.first, !initialState, reason: '从 $initialState 点击应该传递 ${!initialState}');
          }
        });

        testWidgets('应该能够处理长按而不是点击', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final switchFinder = find.byKey(const Key('sound_switch'));

          // 长按 Switch（而不是点击）
          await tester.longPress(switchFinder);
          await tester.pumpAndSettle();

          // Assert - Switch 的 onChanged 应该在长按时也被触发
          // （Flutter Switch 组件在长按时也会触发 onChanged）
          verify(mockController.soundEventChange(any)).called(1);
        });

        testWidgets('应该能够在组件重建后保持交互功能', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act - 第一次构建
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 重建组件
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击 Switch
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          // Assert - 重建后交互仍然正常
          verify(mockController.soundEventChange(any)).called(1);
        });

        testWidgets('应该能够正确处理交互和状态同步', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证初始状态
          final initialSwitch = tester.widget<Switch>(find.byKey(const Key('sound_switch')));
          expect(initialSwitch.value, false);

          // 点击 Switch
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          // Assert - 验证方法调用和期望的状态变化
          verify(mockController.soundEventChange(true)).called(1);

          // 注意：实际的状态变化需要 Controller 来处理
          // 这里我们只验证正确的方法调用
        });
      });

      group('2.5 交互性能和稳定性测试', () {
        testWidgets('大量连续点击不应该导致性能问题', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final switchFinder = find.byKey(const Key('sound_switch'));

          // 大量点击测试性能
          final stopwatch = Stopwatch()..start();

          for (int i = 0; i < 50; i++) {
            await tester.tap(switchFinder);
            await tester.pump(const Duration(milliseconds: 20));
          }
          await tester.pumpAndSettle();

          stopwatch.stop();

          // Assert - 验证性能和调用次数
          verify(mockController.soundEventChange(any)).called(50);
          expect(stopwatch.elapsedMilliseconds, lessThan(5000), reason: '50次点击应该在5秒内完成');
        });

        testWidgets('交互应该不影响其他 UI 组件', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击 Switch 前验证其他组件存在
          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget);
          expect(find.byKey(const Key('sound_switch_label')), findsOneWidget);
          expect(find.text('音 ON/OFF'), findsOneWidget);

          // 点击 Switch
          await tester.tap(find.byKey(const Key('sound_switch')));
          await tester.pumpAndSettle();

          // Assert - 其他组件应该仍然存在且不受影响
          expect(find.byKey(const Key('sound_switch_container')), findsOneWidget, reason: '容器在交互后应该仍然存在');
          expect(find.byKey(const Key('sound_switch_label')), findsOneWidget, reason: '标签在交互后应该仍然存在');
          expect(find.text('音 ON/OFF'), findsOneWidget, reason: '文本在交互后应该仍然存在');

          // 验证方法调用
          verify(mockController.soundEventChange(any)).called(1);
        });
      });
    });
  });
}
