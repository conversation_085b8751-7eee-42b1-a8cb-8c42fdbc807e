import 'package:asset_force_mobile_v2/core/platform/method_channel.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/font_scale_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/controllers/font_size_setting_controller.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'font_size_setting_controller_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<FontScaleService>(),
  MockSpec<IStorageUtils>(),
  MockSpec<DialogService>(),
  MockSpec<PlatformChannel>(),
])
void main() {
  // Mocks
  late MockFontScaleService mockFontScaleService;
  late MockIStorageUtils mockStorage;
  late MockDialogService mockDialogService;
  late MockPlatformChannel mockPlatformChannel;

  // System Under Test (SUT)
  late FontSizeSettingController controller;

  // Helper function to create the controller
  FontSizeSettingController createController() {
    return FontSizeSettingController(
      fontScaleService: mockFontScaleService,
      storage: mockStorage,
      dialogService: mockDialogService,
      platformChannel: mockPlatformChannel,
    );
  }

  setUp(() {
    // Reset GetX before each test to ensure a clean state
    Get.reset();

    // Initialize mocks
    mockFontScaleService = MockFontScaleService();
    mockStorage = MockIStorageUtils();
    mockDialogService = MockDialogService();
    mockPlatformChannel = MockPlatformChannel();

    // Default stubbing for common calls to avoid errors in setup
    when(mockFontScaleService.fontSize).thenReturn('normal');
    when(mockStorage.getValue<String>(any)).thenReturn(null);
    when(mockStorage.setValue(any, any)).thenAnswer((_) async => true);
  });

  tearDown(() {
    // Deregister dependencies to avoid conflicts between tests
    Get.reset();
  });

  group('Phase 0: Test Scaffold and Initialization', () {
    test('Controller should be instantiated without errors', () {
      // Act
      controller = createController();
      // Assert
      expect(controller, isA<FontSizeSettingController>());
      expect(controller, isNotNull);
    });

    test('Dependencies should be injected correctly', () {
      // Act
      controller = createController();
      // Assert
      // A simple way to check if mocks are connected is to trigger an action
      // and verify if the mock method was called.
      controller.onSave();
      verify(mockDialogService.showToast(any)).called(1);
      verify(mockStorage.setValue(any, any)).called(1);
    });
  });

  group('Phase 1: Initialization and State Restoration', () {
    test('onInit - should set selectedFontSize to "normal" when storage is empty', () {
      // Arrange
      // In setUp, storage is already configured to return null
      // Act
      controller = createController();
      controller.onInit(); // Manually call onInit for GetxController tests

      // Assert
      expect(controller.selectedFontSize.value, FontScaleService.keyNormal);
    });

    test('onInit - should restore selectedFontSize from storage if value is valid ("big")', () {
      // Arrange
      when(mockStorage.getValue<String>(any)).thenReturn(FontScaleService.keyBig);

      // Act
      controller = createController();
      controller.onInit();

      // Assert
      expect(controller.selectedFontSize.value, FontScaleService.keyBig);
    });

    test('onInit - should default to "normal" if stored value is invalid', () {
      // Arrange
      when(mockStorage.getValue<String>(any)).thenReturn('invalid_size');

      // Act
      controller = createController();
      controller.onInit();

      // Assert
      expect(controller.selectedFontSize.value, FontScaleService.keyNormal);
    });

    test('onInit - should correctly record the original font size from FontScaleService', () {
      // Arrange
      const initialFontSize = 'large'; // A different value for testing
      when(mockFontScaleService.fontSize).thenReturn(initialFontSize);

      // Act
      controller = createController();
      controller.onInit();

      // Assert
      expect(controller.originValue, initialFontSize);
    });
  });

  group('Phase 2: User Interaction and State Changes', () {
    test('onFontSizeSelect - should update selectedFontSize with the new value', () {
      // Arrange
      controller = createController();
      const newSize = 'big';

      // Act
      controller.onFontSizeSelect(newSize);

      // Assert
      expect(controller.selectedFontSize.value, newSize);
    });

    test('onFontSizeSelect - should call FontScaleService to apply the new font size', () {
      // Arrange
      controller = createController();
      const newSize = 'big';

      // Act
      controller.onFontSizeSelect(newSize);

      // Assert
      verify(mockFontScaleService.toggleFontSize(newSize)).called(1);
    });

    test('onFontSizeSelect - should handle multiple selections correctly', () {
      // Arrange
      controller = createController();

      // Act & Assert
      controller.onFontSizeSelect('big');
      expect(controller.selectedFontSize.value, 'big');
      verify(mockFontScaleService.toggleFontSize('big')).called(1);

      controller.onFontSizeSelect('normal');
      expect(controller.selectedFontSize.value, 'normal');
      verify(mockFontScaleService.toggleFontSize('normal')).called(1);

      // Verify that the calls were specific and happened once for each value
      verifyNever(mockFontScaleService.toggleFontSize('some_other_size'));
    });
  });

  group('Phase 3: Save and Revert Logic', () {
    test('onSave - should persist the selected font size using Storage', () {
      // Arrange
      controller = createController();
      const newSize = 'big';
      controller.onFontSizeSelect(newSize);

      // Act
      controller.onSave();

      // Assert
      verify(mockStorage.setValue(StorageKeys.fontSize, newSize)).called(1);
    });

    test('onSave - should sync the selected font size with the native platform', () {
      // Arrange
      controller = createController();
      const newSize = 'big';
      controller.selectedFontSize.value = newSize;

      // Act
      controller.onSave();

      // Assert
      verify(mockPlatformChannel.syncStorage({StorageKeys.fontSize: newSize})).called(1);
    });

    test('onSave - should show a confirmation toast', () {
      // Arrange
      controller = createController();

      // Act
      controller.onSave();

      // Assert
      verify(mockDialogService.showToast('文字サイズを更新しました。')).called(1);
    });

    test('onSave - should update the originValue to the newly saved value', () {
      // Arrange
      controller = createController();
      controller.originValue = 'normal'; // Set initial origin
      const newSize = 'big';
      controller.selectedFontSize.value = newSize;

      // Act
      controller.onSave();

      // Assert
      expect(controller.originValue, newSize);
    });

    test('onBack - should revert font size if it has been changed', () {
      // Arrange
      const originalSize = 'normal';
      when(mockFontScaleService.fontSize).thenReturn(originalSize);
      controller = createController();
      controller.originValue = originalSize; // Manually set for clarity

      // Simulate a change
      controller.onFontSizeSelect('big');
      when(mockFontScaleService.fontSize).thenReturn('big');

      // Act
      controller.onBack();

      // Assert
      verify(mockFontScaleService.toggleFontSize(originalSize)).called(1);
    });

    test('onBack - should NOT revert font size if it has not been changed', () {
      // Arrange
      const originalSize = 'normal';
      when(mockFontScaleService.fontSize).thenReturn(originalSize);
      controller = createController();
      controller.originValue = originalSize;

      // Act
      controller.onBack();

      // Assert
      verifyNever(mockFontScaleService.toggleFontSize(any));
    });
  });

  group('Phase 4: Boundary Conditions and Robustness', () {
    test('onFontSizeSelect - should handle null input gracefully', () {
      // Arrange
      controller = createController();
      // Act & Assert
      // We expect this to throw an error because the parameter is non-nullable.
      // This test confirms Dart's null safety is working as expected.
      expect(() => controller.onFontSizeSelect(null as String), throwsA(isA<TypeError>()));
    });

    test('onSave - should handle storage write failure', () {
      // Arrange
      when(mockStorage.setValue(any, any)).thenThrow(Exception('Disk full'));
      controller = createController();
      controller.onFontSizeSelect('big');

      // Act & Assert
      // The current implementation does not handle this, but a robust app might.
      // This test documents the current behavior: the exception is not caught.
      expect(() => controller.onSave(), throwsA(isA<Exception>()));

      // In a more complex app, we might verify that a specific error message is shown.
      verifyNever(mockDialogService.showToast('文字サイズを更新しました。'));
    });

    test('onInit - should handle storage read failure', () {
      // Arrange
      when(mockStorage.getValue<String>(any)).thenThrow(Exception('Storage corrupted'));

      // Act & Assert
      // Similar to the save test, this documents that read errors are not currently handled.
      expect(() => createController().onInit(), throwsA(isA<Exception>()));
    });

    test('Rapid successive calls to onSave should only execute once if guarded', () {
      // This is a placeholder for a more advanced scenario.
      // If the onSave button could be tapped multiple times quickly, we might add a guard
      // (e.g., a boolean flag `isSaving`) to prevent multiple concurrent executions.
      // For now, we'll just test the current behavior.

      // Arrange
      controller = createController();

      // Act
      controller.onSave();
      controller.onSave();

      // Assert
      // Currently, it will be called twice.
      verify(mockStorage.setValue(any, any)).called(2);
      verify(mockDialogService.showToast(any)).called(2);
    });
  });
}
