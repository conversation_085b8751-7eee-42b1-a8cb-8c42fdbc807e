// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_sso/presentation/pages/login_sso_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;
import 'dart:ui' as _i16;

import 'package:asset_force_mobile_v2/core/deeplink/i_deeplink_service.dart'
    as _i9;
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i11;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i14;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i3;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i19;
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_controller.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/states/sso_ui_state.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart'
    as _i17;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i20;
import 'package:asset_force_mobile_v2/features/skill_plugin/i_sso_scan_plugin.dart'
    as _i8;
import 'package:flutter/cupertino.dart' as _i5;
import 'package:get/get.dart' as _i7;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i15;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i18;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLoginSsoUseCase_0 extends _i1.SmartFake
    implements _i2.LoginSsoUseCase {
  _FakeLoginSsoUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_1 extends _i1.SmartFake
    implements _i3.NavigationService {
  _FakeNavigationService_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_2 extends _i1.SmartFake implements _i4.DialogService {
  _FakeDialogService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTextEditingController_3 extends _i1.SmartFake
    implements _i5.TextEditingController {
  _FakeTextEditingController_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSsoUIState_4 extends _i1.SmartFake implements _i6.SsoUIState {
  _FakeSsoUIState_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_5<T> extends _i1.SmartFake
    implements _i7.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeISsoScanPlugin_6 extends _i1.SmartFake
    implements _i8.ISsoScanPlugin {
  _FakeISsoScanPlugin_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIDeeplinkService_7 extends _i1.SmartFake
    implements _i9.IDeeplinkService {
  _FakeIDeeplinkService_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_8 extends _i1.SmartFake implements _i10.IStorageUtils {
  _FakeIStorageUtils_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_9 extends _i1.SmartFake implements _i11.IEnvHelper {
  _FakeIEnvHelper_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginSsoController].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginSsoController extends _i1.Mock
    implements _i12.LoginSsoController {
  @override
  _i2.LoginSsoUseCase get loginSsoUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#loginSsoUseCase),
            returnValue: _FakeLoginSsoUseCase_0(
              this,
              Invocation.getter(#loginSsoUseCase),
            ),
            returnValueForMissingStub: _FakeLoginSsoUseCase_0(
              this,
              Invocation.getter(#loginSsoUseCase),
            ),
          )
          as _i2.LoginSsoUseCase);

  @override
  _i3.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i3.NavigationService);

  @override
  _i4.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i4.DialogService);

  @override
  _i5.TextEditingController get textEditingController =>
      (super.noSuchMethod(
            Invocation.getter(#textEditingController),
            returnValue: _FakeTextEditingController_3(
              this,
              Invocation.getter(#textEditingController),
            ),
            returnValueForMissingStub: _FakeTextEditingController_3(
              this,
              Invocation.getter(#textEditingController),
            ),
          )
          as _i5.TextEditingController);

  @override
  _i6.SsoUIState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeSsoUIState_4(this, Invocation.getter(#state)),
            returnValueForMissingStub: _FakeSsoUIState_4(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i6.SsoUIState);

  @override
  _i7.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i7.InternalFinalCallback<void>);

  @override
  _i7.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i7.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void goBackPage() => super.noSuchMethod(
    Invocation.method(#goBackPage, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClickSsoGotoThirdParty() => super.noSuchMethod(
    Invocation.method(#onClickSsoGotoThirdParty, []),
    returnValueForMissingStub: null,
  );

  @override
  void onChangeSsoUrlInput({required String? ssoUrl}) => super.noSuchMethod(
    Invocation.method(#onChangeSsoUrlInput, [], {#ssoUrl: ssoUrl}),
    returnValueForMissingStub: null,
  );

  @override
  void onClickClearSsoUrlInput() => super.noSuchMethod(
    Invocation.method(#onClickClearSsoUrlInput, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i14.ErrorHandlingMode? mode = _i14.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i15.Disposer addListener(_i15.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i15.Disposer);

  @override
  void removeListener(_i16.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i16.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i15.Disposer addListenerId(Object? key, _i15.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i15.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [LoginSsoUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginSsoUseCase extends _i1.Mock implements _i2.LoginSsoUseCase {
  @override
  _i8.ISsoScanPlugin get ssoScanPlugin =>
      (super.noSuchMethod(
            Invocation.getter(#ssoScanPlugin),
            returnValue: _FakeISsoScanPlugin_6(
              this,
              Invocation.getter(#ssoScanPlugin),
            ),
            returnValueForMissingStub: _FakeISsoScanPlugin_6(
              this,
              Invocation.getter(#ssoScanPlugin),
            ),
          )
          as _i8.ISsoScanPlugin);

  @override
  _i9.IDeeplinkService get deeplinkService =>
      (super.noSuchMethod(
            Invocation.getter(#deeplinkService),
            returnValue: _FakeIDeeplinkService_7(
              this,
              Invocation.getter(#deeplinkService),
            ),
            returnValueForMissingStub: _FakeIDeeplinkService_7(
              this,
              Invocation.getter(#deeplinkService),
            ),
          )
          as _i9.IDeeplinkService);

  @override
  _i10.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_8(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_8(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i10.IStorageUtils);

  @override
  _i11.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_9(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_9(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i11.IEnvHelper);

  @override
  _i13.Future<dynamic> call(String? pragma) =>
      (super.noSuchMethod(
            Invocation.method(#call, [pragma]),
            returnValue: _i13.Future<dynamic>.value(),
            returnValueForMissingStub: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);

  @override
  _i13.Future<String?> openScanBarcode() =>
      (super.noSuchMethod(
            Invocation.method(#openScanBarcode, []),
            returnValue: _i13.Future<String?>.value(),
            returnValueForMissingStub: _i13.Future<String?>.value(),
          )
          as _i13.Future<String?>);

  @override
  List<_i17.TenantSsoModel> fromJsonList({required String? jsonString}) =>
      (super.noSuchMethod(
            Invocation.method(#fromJsonList, [], {#jsonString: jsonString}),
            returnValue: <_i17.TenantSsoModel>[],
            returnValueForMissingStub: <_i17.TenantSsoModel>[],
          )
          as List<_i17.TenantSsoModel>);

  @override
  String processTenants({required List<_i17.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processTenants, [], {#tenants: tenants}),
            returnValue: _i18.dummyValue<String>(
              this,
              Invocation.method(#processTenants, [], {#tenants: tenants}),
            ),
            returnValueForMissingStub: _i18.dummyValue<String>(
              this,
              Invocation.method(#processTenants, [], {#tenants: tenants}),
            ),
          )
          as String);

  @override
  String processOrderTenants({required List<_i17.TenantSsoModel>? tenants}) =>
      (super.noSuchMethod(
            Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            returnValue: _i18.dummyValue<String>(
              this,
              Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            ),
            returnValueForMissingStub: _i18.dummyValue<String>(
              this,
              Invocation.method(#processOrderTenants, [], {#tenants: tenants}),
            ),
          )
          as String);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i3.NavigationService {
  @override
  _i13.Future<dynamic> navigateTo(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i13.Future<dynamic>.value(),
            returnValueForMissingStub: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);

  @override
  _i13.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i13.Future<dynamic>.value(),
            returnValueForMissingStub: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);

  @override
  _i13.Future<bool> navigateUntil(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i13.Future<bool>.value(false),
            returnValueForMissingStub: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<dynamic> toAssetDetail(_i19.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i13.Future<dynamic>.value(),
            returnValueForMissingStub: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i4.DialogService {
  @override
  _i13.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i16.VoidCallback? onConfirm,
    _i16.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i20.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i16.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i5.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i20.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}
