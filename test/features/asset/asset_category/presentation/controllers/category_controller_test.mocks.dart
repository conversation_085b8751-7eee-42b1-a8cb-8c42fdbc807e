// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_category/presentation/controllers/category_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/repositories/category_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_assemble_list_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_count_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i9;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCategoryRepository_0 extends _i1.SmartFake
    implements _i2.CategoryRepository {
  _FakeCategoryRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCategoryCountResult_1 extends _i1.SmartFake
    implements _i3.GetCategoryCountResult {
  _FakeGetCategoryCountResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCategoryAssembleListResult_2 extends _i1.SmartFake
    implements _i4.GetCategoryAssembleListResult {
  _FakeGetCategoryAssembleListResult_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeGetCategoryAllListResult_3 extends _i1.SmartFake
    implements _i5.GetCategoryAllListResult {
  _FakeGetCategoryAllListResult_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetCategoryCountUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetCategoryCountUseCase extends _i1.Mock
    implements _i3.GetCategoryCountUseCase {
  MockGetCategoryCountUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.CategoryRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeCategoryRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.CategoryRepository);

  @override
  _i6.Future<_i3.GetCategoryCountResult> call(
    _i3.GetCategoryCountParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i6.Future<_i3.GetCategoryCountResult>.value(
              _FakeGetCategoryCountResult_1(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i6.Future<_i3.GetCategoryCountResult>);
}

/// A class which mocks [GetCategoryAssembleListUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetCategoryAssembleListUseCase extends _i1.Mock
    implements _i4.GetCategoryAssembleListUseCase {
  MockGetCategoryAssembleListUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i4.GetCategoryAssembleListResult> call(
    _i4.GetCategoryAssembleListParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i6.Future<_i4.GetCategoryAssembleListResult>.value(
              _FakeGetCategoryAssembleListResult_2(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i6.Future<_i4.GetCategoryAssembleListResult>);
}

/// A class which mocks [GetCategoryAllListUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetCategoryAllListUseCase extends _i1.Mock
    implements _i5.GetCategoryAllListUseCase {
  MockGetCategoryAllListUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i5.GetCategoryAllListResult> call(
    _i5.GetCategoryAllListParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i6.Future<_i5.GetCategoryAllListResult>.value(
              _FakeGetCategoryAllListResult_3(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i6.Future<_i5.GetCategoryAllListResult>);

  @override
  bool isClickCurrentLevel(_i7.CategoryModel? beforeInfo, dynamic afterInfo) =>
      (super.noSuchMethod(
            Invocation.method(#isClickCurrentLevel, [beforeInfo, afterInfo]),
            returnValue: false,
          )
          as bool);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i8.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<dynamic> toAssetDetail(_i9.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);
}
