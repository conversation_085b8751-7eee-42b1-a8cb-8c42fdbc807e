import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_page_list_api_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_page_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_list_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// Generate mocks for the HomeRepository
@GenerateMocks([HomeRepository])
import 'get_home_page_list_usecase_test.mocks.dart';

void main() {
  late GetHomePageListUseCase useCase;
  late MockHomeRepository mockHomeRepository;

  LogUtil.initialize();

  setUp(() {
    mockHomeRepository = MockHomeRepository();
    useCase = GetHomePageListUseCase(homeRepository: mockHomeRepository);
  });

  group('GetHomePageListUseCase', () {
    // Mock data for tests
    final mockAssetAction1 = SharedAssetAction(
      assetActionId: 1, // int类型
      assetActionName: 'Action 1', // String类型
      actionLabel: 'A', // String类型
      tenantId: 'tenant-001', // String类型
      assetTypeId: 100, // int类型
      assetTypeGroupIds: '1,2,3', // String类型
      assetTypeName: 'Type A', // String类型
      quantityFlg: 'true', // String类型
      assetActionItem: 'item1', // String类型
      scanCondition: '{}', // String类型
      userIds: '10,20,30', // String类型
      groupIds: 'group1,group2', // String类型
      isUpdateAmount: 'false', // String类型
      autoFetchAsset: 'false', // String类型
      assetConditionSearchName: 'condition1', // dynamic类型
      autoFetchSearchId: 500, // int类型
      createdById: 'user-1', // String类型
      createdByName: 'Test User', // String类型
      createdDate: '2023-01-01', // String类型
    );

    final mockAssetAction2 = SharedAssetAction(
      assetActionId: 2, // int类型
      assetActionName: 'Action 2', // String类型
      actionLabel: 'A', // String类型
      tenantId: 'tenant-001', // String类型
      assetTypeId: 101, // int类型
      assetTypeGroupIds: '4,5,6', // String类型
      assetTypeName: 'Type B', // String类型
      quantityFlg: 'true', // String类型
      assetActionItem: 'item2', // String类型
      scanCondition: '{}', // String类型
      userIds: '40,50,60', // String类型
      groupIds: 'group3,group4', // String类型
      isUpdateAmount: 'false', // String类型
      autoFetchAsset: 'false', // String类型
      assetConditionSearchName: 'condition2', // dynamic类型
      autoFetchSearchId: 501, // int类型
      createdById: 'user-2', // String类型
      createdByName: 'Test User 2', // String类型
      createdDate: '2023-01-02', // String类型
    );

    final mockAssetAction3 = SharedAssetAction(
      assetActionId: 3, // int类型
      assetActionName: 'Action 3', // String类型
      actionLabel: 'B', // String类型
      tenantId: 'tenant-001', // String类型
      assetTypeId: 102, // int类型
      assetTypeGroupIds: '7,8,9', // String类型
      assetTypeName: 'Type C', // String类型
      quantityFlg: 'true', // String类型
      assetActionItem: 'item3', // String类型
      scanCondition: '{}', // String类型
      userIds: '70,80,90', // String类型
      groupIds: 'group5,group6', // String类型
      isUpdateAmount: 'false', // String类型
      autoFetchAsset: 'false', // String类型
      assetConditionSearchName: 'condition3', // dynamic类型
      autoFetchSearchId: 502, // int类型
      createdById: 'user-3', // String类型
      createdByName: 'Test User 3', // String类型
      createdDate: '2023-01-03', // String类型
    );

    final mockAssetAction4 = SharedAssetAction(
      assetActionId: 4, // int类型
      assetActionName: 'Action 4', // String类型
      actionLabel: null, // String?类型，可为null
      tenantId: 'tenant-001', // String类型
      assetTypeId: 103, // int类型
      assetTypeGroupIds: '10,11,12', // String类型
      assetTypeName: 'Type D', // String类型
      quantityFlg: 'true', // String类型
      assetActionItem: 'item4', // String类型
      scanCondition: '{}', // String类型
      userIds: '100,110,120', // String类型
      groupIds: 'group7,group8', // String类型
      isUpdateAmount: 'false', // String类型
      autoFetchAsset: 'false', // String类型
      assetConditionSearchName: 'condition4', // dynamic类型
      autoFetchSearchId: 503, // int类型
      createdById: 'user-4', // String类型
      createdByName: 'Test User 4', // String类型
      createdDate: '2023-01-04', // String类型
    );

    final mockWorkflow1 = WorkflowList(
      processDefinitionId: 'process-1', // String类型
      workflowId: 1, // int类型
      workflowName: 'Workflow B', // String类型
      version: 1, // int类型
      description: 'Description for workflow B', // String类型
      comment: 'Comment for workflow B', // String?类型
      assetActionList: [], // dynamic类型
      taskDefinitions: {}, // dynamic类型
      sequenceFlows: [], // dynamic类型
      assetTypeIdWithFirstWf: 100, // int类型
      createdDate: '2023-01-01', // String类型
      assetTypeName: 'Type A', // String?类型
      workflowType: 'Type 1', // String类型
      workflowTypeCode: 'T1', // String类型
      autoFetchAsset: 'false', // String类型
      autoSetItemDefaultValue: 'false', // String类型
      timerStartUserId: 'user-1', // String类型
    );

    final mockWorkflow2 = WorkflowList(
      processDefinitionId: 'process-2', // String类型
      workflowId: 2, // int类型
      workflowName: 'Workflow A', // String类型
      version: 1, // int类型
      description: 'Description for workflow A', // String类型
      comment: 'Comment for workflow A', // String?类型
      assetActionList: [], // dynamic类型
      taskDefinitions: {}, // dynamic类型
      sequenceFlows: [], // dynamic类型
      assetTypeIdWithFirstWf: 101, // int类型
      createdDate: '2023-01-02', // String类型
      assetTypeName: 'Type B', // String?类型
      workflowType: 'Type 2', // String类型
      workflowTypeCode: 'T2', // String类型
      autoFetchAsset: 'false', // String类型
      autoSetItemDefaultValue: 'false', // String类型
      timerStartUserId: 'user-2', // String类型
    );

    final mockHomeListModel = HomeListModel(
      assetActionList: [mockAssetAction3, mockAssetAction1, mockAssetAction4, mockAssetAction2],
      workflowList: [mockWorkflow1, mockWorkflow2],
    );

    // 使用实际的 APIHomePageListByMobileHomeSettingRoot 类，并添加必需的字段
    final mockAPIHomePageList = APIHomePageListByMobileHomeSettingRoot(
      msg: 'success', // 添加msg字段
      code: 200, // 添加code字段
      mobileHomeList: mockHomeListModel,
    );

    test('should return sorted merged list when repository call is successful', () async {
      // arrange
      when(mockHomeRepository.getHomePageListByMobileHomeSetting()).thenAnswer((_) async => mockAPIHomePageList);

      // act
      final result = await useCase(null);

      // assert
      expect(result.length, equals(6)); // 4 actions + 2 workflows

      // Check if asset actions are sorted first by actionLabel then by assetActionName
      expect(result[0], equals(mockAssetAction1)); // 'A', 'Action 1'
      expect(result[1], equals(mockAssetAction2)); // 'A', 'Action 2'
      expect(result[2], equals(mockAssetAction3)); // 'B', 'Action 3'
      expect(result[3], equals(mockAssetAction4)); // null should be last

      // Check if workflows are sorted by workflowName
      expect(result[4], equals(mockWorkflow2)); // 'Workflow A'
      expect(result[5], equals(mockWorkflow1)); // 'Workflow B'

      verify(mockHomeRepository.getHomePageListByMobileHomeSetting());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should correctly handle empty lists', () async {
      // arrange
      final emptyHomeListModel = HomeListModel(assetActionList: [], workflowList: []);

      // 使用实际的 APIHomePageListByMobileHomeSettingRoot 类，并添加必需的字段
      final emptyAPIHomePageList = APIHomePageListByMobileHomeSettingRoot(
        msg: 'success', // 添加msg字段
        code: 200, // 添加code字段
        mobileHomeList: emptyHomeListModel,
      );

      when(mockHomeRepository.getHomePageListByMobileHomeSetting()).thenAnswer((_) async => emptyAPIHomePageList);

      // act
      final result = await useCase(null);

      // assert
      expect(result, isEmpty);
      verify(mockHomeRepository.getHomePageListByMobileHomeSetting());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should sort asset actions with null values correctly', () async {
      // arrange
      final actionWithNulls = HomeListModel(
        assetActionList: [
          SharedAssetAction(
            assetActionId: 1, // int类型
            assetActionName: null, // String?类型，可为null
            actionLabel: 'A', // String类型
            tenantId: 'tenant-001', // String类型
            assetTypeId: 100, // int类型
            assetTypeGroupIds: '1,2,3', // String类型
            assetTypeName: 'Type A', // String类型
            quantityFlg: 'true', // String类型
            assetActionItem: 'item1', // String类型
            scanCondition: '{}', // String类型
            userIds: '10,20,30', // String类型
            groupIds: 'group1,group2', // String类型
            isUpdateAmount: 'false', // String类型
            autoFetchAsset: 'false', // String类型
            assetConditionSearchName: 'condition1', // dynamic类型
            autoFetchSearchId: 500, // int类型
            createdById: 'user-1', // String类型
            createdByName: 'Test User', // String类型
            createdDate: '2023-01-01', // String类型
          ),
          SharedAssetAction(
            assetActionId: 2, // int类型
            assetActionName: 'Action', // String类型
            actionLabel: null, // String?类型，可为null
            tenantId: 'tenant-001', // String类型
            assetTypeId: 101, // int类型
            assetTypeGroupIds: '4,5,6', // String类型
            assetTypeName: 'Type B', // String类型
            quantityFlg: 'true', // String类型
            assetActionItem: 'item2', // String类型
            scanCondition: '{}', // String类型
            userIds: '40,50,60', // String类型
            groupIds: 'group3,group4', // String类型
            isUpdateAmount: 'false', // String类型
            autoFetchAsset: 'false', // String类型
            assetConditionSearchName: 'condition2', // dynamic类型
            autoFetchSearchId: 501, // int类型
            createdById: 'user-2', // String类型
            createdByName: 'Test User 2', // String类型
            createdDate: '2023-01-02', // String类型
          ),
        ],
        workflowList: [],
      );

      // 使用实际的 APIHomePageListByMobileHomeSettingRoot 类，并添加必需的字段
      final nullActionsAPIHomePageList = APIHomePageListByMobileHomeSettingRoot(
        msg: 'success', // 添加msg字段
        code: 200, // 添加code字段
        mobileHomeList: actionWithNulls,
      );

      when(mockHomeRepository.getHomePageListByMobileHomeSetting()).thenAnswer((_) async => nullActionsAPIHomePageList);

      // act
      final result = await useCase(null);

      // assert
      expect(result.length, equals(2));
      expect(result[0].assetActionId, equals(1)); // actionLabel 'A' comes before null
      expect(result[1].assetActionId, equals(2)); // null actionLabel comes last

      verify(mockHomeRepository.getHomePageListByMobileHomeSetting());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should sort workflows with null values correctly', () async {
      // arrange
      final workflowWithNulls = HomeListModel(
        assetActionList: [],
        workflowList: [
          WorkflowList(
            processDefinitionId: 'process-1', // String类型
            workflowId: 1, // int类型
            workflowName: null, // String?类型，可为null
            version: 1, // int类型
            description: 'Description for workflow 1', // String类型
            comment: 'Comment for workflow 1', // String?类型
            assetActionList: [], // dynamic类型
            taskDefinitions: {}, // dynamic类型
            sequenceFlows: [], // dynamic类型
            assetTypeIdWithFirstWf: 100, // int类型
            createdDate: '2023-01-01', // String类型
            assetTypeName: 'Type A', // String?类型
            workflowType: 'Type 1', // String类型
            workflowTypeCode: 'T1', // String类型
            autoFetchAsset: 'false', // String类型
            autoSetItemDefaultValue: 'false', // String类型
            timerStartUserId: 'user-1', // String类型
          ),
          WorkflowList(
            processDefinitionId: 'process-2', // String类型
            workflowId: 2, // int类型
            workflowName: 'Test Workflow', // String类型
            version: 1, // int类型
            description: 'Description for test workflow', // String类型
            comment: 'Comment for test workflow', // String?类型
            assetActionList: [], // dynamic类型
            taskDefinitions: {}, // dynamic类型
            sequenceFlows: [], // dynamic类型
            assetTypeIdWithFirstWf: 101, // int类型
            createdDate: '2023-01-02', // String类型
            assetTypeName: 'Type B', // String?类型
            workflowType: 'Type 2', // String类型
            workflowTypeCode: 'T2', // String类型
            autoFetchAsset: 'false', // String类型
            autoSetItemDefaultValue: 'false', // String类型
            timerStartUserId: 'user-2', // String类型
          ),
        ],
      );

      // 使用实际的 APIHomePageListByMobileHomeSettingRoot 类，并添加必需的字段
      final nullWorkflowsAPIHomePageList = APIHomePageListByMobileHomeSettingRoot(
        msg: 'success', // 添加msg字段
        code: 200, // 添加code字段
        mobileHomeList: workflowWithNulls,
      );

      when(
        mockHomeRepository.getHomePageListByMobileHomeSetting(),
      ).thenAnswer((_) async => nullWorkflowsAPIHomePageList);

      // act
      final result = await useCase(null);

      // assert
      expect(result.length, equals(2));
      expect(result[0].workflowId, equals(1)); // null comes before any string
      expect(result[1].workflowId, equals(2));

      verify(mockHomeRepository.getHomePageListByMobileHomeSetting());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should rethrow SystemException when repository throws it', () async {
      // arrange
      final tException = SystemException();
      when(mockHomeRepository.getHomePageListByMobileHomeSetting()).thenThrow(tException);

      // act & assert
      expect(
        () => useCase(null),
        throwsA(same(tException)), // We expect the exact same exception object to be rethrown
      );
      verify(mockHomeRepository.getHomePageListByMobileHomeSetting());
      verifyNoMoreInteractions(mockHomeRepository);
    });
  });
}
