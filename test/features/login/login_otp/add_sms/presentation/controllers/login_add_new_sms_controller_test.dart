import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/ipn_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/controllers/login_add_new_sms_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 Mock 类
@GenerateNiceMocks([MockSpec<NavigationService>(), MockSpec<DialogService>()])
import 'login_add_new_sms_controller_test.mocks.dart';

// 测试专用的 LoginAddNewSmsController，避免影响原逻辑
class TestLoginAddNewSmsController extends LoginAddNewSmsController {
  TestLoginAddNewSmsController({required super.navigationService, required super.dialogService, this.testArguments});

  final dynamic testArguments;
  final IpnService _testIpnService = IpnService(); // 测试专用的 IpnService 实例
  bool exceptionHandled = false;
  bool showLoadingCalled = false;
  bool hideLoadingCalled = false;
  bool hideKeyboardCalled = false;

  @override
  Future<void> onReady() async {
    // 不调用 super.onReady() 而是手动调用初始化逻辑
    await _initializeDataWithTestArguments();
  }

  // 使用测试参数而不是 Get.arguments
  Future<void> _initializeDataWithTestArguments() async {
    final param = testArguments;
    await showLoading();

    if (param is RePhoneAndIpnCodeModel) {
      final String callingCode = _testIpnService.loadDefaultJPData.callingCode;
      final String phoneNum = param.phoneNum;
      state.nationCode.value = callingCode;
      if (callingCode.isEmpty || phoneNum.isEmpty) {
        state.inputPhoneNumber.value = '';
      } else {
        state.inputPhoneNumber.value = phoneNum;
      }
      textEditingController.text = state.inputPhoneNumber.value;
    }

    hideLoading();
  }

  @override
  Future<void> showLoading() async {
    showLoadingCalled = true;
    // 在测试中跳过Loading UI显示
  }

  @override
  void hideLoading() {
    hideLoadingCalled = true;
    // 在测试中跳过Loading UI隐藏
  }

  @override
  Future<void> handleException(dynamic exception, [StackTrace? stackTrace, ErrorHandlingMode? errorHandlingMode]) {
    exceptionHandled = true;
    return Future<void>.value();
  }

  @override
  void hideKeyboard() {
    hideKeyboardCalled = true;
    // 在测试中跳过键盘隐藏
  }

  @override
  getIpnImage() => _testIpnService.getImage();

  // 不重写业务逻辑方法，让它们使用真实的 navigationService
  // 但记录一些关键调用用于测试验证
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockNavigationService mockNavigationService;
  late MockDialogService mockDialogService;

  setUp(() {
    mockNavigationService = MockNavigationService();
    mockDialogService = MockDialogService();

    // 初始化 LogUtil 以避免测试中的 LateInitializationError
    LogUtil.initialize();

    Get.testMode = true;
  });

  tearDown(() {
    Get.reset();
  });

  /// ----------------------------
  /// LoginAddNewSmsController Tests
  /// ----------------------------

  /// 基本结构测试
  group('基本结构测试', () {
    test('应该能正常创建控制器', () {
      // Arrange & Act
      final controller = LoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
      );

      // Assert
      expect(controller, isNotNull);
      expect(controller.navigationService, mockNavigationService);
      expect(controller.dialogService, mockDialogService);
    });

    test('应该能正常创建测试专用控制器', () {
      // Arrange & Act
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Assert
      expect(controller, isNotNull);
      expect(controller.navigationService, mockNavigationService);
      expect(controller.dialogService, mockDialogService);
      expect(controller.exceptionHandled, false);
      expect(controller.showLoadingCalled, false);
      expect(controller.hideLoadingCalled, false);
    });
  });

  /// 初始状态测试
  group('初始状态测试', () {
    late TestLoginAddNewSmsController controller;

    setUp(() {
      controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );
    });

    test('状态字段应为初始默认值', () {
      // Assert - 验证状态字段的初始值
      expect(controller.state.nationCode.value, '');
      expect(controller.state.inputPhoneNumber.value, '');
    });

    test('TextEditingController 应为初始状态', () {
      // Assert - 验证 TextEditingController 的初始状态
      expect(controller.textEditingController.text, '');
      expect(controller.textEditingController.selection.isValid, false);
    });

    test('测试辅助字段应为初始状态', () {
      // Assert - 验证测试辅助字段的初始状态
      expect(controller.exceptionHandled, false);
      expect(controller.showLoadingCalled, false);
      expect(controller.hideLoadingCalled, false);
      expect(controller.hideKeyboardCalled, false);
    });

    test('IpnService 应能正常工作', () {
      // Act & Assert - 验证 IpnService 的基本功能
      expect(() => controller.getIpnImage(), returnsNormally);

      // 验证 getIpnImage 返回值不为空
      final image = controller.getIpnImage();
      expect(image, isNotNull);
    });

    test('依赖服务应正确注入', () {
      // Assert - 验证依赖注入
      expect(controller.navigationService, equals(mockNavigationService));
      expect(controller.dialogService, equals(mockDialogService));
    });
  });

  /// 数据初始化测试 (onReady)
  group('数据初始化测试 (onReady)', () {
    // 测试数据
    final testValidPhoneModel = RePhoneAndIpnCodeModel(phoneNum: '09012345678', nationCode: '+81');

    final testEmptyPhoneModel = RePhoneAndIpnCodeModel(phoneNum: '', nationCode: '+81');

    test('正常情况下应成功初始化数据', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testValidPhoneModel,
      );

      // Act
      await controller.onReady();

      // Assert
      expect(controller.showLoadingCalled, true);
      expect(controller.hideLoadingCalled, true);

      // 验证国家代码设置为日本默认值
      expect(controller.state.nationCode.value, isNotEmpty);

      // 验证电话号码正确设置
      expect(controller.state.inputPhoneNumber.value, '09012345678');
      expect(controller.textEditingController.text, '09012345678');
    });

    test('当电话号码为空时应设置为空字符串', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testEmptyPhoneModel,
      );

      // Act
      await controller.onReady();

      // Assert
      expect(controller.showLoadingCalled, true);
      expect(controller.hideLoadingCalled, true);

      // 验证国家代码设置
      expect(controller.state.nationCode.value, isNotEmpty);

      // 验证空电话号码处理
      expect(controller.state.inputPhoneNumber.value, '');
      expect(controller.textEditingController.text, '');
    });

    test('当 arguments 为 null 时应正常处理', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act
      await controller.onReady();

      // Assert
      expect(controller.showLoadingCalled, true);
      expect(controller.hideLoadingCalled, true);

      // 验证状态保持初始值
      expect(controller.state.nationCode.value, '');
      expect(controller.state.inputPhoneNumber.value, '');
      expect(controller.textEditingController.text, '');
    });

    test('当 arguments 不是 RePhoneAndIpnCodeModel 类型时应正常处理', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: 'invalid-type',
      );

      // Act
      await controller.onReady();

      // Assert
      expect(controller.showLoadingCalled, true);
      expect(controller.hideLoadingCalled, true);

      // 验证状态保持初始值
      expect(controller.state.nationCode.value, '');
      expect(controller.state.inputPhoneNumber.value, '');
      expect(controller.textEditingController.text, '');
    });

    test('应正确调用 Loading 方法序列', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testValidPhoneModel,
      );

      // Act
      await controller.onReady();

      // Assert - 验证 Loading 方法调用顺序
      expect(controller.showLoadingCalled, true);
      expect(controller.hideLoadingCalled, true);
    });

    test('应正确设置日本国家代码', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testValidPhoneModel,
      );

      // Act
      await controller.onReady();

      // Assert - 验证日本国家代码
      final nationCode = controller.state.nationCode.value;
      expect(nationCode, isNotEmpty);
      // 日本的国际区号应该是 +81
      expect(nationCode, contains('81'));
    });
  });

  /// 用户交互功能测试
  group('用户交互功能测试', () {
    late TestLoginAddNewSmsController controller;

    setUp(() {
      controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );
    });

    group('电话号码输入测试 (onChangePhoneInput)', () {
      test('应正确更新输入内容到状态', () {
        // Arrange
        const testInput = '09012345678';

        // Act
        controller.onChangePhoneInput(testInput);

        // Assert
        expect(controller.state.inputPhoneNumber.value, testInput);
        expect(controller.textEditingController.text, testInput);
      });

      test('应正确设置光标位置到文本末尾', () {
        // Arrange
        const testInput = '09012345678';

        // Act
        controller.onChangePhoneInput(testInput);

        // Assert
        expect(controller.textEditingController.selection.baseOffset, testInput.length);
        expect(controller.textEditingController.selection.extentOffset, testInput.length);
        expect(controller.textEditingController.selection.isCollapsed, true);
      });

      test('应能处理空字符串输入', () {
        // Arrange
        const testInput = '';

        // Act
        controller.onChangePhoneInput(testInput);

        // Assert
        expect(controller.state.inputPhoneNumber.value, '');
        expect(controller.textEditingController.text, '');
        expect(controller.textEditingController.selection.baseOffset, 0);
      });

      test('应能处理连续输入更新', () {
        // Arrange & Act - 第一次输入
        controller.onChangePhoneInput('090');
        expect(controller.state.inputPhoneNumber.value, '090');
        expect(controller.textEditingController.text, '090');

        // Act - 第二次输入
        controller.onChangePhoneInput('09012');
        expect(controller.state.inputPhoneNumber.value, '09012');
        expect(controller.textEditingController.text, '09012');

        // Act - 第三次输入
        controller.onChangePhoneInput('09012345678');
        expect(controller.state.inputPhoneNumber.value, '09012345678');
        expect(controller.textEditingController.text, '09012345678');
      });

      test('应能处理特殊字符输入', () {
        // Arrange
        const testInputs = ['090-1234-5678', '090 1234 5678', '090.1234.5678'];

        for (final testInput in testInputs) {
          // Act
          controller.onChangePhoneInput(testInput);

          // Assert
          expect(controller.state.inputPhoneNumber.value, testInput);
          expect(controller.textEditingController.text, testInput);
        }
      });
    });

    group('清空输入测试 (clearInput)', () {
      test('应清空所有输入内容', () {
        // Arrange - 先设置一些内容
        controller.onChangePhoneInput('09012345678');
        expect(controller.state.inputPhoneNumber.value, '09012345678');
        expect(controller.textEditingController.text, '09012345678');

        // Act
        controller.clearInput();

        // Assert
        expect(controller.state.inputPhoneNumber.value, '');
        expect(controller.textEditingController.text, '');
      });

      test('对空内容调用应不产生错误', () {
        // Arrange - 确保内容为空
        expect(controller.state.inputPhoneNumber.value, '');
        expect(controller.textEditingController.text, '');

        // Act & Assert - 应不抛出异常
        expect(() => controller.clearInput(), returnsNormally);
        expect(controller.state.inputPhoneNumber.value, '');
        expect(controller.textEditingController.text, '');
      });

      test('清空后再输入应正常工作', () {
        // Arrange - 设置内容然后清空
        controller.onChangePhoneInput('09012345678');
        controller.clearInput();

        // Act - 重新输入
        controller.onChangePhoneInput('08012345678');

        // Assert
        expect(controller.state.inputPhoneNumber.value, '08012345678');
        expect(controller.textEditingController.text, '08012345678');
      });
    });

    group('键盘隐藏测试 (hideKeyboard)', () {
      test('应能正常调用不抛出异常', () {
        // Act & Assert
        expect(() => controller.hideKeyboard(), returnsNormally);
        expect(controller.hideKeyboardCalled, true);
      });

      test('多次调用应正常工作', () {
        // Act
        controller.hideKeyboard();
        controller.hideKeyboard();
        controller.hideKeyboard();

        // Assert
        expect(controller.hideKeyboardCalled, true);
      });
    });

    group('工具方法测试 (getIpnImage)', () {
      test('应返回非空结果', () {
        // Act
        final result = controller.getIpnImage();

        // Assert
        expect(result, isNotNull);
      });

      test('多次调用应返回一致结果', () {
        // Act
        final result1 = controller.getIpnImage();
        final result2 = controller.getIpnImage();

        // Assert
        expect(result1, equals(result2));
      });
    });
  });

  /// 业务逻辑测试
  group('业务逻辑测试', () {
    late TestLoginAddNewSmsController controller;

    setUp(() {
      controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );
    });

    group('确定按钮测试 (sureBtnOnClick)', () {
      test('有效电话号码应成功验证并返回结果', () async {
        // Arrange - 设置有效的日本手机号码
        const validPhoneNumber = '09012345678';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = validPhoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert
        expect(controller.exceptionHandled, false);

        // 验证 navigationService.goBack 被调用并传递正确参数
        final captured = verify(mockNavigationService.goBack(result: captureAnyNamed('result'))).captured;
        expect(captured.length, 1);
        expect(captured.first, isA<RePhoneAndIpnCodeModel>());

        final result = captured.first as RePhoneAndIpnCodeModel;
        expect(result.phoneNum, validPhoneNumber);
        expect(result.nationCode, nationCode);

        // 验证 dialogService 没有被调用
        verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
      });

      test('另一种有效电话号码格式应成功验证', () async {
        // Arrange - 测试080开头的手机号码
        const validPhoneNumber = '08012345678';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = validPhoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert
        expect(controller.exceptionHandled, false);

        // 验证 navigationService.goBack 被调用
        verify(mockNavigationService.goBack(result: anyNamed('result'))).called(1);

        // 验证 dialogService 没有被调用
        verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
      });

      test('无效电话号码应显示错误对话框', () async {
        // Arrange - 设置无效电话号码（太短）
        const invalidPhoneNumber = '123';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = invalidPhoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert
        expect(controller.exceptionHandled, false);

        // 验证 dialogService.show 被调用
        verify(
          mockDialogService.show(
            content: argThat(isNotNull, named: 'content'),
            type: DialogType.error,
          ),
        ).called(1);

        // 验证 navigationService.goBack 没有被调用
        verifyNever(mockNavigationService.goBack(result: anyNamed('result')));
      });

      test('包含非法字符的电话号码应显示错误对话框', () async {
        // Arrange - 设置包含字母的无效电话号码
        const invalidPhoneNumber = '090abc5678';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = invalidPhoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert
        verify(mockDialogService.show(content: anyNamed('content'), type: DialogType.error)).called(1);
        verifyNever(mockNavigationService.goBack(result: anyNamed('result')));
      });

      test('过长的电话号码应显示错误对话框', () async {
        // Arrange - 设置过长的电话号码
        const invalidPhoneNumber = '090123456789012345';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = invalidPhoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert
        verify(mockDialogService.show(content: anyNamed('content'), type: DialogType.error)).called(1);
        verifyNever(mockNavigationService.goBack(result: anyNamed('result')));
      });

      test('空电话号码应显示错误对话框', () async {
        // Arrange - 设置空电话号码
        const invalidPhoneNumber = '';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = invalidPhoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert
        verify(mockDialogService.show(content: anyNamed('content'), type: DialogType.error)).called(1);
        verifyNever(mockNavigationService.goBack(result: anyNamed('result')));
      });

      test('应正确传递参数给 IpnService 验证方法', () async {
        // Arrange
        const phoneNumber = '09012345678';
        const nationCode = '+81';

        controller.state.inputPhoneNumber.value = phoneNumber;
        controller.state.nationCode.value = nationCode;

        // Act
        await controller.sureBtnOnClick();

        // Assert - 验证成功调用 navigationService
        verify(mockNavigationService.goBack(result: anyNamed('result'))).called(1);
        verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
      });
    });

    group('返回按钮测试 (goBackPage)', () {
      test('应调用 navigationService.goBack', () {
        // Act
        controller.goBackPage();

        // Assert
        verify(mockNavigationService.goBack()).called(1);
      });

      test('多次调用应正常工作', () {
        // Act
        controller.goBackPage();
        controller.goBackPage();

        // Assert
        verify(mockNavigationService.goBack()).called(2);
      });

      test('不应影响其他状态', () {
        // Arrange - 确保其他状态为初始值
        expect(controller.exceptionHandled, false);

        // Act
        controller.goBackPage();

        // Assert - 其他状态应保持不变
        expect(controller.exceptionHandled, false);

        // 验证只调用了导航方法
        verify(mockNavigationService.goBack()).called(1);
        verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
      });
    });
  });

  /// 生命周期测试
  group('生命周期测试', () {
    late TestLoginAddNewSmsController controller;

    setUp(() {
      controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );
    });

    test('onClose 应正确释放资源', () {
      // Arrange - 设置一些数据确保控制器有状态
      controller.onChangePhoneInput('09012345678');
      expect(controller.textEditingController.text, '09012345678');

      // Act & Assert - 验证onClose被正常调用不抛出异常
      expect(() => controller.onClose(), returnsNormally);
    });

    test('onClose 应能正常调用', () {
      // Arrange - 创建一个新的控制器实例用于这个测试
      final testController = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act & Assert - 验证onClose不抛出异常
      expect(() => testController.onClose(), returnsNormally);
    });
  });

  /// 异常处理测试
  group('异常处理测试', () {
    late TestLoginAddNewSmsController controller;

    setUp(() {
      controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );
    });

    test('handleException 应正确处理 SystemException', () async {
      // Act
      await controller.handleException(SystemException());

      // Assert
      expect(controller.exceptionHandled, true);
    });

    test('handleException 应正确处理其他类型异常', () async {
      // Act
      await controller.handleException(Exception('Test exception'));

      // Assert
      expect(controller.exceptionHandled, true);
    });

    test('handleException 应正确处理 null 异常', () async {
      // Act & Assert
      expect(() async => await controller.handleException(null), returnsNormally);
      expect(controller.exceptionHandled, true);
    });
  });

  /// 集成测试场景
  group('集成测试场景', () {
    test('完整的成功流程：初始化 -> 输入 -> 验证 -> 返回结果', () async {
      // Arrange - 创建带有初始数据的控制器
      final testModel = RePhoneAndIpnCodeModel(phoneNum: '09012345678', nationCode: '+81');

      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testModel,
      );

      // Act - 执行完整流程
      // 1. 初始化
      await controller.onReady();
      expect(controller.state.inputPhoneNumber.value, '09012345678');
      expect(controller.showLoadingCalled, true);
      expect(controller.hideLoadingCalled, true);

      // 2. 用户修改输入
      controller.onChangePhoneInput('08012345678');
      expect(controller.state.inputPhoneNumber.value, '08012345678');

      // 3. 确定提交
      await controller.sureBtnOnClick();

      // Assert - 验证完整流程结果
      expect(controller.exceptionHandled, false);

      // 验证成功调用导航服务
      final captured = verify(mockNavigationService.goBack(result: captureAnyNamed('result'))).captured;
      expect(captured.length, 1);
      expect(captured.first, isA<RePhoneAndIpnCodeModel>());

      final result = captured.first as RePhoneAndIpnCodeModel;
      expect(result.phoneNum, '08012345678');
      expect(result.nationCode, isNotEmpty);

      // 验证没有显示错误对话框
      verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
    });

    test('完整的错误流程：初始化 -> 输入无效号码 -> 显示错误', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act - 执行错误流程
      // 1. 初始化（无参数）
      await controller.onReady();
      expect(controller.state.inputPhoneNumber.value, '');

      // 2. 设置国家代码
      controller.state.nationCode.value = '+81';

      // 3. 输入无效电话号码
      controller.onChangePhoneInput('abc123');
      expect(controller.state.inputPhoneNumber.value, 'abc123');

      // 4. 尝试提交
      await controller.sureBtnOnClick();

      // Assert - 验证错误处理
      // 验证显示了错误对话框
      verify(
        mockDialogService.show(
          content: argThat(isNotNull, named: 'content'),
          type: DialogType.error,
        ),
      ).called(1);

      // 验证没有调用导航返回
      verifyNever(mockNavigationService.goBack(result: anyNamed('result')));
    });

    test('用户操作序列：输入 -> 清空 -> 重新输入 -> 提交', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );
      controller.state.nationCode.value = '+81';

      // Act - 用户操作序列
      // 1. 首次输入
      controller.onChangePhoneInput('09012345678');
      expect(controller.state.inputPhoneNumber.value, '09012345678');

      // 2. 清空输入
      controller.clearInput();
      expect(controller.state.inputPhoneNumber.value, '');
      expect(controller.textEditingController.text, '');

      // 3. 重新输入
      controller.onChangePhoneInput('08012345678');
      expect(controller.state.inputPhoneNumber.value, '08012345678');

      // 4. 隐藏键盘
      controller.hideKeyboard();
      expect(controller.hideKeyboardCalled, true);

      // 5. 提交
      await controller.sureBtnOnClick();

      // Assert
      // 验证成功调用导航服务
      verify(mockNavigationService.goBack(result: anyNamed('result'))).called(1);

      // 验证没有显示错误对话框
      verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
    });

    test('边界情况综合测试：空值和极端输入', () async {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act & Assert - 测试各种边界情况
      // 1. 空国家代码 + 空电话号码
      controller.state.nationCode.value = '';
      controller.state.inputPhoneNumber.value = '';
      await controller.sureBtnOnClick();

      // 验证显示了错误对话框
      verify(mockDialogService.show(content: anyNamed('content'), type: DialogType.error)).called(1);

      // 重置mock状态
      reset(mockDialogService);

      // 2. 有效国家代码 + 空电话号码
      controller.state.nationCode.value = '+81';
      controller.state.inputPhoneNumber.value = '';
      await controller.sureBtnOnClick();

      // 验证显示了错误对话框
      verify(mockDialogService.show(content: anyNamed('content'), type: DialogType.error)).called(1);

      // 重置mock状态
      reset(mockDialogService);

      // 3. 单字符输入
      controller.onChangePhoneInput('1');
      await controller.sureBtnOnClick();

      // 验证显示了错误对话框
      verify(mockDialogService.show(content: anyNamed('content'), type: DialogType.error)).called(1);

      // 4. 超长输入
      controller.onChangePhoneInput('1' * 50);
      expect(controller.state.inputPhoneNumber.value, '1' * 50);
    });

    test('取消操作流程：输入 -> 取消返回', () {
      // Arrange
      final controller = TestLoginAddNewSmsController(
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act - 取消流程
      // 1. 输入一些数据
      controller.onChangePhoneInput('09012345678');
      expect(controller.state.inputPhoneNumber.value, '09012345678');

      // 2. 用户点击返回按钮
      controller.goBackPage();

      // Assert - 验证取消行为
      verify(mockNavigationService.goBack()).called(1);

      // 确认没有触发业务逻辑验证（没有调用对话框）
      verifyNever(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type')));
    });
  });
}
