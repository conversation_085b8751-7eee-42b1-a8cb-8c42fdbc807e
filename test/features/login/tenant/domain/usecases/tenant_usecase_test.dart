import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../login_otp/opt_chick/domain/usecases/opt_usecase_test.mocks.dart';

@GenerateMocks([TenantRepository, UserRepository, IStorageUtils])
void main() {
  group('TenantUseCase', () {
    late TenantUseCase useCase;
    late MockTenantRepository mockTenantRepository;
    late MockUserRepository mockUserRepository;
    late MockIStorageUtils mockStorageUtils;

    setUp(() {
      LogUtil.initialize();
      mockTenantRepository = MockTenantRepository();
      mockUserRepository = MockUserRepository();
      mockStorageUtils = MockIStorageUtils();

      useCase = TenantUseCase(
        tenantRepository: mockTenantRepository,
        userRepository: mockUserRepository,
        storageUtils: mockStorageUtils,
      );

      // Mock设置：直接使用mockStorageUtils

      // 设置环境
      EnvHelper.environment = 'dev';
    });

    tearDown(() {
      // 测试后清理
    });

    group('getBasicInfo', () {
      test('should throw SystemException when ticket is empty', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: '', tenantId: 'test-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
      });

      test('should throw SystemException when tenantId is empty', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'valid-ticket', tenantId: '', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
      });

      test('should throw SystemException when dealTenant is null', () async {
        // Arrange
        final params = SelectTenantModel(zoneId: 'test-zone', dealTenant: null, dealDeeplink: null, optTenant: null);

        // Act & Assert
        expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
      });

      test('should successfully call loginTenant with valid parameters', () async {
        // Arrange
        const ticket = 'valid-ticket';
        const tenantId = 'test-tenant';
        const isBiometrics = false;
        const code = '123456';

        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: code),
          dealDeeplink: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');

        when(
          mockTenantRepository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: code),
        ).thenAnswer((_) async => mockResult);

        // Act
        final result = await useCase.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));
        verify(
          mockTenantRepository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: code),
        ).called(1);
      });

      test('should successfully call loginTenant with null code', () async {
        // Arrange
        const ticket = 'valid-ticket';
        const tenantId = 'test-tenant';
        const isBiometrics = true;

        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: ''),
          dealDeeplink: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');

        when(
          mockTenantRepository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: ''),
        ).thenAnswer((_) async => mockResult);

        // Act
        final result = await useCase.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));
        verify(
          mockTenantRepository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: ''),
        ).called(1);
      });

      test('should successfully call loginTenant with biometrics enabled', () async {
        // Arrange
        const ticket = 'valid-ticket';
        const tenantId = 'test-tenant';
        const isBiometrics = true;
        const code = '654321';

        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: code),
          dealDeeplink: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');

        when(
          mockTenantRepository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: code),
        ).thenAnswer((_) async => mockResult);

        // Act
        final result = await useCase.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));
        verify(
          mockTenantRepository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics, code: code),
        ).called(1);
      });
    });
  });
}
