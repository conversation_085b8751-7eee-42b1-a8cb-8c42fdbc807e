import 'package:asset_force_mobile_v2/core/deeplink/deeplink_service.dart';
import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/sso_scan_plugin.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';

// 自动生成 mock 类

@GenerateMocks([SsoScanPlugin, DeeplinkService, CommonDialog, IStorageUtils])
import 'login_sso_usecase_test.mocks.dart';

void main() {
  group('LoginSsoUseCase', () {
    late LoginSsoUseCase loginSsoUseCase;
    late MockSsoScanPlugin mockSsoScanPlugin;
    late MockDeeplinkService mockDeeplinkService;
    late MockCommonDialog mockCommonDialog;
    late MockIStorageUtils mockStorageUtils;

    setUp(() {
      // 初始化LogUtil
      LogUtil.initialize();

      mockSsoScanPlugin = MockSsoScanPlugin();
      mockDeeplinkService = MockDeeplinkService();
      mockCommonDialog = MockCommonDialog();
      mockStorageUtils = MockIStorageUtils();

      // Mock设置：直接使用mockStorageUtils

      // 设置环境变量
      EnvHelper.environment = 'dev';

      loginSsoUseCase = LoginSsoUseCase(storageUtils: mockStorageUtils);
    });

    tearDown(() {
      // 测试后清理
    });

    group('基础功能测试', () {
      test('应该能够创建 LoginSsoUseCase 实例', () {
        expect(loginSsoUseCase, isNotNull);
        expect(loginSsoUseCase, isA<LoginSsoUseCase>());
      });

      test('应该实现 UseCase 接口', () {
        expect(loginSsoUseCase, isA<UseCase>());
      });
    });
  });
}
