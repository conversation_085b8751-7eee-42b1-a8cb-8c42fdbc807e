import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/controllers/asset_list_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/pages/asset_list_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/states/asset_list_ui_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/widgets/asset_item_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/scroll_to_top_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateNiceMocks([MockSpec<AssetListController>()])
import 'asset_list_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockAssetListController mockController;
  late AssetListUIState mockState;

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: AssetListPage());
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    mockController = MockAssetListController();
    mockState = AssetListUIState();

    // Stub getters
    when(mockController.state).thenReturn(mockState);

    // Stub 方法
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});
    when(mockController.refreshData()).thenAnswer((_) async {});
    when(mockController.loadMoreData()).thenAnswer((_) async {});
    when(mockController.scrollToTop(any)).thenReturn(null);
    when(mockController.onClickSearchConditionEnter()).thenAnswer((_) async {});
    when(mockController.onClickClearSearch()).thenAnswer((_) async {});
    when(mockController.onClickAssetTypeSelect()).thenAnswer((_) async {});
    when(mockController.onClickStartToCategory()).thenAnswer((_) async {});
    when(mockController.onClickClearCategory()).thenAnswer((_) async {});
    when(mockController.toDetailPage(any)).thenAnswer((_) async {});
    when(mockController.toggleDisplayMode()).thenAnswer((_) async {});
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    Get.put<AssetListController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
    clearInteractions(mockController);
  });

  // ================================
  // UI ELEMENT TESTS
  // ================================
  group('UI ELEMENT TESTS', () {
    testWidgets('Displays No data label when noData is true and data is empty', (tester) async {
      // Arrange
      mockController.state.noData.value = true;
      mockController.state.data.clear();

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('データがありません'), findsOneWidget);
    });

    testWidgets('Hides No data label when data is available', (tester) async {
      // Arrange
      mockController.state.noData.value = false;
      mockController.state.data.assignAll([AssetUIModel(imageUrl: 'http://xx.jpg', assetDisplayList: [], assetId: 0)]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('データがありません'), findsNothing);
    });
  });

  // ================================
  // INTERACTION TESTS
  // ================================
  group('INTERACTION TESTS', () {
    testWidgets('Pull-to-refresh should call refreshData()', (tester) async {
      // Arrange
      mockController.state.data.assignAll([AssetUIModel(imageUrl: 'demo', assetDisplayList: [], assetId: 0)]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Pull down to refresh
      final listFinder = find.byType(CustomScrollView);
      await tester.drag(listFinder, const Offset(0, 300));

      // 允许滚动和监听器处理
      await tester.pump(); // 开始拖动
      await tester.pump(const Duration(milliseconds: 100)); // 让滚动稳定
      await tester.pumpAndSettle(); // 处理剩余帧

      // Assert
      verify(mockController.refreshData()).called(1);
    });

    testWidgets('Tapping grid/list button toggles displayMode and verify colors', (tester) async {
      // Arrange
      mockController.state.displayMode.value = DisplayMode.list;
      mockController.state.data.assignAll(
        List.generate(50, (i) => AssetUIModel(imageUrl: 'http://xx_$i.jpg', assetDisplayList: [], assetId: i)),
      );
      mockController.state.searchCount.value = 50;

      // Act & Assert - Initial state (list mode)
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 获取按钮容器
      final listButtonContainer = tester.widget<Container>(
        find
            .descendant(
              of: find.byKey(const Key('toggle-list-button')), // 假设列表按钮有唯一Key
              matching: find.byType(Container),
            )
            .first,
      );

      final gridButtonContainer = tester.widget<Container>(
        find
            .descendant(
              of: find.byKey(const Key('toggle-grid-button')), // 假设网格按钮有唯一Key
              matching: find.byType(Container),
            )
            .first,
      );

      // 验证初始颜色
      expect(
        (listButtonContainer.decoration as BoxDecoration).color,
        Colors.white,
        reason: 'List button should be active in list mode',
      );

      expect(
        (gridButtonContainer.decoration as BoxDecoration).color,
        Colors.white.withAlpha(128),
        reason: 'Grid button should be inactive in list mode',
      );

      // Act - Toggle to grid mode
      await tester.tap(find.byKey(const Key('toggle-grid-button')));
      await tester.pumpAndSettle();

      // 手动更新状态
      mockState.displayMode.value = DisplayMode.grid;
      await tester.pump(); // 触发界面更新

      // 获取更新后的容器
      final updatedListButtonContainer = tester.widget<Container>(
        find.descendant(of: find.byKey(const Key('toggle-list-button')), matching: find.byType(Container)).first,
      );

      final updatedGridButtonContainer = tester.widget<Container>(
        find.descendant(of: find.byKey(const Key('toggle-grid-button')), matching: find.byType(Container)).first,
      );

      // 验证切换后的颜色
      expect(
        (updatedListButtonContainer.decoration as BoxDecoration).color,
        Colors.white.withAlpha(128),
        reason: 'List button should be inactive in grid mode',
      );

      expect(
        (updatedGridButtonContainer.decoration as BoxDecoration).color,
        Colors.white,
        reason: 'Grid button should be active in grid mode',
      );

      // 验证方法调用
      verify(mockController.toggleDisplayMode()).called(1);
    });

    testWidgets('Tapping search bar calls onClickSearchConditionEnter()', (tester) async {
      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      final searchBar = find.text('何をお探しですか？');
      await tester.tap(searchBar);
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.onClickSearchConditionEnter()).called(1);
    });

    testWidgets('Tapping X icon calls onClickClearSearch() if searchKey != ""', (tester) async {
      // Arrange
      mockController.state.searchKey.value = 'keyword';

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      final xButton = find.byIcon(Icons.close);
      await tester.tap(xButton);
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.onClickClearSearch()).called(1);
    });
  });

  // ================================
  // CATEGORY & ASSET TYPE
  // ================================
  group('CATEGORY & ASSET TYPE', () {
    testWidgets('Tapping asset type widget calls onClickAssetTypeSelect()', (tester) async {
      // Arrange
      mockController.state.assetTypeName.value = '写真';

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      final assetTypeText = find.text('写真');
      await tester.tap(assetTypeText);
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.onClickAssetTypeSelect()).called(1);
    });

    testWidgets('Tapping category button calls onClickStartToCategory()', (tester) async {
      // Arrange
      // 当 filterCategoryKeyWordList 不为空时，"カテゴリ" 按钮可能会显示
      mockController.state.filterCategoryKeyWordList.clear();
      // 示例：根据需要添加类别项
      // mockController.state.filterCategoryKeyWordList.add(CategoryModel(...));

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      final categoryBtn = find.text('カテゴリ');
      if (categoryBtn.evaluate().isNotEmpty) {
        await tester.tap(categoryBtn);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onClickStartToCategory()).called(1);
      }
    });

    testWidgets('Tapping clear button calls onClickClearCategory()', (tester) async {
      // Arrange
      mockController.state.selectedCategoryTitle.value = ['カテゴリA', 'カテゴリB', 'カテゴリC'];

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      final clearButton = find.text('クリア');
      await tester.tap(clearButton);
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.onClickClearCategory()).called(1);
    });

    testWidgets('Should show category button when filterCategoryKeyWordList exists', (tester) async {
      // Arrange
      mockController.state.filterCategoryKeyWordList.value = [
        const CategoryModel(itemId: 0, itemDisplayName: '', itemName: 'itemName', itemType: 'itemType'),
      ];

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byKey(const ValueKey('asset-list-category-button')), findsOneWidget);
    });

    testWidgets('Should not show category button when filterCategoryKeyWordList does not exist', (tester) async {
      // Arrange
      mockController.state.filterCategoryKeyWordList.value = [];

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byKey(const ValueKey('asset-list-category-button')), findsNothing);
    });

    testWidgets('Should show total count when hasFilters is true (category selected)', (tester) async {
      // Arrange
      mockController.state
        ..selectedCategoryTitle.value =
            ['パソコン'] // 设置分类筛选
        ..searchKey.value =
            '' // 清除搜索关键词
        ..totalCount.value = 50
        ..searchCount.value = 20;

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('50件中'), findsOneWidget);
      expect(find.text('20件'), findsOneWidget);
    });

    testWidgets('Should NOT show total count when no filters', (tester) async {
      // Arrange
      mockController.state
        ..selectedCategoryTitle.value = []
        ..searchKey.value = ''
        ..totalCount.value = 200
        ..searchCount.value = 200;

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('200件中'), findsNothing);
      expect(find.text('200件'), findsOneWidget);
    });
  });

  // ================================
  // CLICK ITEM -> DETAIL
  // ================================
  group('CLICK ITEM -> DETAIL', () {
    testWidgets('Tapping item calls toDetailPage()', (tester) async {
      // Arrange
      mockController.state.noData.value = false; // 确保 noData 为 false
      mockController.state.isError.value = false;
      final item = AssetUIModel(imageUrl: 'http://xx.jpg', assetDisplayList: [], assetId: 123);
      mockController.state.data.assignAll([item]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 使用唯一 Key 查找特定的 AssetItemWidget
      final itemFinder = find.byKey(const Key('asset-item-123'));
      expect(itemFinder, findsOneWidget, reason: 'Asset item should be present');

      await tester.tap(itemFinder);
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.toDetailPage(item)).called(1);
    });
  });

  // ================================
  // ASSET ITEM TESTS
  // ================================
  group('AssetItemWidget Tests', () {
    testWidgets('Should show no-image when imageUrl is null', (tester) async {
      // Arrange
      final testItem = AssetUIModel(imageUrl: null, assetDisplayList: [], assetId: 1);
      mockController.state.displayMode.value = DisplayMode.grid;

      // Act
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(body: AssetItemWidget(assetUIModel: testItem)),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.image(const AssetImage('assets/images/no-image.png')), findsOneWidget);
    });

    testWidgets('Should display first display item in grid mode', (tester) async {
      // Arrange
      final testItem = AssetUIModel(
        imageUrl: 'http://valid.url',
        assetDisplayList: [
          AssetUIDisplay(title: '型号', content: 'X-1000'),
          AssetUIDisplay(title: '序列号', content: 'SN123456'),
        ],
        assetId: 1,
      );
      mockController.state.displayMode.value = DisplayMode.grid;

      // Act
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(body: AssetItemWidget(assetUIModel: testItem)),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('X-1000'), findsOneWidget);
      expect(find.text('SN123456'), findsNothing); // 验证只显示第一个
    });

    testWidgets('Should handle empty display list gracefully', (tester) async {
      // Arrange
      final testItem = AssetUIModel(imageUrl: 'http://valid.url', assetDisplayList: [], assetId: 1);
      mockController.state.displayMode.value = DisplayMode.grid;

      // Act
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(body: AssetItemWidget(assetUIModel: testItem)),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(Text), findsNothing); // 没有显示任何文本
    });

    testWidgets('Should display all display items in list mode', (tester) async {
      // Arrange
      final testItem = AssetUIModel(
        imageUrl: 'http://valid.url',
        assetDisplayList: [
          AssetUIDisplay(title: 'number', content: 'X-1000'),
          AssetUIDisplay(title: 'serialNumber', content: 'SN123456'),
          AssetUIDisplay(title: 'status', content: '使用中'),
        ],
        assetId: 1,
      );
      mockController.state.displayMode.value = DisplayMode.list;

      // Act
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(body: AssetItemWidget(assetUIModel: testItem)),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('number'), findsOneWidget);
      expect(find.text('X-1000'), findsOneWidget);
      expect(find.text('serialNumber'), findsOneWidget);
      expect(find.text('SN123456'), findsOneWidget);
      expect(find.text('status'), findsOneWidget);
      expect(find.text('使用中'), findsOneWidget);
    });

    testWidgets('Should handle empty display list gracefully', (tester) async {
      // Arrange
      final testItem = AssetUIModel(imageUrl: 'http://valid.url', assetDisplayList: null, assetId: 1);
      mockController.state.displayMode.value = DisplayMode.list;

      // Act
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(body: AssetItemWidget(assetUIModel: testItem)),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(Text), findsNothing); // 没有显示任何文本
      expect(find.text('[]'), findsNothing); // 没有显示空数组符号
      mockController.state.reset();
    });
  });

  group('SCROLL TO TOP BUTTON', () {
    testWidgets('Displays ScrollToTopButton when isShowScrollToTop is true', (tester) async {
      // Arrange: 将状态设置为 true
      mockController.state.isShowScrollToTop.value = true;

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert: 验证 ScrollToTopButton 出现在界面上，并且它的 Positioned 参数符合要求
      expect(find.byType(ScrollToTopButton), findsOneWidget);

      // 获取祖先 Positioned 并验证其 bottom 和 right 属性
      final positionedFinder = find.ancestor(of: find.byType(ScrollToTopButton), matching: find.byType(Positioned));
      expect(positionedFinder, findsOneWidget);

      final positioned = tester.widget<Positioned>(positionedFinder);
      expect(positioned.bottom, 16.0);
      expect(positioned.right, 16.0);

      // Act: 找到 ScrollToTopButton 并点击它
      final scrollButtonFinder = find.byType(ScrollToTopButton);
      expect(scrollButtonFinder, findsOneWidget);
      await tester.tap(scrollButtonFinder);
      await tester.pumpAndSettle();

      // Assert: 验证 controller.scrollToTop 方法被调用，并传入了任意的 ScrollController 实例
      verify(mockController.scrollToTop(any)).called(1);
    });

    testWidgets('Does not display ScrollToTopButton when isShowScrollToTop is false', (tester) async {
      // Arrange: 将状态设置为 false
      mockController.state.isShowScrollToTop.value = false;

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert: 界面中不应显示 ScrollToTopButton
      expect(find.byType(ScrollToTopButton), findsNothing);
    });
  });
}
