# asset-force-mobile 项目贡献指南

感谢您参与 **asset-force-mobile** 项目的开发。本项目采用 Git Flow 分支模型，并有一整套内部开发流程、代码规范、测试要求及提交流程。本指南旨在帮助内部开发人员快速了解项目开发、分支管理、代码提交及测试规范。

---

## 目录

- [行为准则](#行为准则)
- [Git Flow 分支模型](#git-flow-分支模型)
- [如何贡献](#如何贡献)
    - [报告 Bug](#报告-bug)
    - [提出新功能建议](#提出新功能建议)
    - [提交代码](#提交代码)
- [开发环境搭建](#开发环境搭建)
- [代码风格与提交规范](#代码风格与提交规范)
- [测试要求](#测试要求)
- [文档与示例](#文档与示例)
- [其它事项](#其它事项)
- [联系我们](#联系我们)

---

## 行为准则

- **尊重协作**：请以礼貌、友善的态度与团队其他成员沟通交流，互相帮助，共同提高。
- **高质量要求**：提交的代码应经过充分测试、符合团队代码规范，确保稳定性与可维护性。
- **开放讨论**：遇到疑难问题或架构性改动，欢迎先在内部 Issue 或讨论组中提出讨论，征求意见后再进行修改。
- **保密性**：本项目为公司内部项目，请勿将内部讨论内容或代码泄露到外部。

---

## Git Flow 分支模型

本项目采用 `Git Flow` 分支管理模型，主要分支说明如下：

- **`main`分支**
    - 始终保持可发布的稳定代码，只有经过充分测试和审核后才会合并到该分支。

- **`develop`分支**
    - 作为日常开发的主要分支，所有新功能和改动首先合并到 `develop` 分支，经测试通过后再合并到 `main` 分支发布。

- **`feature`分支**
    - 用于开发新功能或改进，分支命名格式为 `feature/{developer_name}/ASCHK-{JIRA番号}`。
    - 从 `develop` 分支创建，开发完成后通过 Pull Request 合并回 `develop` 分支。

- **`bugfix`分支**
    - 用于修复 `develop` 分支中的 Bug，命名格式为 `bugfix/{developer_name}/ASCHK-{JIRA番号}`。
    - 开发完成后合并回 `develop` 分支。

- **`release`分支**
    - 用于准备发布版本，从 `develop` 分支创建，进行最后的调试和小修，发布后合并回 `master`，并合并回 `develop` 以保持同步。

- **`hotfix`分支**
    - 用于紧急修复 `master` 分支上发布版本中的严重 Bug，命名格式为 `hotfix/{developer_name}/ASCHK-{JIRA番号}`。
    - 修复后分别合并到 `master` 和 `develop` 分支，保证版本一致性。

在提交 Pull Request 时，请确保基于正确的分支创建新分支，并遵循以上命名规范。

---

## 如何贡献

### 报告 Bug

1. **搜索已有问题**  
   在提交新 Bug 之前，请先在team内部确认问题未被报告。
2. **提交 Bug 报告**
    - 请在 Bug 报告中详细描述问题现象、重现步骤、涉及环境（Flutter 版本、平台、依赖库版本等）。
    - 如有错误日志或截图，请一并附上。

### 提出新功能建议

1. **讨论需求**  
   在提出新功能建议前，请先在内部讨论组中讨论，确认需求合理性。
2. **提交建议**
    - 详细描述建议内容、背景及预期效果。
    - 如有参考资料或实现思路，也请附上。

### 提交代码

1. **Fork 或创建分支**
    - 基于 `develop` 分支创建新的 feature、bugfix 分支，分支命名需符合规范（如 `feature/{developer_name}/ASCHK-{JIRA番号}`）。
2. **开发与测试**
    - 编写代码时请遵循项目的目录结构和代码规范。
    - 开发完成后，请运行 `flutter analyze`、`flutter test`、Widget 测试及集成测试，确保所有测试通过。
3. **提交 Pull Request**
    - 提交代码时请采用规范的提交信息格式（请参考下方代码风格与提交规范）。
    - 创建 PR 时请关联相关JIRA票，详细描述改动内容及测试情况，等待审核。

---

## 开发环境搭建

1. **安装 Flutter SDK**  
   请参考 [Flutter 官方文档](https://flutter.dev/docs/get-started/install) 安装 Flutter。
2. **配置环境变量**  
   将 Flutter SDK 的 `bin` 目录添加到系统 PATH 中。
3. **安装依赖工具**  
   根据项目需要安装 Android Studio（用于 Android 开发）、Xcode（用于 iOS 开发）等工具。
4. **克隆项目**
   ```bash
   git clone git@your-internal-git:asset-force-mobile.git
   cd asset-force-mobile
   flutter pub get
5. **运行项目**
使用 flutter run 或通过 IDE 运行项目，确保环境配置正确。

## 代码风格与提交规范
### 代码风格
请参考Confluence：
https://smfl.atlassian.net/wiki/spaces/PROJASF/pages/40821632/assetforce+mobile+flutter
### 提交规范
情确认PR提交模版

## 测试要求
### 单元测试
编写 Dart 单元测试覆盖核心业务逻辑，运行 flutter test 确保所有测试通过。
* Widget 测试
  * 为重要 UI 组件编写 Widget 测试，验证交互与布局。
* 集成测试
    * 使用 integration_test 包编写端到端测试，确保整体流程正常。
* 静态分析
    * 使用 flutter analyze 进行代码静态分析，确保代码符合规范，无错误或警告。

在每次提交前，请确保以上所有测试均已通过。

## 文档与示例
https://smfl.atlassian.net/wiki/spaces/PROJASF/pages/40820044/af+mobile
* 完善文档
在代码中添加必要的注释，并保持 README、Wiki 与开发文档的更新。

## 其它事项
* 反馈与讨论
    * 请通过内部 JIRA 系统和讨论组积极反馈问题与建议，共同完善项目。
* 版权与保密
    * 本项目为公司内部项目，所有代码及文档均受公司保密协议约束，贡献的代码将遵循内部许可协议。
* 贡献前沟通
    * 对于较大改动或架构变更，建议先与团队沟通，形成共识后再提交 PR。
