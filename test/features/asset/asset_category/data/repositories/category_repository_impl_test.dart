import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_list_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/repositories/category_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/repositories/category_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../../../../core/utils/dio_utils_test.mocks.dart';
import '../../mock/asset_category_mock_data.dart';

void main() {
  late CategoryRepository categoryRepository;
  late MockDioUtil dioUtil;
  setUp(() {
    LogUtil.initialize();
    dioUtil = MockDioUtil();
    categoryRepository = CategoryRepositoryImpl(dioUtil: dioUtil);
  });
  group('getCategoryCount', () {
    test('getCategoryCount case1', () async {
      when(
        dioUtil.post(
          '/secure/AssetMobileSetting/getCategoryCountForMobile?searchId=2',
          data: anyNamed('data'),
          useFormUrlEncoded: false,
        ),
      ).thenAnswer(
        (_) async =>
            Response(data: AssetCategoryMockData.categoryItemValueListResponse, requestOptions: RequestOptions()),
      );
      final result = await categoryRepository.getCategoryCount(params: {}, searchId: '2', keyword: '');
      expect(result, isA<CategoryItemValueListResponse>());
    });

    test('getCategoryCount error case1', () async {
      when(
        dioUtil.post(
          '/secure/AssetMobileSetting/getCategoryCountForMobile?searchId=2',
          data: anyNamed('data'),
          useFormUrlEncoded: false,
        ),
      ).thenThrow(SystemException(technicalMessage: 'Error to fetch category count.'));
      try {
        await categoryRepository.getCategoryCount(params: {}, searchId: '2', keyword: '123');
      } catch (e) {
        expect(e, isA<SystemException>());
      }
    });
  });
}
