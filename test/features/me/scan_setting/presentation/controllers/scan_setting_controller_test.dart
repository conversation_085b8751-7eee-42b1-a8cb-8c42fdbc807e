import 'package:asset_force_mobile_v2/core/constant/method_channel_constant.dart';
import 'package:asset_force_mobile_v2/core/platform/method_channel.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/controllers/scan_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/models/scan_setting_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'scan_setting_controller_test.mocks.dart';

// 生成 Mock 类
@GenerateMocks([IStorageUtils, PlatformChannel])
void main() {
  group('📱 ScanSettingsController 单元测试', () {
    late ScanSettingsController controller;
    late MockIStorageUtils mockStorageUtils;
    late MockPlatformChannel mockPlatformChannel;

    setUp(() {
      mockStorageUtils = MockIStorageUtils();
      mockPlatformChannel = MockPlatformChannel();

      // 创建带有 Mock 依赖的 Controller
      controller = ScanSettingsController(storageUtils: mockStorageUtils, platformChannel: mockPlatformChannel);
    });

    tearDown(() {
      reset(mockStorageUtils);
      reset(mockPlatformChannel);
    });

    group('🏗️ 初始化测试', () {
      test('应该正确加载默认设置', () async {
        // Arrange - 设置 Mock 返回值
        when(mockStorageUtils.getValue<bool>(StorageKeys.isVolume)).thenReturn(null);
        when(mockStorageUtils.getValue<bool>(StorageKeys.isInpact)).thenReturn(null);
        when(mockStorageUtils.getValue<int>(StorageKeys.scanMusicList)).thenReturn(null);
        when(mockStorageUtils.getValue<double>(StorageKeys.musicVolume)).thenReturn(null);
        when(mockStorageUtils.getValue<double>(StorageKeys.inpactVolume)).thenReturn(null);

        // Act - 触发初始化
        controller.onInit();
        await Future.delayed(Duration.zero); // 等待异步操作完成

        // Assert - 验证默认值
        expect(controller.model.value.useSound, false);
        expect(controller.model.value.useVibration, false);
        expect(controller.model.value.scanMusicList, 1); // 默认为 1 (不是 2 就是 1)
        expect(controller.model.value.soundValue, 0.0);
        expect(controller.model.value.vibrationValue, 0.0);

        // 验证存储读取被调用
        verify(mockStorageUtils.getValue<bool>(StorageKeys.isVolume)).called(1);
        verify(mockStorageUtils.getValue<bool>(StorageKeys.isInpact)).called(1);
        verify(mockStorageUtils.getValue<int>(StorageKeys.scanMusicList)).called(1);
        verify(mockStorageUtils.getValue<double>(StorageKeys.musicVolume)).called(1);
        verify(mockStorageUtils.getValue<double>(StorageKeys.inpactVolume)).called(1);
      });

      test('应该正确加载已保存的设置', () async {
        // Arrange - 设置已保存的值
        when(mockStorageUtils.getValue<bool>(StorageKeys.isVolume)).thenReturn(true);
        when(mockStorageUtils.getValue<bool>(StorageKeys.isInpact)).thenReturn(true);
        when(mockStorageUtils.getValue<int>(StorageKeys.scanMusicList)).thenReturn(2);
        when(mockStorageUtils.getValue<double>(StorageKeys.musicVolume)).thenReturn(75.0);
        when(mockStorageUtils.getValue<double>(StorageKeys.inpactVolume)).thenReturn(50.0);

        // Act
        controller.onInit();
        await Future.delayed(Duration.zero);

        // Assert
        expect(controller.model.value.useSound, true);
        expect(controller.model.value.useVibration, true);
        expect(controller.model.value.scanMusicList, 2);
        expect(controller.model.value.soundValue, 75.0);
        expect(controller.model.value.vibrationValue, 50.0);
      });
    });

    group('🔊 声音设置测试', () {
      test('soundEventChange 应该更新声音设置并保存', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});

        // Act
        await controller.soundEventChange(true);

        // Assert
        expect(controller.model.value.useSound, true);
        verify(mockStorageUtils.setValue(StorageKeys.isVolume, true)).called(1);
      });

      test('soundValueChange 应该更新音量并触发平台调用', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
        when(mockPlatformChannel.invokeMethod(any, any)).thenAnswer((_) async {});

        // Act
        await controller.soundValueChange(80.0);

        // Assert
        expect(controller.model.value.soundValue, 80.0);
        verify(mockStorageUtils.setValue(StorageKeys.musicVolume, 80.0)).called(1);
        verify(
          mockPlatformChannel.invokeMethod(
            MethodChannelConstant.scanSetting,
            argThat(allOf([contains('type'), containsPair('type', '1'), containsPair('musicVolume', 80.0)])),
          ),
        ).called(1);
      });
    });

    group('📳 振动设置测试', () {
      test('vibrationEventChange 应该更新振动设置并保存', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});

        // Act
        await controller.vibrationEventChange(true);

        // Assert
        expect(controller.model.value.useVibration, true);
        verify(mockStorageUtils.setValue(StorageKeys.isInpact, true)).called(1);
      });

      test('vibrationValueChange 应该更新振动强度并触发平台调用', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
        when(mockPlatformChannel.invokeMethod(any, any)).thenAnswer((_) async {});

        // Act
        await controller.vibrationValueChange(60.0);

        // Assert
        expect(controller.model.value.vibrationValue, 60.0);
        verify(mockStorageUtils.setValue(StorageKeys.inpactVolume, 60.0)).called(1);
        verify(
          mockPlatformChannel.invokeMethod(
            MethodChannelConstant.scanSetting,
            argThat(allOf([containsPair('type', '2'), containsPair('inpactVolume', 60.0)])),
          ),
        ).called(1);
      });
    });

    group('🎵 音效类型测试', () {
      test('musicChange 应该更新音效类型并触发平台调用', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
        when(mockPlatformChannel.invokeMethod(any, any)).thenAnswer((_) async {});

        // Act
        await controller.musicChange(2);

        // Assert
        expect(controller.model.value.scanMusicList, 2);
        verify(mockStorageUtils.setValue(StorageKeys.scanMusicList, 2)).called(1);
        verify(
          mockPlatformChannel.invokeMethod(
            MethodChannelConstant.scanSetting,
            argThat(allOf([containsPair('type', '3'), containsPair('scanMusicList', 2)])),
          ),
        ).called(1);
      });
    });

    group('💾 存储同步测试', () {
      test('onClose 应该同步存储到平台', () async {
        // Arrange
        when(mockStorageUtils.getValue<bool>(StorageKeys.isVolume)).thenReturn(true);
        when(mockStorageUtils.getValue<bool>(StorageKeys.isInpact)).thenReturn(false);
        when(mockStorageUtils.getValue<int>(StorageKeys.scanMusicList)).thenReturn(1);
        when(mockStorageUtils.getValue<double>(StorageKeys.musicVolume)).thenReturn(70.0);
        when(mockStorageUtils.getValue<double>(StorageKeys.inpactVolume)).thenReturn(30.0);
        when(mockPlatformChannel.syncStorage(any)).thenAnswer((_) async {});

        // Act
        controller.onClose();
        await Future.delayed(Duration.zero);

        // Assert
        verify(
          mockPlatformChannel.syncStorage(
            argThat(
              allOf([
                containsPair(StorageKeys.isVolume, true),
                containsPair(StorageKeys.isInpact, false),
                containsPair(StorageKeys.scanMusicList, 1),
                containsPair(StorageKeys.musicVolume, 70.0),
                containsPair(StorageKeys.inpactVolume, 30.0),
              ]),
            ),
          ),
        ).called(1);
      });
    });

    group('🔧 数据验证测试', () {
      test('musicList 应该验证为有效值 (1 或 2)', () async {
        // Arrange - 设置无效值
        when(mockStorageUtils.getValue<bool>(StorageKeys.isVolume)).thenReturn(false);
        when(mockStorageUtils.getValue<bool>(StorageKeys.isInpact)).thenReturn(false);
        when(mockStorageUtils.getValue<int>(StorageKeys.scanMusicList)).thenReturn(5); // 无效值
        when(mockStorageUtils.getValue<double>(StorageKeys.musicVolume)).thenReturn(0.0);
        when(mockStorageUtils.getValue<double>(StorageKeys.inpactVolume)).thenReturn(0.0);

        // Act
        controller.onInit();
        await Future.delayed(Duration.zero);

        // Assert - 应该默认为 1 (因为不是 2 就是 1)
        expect(controller.model.value.scanMusicList, 1);
      });

      test('null 值应该使用默认值', () async {
        // Arrange - 所有值都为 null
        when(mockStorageUtils.getValue<bool>(any)).thenReturn(null);
        when(mockStorageUtils.getValue<int>(any)).thenReturn(null);
        when(mockStorageUtils.getValue<double>(any)).thenReturn(null);

        // Act
        controller.onInit();
        await Future.delayed(Duration.zero);

        // Assert
        expect(controller.model.value.useSound, false);
        expect(controller.model.value.useVibration, false);
        expect(controller.model.value.scanMusicList, 1);
        expect(controller.model.value.soundValue, 0.0);
        expect(controller.model.value.vibrationValue, 0.0);
      });
    });

    group('⚠️ 错误处理测试', () {
      test('存储失败应该不影响 UI 状态更新', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenThrow(Exception('Storage failed'));

        // Act & Assert - 存储异常应该被抛出，但 UI 状态仍会更新
        try {
          await controller.soundEventChange(true);
          fail('应该抛出存储异常');
        } catch (e) {
          expect(e, isA<Exception>());
          expect(e.toString(), contains('Storage failed'));
        }

        // UI 状态应该已更新（在存储调用之前）
        expect(controller.model.value.useSound, true);
      });

      test('平台调用失败应该不影响存储', () async {
        // Arrange
        when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
        when(mockPlatformChannel.invokeMethod(any, any)).thenThrow(Exception('Platform call failed'));

        // Act & Assert - 平台调用异常应该被抛出，但存储和 UI 更新应该成功
        try {
          await controller.soundValueChange(50.0);
          fail('应该抛出平台调用异常');
        } catch (e) {
          expect(e, isA<Exception>());
          expect(e.toString(), contains('Platform call failed'));
        }

        // 存储应该成功（在平台调用失败之前）
        verify(mockStorageUtils.setValue(StorageKeys.musicVolume, 50.0)).called(1);
        expect(controller.model.value.soundValue, 50.0);
      });
    });
  });
}
