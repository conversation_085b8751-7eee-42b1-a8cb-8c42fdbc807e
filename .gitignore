# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
pubspec.lock # For libraries only, remove this if you're working on an application

# Generated Plugin Registrant
**/GeneratedPluginRegistrant.*

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Android specific
.android/local.properties
.android/.gradle/
.android/app/build/
.android/build/
android/gradlew
android/gradlew.bat
android/gradle-wrapper.jar
android/gradle-wrapper.properties
android/gradle.properties
android/include_flutter.groovy

# iOS specific
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Generated.xcconfig
ios/Pods/
ios/.symlinks/
ios/Flutter/Flutter.podspec
ios/Flutter/flutter_export_environment.sh
*.xcworkspace
*.xcuserstate
*.mode1v3
*.mode2v3
*.pbxuser
.ios/Flutter/AppFrameworkInfo.plist
.ios/Flutter/flutter_export_environment.sh
.ios/Flutter/Generated.xcconfig
/.ios/Flutter/FlutterPluginRegistrant/FlutterPluginRegistrant.podspec
# Xcode workspace data
ios/Runner.xcworkspace/

# CocoaPods specific
ios/Podfile.lock

# macOS specific
*.DS_Store

# Windows specific
*.exe
*.dll
*.sys
*.bat

# Gradle files
.android/gradle/wrapper/gradle-wrapper.jar
.android/gradle/wrapper/gradle-wrapper.properties
.android/gradlew
.android/gradlew.bat
.android/include_flutter.groovy
/coverage/
.aider*

# FVM Version Cache
.fvm/
/CLAUDE.md
