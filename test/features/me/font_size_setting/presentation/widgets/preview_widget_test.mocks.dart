// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/font_size_setting/presentation/widgets/preview_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i9;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i7;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/controllers/font_size_setting_controller.dart'
    as _i4;
import 'package:get/get.dart' as _i2;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRx_0<T> extends _i1.SmartFake implements _i2.Rx<T> {
  _FakeRx_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_1 extends _i1.SmartFake
    implements _i3.NavigationService {
  _FakeNavigationService_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_2<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FontSizeSettingController].
///
/// See the documentation for Mockito's code generation for more information.
class MockFontSizeSettingController extends _i1.Mock
    implements _i4.FontSizeSettingController {
  @override
  _i2.Rx<String> get selectedFontSize =>
      (super.noSuchMethod(
            Invocation.getter(#selectedFontSize),
            returnValue: _FakeRx_0<String>(
              this,
              Invocation.getter(#selectedFontSize),
            ),
            returnValueForMissingStub: _FakeRx_0<String>(
              this,
              Invocation.getter(#selectedFontSize),
            ),
          )
          as _i2.Rx<String>);

  @override
  set selectedFontSize(_i2.Rx<String>? _selectedFontSize) => super.noSuchMethod(
    Invocation.setter(#selectedFontSize, _selectedFontSize),
    returnValueForMissingStub: null,
  );

  @override
  _i2.Rx<bool> get expand =>
      (super.noSuchMethod(
            Invocation.getter(#expand),
            returnValue: _FakeRx_0<bool>(this, Invocation.getter(#expand)),
            returnValueForMissingStub: _FakeRx_0<bool>(
              this,
              Invocation.getter(#expand),
            ),
          )
          as _i2.Rx<bool>);

  @override
  set expand(_i2.Rx<bool>? _expand) => super.noSuchMethod(
    Invocation.setter(#expand, _expand),
    returnValueForMissingStub: null,
  );

  @override
  String get originValue =>
      (super.noSuchMethod(
            Invocation.getter(#originValue),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#originValue),
            ),
            returnValueForMissingStub: _i5.dummyValue<String>(
              this,
              Invocation.getter(#originValue),
            ),
          )
          as String);

  @override
  set originValue(String? _originValue) => super.noSuchMethod(
    Invocation.setter(#originValue, _originValue),
    returnValueForMissingStub: null,
  );

  @override
  _i3.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i3.NavigationService);

  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onFontSizeSelect(String? size) => super.noSuchMethod(
    Invocation.method(#onFontSizeSelect, [size]),
    returnValueForMissingStub: null,
  );

  @override
  void onBack() => super.noSuchMethod(
    Invocation.method(#onBack, []),
    returnValueForMissingStub: null,
  );

  @override
  void onSave() => super.noSuchMethod(
    Invocation.method(#onSave, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i7.ErrorHandlingMode? mode = _i7.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Disposer addListener(_i8.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i8.Disposer);

  @override
  void removeListener(_i9.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i9.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Disposer addListenerId(Object? key, _i8.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i8.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
