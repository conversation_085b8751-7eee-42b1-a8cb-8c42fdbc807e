# プルリクエスト

<!--  
この PR の概要、背景、変更点、テスト方法、影響範囲、リスク評価など、レビュワーが素早く内容を把握できるように詳細に記載してください。  
-->

## 概要 (Summary)
- **変更内容の概要**:  
  本 PR では、（例：新機能の追加、バグ修正、パフォーマンス改善、リファクタリングなど）を実施しました。
- **関連 JIRA票**:  
  関連する JIRA票 があれば記載してください（例：`ASCHK-XXXXX [af mobile] XXXXXX`）。

## 背景と目的 (Background & Motivation)
- **背景**:  
  なぜこの変更が必要なのか、現在の問題点や課題、ビジネス上の要件などを説明してください。
- **目的**:  
  この変更によって解決したい問題や、期待する効果について記載してください。

## 変更内容の詳細 (Detailed Changes)
- **実施内容**:  
  主な変更点、追加・修正した機能やコードの概要を箇条書きで記載してください。
  - 例: ユーザー認証機能の追加
  - 例: データ保存ロジックのリファクタリング
  - 例: ログイン時のクラッシュ修正
- **UI/UX の変更（該当する場合）**:  
  画面や操作性に関する変更がある場合、変更前後の比較やその理由を記載してください。

## テストと検証 (Testing & Verification)
- **テスト計画**:  
  本変更に対して実施した単体テスト、統合テスト、手動テストの内容を具体的に記載してください。  
  自動テストの実行方法や、必要なテスト環境の設定方法も含めるとよいでしょう。
- **検証方法**:  
  QA やレビュワーがどのように動作確認を行えばよいか、手順や確認ポイントを具体的に記載してください。

## チェックリスト (Checklist)
プルリクエストを提出する前に、以下の項目を確認してください：
- [ ] **コードとコメント**: コードはチームのコーディング規約に沿って記述され、必要なコメントが付与されているか。
- [ ] **単体テスト**: 変更に伴うテストが追加・更新され、すべてのテストがパスしているか。
- [ ] **ドキュメントの更新**: 外部 API や利用方法に変更がある場合、JIRAとConfluenceのドキュメントも更新されているか。
- [ ] **コード整形**: コードのフォーマットが適切に整形されているか。
- [ ] **関連 JIRA票 の更新**: 関連 JIRA票 があれば、PR 提出後にクローズまたは更新されるようになっているか。
- [ ] **セキュリティ**: 本変更がセキュリティ上の問題を引き起こさないか、セキュリティのベストプラクティスに沿っているか。

## 影響範囲とリスク評価 (Impact & Risk Assessment)
- **影響範囲**:  
  今回の変更が影響を及ぼすモジュールや機能、互換性の問題の有無などを記載してください。
- **リスク評価と対策**:  
  本変更に伴う潜在的なリスクや、問題が発生した際のロールバック計画などを記載してください。

## 補足情報 (Additional Notes)
- **議論やフィードバック**:  
  この PR に関する疑問点や今後の改善点、追加の議論が必要な点など、レビュワーへのメッセージを記載してください。
- **スクリーンショット/動画**（必要に応じて）:  
  UI の変更がある場合、変更前後のスクリーンショットや動画を添付してください。

---

