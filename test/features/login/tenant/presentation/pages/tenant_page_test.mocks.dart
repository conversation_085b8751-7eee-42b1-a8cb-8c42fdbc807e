// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/tenant/presentation/pages/tenant_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i11;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i9;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/controllers/tenant_controller.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart'
    as _i7;
import 'package:get/get.dart' as _i2;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRxList_0<E> extends _i1.SmartFake implements _i2.RxList<E> {
  _FakeRxList_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxString_1 extends _i1.SmartFake implements _i2.RxString {
  _FakeRxString_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTenantUseCase_2 extends _i1.SmartFake implements _i3.TenantUseCase {
  _FakeTenantUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_3 extends _i1.SmartFake implements _i4.DialogService {
  _FakeDialogService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_4 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_5 extends _i1.SmartFake implements _i2.RxBool {
  _FakeRxBool_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_6<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TenantController].
///
/// See the documentation for Mockito's code generation for more information.
class MockTenantController extends _i1.Mock implements _i6.TenantController {
  @override
  _i2.RxList<_i7.SharedTenantModel> get tenantsList =>
      (super.noSuchMethod(
            Invocation.getter(#tenantsList),
            returnValue: _FakeRxList_0<_i7.SharedTenantModel>(
              this,
              Invocation.getter(#tenantsList),
            ),
            returnValueForMissingStub: _FakeRxList_0<_i7.SharedTenantModel>(
              this,
              Invocation.getter(#tenantsList),
            ),
          )
          as _i2.RxList<_i7.SharedTenantModel>);

  @override
  set tenantsList(_i2.RxList<_i7.SharedTenantModel>? _tenantsList) =>
      super.noSuchMethod(
        Invocation.setter(#tenantsList, _tenantsList),
        returnValueForMissingStub: null,
      );

  @override
  _i2.RxString get tenantTitle =>
      (super.noSuchMethod(
            Invocation.getter(#tenantTitle),
            returnValue: _FakeRxString_1(this, Invocation.getter(#tenantTitle)),
            returnValueForMissingStub: _FakeRxString_1(
              this,
              Invocation.getter(#tenantTitle),
            ),
          )
          as _i2.RxString);

  @override
  set tenantTitle(_i2.RxString? _tenantTitle) => super.noSuchMethod(
    Invocation.setter(#tenantTitle, _tenantTitle),
    returnValueForMissingStub: null,
  );

  @override
  _i3.TenantUseCase get tenantUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#tenantUseCase),
            returnValue: _FakeTenantUseCase_2(
              this,
              Invocation.getter(#tenantUseCase),
            ),
            returnValueForMissingStub: _FakeTenantUseCase_2(
              this,
              Invocation.getter(#tenantUseCase),
            ),
          )
          as _i3.TenantUseCase);

  @override
  _i4.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_3(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_3(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i4.DialogService);

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_4(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_4(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  _i2.RxString get ticket =>
      (super.noSuchMethod(
            Invocation.getter(#ticket),
            returnValue: _FakeRxString_1(this, Invocation.getter(#ticket)),
            returnValueForMissingStub: _FakeRxString_1(
              this,
              Invocation.getter(#ticket),
            ),
          )
          as _i2.RxString);

  @override
  set ticket(_i2.RxString? _ticket) => super.noSuchMethod(
    Invocation.setter(#ticket, _ticket),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RxBool get isBiometrics =>
      (super.noSuchMethod(
            Invocation.getter(#isBiometrics),
            returnValue: _FakeRxBool_5(this, Invocation.getter(#isBiometrics)),
            returnValueForMissingStub: _FakeRxBool_5(
              this,
              Invocation.getter(#isBiometrics),
            ),
          )
          as _i2.RxBool);

  @override
  set isBiometrics(_i2.RxBool? _isBiometrics) => super.noSuchMethod(
    Invocation.setter(#isBiometrics, _isBiometrics),
    returnValueForMissingStub: null,
  );

  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  dynamic onTenantClick(_i7.SharedTenantModel? tenant) => super.noSuchMethod(
    Invocation.method(#onTenantClick, [tenant]),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Future<void> onTenantOption({
    required _i7.SharedTenantModel? tenant,
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#onTenantOption, [], {
              #tenant: tenant,
              #userName: userName,
              #password: password,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i9.ErrorHandlingMode? mode = _i9.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Disposer addListener(_i10.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i10.Disposer);

  @override
  void removeListener(_i11.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i11.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Disposer addListenerId(Object? key, _i10.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i10.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
