import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/domain/usecase/get_asset_type_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

// Import generated mock file
import 'asset_type_repository_test.mocks.dart';

void main() {
  late GetAssetTypeListUseCase getAssetTypeListUseCase;
  late MockAssetTypeRepository mockAssetTypeRepository;

  setUp(() {
    LogUtil.initialize(); // Initialize logging utility
    mockAssetTypeRepository = MockAssetTypeRepository();
    getAssetTypeListUseCase = GetAssetTypeListUseCase(mockAssetTypeRepository);
  });

  tearDown(() {
    reset(mockAssetTypeRepository); // Reset mock instance
  });

  /// ----------------------------
  /// GetAssetTypeListUseCase Tests
  /// ----------------------------
  group('Tests for getAllAssetTypes method', () {
    test('Should return valid data when asset types are successfully retrieved', () async {
      // Arrange
      final assetTypeList = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: '类型 1'),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
      ];

      final assetTypeResponse = AssetTypeResponse(
        msg: '',
        code: 0,
        assetTypeList: assetTypeList,
        commonJS: '',
        js: '',
        jsQRlogic: '',
        jsSaveAsset: '',
        rememberedAssetType: '',
      );

      when(mockAssetTypeRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // Act
      final result = await getAssetTypeListUseCase(const NoParams());

      // Assert
      expect(result.assetTypeList, equals(assetTypeList));
      expect(result.hasAuthority, isFalse);
      verify(mockAssetTypeRepository.getAllAssetTypes()).called(1);

      // Verify data integrity
      expect(result.assetTypeList.first.assetTypeId, equals(1));
      expect(result.assetTypeList.first.assetTypeName, equals('类型 1'));
    });

    test('Should throw BusinessException when repository call fails', () async {
      // Arrange
      when(mockAssetTypeRepository.getAllAssetTypes()).thenThrow(BusinessException('获取数据失败'));

      // Act & Assert
      expect(() async => await getAssetTypeListUseCase(const NoParams()), throwsA(isA<BusinessException>()));

      verify(mockAssetTypeRepository.getAllAssetTypes()).called(1);
    });

    test('Should throw SystemException when repository call throws SystemException', () async {
      // Arrange
      when(mockAssetTypeRepository.getAllAssetTypes()).thenThrow(SystemException(technicalMessage: '系统错误'));

      // Act & Assert
      expect(() async => await getAssetTypeListUseCase(const NoParams()), throwsA(isA<SystemException>()));

      verify(mockAssetTypeRepository.getAllAssetTypes()).called(1);
    });
  });

  /// 权限检查
  group('Tests for checkAuthority method', () {
    test('Should return true when authority check passes', () async {
      // Arrange
      when(mockAssetTypeRepository.checkAssetAuthority(1, 0)).thenAnswer((_) async => 1);

      // Act
      final result = await getAssetTypeListUseCase.checkAuthority(assetTypeId: 1);

      // Assert
      expect(result, isTrue);
      verify(mockAssetTypeRepository.checkAssetAuthority(1, 0)).called(1);
    });

    test('Should return false when authority check fails', () async {
      // Arrange
      when(mockAssetTypeRepository.checkAssetAuthority(1, 0)).thenAnswer((_) async => 0);

      // Act
      final result = await getAssetTypeListUseCase.checkAuthority(assetTypeId: 1);

      // Assert
      expect(result, isFalse);
      verify(mockAssetTypeRepository.checkAssetAuthority(1, 0)).called(1);
    });

    test('Should throw BusinessException when authority check fails', () async {
      // Arrange
      when(mockAssetTypeRepository.checkAssetAuthority(1, 0)).thenThrow(BusinessException('权限检查失败'));

      // Act & Assert
      expect(
        () async => await getAssetTypeListUseCase.checkAuthority(assetTypeId: 1),
        throwsA(isA<BusinessException>()),
      );

      verify(mockAssetTypeRepository.checkAssetAuthority(1, 0)).called(1);
    });

    test('Should throw SystemException when authority check throws SystemException', () async {
      // Arrange
      when(mockAssetTypeRepository.checkAssetAuthority(1, 0)).thenThrow(SystemException(technicalMessage: '系统权限检查错误'));

      // Act & Assert
      expect(() async => await getAssetTypeListUseCase.checkAuthority(assetTypeId: 1), throwsA(isA<SystemException>()));

      verify(mockAssetTypeRepository.checkAssetAuthority(1, 0)).called(1);
    });

    test('Should handle concurrent authority checks', () async {
      // Arrange
      when(mockAssetTypeRepository.checkAssetAuthority(any, any)).thenAnswer((_) async => 1);

      // Act
      final results = await Future.wait([
        getAssetTypeListUseCase.checkAuthority(assetTypeId: 1),
        getAssetTypeListUseCase.checkAuthority(assetTypeId: 2),
      ]);

      // Assert
      expect(results, [true, true]);
      verify(mockAssetTypeRepository.checkAssetAuthority(1, 0)).called(1);
      verify(mockAssetTypeRepository.checkAssetAuthority(2, 0)).called(1);
    });
  });
}
