import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_conditions_result.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/search_conditions_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'get_search_id_usecase_test.mocks.dart';

@GenerateMocks([SearchConditionsRepository])
void main() {
  late GetSearchIdUseCase useCase;
  late MockSearchConditionsRepository mockRepository;

  setUp(() {
    mockRepository = MockSearchConditionsRepository();
    useCase = GetSearchIdUseCase(mockRepository);
  });

  group('GetSearchIdUseCase Tests', () {
    test('should return valid data when search conditions are successfully retrieved', () async {
      // Arrange
      final searchConditions = [
        SearchConditionModel(searchId: 2, searchName: 'Condition 2'),
        SearchConditionModel(searchId: 0, searchName: 'Condition 0'),
        SearchConditionModel(searchId: 1, searchName: 'Condition 1'),
      ];
      final searchConditionsResult = SearchConditionsResult(assetSearchNameList: searchConditions, code: 1, msg: '');

      when(mockRepository.getSearchConditions(any)).thenAnswer((_) async => searchConditionsResult);

      // Act
      final result = await useCase(GetAssetTypeIdParams(assetTypeId: 1));

      // Assert
      expect(result.searchId, 2);
      expect(result.assetSearchNameList.length, 3);
      verify(mockRepository.getSearchConditions(any)).called(1);
    });

    test('should return searchIdConditionZero when search conditions result is null', () async {
      // Arrange
      when(mockRepository.getSearchConditions(any)).thenAnswer((_) async => null);

      // Act
      final result = await useCase(GetAssetTypeIdParams(assetTypeId: 1));

      // Assert
      expect(result.searchId, searchIdConditionZero);
      expect(result.assetSearchNameList, isEmpty);
      verify(mockRepository.getSearchConditions(any)).called(1);
    });

    test('should return searchIdConditionZero when search conditions list is empty', () async {
      // Arrange
      final searchConditionsResult = SearchConditionsResult(assetSearchNameList: [], code: 1, msg: '');

      when(mockRepository.getSearchConditions(any)).thenAnswer((_) async => searchConditionsResult);

      // Act
      final result = await useCase(GetAssetTypeIdParams(assetTypeId: 1));

      // Assert
      expect(result.searchId, searchIdConditionZero);
      expect(result.assetSearchNameList, isEmpty);
      verify(mockRepository.getSearchConditions(any)).called(1);
    });

    test('should return searchIdConditionZero when search conditions list is null', () async {
      // Arrange
      final searchConditionsResult = SearchConditionsResult(assetSearchNameList: null, code: 1, msg: '');

      when(mockRepository.getSearchConditions(any)).thenAnswer((_) async => searchConditionsResult);

      // Act
      final result = await useCase(GetAssetTypeIdParams(assetTypeId: 1));

      // Assert
      expect(result.searchId, searchIdConditionZero);
      expect(result.assetSearchNameList, isEmpty);
      verify(mockRepository.getSearchConditions(any)).called(1);
    });

    test('should return searchIdConditionZero when no matching searchId is found', () async {
      // Arrange
      final searchConditions = [
        SearchConditionModel(searchId: 3, searchName: 'Condition 3'),
        SearchConditionModel(searchId: 4, searchName: 'Condition 4'),
      ];
      final searchConditionsResult = SearchConditionsResult(assetSearchNameList: searchConditions, code: 1, msg: '');

      when(mockRepository.getSearchConditions(any)).thenAnswer((_) async => searchConditionsResult);

      // Act
      final result = await useCase(GetAssetTypeIdParams(assetTypeId: 1));

      // Assert
      expect(result.searchId, searchIdConditionZero);
      expect(result.assetSearchNameList.length, 2);
      verify(mockRepository.getSearchConditions(any)).called(1);
    });
  });
}
