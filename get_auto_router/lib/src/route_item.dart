import 'dart:convert';

class RouteItem {
  String routeName;
  Set<String> import;
  String className;
  String? binding;
  Set<String> bindings;
  bool isConst;
  List<String> parameters = [];

  RouteItem({
    this.routeName = '',
    this.import = const {},
    this.className = '',
    this.binding,
    this.bindings = const {},
    this.isConst = true,
    List<String> parameters = const [],
  }) {
    this.parameters = List<String>.from(parameters);
  }

  Map<String, dynamic> toMap() {
    return {
      'routeName': routeName,
      'import': import.toList(),
      'className': className,
      'binding': binding,
      'bindings': bindings.toList(),
      'isConst': isConst,
      'parameters': parameters,
    };
  }

  factory RouteItem.fromMap(Map<String, dynamic> map) {
    return RouteItem(
      routeName: map['routeName'] as String? ?? '',
      import: (map['import'] as List<dynamic>?)?.map((e) => e as String).toSet() ?? {},
      className: map['className'] as String? ?? '',
      binding: map['binding'] as String?,
      bindings: (map['bindings'] as List<dynamic>?)?.map((e) => e as String).toSet() ?? {},
      isConst: map['isConst'] as bool? ?? true,
      parameters: (map['parameters'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
    );
  }

  String toJson() => json.encode(toMap());

  factory RouteItem.fromJson(String source) => RouteItem.fromMap(json.decode(source) as Map<String, dynamic>);
}
