import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/controllers/font_size_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/widgets/preview_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'preview_widget_test.mocks.dart';

// Helper mock class for GetX lifecycle callbacks
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// 1. Mock the controller
@GenerateNiceMocks([MockSpec<FontSizeSettingController>()])
void main() {
  // 2. Declare the mock controller instance
  late MockFontSizeSettingController mockController;
  late MockInternalFinalCallback<void> mockInternalFinalCallback;

  // 3. setUp and tearDown for a clean test environment
  setUp(() {
    Get.reset();
    mockController = MockFontSizeSettingController();
    mockInternalFinalCallback = MockInternalFinalCallback<void>();

    // Default stubbing for the controller's state
    when(mockController.selectedFontSize).thenReturn('normal'.obs);
    when(mockController.expand).thenReturn(true.obs);

    // Stub GetX lifecycle methods to prevent FakeUsedError
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);
    when(mockController.onDelete).thenReturn(mockInternalFinalCallback);

    // Put the mock controller into GetX's dependency management
    Get.put<FontSizeSettingController>(mockController);
  });

  tearDown(() {
    Get.reset();
  });

  // 4. Helper function to build the widget
  Future<void> pumpWidget(WidgetTester tester) async {
    await tester.pumpWidget(const GetMaterialApp(home: Scaffold(body: PreviewWidget())));
    await tester.pumpAndSettle();
  }

  // Helper function to setup mock controller with specific state
  void setupMockController({String? selectedFontSize, bool? expand}) {
    Get.reset();
    mockController = MockFontSizeSettingController();
    when(mockController.selectedFontSize).thenReturn((selectedFontSize ?? 'normal').obs);
    when(mockController.expand).thenReturn((expand ?? true).obs);
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);
    when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
    when(mockController.onFontSizeSelect(any)).thenReturn(null);
    when(mockController.onBack()).thenReturn(null);
    when(mockController.onSave()).thenReturn(null);
    Get.put<FontSizeSettingController>(mockController);
  }

  // Phase 0: 架子搭建测试
  group('Phase 0: Scaffold Tests', () {
    testWidgets('PreviewWidget renders without crashing', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证基本的widget渲染
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
    });

    testWidgets('Controller dependency injection works correctly', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证controller正确注入
      expect(Get.find<FontSizeSettingController>(), isNotNull);
      expect(Get.find<FontSizeSettingController>(), equals(mockController));
    });

    testWidgets('Mock controller state is properly set up', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证mock状态设置正确
      final controller = Get.find<FontSizeSettingController>();
      expect(controller.selectedFontSize.value, 'normal');
      expect(controller.expand.value, true);
    });

    testWidgets('Basic UI structure exists', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证基本UI结构存在
      expect(find.byType(Column), findsAtLeastNWidgets(1));
      expect(find.byType(GestureDetector), findsAtLeastNWidgets(1));
      expect(find.byType(AnimatedContainer), findsOneWidget);
    });

    testWidgets('GetX Obx widgets work correctly', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证Obx响应式组件正常工作
      expect(find.byType(Obx), findsAtLeastNWidgets(1));

      // 验证基于expand状态的UI元素
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
    });

    testWidgets('Helper method works with different states', (WidgetTester tester) async {
      // Arrange - 使用helper方法设置不同状态
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证helper方法正确设置状态
      final controller = Get.find<FontSizeSettingController>();
      expect(controller.expand.value, false);

      // 验证UI反映了状态变化
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsNothing);
    });

    testWidgets('Widget rebuilds correctly after state change', (WidgetTester tester) async {
      // Arrange - 使用可变状态
      final expandObs = true.obs;
      setupMockController();
      when(mockController.expand).thenReturn(expandObs);

      await pumpWidget(tester);

      // 验证初始状态
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);

      // Act - 改变状态
      expandObs.value = false;
      await tester.pumpAndSettle();

      // Assert - 验证UI更新
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsNothing);
    });

    testWidgets('All key components can be found', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证所有添加的Key都可以找到
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expand_gesture')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_animated_container')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_header_container')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expand_icon')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expandable_content')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_static_container')), findsOneWidget);
    });
  });

  // Phase 1: UI元素结构验证测试
  group('Phase 1: UI Elements Structure Verification', () {
    testWidgets('Main column structure is correctly rendered', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证主列结构
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);

      final mainColumn = tester.widget<Column>(find.byKey(const Key('preview_widget_main_column')));
      expect(mainColumn.crossAxisAlignment, CrossAxisAlignment.start);
      expect(mainColumn.children.length, 3); // " 表示例"文本 + 可展开容器 + 静态容器
    });

    testWidgets('Preview title text is displayed correctly', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证" 表示例"标题
      expect(find.text(' 表示例'), findsOneWidget);

      final titleText = tester.widget<Text>(find.text(' 表示例'));
      expect(titleText.style?.color, Colors.white);
    });

    testWidgets('Expandable gesture detector is properly configured', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证手势检测器
      expect(find.byKey(const Key('preview_widget_expand_gesture')), findsOneWidget);

      final gestureDetector = tester.widget<GestureDetector>(find.byKey(const Key('preview_widget_expand_gesture')));
      expect(gestureDetector.onTap, isNotNull);
    });

    testWidgets('Animated container has correct configuration', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证动画容器
      expect(find.byKey(const Key('preview_widget_animated_container')), findsOneWidget);

      final animatedContainer = tester.widget<AnimatedContainer>(
        find.byKey(const Key('preview_widget_animated_container')),
      );
      expect(animatedContainer.duration, const Duration(milliseconds: 300));
      expect(animatedContainer.margin, const EdgeInsets.only(top: 12));

      // 验证装饰
      final decoration = animatedContainer.decoration as BoxDecoration;
      expect(decoration.color, Colors.white);
      expect(decoration.borderRadius, BorderRadius.circular(8));
      expect(decoration.boxShadow, isNotNull);
      expect(decoration.boxShadow!.length, 1);
    });

    testWidgets('Header container structure is correct', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证头部容器
      expect(find.byKey(const Key('preview_widget_header_container')), findsOneWidget);

      final headerContainer = tester.widget<Container>(find.byKey(const Key('preview_widget_header_container')));
      expect(headerContainer.padding, const EdgeInsets.all(8));

      // 验证头部包含Row布局（可能有多个Row）
      expect(
        find.descendant(of: find.byKey(const Key('preview_widget_header_container')), matching: find.byType(Row)),
        findsAtLeastNWidgets(1),
      );
    });

    testWidgets('Header content displays correctly', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证头部内容
      expect(find.text('資産情報'), findsOneWidget);

      final titleText = tester.widget<Text>(find.text('資産情報'));
      expect(titleText.style?.fontSize, 16);
      expect(titleText.style?.fontWeight, FontWeight.bold);
      expect(titleText.style?.color, Colors.black);

      // 验证有竖线分隔符
      expect(
        find.descendant(of: find.byKey(const Key('preview_widget_header_container')), matching: find.byType(Container)),
        findsAtLeastNWidgets(1),
      );
    });

    testWidgets('Expand icon displays correctly in expanded state', (WidgetTester tester) async {
      // Arrange - 设置展开状态
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证展开图标
      expect(find.byKey(const Key('preview_widget_expand_icon')), findsOneWidget);
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);

      final icon = tester.widget<Icon>(find.byKey(const Key('preview_widget_expand_icon')));
      expect(icon.icon, Icons.remove_circle_outline_rounded);
      expect(icon.color, Colors.black45);
    });

    testWidgets('Expand icon displays correctly in collapsed state', (WidgetTester tester) async {
      // Arrange - 设置收起状态
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证收起图标
      expect(find.byKey(const Key('preview_widget_expand_icon')), findsOneWidget);
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);

      final icon = tester.widget<Icon>(find.byKey(const Key('preview_widget_expand_icon')));
      expect(icon.icon, Icons.add_circle_outline);
      expect(icon.color, Colors.black45);
    });

    testWidgets('Expandable content container structure is correct', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证可展开内容容器
      expect(find.byKey(const Key('preview_widget_expandable_content')), findsOneWidget);

      final expandableContainer = tester.widget<Container>(find.byKey(const Key('preview_widget_expandable_content')));
      final decoration = expandableContainer.decoration as BoxDecoration;
      expect(decoration.color, Colors.black12);
      expect(
        decoration.borderRadius,
        const BorderRadius.only(bottomLeft: Radius.circular(8), bottomRight: Radius.circular(8)),
      );

      // 验证包含Obx组件
      expect(
        find.descendant(of: find.byKey(const Key('preview_widget_expandable_content')), matching: find.byType(Obx)),
        findsOneWidget,
      );
    });

    testWidgets('Expanded content displays correctly when expand is true', (WidgetTester tester) async {
      // Arrange - 设置展开状态
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证展开内容
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsOneWidget);

      // 验证具体内容文本存在（重点验证关键字段）
      expect(find.text('資産数'), findsWidgets);
      expect(find.text('資産種類'), findsWidgets);

      // 验证包含特定文本的内容
      expect(find.textContaining('件'), findsWidgets);
      expect(find.textContaining('登録済み'), findsWidgets);

      // 验证右箭头图标
      expect(find.byIcon(Icons.keyboard_arrow_right), findsAtLeastNWidgets(1));
    });

    testWidgets('Collapsed content displays correctly when expand is false', (WidgetTester tester) async {
      // Arrange - 设置收起状态
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证收起内容
      expect(find.byKey(const Key('preview_widget_collapsed_container')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsNothing);

      // 验证展开内容不显示
      expect(find.text('資産数'), findsNothing);
      expect(find.text('10件'), findsNothing);
      expect(find.text('資産種類'), findsNothing);
      expect(find.text('登録済みの資産種類'), findsNothing);
    });

    testWidgets('Static container structure is correct', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证静态容器
      expect(find.byKey(const Key('preview_widget_static_container')), findsOneWidget);

      final staticContainer = tester.widget<Container>(find.byKey(const Key('preview_widget_static_container')));
      expect(staticContainer.margin, const EdgeInsets.only(top: 12));
      expect(staticContainer.padding, const EdgeInsets.all(12));

      final decoration = staticContainer.decoration as BoxDecoration;
      expect(decoration.color, Colors.white70);
      expect(decoration.borderRadius, BorderRadius.circular(8));
      expect(decoration.boxShadow, isNotNull);
    });

    testWidgets('Static content displays correctly', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证静态内容（这些内容应该始终显示）
      expect(find.text('資産名'), findsWidgets);
      expect(find.textContaining('assetforce登録資産'), findsWidgets);
      expect(find.text('担当者'), findsWidgets);
      expect(find.textContaining('三住 太郎'), findsWidgets);
      expect(find.text('登録の日時'), findsWidgets);
      expect(find.textContaining('2022/01/01 12:00:00'), findsWidgets);

      // 验证静态容器中也有右箭头图标
      expect(
        find.descendant(
          of: find.byKey(const Key('preview_widget_static_container')),
          matching: find.byIcon(Icons.keyboard_arrow_right),
        ),
        findsOneWidget,
      );
    });

    testWidgets('Text content formatting is correct', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证文本格式
      // 检查冒号格式的文本
      expect(find.textContaining(':  assetforce登録資産'), findsOneWidget);
      expect(find.textContaining(':  三住 太郎'), findsOneWidget);
      expect(find.textContaining(':  2022/01/01 12:00:00'), findsOneWidget);

      // 验证粗体文本样式
      final assetNameText = tester.widget<Text>(find.textContaining(':  assetforce登録資産'));
      expect(assetNameText.style?.fontWeight, FontWeight.bold);
    });

    testWidgets('Layout spacing and margins are correct', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证布局间距
      // 验证SizedBox间距
      expect(find.byType(SizedBox), findsAtLeastNWidgets(1));

      // 验证动画容器的margin
      final animatedContainer = tester.widget<AnimatedContainer>(
        find.byKey(const Key('preview_widget_animated_container')),
      );
      expect(animatedContainer.margin, const EdgeInsets.only(top: 12));

      // 验证静态容器的margin
      final staticContainer = tester.widget<Container>(find.byKey(const Key('preview_widget_static_container')));
      expect(staticContainer.margin, const EdgeInsets.only(top: 12));
    });

    testWidgets('All visual elements have proper styling', (WidgetTester tester) async {
      // Arrange & Act
      await pumpWidget(tester);

      // Assert - 验证视觉样式元素
      // 验证阴影效果
      final animatedContainer = tester.widget<AnimatedContainer>(
        find.byKey(const Key('preview_widget_animated_container')),
      );
      final animatedDecoration = animatedContainer.decoration as BoxDecoration;
      expect(animatedDecoration.boxShadow!.first.color, Colors.grey.withAlpha(51));
      expect(animatedDecoration.boxShadow!.first.blurRadius, 5.0);
      expect(animatedDecoration.boxShadow!.first.offset, const Offset(0, 2));

      // 验证圆角
      expect(animatedDecoration.borderRadius, BorderRadius.circular(8));

      // 验证静态容器的样式
      final staticContainer = tester.widget<Container>(find.byKey(const Key('preview_widget_static_container')));
      final staticDecoration = staticContainer.decoration as BoxDecoration;
      expect(staticDecoration.borderRadius, BorderRadius.circular(8));
    });
  });

  // Phase 2: 响应式状态测试
  group('Phase 2: Responsive State Testing', () {
    testWidgets('Expand icon displays correctly when expand is false', (WidgetTester tester) async {
      // Arrange - 设置收起状态
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证收起图标
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsNothing);
    });

    testWidgets('Expand icon displays correctly when expand is true', (WidgetTester tester) async {
      // Arrange - 设置展开状态
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证展开图标
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.byIcon(Icons.add_circle_outline), findsNothing);
    });

    testWidgets('Expandable content shows collapsed container when expand is false', (WidgetTester tester) async {
      // Arrange - 设置收起状态
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证收起状态下的内容
      expect(find.byKey(const Key('preview_widget_collapsed_container')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsNothing);
      expect(find.text('資産数'), findsNothing);
    });

    testWidgets('Expandable content shows expanded content when expand is true', (WidgetTester tester) async {
      // Arrange - 设置展开状态
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证展开状态下的内容
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_collapsed_container')), findsNothing);
      expect(find.text('資産数'), findsWidgets);
    });

    testWidgets('Animated container configuration is consistent in collapsed state', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证动画容器配置（收起状态）
      final container = tester.widget<AnimatedContainer>(find.byKey(const Key('preview_widget_animated_container')));
      expect(container.duration, const Duration(milliseconds: 300));
      expect(container.margin, const EdgeInsets.only(top: 12));
    });

    testWidgets('Animated container configuration is consistent in expanded state', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证动画容器配置（展开状态）
      final container = tester.widget<AnimatedContainer>(find.byKey(const Key('preview_widget_animated_container')));
      expect(container.duration, const Duration(milliseconds: 300));
      expect(container.margin, const EdgeInsets.only(top: 12));
    });

    testWidgets('Header content displays correctly in collapsed state', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证头部内容（收起状态）
      expect(find.text('資産情報'), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_header_container')), findsOneWidget);
    });

    testWidgets('Header content displays correctly in expanded state', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证头部内容（展开状态）
      expect(find.text('資産情報'), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_header_container')), findsOneWidget);
    });

    testWidgets('Static content is visible in collapsed state', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证静态内容（收起状态）
      expect(find.byKey(const Key('preview_widget_static_container')), findsOneWidget);
      expect(find.text('資産名'), findsWidgets);
      expect(find.text('担当者'), findsWidgets);
    });

    testWidgets('Static content is visible in expanded state', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证静态内容（展开状态）
      expect(find.byKey(const Key('preview_widget_static_container')), findsOneWidget);
      expect(find.text('資産名'), findsWidgets);
      expect(find.text('担当者'), findsWidgets);
    });

    testWidgets('Text styling works correctly with normal font size', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: 'normal', expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证normal状态下的文本样式
      expect(find.text(' 表示例'), findsOneWidget);
      final titleText = tester.widget<Text>(find.text(' 表示例'));
      expect(titleText.style?.color, Colors.white);
    });

    testWidgets('Text styling works correctly with big font size', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: 'big', expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证big状态下的文本正确显示
      expect(find.text(' 表示例'), findsOneWidget);
      final titleText = tester.widget<Text>(find.text(' 表示例'));
      expect(titleText.style?.color, Colors.white);
    });

    testWidgets('Combined state works correctly - normal font + collapsed', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: 'normal', expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证组合状态
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_collapsed_container')), findsOneWidget);
    });

    testWidgets('Combined state works correctly - big font + expanded', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: 'big', expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证组合状态
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsOneWidget);
    });

    testWidgets('Obx widgets are present and functional', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证Obx组件存在并工作
      expect(find.byType(Obx), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    });

    testWidgets('Widget rebuilds correctly after setup', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证widget正确构建
      final container = tester.widget<AnimatedContainer>(find.byKey(const Key('preview_widget_animated_container')));
      expect(container.duration, const Duration(milliseconds: 300));
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    });

    testWidgets('Edge case: null or invalid expand state handling', (WidgetTester tester) async {
      // Arrange - 使用默认状态（应该是安全的）
      Get.reset();
      mockController = MockFontSizeSettingController();
      when(mockController.selectedFontSize).thenReturn('normal'.obs);
      // 不设置expand状态，测试默认行为
      when(mockController.expand).thenReturn(true.obs); // 给一个默认值
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);
      Get.put<FontSizeSettingController>(mockController);

      // Act
      await pumpWidget(tester);

      // Assert - 验证widget仍然正常渲染
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expand_icon')), findsOneWidget);
    });

    testWidgets('State persistence during widget lifecycle', (WidgetTester tester) async {
      // Arrange - 设置初始状态
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 模拟widget重建（如页面重新打开）
      await tester.pumpAndSettle();

      // Assert - 验证状态保持
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsOneWidget);

      // Act - 再次重建
      await tester.pump(const Duration(milliseconds: 300));

      // Assert - 验证状态仍然保持
      expect(find.byIcon(Icons.remove_circle_outline_rounded), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expanded_padding')), findsOneWidget);
    });
  });

  // Phase 3: 用户交互测试
  group('Phase 3: User Interaction Testing', () {
    testWidgets('Tapping gesture detector triggers expand state change', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Assert - 验证初始状态
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);

      // Act - 点击手势检测器
      await tester.tap(find.byKey(const Key('preview_widget_expand_gesture')));
      await tester.pumpAndSettle();

      // Note: 由于我们使用mock controller，实际的状态变化不会发生
      // 但我们可以验证gesture detector是可点击的
      expect(find.byKey(const Key('preview_widget_expand_gesture')), findsOneWidget);
    });

    testWidgets('Gesture detector has proper tap callback configuration', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act & Assert - 验证手势检测器配置
      final gestureDetector = tester.widget<GestureDetector>(find.byKey(const Key('preview_widget_expand_gesture')));
      expect(gestureDetector.onTap, isNotNull);
      expect(gestureDetector.child, isA<AnimatedContainer>());
    });

    testWidgets('Expand icon area is tappable', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 点击图标区域
      await tester.tap(find.byKey(const Key('preview_widget_expand_icon')));
      await tester.pumpAndSettle();

      // Assert - 验证点击没有错误
      expect(find.byKey(const Key('preview_widget_expand_icon')), findsOneWidget);
    });

    testWidgets('Header container area is interactive', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 点击头部容器
      await tester.tap(find.byKey(const Key('preview_widget_header_container')), warnIfMissed: false);
      await tester.pumpAndSettle();

      // Assert - 验证头部容器可交互
      expect(find.byKey(const Key('preview_widget_header_container')), findsOneWidget);
    });

    testWidgets('Expandable content area handles taps correctly', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act & Assert - 验证可展开内容容器可点击
      expect(find.byKey(const Key('preview_widget_expandable_content')), findsOneWidget);
      await tester.tap(find.byKey(const Key('preview_widget_expandable_content')), warnIfMissed: false);
      await tester.pumpAndSettle();

      // 验证点击后没有错误
      expect(find.byKey(const Key('preview_widget_expandable_content')), findsOneWidget);
    });

    testWidgets('Static container area handles taps correctly', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 点击静态容器
      await tester.tap(find.byKey(const Key('preview_widget_static_container')));
      await tester.pumpAndSettle();

      // Assert - 验证静态容器可点击
      expect(find.byKey(const Key('preview_widget_static_container')), findsOneWidget);
    });

    testWidgets('Multiple rapid taps are handled gracefully', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 快速多次点击
      for (int i = 0; i < 5; i++) {
        await tester.tap(find.byKey(const Key('preview_widget_expand_gesture')));
        await tester.pump(const Duration(milliseconds: 50));
      }
      await tester.pumpAndSettle();

      // Assert - 验证widget仍然稳定
      expect(find.byKey(const Key('preview_widget_expand_gesture')), findsOneWidget);
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Touch target areas are adequately sized', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act & Assert - 验证触摸目标大小
      final gestureDetectorSize = tester.getSize(find.byKey(const Key('preview_widget_expand_gesture')));
      expect(gestureDetectorSize.width, greaterThan(40)); // 最小触摸目标
      expect(gestureDetectorSize.height, greaterThan(40));

      final headerSize = tester.getSize(find.byKey(const Key('preview_widget_header_container')));
      expect(headerSize.width, greaterThan(0));
      expect(headerSize.height, greaterThan(0));
    });

    testWidgets('Gesture detector responds to different touch events', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act & Assert - 测试不同的触摸事件
      final gestureDetector = find.byKey(const Key('preview_widget_expand_gesture'));

      // 测试tap down
      await tester.startGesture(tester.getCenter(gestureDetector));
      await tester.pump();

      // 测试tap up
      await tester.tapAt(tester.getCenter(gestureDetector));
      await tester.pumpAndSettle();

      // Assert - 验证没有异常
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Widget handles touch events outside interactive areas', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 点击非交互区域（主列）
      await tester.tap(find.byKey(const Key('preview_widget_main_column')), warnIfMissed: false);
      await tester.pumpAndSettle();

      // Assert - 验证点击非交互区域不会出错
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
    });

    testWidgets('Interactive elements maintain accessibility', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act & Assert - 验证可访问性
      final gestureDetector = tester.widget<GestureDetector>(find.byKey(const Key('preview_widget_expand_gesture')));
      // GestureDetector.behavior 可以为null，这是正常的
      expect(gestureDetector.behavior, anyOf(isNull, isA<HitTestBehavior>()));

      // 验证图标可访问性
      final icon = tester.widget<Icon>(find.byKey(const Key('preview_widget_expand_icon')));
      expect(icon.icon, isNotNull);
      expect(icon.color, isNotNull);
    });

    testWidgets('Gesture conflicts are handled properly', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 测试潜在的手势冲突
      final gestureDetectorFinder = find.byKey(const Key('preview_widget_expand_gesture'));
      final iconFinder = find.byKey(const Key('preview_widget_expand_icon'));

      // 点击gesture detector
      await tester.tap(gestureDetectorFinder);
      await tester.pump();

      // 点击图标区域
      await tester.tap(iconFinder);
      await tester.pumpAndSettle();

      // Assert - 验证没有手势冲突
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Long press events are handled correctly', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 测试长按
      await tester.longPress(find.byKey(const Key('preview_widget_expand_gesture')));
      await tester.pumpAndSettle();

      // Assert - 验证长按不会导致错误
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_expand_gesture')), findsOneWidget);
    });

    testWidgets('Touch feedback is appropriate', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 模拟触摸反馈
      final gesture = await tester.startGesture(
        tester.getCenter(find.byKey(const Key('preview_widget_expand_gesture'))),
      );
      await tester.pump(const Duration(milliseconds: 100));
      await gesture.up();
      await tester.pumpAndSettle();

      // Assert - 验证触摸反馈正常
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Interaction works correctly with different screen sizes', (WidgetTester tester) async {
      // Arrange - 设置不同的屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(400, 800));
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 点击交互元素
      await tester.tap(find.byKey(const Key('preview_widget_expand_gesture')));
      await tester.pumpAndSettle();

      // Assert - 验证在不同屏幕尺寸下交互正常
      expect(find.byType(PreviewWidget), findsOneWidget);

      // 恢复默认屏幕尺寸
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('Simultaneous touches on different areas work correctly', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 模拟同时触摸不同区域（虽然在实际应用中不常见）
      await tester.tap(find.byKey(const Key('preview_widget_header_container')), warnIfMissed: false);
      await tester.pump(const Duration(milliseconds: 10));
      await tester.tap(find.byKey(const Key('preview_widget_static_container')));
      await tester.pumpAndSettle();

      // Assert - 验证多点触摸处理正常
      expect(find.byType(PreviewWidget), findsOneWidget);
    });
  });

  // Phase 4: 边界条件测试
  group('Phase 4: Boundary Conditions Testing', () {
    testWidgets('Widget handles extreme font size values', (WidgetTester tester) async {
      // Arrange - 极端字体大小值
      setupMockController(selectedFontSize: 'extremely_large_font_size_that_might_not_exist', expand: true);

      // Act
      await pumpWidget(tester);

      // Assert - 验证widget仍然正常渲染
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
    });

    testWidgets('Widget handles empty font size values', (WidgetTester tester) async {
      // Arrange
      setupMockController(selectedFontSize: '', expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证widget处理空字体大小值
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.text(' 表示例'), findsOneWidget); // 标题文本应该仍然显示
    });

    testWidgets('Widget maintains stability with rapid rendering', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 快速重绘测试
      for (int i = 0; i < 10; i++) {
        await tester.pump(const Duration(milliseconds: 16)); // 60fps
      }
      await tester.pumpAndSettle();

      // Assert - 验证widget保持稳定
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
    });

    testWidgets('Widget performance under reasonable stress', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);
      await pumpWidget(tester);

      // Act - 适度压力测试
      final stopwatch = Stopwatch()..start();
      for (int i = 0; i < 50; i++) {
        await tester.pump(const Duration(milliseconds: 1));
      }
      stopwatch.stop();

      // Assert - 验证性能在合理范围内
      expect(stopwatch.elapsedMilliseconds, lessThan(3000)); // 3秒内完成50次重绘
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Widget handles controller re-creation gracefully', (WidgetTester tester) async {
      // Arrange & Act - 模拟controller重新创建
      for (int i = 0; i < 3; i++) {
        setupMockController(expand: i % 2 == 0);
        await pumpWidget(tester);
        await tester.pumpAndSettle();

        // 验证每次都能正常渲染
        expect(find.byType(PreviewWidget), findsOneWidget);

        // 清理
        Get.reset();
      }

      // 最后一次验证
      setupMockController(expand: true);
      await pumpWidget(tester);
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Widget handles basic data integrity', (WidgetTester tester) async {
      // Arrange - 基本controller配置
      setupMockController(selectedFontSize: 'normal', expand: false);

      // Act
      await pumpWidget(tester);

      // Assert - 验证基本数据完整性
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_main_column')), findsOneWidget);
      expect(find.text(' 表示例'), findsOneWidget);
    });

    testWidgets('Widget handles animation states correctly', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 测试动画相关操作
      await tester.tap(find.byKey(const Key('preview_widget_expand_gesture')));
      await tester.pump(const Duration(milliseconds: 100)); // 动画进行中

      // 再次操作
      await tester.tap(find.byKey(const Key('preview_widget_expand_gesture')));
      await tester.pump(const Duration(milliseconds: 50));
      await tester.pumpAndSettle();

      // Assert - 验证动画操作不会导致问题
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.byKey(const Key('preview_widget_animated_container')), findsOneWidget);
    });

    testWidgets('Widget handles different themes correctly', (WidgetTester tester) async {
      // Arrange - 使用深色主题
      setupMockController(expand: true);

      // Act - 在深色主题下渲染
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.dark(),
          home: const Scaffold(body: PreviewWidget()),
        ),
      );

      // Assert - 验证在不同主题下正常工作
      expect(find.byType(PreviewWidget), findsOneWidget);
      expect(find.text(' 表示例'), findsOneWidget);
    });

    testWidgets('Widget handles GetX lifecycle changes', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: false);
      await pumpWidget(tester);

      // Act - 模拟GetX生命周期变化
      final originalController = Get.find<FontSizeSettingController>();
      expect(originalController, isNotNull);

      // 重新设置controller
      setupMockController(expand: true);
      await tester.pump();
      await tester.pumpAndSettle();

      // Assert - 验证widget仍然正常工作
      expect(find.byType(PreviewWidget), findsOneWidget);
    });

    testWidgets('Widget rendering performance is acceptable', (WidgetTester tester) async {
      // Arrange
      setupMockController(expand: true);

      // Act - 测量渲染时间
      final renderStartTime = DateTime.now();
      await pumpWidget(tester);
      final renderEndTime = DateTime.now();

      // Assert - 验证渲染时间在合理范围内
      final renderDuration = renderEndTime.difference(renderStartTime);
      expect(renderDuration.inMilliseconds, lessThan(500)); // 500ms内完成渲染
      expect(find.byType(PreviewWidget), findsOneWidget);
    });
  });
}
