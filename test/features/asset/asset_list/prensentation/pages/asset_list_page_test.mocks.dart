// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_list/prensentation/pages/asset_list_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i11;
import 'dart:ui' as _i17;

import 'package:asset_force_mobile_v2/core/event_bus/event_bus_core.dart'
    as _i18;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i14;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i6;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_asset_type_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/controllers/asset_list_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_list_ui_model.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/states/asset_list_ui_state.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart'
    as _i15;
import 'package:flutter/material.dart' as _i12;
import 'package:get/get.dart' as _i8;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i16;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetAssetTypeUseCase_0 extends _i1.SmartFake
    implements _i2.GetAssetTypeUseCase {
  _FakeGetAssetTypeUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetSearchIdUseCase_1 extends _i1.SmartFake
    implements _i3.GetSearchIdUseCase {
  _FakeGetSearchIdUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoadDataUseCase_2 extends _i1.SmartFake
    implements _i4.LoadDataUseCase {
  _FakeLoadDataUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_3 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_4 extends _i1.SmartFake implements _i6.DialogService {
  _FakeDialogService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetListUIState_5 extends _i1.SmartFake
    implements _i7.AssetListUIState {
  _FakeAssetListUIState_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_6<T> extends _i1.SmartFake
    implements _i8.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeObject_7 extends _i1.SmartFake implements Object {
  _FakeObject_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoadDataResult_8 extends _i1.SmartFake
    implements _i9.LoadDataResult {
  _FakeLoadDataResult_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AssetListController].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetListController extends _i1.Mock
    implements _i10.AssetListController {
  @override
  _i2.GetAssetTypeUseCase get getAssetTypeUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getAssetTypeUseCase),
            returnValue: _FakeGetAssetTypeUseCase_0(
              this,
              Invocation.getter(#getAssetTypeUseCase),
            ),
            returnValueForMissingStub: _FakeGetAssetTypeUseCase_0(
              this,
              Invocation.getter(#getAssetTypeUseCase),
            ),
          )
          as _i2.GetAssetTypeUseCase);

  @override
  _i3.GetSearchIdUseCase get getSearchIdUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getSearchIdUseCase),
            returnValue: _FakeGetSearchIdUseCase_1(
              this,
              Invocation.getter(#getSearchIdUseCase),
            ),
            returnValueForMissingStub: _FakeGetSearchIdUseCase_1(
              this,
              Invocation.getter(#getSearchIdUseCase),
            ),
          )
          as _i3.GetSearchIdUseCase);

  @override
  _i4.LoadDataUseCase get loadDataUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#loadDataUseCase),
            returnValue: _FakeLoadDataUseCase_2(
              this,
              Invocation.getter(#loadDataUseCase),
            ),
            returnValueForMissingStub: _FakeLoadDataUseCase_2(
              this,
              Invocation.getter(#loadDataUseCase),
            ),
          )
          as _i4.LoadDataUseCase);

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  _i6.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_4(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_4(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i6.DialogService);

  @override
  _i7.AssetListUIState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeAssetListUIState_5(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeAssetListUIState_5(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i7.AssetListUIState);

  @override
  _i8.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i8.InternalFinalCallback<void>);

  @override
  _i8.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i8.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  Object get eventBusScopeKey =>
      (super.noSuchMethod(
            Invocation.getter(#eventBusScopeKey),
            returnValue: _FakeObject_7(
              this,
              Invocation.getter(#eventBusScopeKey),
            ),
            returnValueForMissingStub: _FakeObject_7(
              this,
              Invocation.getter(#eventBusScopeKey),
            ),
          )
          as Object);

  @override
  set eventBusScopeKey(Object? _eventBusScopeKey) => super.noSuchMethod(
    Invocation.setter(#eventBusScopeKey, _eventBusScopeKey),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  int parseInitialAssetTypeId([dynamic arguments]) =>
      (super.noSuchMethod(
            Invocation.method(#parseInitialAssetTypeId, [arguments]),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  _i11.Future<void> initialize([dynamic arguments]) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [arguments]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void initializeListener(_i12.ScrollController? controller) =>
      super.noSuchMethod(
        Invocation.method(#initializeListener, [controller]),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> refreshData() =>
      (super.noSuchMethod(
            Invocation.method(#refreshData, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> loadMoreData() =>
      (super.noSuchMethod(
            Invocation.method(#loadMoreData, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void toggleDisplayMode() => super.noSuchMethod(
    Invocation.method(#toggleDisplayMode, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> toDetailPage(_i13.AssetUIModel? assetUIDisplay) =>
      (super.noSuchMethod(
            Invocation.method(#toDetailPage, [assetUIDisplay]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> onClickAssetTypeSelect() =>
      (super.noSuchMethod(
            Invocation.method(#onClickAssetTypeSelect, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> onClickStartToCategory() =>
      (super.noSuchMethod(
            Invocation.method(#onClickStartToCategory, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> onClickSearchConditionEnter() =>
      (super.noSuchMethod(
            Invocation.method(#onClickSearchConditionEnter, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> onClickClearSearch() =>
      (super.noSuchMethod(
            Invocation.method(#onClickClearSearch, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void clearCategory() => super.noSuchMethod(
    Invocation.method(#clearCategory, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> onClickClearCategory() =>
      (super.noSuchMethod(
            Invocation.method(#onClickClearCategory, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> categorySearchResultEmpty() =>
      (super.noSuchMethod(
            Invocation.method(#categorySearchResultEmpty, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void handleScrollToTopVisibility() => super.noSuchMethod(
    Invocation.method(#handleScrollToTopVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void handleLoadMoreTrigger() => super.noSuchMethod(
    Invocation.method(#handleLoadMoreTrigger, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> prepareForLoading(
    bool? firstLoad,
    int? targetAssetTypeId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#prepareForLoading, [
              firstLoad,
              targetAssetTypeId,
            ]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> finishLoading(bool? firstLoad) =>
      (super.noSuchMethod(
            Invocation.method(#finishLoading, [firstLoad]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> loadAssetType() =>
      (super.noSuchMethod(
            Invocation.method(#loadAssetType, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> loadSearchId() =>
      (super.noSuchMethod(
            Invocation.method(#loadSearchId, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<_i9.LoadDataResult> loadAssetListData() =>
      (super.noSuchMethod(
            Invocation.method(#loadAssetListData, []),
            returnValue: _i11.Future<_i9.LoadDataResult>.value(
              _FakeLoadDataResult_8(
                this,
                Invocation.method(#loadAssetListData, []),
              ),
            ),
            returnValueForMissingStub: _i11.Future<_i9.LoadDataResult>.value(
              _FakeLoadDataResult_8(
                this,
                Invocation.method(#loadAssetListData, []),
              ),
            ),
          )
          as _i11.Future<_i9.LoadDataResult>);

  @override
  _i11.Future<void> handleLoadDataError(
    dynamic error,
    StackTrace? stackTrace,
    _i14.ErrorHandlingMode? errorMode,
    bool? firstLoad,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#handleLoadDataError, [
              error,
              stackTrace,
              errorMode,
              firstLoad,
            ]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> executeDataLoading(bool? firstLoad, bool? isRefresh) =>
      (super.noSuchMethod(
            Invocation.method(#executeDataLoading, [firstLoad, isRefresh]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> loadData({
    bool? firstLoad = false,
    bool? isRefresh = false,
    dynamic errorHandlingMode = _i14.ErrorHandlingMode.dialog,
    int? targetAssetTypeId = 0,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loadData, [], {
              #firstLoad: firstLoad,
              #isRefresh: isRefresh,
              #errorHandlingMode: errorHandlingMode,
              #targetAssetTypeId: targetAssetTypeId,
            }),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void updateStateWithData(
    _i9.LoadDataResult? result, {
    bool? isRefresh = false,
  }) => super.noSuchMethod(
    Invocation.method(#updateStateWithData, [result], {#isRefresh: isRefresh}),
    returnValueForMissingStub: null,
  );

  @override
  void updateBasicState(_i9.LoadDataResult? result) => super.noSuchMethod(
    Invocation.method(#updateBasicState, [result]),
    returnValueForMissingStub: null,
  );

  @override
  void updateDataList(
    List<_i13.AssetUIModel>? assetList, {
    bool? isRefresh = false,
    required int? totalCount,
  }) => super.noSuchMethod(
    Invocation.method(
      #updateDataList,
      [assetList],
      {#isRefresh: isRefresh, #totalCount: totalCount},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void updateCountAndResetError(int? searchAssetCount) => super.noSuchMethod(
    Invocation.method(#updateCountAndResetError, [searchAssetCount]),
    returnValueForMissingStub: null,
  );

  @override
  void handleCategoryEmptyResult(int? searchAssetCount) => super.noSuchMethod(
    Invocation.method(#handleCategoryEmptyResult, [searchAssetCount]),
    returnValueForMissingStub: null,
  );

  @override
  void checkCategoryState() => super.noSuchMethod(
    Invocation.method(#checkCategoryState, []),
    returnValueForMissingStub: null,
  );

  @override
  bool isValidAssetType(_i15.AssetTypeListModel? assetType) =>
      (super.noSuchMethod(
            Invocation.method(#isValidAssetType, [assetType]),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void updateAssetType(_i15.AssetTypeListModel? assetType) =>
      super.noSuchMethod(
        Invocation.method(#updateAssetType, [assetType]),
        returnValueForMissingStub: null,
      );

  @override
  void scrollToTop(_i12.ScrollController? scrollController) =>
      super.noSuchMethod(
        Invocation.method(#scrollToTop, [scrollController]),
        returnValueForMissingStub: null,
      );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i14.ErrorHandlingMode? mode = _i14.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i16.Disposer addListener(_i16.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i16.Disposer);

  @override
  void removeListener(_i17.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i17.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i16.Disposer addListenerId(Object? key, _i16.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i16.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void onEvent<T>(_i18.AsyncEventHandler<T>? handler) => super.noSuchMethod(
    Invocation.method(#onEvent, [handler]),
    returnValueForMissingStub: null,
  );

  @override
  void onceEvent<T>(_i18.AsyncEventHandler<T>? handler) => super.noSuchMethod(
    Invocation.method(#onceEvent, [handler]),
    returnValueForMissingStub: null,
  );
}
