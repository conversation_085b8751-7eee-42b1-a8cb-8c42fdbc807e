import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/me/message/data/models/notification_model.dart';
import 'package:asset_force_mobile_v2/features/me/message/domain/repositories/message_repository.dart';
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_usecase.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
@GenerateMocks([MessageRepository])
import 'message_usecase_test.mocks.dart';

/// 测试数据辅助类
class MessageUseCaseTestData {
  /// 创建基础查询对象
  static NotificationListQuery createBasicQuery({int? startRow, int? endRow}) {
    return NotificationListQuery(startRow: startRow ?? 0, endRow: endRow ?? 20);
  }

  /// 创建边界查询对象
  static List<NotificationListQuery> createBoundaryQueries() {
    return [
      NotificationListQuery(startRow: 0, endRow: 1), // 最小分页
      NotificationListQuery(startRow: 0, endRow: 100), // 大分页
      NotificationListQuery(startRow: 100, endRow: 200), // 中间分页
      NotificationListQuery(startRow: 0, endRow: 0), // 边界情况
      NotificationListQuery(startRow: -1, endRow: 10), // 负数开始
    ];
  }

  /// 创建通知模型列表
  static List<NotificationModel> createNotificationList(int count) {
    return List.generate(
      count,
      (index) => NotificationModel(
        notificationId: 1000 + index,
        notificationTitle: 'Test Notification $index',
        notificationBody: 'Test body content $index',
        activeStartDate: '2024-01-15 10:${(index % 60).toString().padLeft(2, '0')}:00',
        category: (index % 6) + 1,
        readStatus: index % 2 == 0 ? '0' : '1',
        fileInformation: index % 3 == 0 ? '[]' : null,
        createdById: 'user${index % 5}',
        createdDate: '2024-01-15 09:00:00',
        modifiedById: 'user${index % 5}',
        lastEditorName: 'Editor $index',
        modifiedDate: '2024-01-15 09:00:00',
      ),
    );
  }

  /// 创建期望的成功结果
  static LoadDataResult createSuccessResult({int? count, List<NotificationModel>? dataList}) {
    return LoadDataResult(count: count ?? 50, dataList: dataList ?? createNotificationList(20), success: true, msg: '');
  }

  /// 创建期望的失败结果
  static LoadDataResult createFailureResult({String? customMessage}) {
    return LoadDataResult(count: 0, dataList: [], success: false, msg: customMessage ?? 'データの読み込みに失敗しました');
  }
}

void main() {
  group('MessageListLoadUseCase', () {
    late MessageListLoadUseCase usecase;
    late MockMessageRepository mockMessageRepository;

    setUp(() {
      LogUtil.initialize();
      mockMessageRepository = MockMessageRepository();
      usecase = MessageListLoadUseCase(messageRepository: mockMessageRepository);
    });

    tearDown(() {
      reset(mockMessageRepository);
    });

    // Phase 1: 基础设施测试
    group('Phase 1: 基础设施测试', () {
      test('应该能够创建 MessageListLoadUseCase 实例', () {
        // Assert
        expect(usecase, isNotNull);
        expect(usecase, isA<MessageListLoadUseCase>());
      });

      test('应该实现 UseCase 接口', () {
        // Assert
        expect(usecase, isA<UseCase<LoadDataResult, NotificationListQuery>>());
      });

      test('应该正确注入 MessageRepository 依赖', () {
        // Arrange
        final testRepository = MockMessageRepository();

        // Act
        final testUsecase = MessageListLoadUseCase(messageRepository: testRepository);

        // Assert
        expect(testUsecase.messageRepository, equals(testRepository));
      });

      test('构造函数应该要求 MessageRepository 参数', () {
        // Act & Assert - 验证构造函数参数为required
        expect(() => MessageListLoadUseCase(messageRepository: mockMessageRepository), returnsNormally);
      });

      test('应该具有正确的方法签名', () {
        // Assert - 验证call方法签名
        expect(usecase.call, isA<Future<LoadDataResult> Function(NotificationListQuery)>());
      });

      test('应该能够访问messageRepository属性', () {
        // Assert
        expect(usecase.messageRepository, equals(mockMessageRepository));
        expect(usecase.messageRepository, isA<MessageRepository>());
      });

      test('不同实例应该有独立的repository引用', () {
        // Arrange
        final repository1 = MockMessageRepository();
        final repository2 = MockMessageRepository();

        // Act
        final usecase1 = MessageListLoadUseCase(messageRepository: repository1);
        final usecase2 = MessageListLoadUseCase(messageRepository: repository2);

        // Assert
        expect(usecase1.messageRepository, equals(repository1));
        expect(usecase2.messageRepository, equals(repository2));
        expect(usecase1.messageRepository, isNot(equals(usecase2.messageRepository)));
      });

      test('应该继承UseCase的所有特性', () {
        // Assert - 验证泛型参数正确
        expect(usecase, isA<UseCase<LoadDataResult, NotificationListQuery>>());

        // 验证可以作为UseCase使用
        UseCase<LoadDataResult, NotificationListQuery> genericUsecase = usecase;
        expect(genericUsecase, isNotNull);
      });
    });

    // NotificationListQuery 类测试
    group('NotificationListQuery 类测试', () {
      test('应该使用正确的默认值创建', () {
        // Act
        final query = NotificationListQuery();

        // Assert
        expect(query.startRow, equals(0));
        expect(query.endRow, equals(20));
      });

      test('应该正确设置自定义值', () {
        // Act
        final query = NotificationListQuery(startRow: 10, endRow: 30);

        // Assert
        expect(query.startRow, equals(10));
        expect(query.endRow, equals(30));
      });

      test('toJson方法应该返回正确的格式', () {
        // Arrange
        final query = NotificationListQuery(startRow: 5, endRow: 25);

        // Act
        final json = query.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['startRow'], equals(5));
        expect(json['endRow'], equals(25));
      });

      test('toJson方法应该包含所有必需字段', () {
        // Arrange
        final query = NotificationListQuery();

        // Act
        final json = query.toJson();

        // Assert
        expect(json.keys, containsAll(['startRow', 'endRow']));
        expect(json.keys.length, equals(2));
      });
    });

    // LoadDataResult 类测试
    group('LoadDataResult 类测试', () {
      test('应该正确创建成功结果', () {
        // Arrange
        final testData = MessageUseCaseTestData.createNotificationList(5);

        // Act
        final result = LoadDataResult(count: 100, dataList: testData);

        // Assert
        expect(result.count, equals(100));
        expect(result.dataList, equals(testData));
        expect(result.success, isTrue); // 默认为true
        expect(result.msg, equals('')); // 默认为空字符串
      });

      test('应该正确创建失败结果', () {
        // Act
        final result = LoadDataResult(count: 0, dataList: [], success: false, msg: 'Error occurred');

        // Assert
        expect(result.count, equals(0));
        expect(result.dataList, isEmpty);
        expect(result.success, isFalse);
        expect(result.msg, equals('Error occurred'));
      });

      test('应该正确处理所有字段组合', () {
        // Arrange
        final testData = MessageUseCaseTestData.createNotificationList(3);

        // Act
        final result = LoadDataResult(count: 50, dataList: testData, success: true, msg: 'Custom message');

        // Assert
        expect(result.count, equals(50));
        expect(result.dataList, hasLength(3));
        expect(result.success, isTrue);
        expect(result.msg, equals('Custom message'));
      });
    });

    // Phase 2: 核心业务逻辑测试
    group('Phase 2: 核心业务逻辑测试', () {
      group('2.1 正常流程测试', () {
        test('应该正确协调双API调用并返回成功结果', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery(startRow: 10, endRow: 30);
          final expectedCount = 150;
          final expectedList = MessageUseCaseTestData.createNotificationList(20);

          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => expectedCount);
          when(mockMessageRepository.getNotificationList(query: query)).thenAnswer((_) async => expectedList);

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result, isA<LoadDataResult>());
          expect(result.success, isTrue);
          expect(result.count, equals(expectedCount));
          expect(result.dataList, equals(expectedList));
          expect(result.msg, equals(''));

          // 验证API调用顺序和参数
          verifyInOrder([
            mockMessageRepository.getNotificationCount(),
            mockMessageRepository.getNotificationList(query: query),
          ]);
        });

        test('应该正确处理空数据列表', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          final expectedCount = 0;
          final expectedList = <NotificationModel>[];

          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => expectedCount);
          when(mockMessageRepository.getNotificationList(query: query)).thenAnswer((_) async => expectedList);

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isTrue);
          expect(result.count, equals(0));
          expect(result.dataList, isEmpty);
          expect(result.msg, equals(''));
        });

        test('应该正确处理大量数据', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery(startRow: 0, endRow: 100);
          final expectedCount = 10000;
          final expectedList = MessageUseCaseTestData.createNotificationList(100);

          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => expectedCount);
          when(mockMessageRepository.getNotificationList(query: query)).thenAnswer((_) async => expectedList);

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isTrue);
          expect(result.count, equals(10000));
          expect(result.dataList, hasLength(100));
        });

        test('应该正确传递分页参数', () async {
          // Arrange
          final boundaryQueries = MessageUseCaseTestData.createBoundaryQueries();

          for (final query in boundaryQueries) {
            when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 50);
            when(
              mockMessageRepository.getNotificationList(query: query),
            ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(5));

            // Act
            final result = await usecase.call(query);

            // Assert
            expect(result.success, isTrue);
            verify(mockMessageRepository.getNotificationList(query: query)).called(1);
          }
        });
      });

      group('2.2 异常处理测试', () {
        test('getNotificationCount失败应该返回失败结果', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenThrow(Exception('Count API failed'));

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isFalse);
          expect(result.count, equals(0));
          expect(result.dataList, isEmpty);
          expect(result.msg, equals('データの読み込みに失敗しました'));

          // 验证第一个API失败后不会调用第二个API
          verify(mockMessageRepository.getNotificationCount()).called(1);
          verifyNever(mockMessageRepository.getNotificationList(query: anyNamed('query')));
        });

        test('getNotificationList失败应该返回失败结果', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 100);
          when(mockMessageRepository.getNotificationList(query: query)).thenThrow(Exception('List API failed'));

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isFalse);
          expect(result.count, equals(0));
          expect(result.dataList, isEmpty);
          expect(result.msg, equals('データの読み込みに失敗しました'));

          // 验证两个API都被调用了
          verify(mockMessageRepository.getNotificationCount()).called(1);
          verify(mockMessageRepository.getNotificationList(query: query)).called(1);
        });

        test('网络异常应该被正确处理', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenThrow(ArgumentError('Network timeout'));

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isFalse);
          expect(result.count, equals(0));
          expect(result.dataList, isEmpty);
          expect(result.msg, equals('データの読み込みに失敗しました'));
        });

        test('不同类型异常都应该产生相同的失败结果', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          final exceptions = [
            Exception('General exception'),
            ArgumentError('Argument error'),
            StateError('State error'),
            FormatException('Format exception'),
          ];

          for (final exception in exceptions) {
            when(mockMessageRepository.getNotificationCount()).thenThrow(exception);

            // Act
            final result = await usecase.call(query);

            // Assert
            expect(result.success, isFalse);
            expect(result.count, equals(0));
            expect(result.dataList, isEmpty);
            expect(result.msg, equals('データの読み込みに失敗しました'));
          }
        });
      });

      group('2.3 数据一致性测试', () {
        test('查询参数应该完整传递给repository', () async {
          // Arrange
          final customQuery = NotificationListQuery(startRow: 50, endRow: 80);
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 200);
          when(
            mockMessageRepository.getNotificationList(query: customQuery),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(30));

          // Act
          await usecase.call(customQuery);

          // Assert - 验证exact参数传递
          final captured =
              verify(mockMessageRepository.getNotificationList(query: captureAnyNamed('query'))).captured.single
                  as NotificationListQuery;
          expect(captured.startRow, equals(50));
          expect(captured.endRow, equals(80));
        });

        test('repository返回的数据应该完整保留', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          final expectedCount = 99;
          final expectedList = MessageUseCaseTestData.createNotificationList(25);

          // 确保测试数据具有特定特征
          expectedList[0].notificationTitle = '特定测试标题';
          expectedList[0].category = 7;

          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => expectedCount);
          when(mockMessageRepository.getNotificationList(query: query)).thenAnswer((_) async => expectedList);

          // Act
          final result = await usecase.call(query);

          // Assert - 验证数据完整性
          expect(result.count, equals(99));
          expect(result.dataList, hasLength(25));
          expect(result.dataList[0].notificationTitle, equals('特定测试标题'));
          expect(result.dataList[0].category, equals(7));
          expect(identical(result.dataList, expectedList), isTrue); // 验证是同一个对象引用
        });

        test('count和dataList应该来自独立的API调用', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 1000);
          when(
            mockMessageRepository.getNotificationList(query: query),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(20));

          // Act
          final result = await usecase.call(query);

          // Assert - count和dataList数量不匹配是正常的（分页场景）
          expect(result.count, equals(1000));
          expect(result.dataList, hasLength(20));
          expect(result.success, isTrue);
        });
      });

      group('2.4 边界条件测试', () {
        test('处理极小分页请求', () async {
          // Arrange
          final query = NotificationListQuery(startRow: 0, endRow: 1);
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 1);
          when(
            mockMessageRepository.getNotificationList(query: query),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(1));

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isTrue);
          expect(result.count, equals(1));
          expect(result.dataList, hasLength(1));
        });

        test('处理零大小分页请求', () async {
          // Arrange
          final query = NotificationListQuery(startRow: 0, endRow: 0);
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 100);
          when(mockMessageRepository.getNotificationList(query: query)).thenAnswer((_) async => <NotificationModel>[]);

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isTrue);
          expect(result.count, equals(100));
          expect(result.dataList, isEmpty);
        });

        test('处理负数分页参数', () async {
          // Arrange
          final query = NotificationListQuery(startRow: -1, endRow: 10);
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 50);
          when(
            mockMessageRepository.getNotificationList(query: query),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(5));

          // Act
          final result = await usecase.call(query);

          // Assert - 应该正常处理，由repository决定如何处理负数
          expect(result.success, isTrue);
          verify(mockMessageRepository.getNotificationList(query: query)).called(1);
        });

        test('处理极大分页请求', () async {
          // Arrange
          final query = NotificationListQuery(startRow: 0, endRow: 10000);
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 50000);
          when(
            mockMessageRepository.getNotificationList(query: query),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(10000));

          // Act
          final result = await usecase.call(query);

          // Assert
          expect(result.success, isTrue);
          expect(result.count, equals(50000));
          expect(result.dataList, hasLength(10000));
        });
      });

      group('2.5 API调用时序测试', () {
        test('应该先调用getNotificationCount再调用getNotificationList', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 100);
          when(
            mockMessageRepository.getNotificationList(query: query),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(20));

          // Act
          await usecase.call(query);

          // Assert - 验证调用顺序
          verifyInOrder([
            mockMessageRepository.getNotificationCount(),
            mockMessageRepository.getNotificationList(query: query),
          ]);
        });

        test('第一个API失败时不应该调用第二个API', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenThrow(Exception('First API failed'));

          // Act
          await usecase.call(query);

          // Assert
          verify(mockMessageRepository.getNotificationCount()).called(1);
          verifyNever(mockMessageRepository.getNotificationList(query: anyNamed('query')));
        });

        test('多次调用应该产生多次API调用', () async {
          // Arrange
          final query = MessageUseCaseTestData.createBasicQuery();
          when(mockMessageRepository.getNotificationCount()).thenAnswer((_) async => 100);
          when(
            mockMessageRepository.getNotificationList(query: query),
          ).thenAnswer((_) async => MessageUseCaseTestData.createNotificationList(20));

          // Act - 调用3次
          await usecase.call(query);
          await usecase.call(query);
          await usecase.call(query);

          // Assert - 每个API都应该被调用3次
          verify(mockMessageRepository.getNotificationCount()).called(3);
          verify(mockMessageRepository.getNotificationList(query: query)).called(3);
        });
      });
    });
  });
}
