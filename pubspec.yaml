name: asset_force_mobile_v2
description: "flutter version of asset-force-mobile."
publish_to: 'none' # Prevents the package from being accidentally published to pub.dev.
                   # Keep this as 'none' during development or for private/internal projects.

version: 1.0.0+1   # Semantic versioning for the app.
                   # 1.0.0 is the version (major.minor.patch) of the app.
                   # +1 is the build number, used for tracking individual builds.
                   # Increment the build number (+1, +2, etc.) for every internal or QA build.

environment:
  sdk: ">=3.8.0 <4.0.0" # Flutter 3.32.5对应的Dart SDK版本
  flutter: "3.32.5" # 严格固定Flutter版本为3.32.5

dependencies:
  flutter:
    sdk: flutter

  image: ^4.5.3
  json_annotation: ^4.9.0
  mmkv: ^2.1.0
  dio: ^5.7.0
  flutter_dotenv: ^5.2.1
  intl: ^0.19.0
  get: ^4.6.6
  flutter_svg: ^2.0.16
  flutter_secure_storage: ^9.2.2
  cupertino_icons: ^1.0.8
  collection: ^1.18.0
  logger: ^2.5.0
  webview_flutter: ^4.10.0
  url_launcher: ^6.3.1
  crypto: ^3.0.6
  open_file: ^3.5.10
  local_auth: ^2.3.0
  image_picker: ^1.1.2
  file_picker: ^10.1.0
  get_auto_router_annotation:
    path: ./get_auto_router_annotation
  path_provider: ^2.1.5 # 显示依赖，解决 flutter analyze 提示， 实际上在 mmkv内部已经依赖了。
  package_info_plus: ^8.1.4
  app_links: ^6.4.0
  geolocator: ^13.0.2
  cached_network_image: ^3.3.0
  big_dart: ^1.0.1
  flutter_inappwebview: ^6.1.5
  permission_handler: ^12.0.1


dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.5
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  get_auto_router:
    path: ./get_auto_router

flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/env/
    - assets/fonts/
    - assets/icons/
    - assets/images/
    - assets/images/list/
    - assets/images/login/
    - assets/images/segment/
    - assets/images/tabs/
    - assets/jscode/
    - assets/intl_phone_code_flag/

  fonts:
    - family: NotoSansJP
      fonts:
        - asset: assets/fonts/NotoSansJP-Bold.otf
          weight: 700
        - asset: assets/fonts/NotoSansJP-Medium.otf
          weight: 500
        - asset: assets/fonts/NotoSansJP-Regular.otf
          weight: 400
    - family: CustomIcons
      fonts:
        - asset: assets/fonts/Material-Design-Iconic-Font.ttf
