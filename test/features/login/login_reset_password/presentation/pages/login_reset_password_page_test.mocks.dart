// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_reset_password/presentation/pages/login_reset_password_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;
import 'dart:ui' as _i12;

import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/usecases/reset_password_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/controllers/login_reset_password_controllers.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/states/reset_password_ui_state.dart'
    as _i7;
import 'package:flutter/material.dart' as _i6;
import 'package:get/get.dart' as _i8;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i11;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBiometricsSwitchUtil_0 extends _i1.SmartFake
    implements _i2.BiometricsSwitchUtil {
  _FakeBiometricsSwitchUtil_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResetPasswordUsecase_1 extends _i1.SmartFake
    implements _i3.ResetPasswordUsecase {
  _FakeResetPasswordUsecase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_2 extends _i1.SmartFake implements _i4.DialogService {
  _FakeDialogService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_3 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTextEditingController_4 extends _i1.SmartFake
    implements _i6.TextEditingController {
  _FakeTextEditingController_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResetPasswordUiState_5 extends _i1.SmartFake
    implements _i7.ResetPasswordUiState {
  _FakeResetPasswordUiState_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_6<T> extends _i1.SmartFake
    implements _i8.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginResetPasswordControllers].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginResetPasswordControllers extends _i1.Mock
    implements _i9.LoginResetPasswordControllers {
  @override
  _i2.BiometricsSwitchUtil get biometricsSwitchUtil =>
      (super.noSuchMethod(
            Invocation.getter(#biometricsSwitchUtil),
            returnValue: _FakeBiometricsSwitchUtil_0(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
            returnValueForMissingStub: _FakeBiometricsSwitchUtil_0(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
          )
          as _i2.BiometricsSwitchUtil);

  @override
  _i3.ResetPasswordUsecase get resetPasswordUsecase =>
      (super.noSuchMethod(
            Invocation.getter(#resetPasswordUsecase),
            returnValue: _FakeResetPasswordUsecase_1(
              this,
              Invocation.getter(#resetPasswordUsecase),
            ),
            returnValueForMissingStub: _FakeResetPasswordUsecase_1(
              this,
              Invocation.getter(#resetPasswordUsecase),
            ),
          )
          as _i3.ResetPasswordUsecase);

  @override
  _i4.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i4.DialogService);

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  _i6.TextEditingController get newPasswordController =>
      (super.noSuchMethod(
            Invocation.getter(#newPasswordController),
            returnValue: _FakeTextEditingController_4(
              this,
              Invocation.getter(#newPasswordController),
            ),
            returnValueForMissingStub: _FakeTextEditingController_4(
              this,
              Invocation.getter(#newPasswordController),
            ),
          )
          as _i6.TextEditingController);

  @override
  _i6.TextEditingController get newPasswordConfirmController =>
      (super.noSuchMethod(
            Invocation.getter(#newPasswordConfirmController),
            returnValue: _FakeTextEditingController_4(
              this,
              Invocation.getter(#newPasswordConfirmController),
            ),
            returnValueForMissingStub: _FakeTextEditingController_4(
              this,
              Invocation.getter(#newPasswordConfirmController),
            ),
          )
          as _i6.TextEditingController);

  @override
  _i7.ResetPasswordUiState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeResetPasswordUiState_5(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeResetPasswordUiState_5(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i7.ResetPasswordUiState);

  @override
  set param(_i3.ResetPasswordArgumentsModel? _param) => super.noSuchMethod(
    Invocation.setter(#param, _param),
    returnValueForMissingStub: null,
  );

  @override
  _i8.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i8.InternalFinalCallback<void>);

  @override
  _i8.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i8.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void initStateFromParam() => super.noSuchMethod(
    Invocation.method(#initStateFromParam, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Future<void> onCancel() =>
      (super.noSuchMethod(
            Invocation.method(#onCancel, []),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> updatePassword() =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, []),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListener(_i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i11.Disposer);

  @override
  void removeListener(_i12.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i12.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListenerId(Object? key, _i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i11.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
