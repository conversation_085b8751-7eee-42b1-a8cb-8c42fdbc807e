import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/data/repositories/tenant_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
import '../../../../../core/utils/dio_utils_test.mocks.dart';

@GenerateMocks([DioUtil])
void main() {
  group('TenantRepositoryImpl', () {
    late TenantRepositoryImpl repository;
    late MockDioUtil mockDioUtil;

    setUp(() {
      LogUtil.initialize();
      mockDioUtil = MockDioUtil();
      repository = TenantRepositoryImpl(dioUtil: mockDioUtil);
    });

    group('基础功能测试', () {
      test('应该能够创建 TenantRepositoryImpl 实例', () {
        expect(repository, isNotNull);
        expect(repository, isA<TenantRepositoryImpl>());
      });

      test('应该实现 TenantRepository 接口', () {
        expect(repository, isA<TenantRepository>());
      });

      test('应该混入 RepositoryErrorHandler', () {
        expect(repository, isA<RepositoryErrorHandler>());
      });
    });

    group('loginTenant() 方法测试', () {
      test('应该成功处理正常租户登录请求', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试
        final result = await repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).called(1);
      });

      test('应该正确处理生物识别标志转换', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = false;
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '0'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试
        final result = await repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用时生物识别标志正确转换
        verify(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '0'},
          ),
        ).called(1);
      });

      test('应该正确处理可选的 code 参数', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;
        const code = 'test_code';
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1', 'code': code},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试
        final result = await repository.loginTenant(
          ticket: ticket,
          tenantId: tenantId,
          isBiometrics: isBiometrics,
          code: code,
        );

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用包含 code 参数
        verify(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1', 'code': code},
          ),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - API 调用失败
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<SystemException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).called(1);
      });
    });

    group('getFullUserInfo() 方法测试', () {
      test('应该成功获取完整用户信息', () async {
        // 准备测试数据
        const successResponse = {
          'code': 200,
          'msg': 'success',
          'userInfo': {'id': 1, 'name': 'test_user', 'email': '<EMAIL>'},
        };

        // 设置 Mock 行为
        when(mockDioUtil.get(GlobalVariable.getFullUserInfo)).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getFullUserInfo),
          ),
        );

        // 执行测试
        final result = await repository.getFullUserInfo();

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用
        verify(mockDioUtil.get(GlobalVariable.getFullUserInfo)).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 设置 Mock 行为 - API 调用失败
        when(mockDioUtil.get(GlobalVariable.getFullUserInfo)).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.getFullUserInfo),
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.getFullUserInfo(), throwsA(isA<SystemException>()));

        // 验证 Mock 调用
        verify(mockDioUtil.get(GlobalVariable.getFullUserInfo)).called(1);
      });
    });

    group('getVerificationCode() 方法测试', () {
      test('应该成功获取验证码', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';
        const successResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
          ),
        );

        // 执行测试
        final result = await repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用时电话号码已格式化
        verify(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).called(1);
      });

      test('应该正确处理电话号码格式化', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+86';
        const phone = '138-8888-8888';
        const successResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '13888888888'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
          ),
        );

        // 执行测试
        final result = await repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用时电话号码已移除连字符
        verify(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '13888888888'},
          ),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';

        // 设置 Mock 行为 - API 调用失败
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone),
          throwsA(isA<SystemException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).called(1);
      });
    });

    group('getUserBindPhone() 方法测试', () {
      test('应该成功绑定用户手机', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';
        const userName = 'test_user';
        const password = 'test_password';
        const code = '123456';
        const tenantId = 'test_tenant';
        const userId = 1;
        const successResponse = {'code': 200, 'msg': 'success', 'bindResult': true};

        // 设置 Mock 行为
        when(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.userBindPhone),
          ),
        );

        // 执行测试
        final result = await repository.getUserBindPhone(
          ticket: ticket,
          countryCode: countryCode,
          phone: phone,
          userName: userName,
          password: password,
          code: code,
          tenantId: tenantId,
          userId: userId,
        );

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';
        const userName = 'test_user';
        const password = 'test_password';
        const code = '123456';
        const tenantId = 'test_tenant';
        const userId = 1;

        // 设置 Mock 行为 - API 调用失败
        when(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.userBindPhone),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getUserBindPhone(
            ticket: ticket,
            countryCode: countryCode,
            phone: phone,
            userName: userName,
            password: password,
            code: code,
            tenantId: tenantId,
            userId: userId,
          ),
          throwsA(isA<SystemException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });
    });

    group('getSendMfaMailVerifyCode() 方法测试', () {
      test('应该成功发送MFA邮件验证码', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const email = '<EMAIL>';
        const successResponse = {'code': 200, 'msg': 'success', 'emailSent': true};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.getEmailVerificationCode, queryParams: {'ticket': ticket, 'userName': email}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getEmailVerificationCode),
          ),
        );

        // 执行测试
        final result = await repository.getSendMfaMailVerifyCode(ticket: ticket, email: email);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.getEmailVerificationCode, queryParams: {'ticket': ticket, 'userName': email}),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const email = '<EMAIL>';

        // 设置 Mock 行为 - API 调用失败
        when(
          mockDioUtil.get(GlobalVariable.getEmailVerificationCode, queryParams: {'ticket': ticket, 'userName': email}),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.getEmailVerificationCode),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getSendMfaMailVerifyCode(ticket: ticket, email: email),
          throwsA(isA<SystemException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.getEmailVerificationCode, queryParams: {'ticket': ticket, 'userName': email}),
        ).called(1);
      });
    });

    group('边界测试', () {
      test('应该处理空字符串参数', () async {
        // 准备测试数据
        const ticket = '';
        const tenantId = '';
        const isBiometrics = true;
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试
        final result = await repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());
      });

      test('应该处理特殊字符参数', () async {
        // 准备测试数据
        const ticket = 'test_ticket_123';
        const tenantId = 'tenant_456';
        const isBiometrics = true;
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试
        final result = await repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());
      });

      test('应该处理超长参数', () async {
        // 准备测试数据
        final ticket = 'a' * 1000;
        final tenantId = 'b' * 500;
        const isBiometrics = true;
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试
        final result = await repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());
      });

      test('应该处理电话号码格式边界情况', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678-9999'; // 超长电话号码
        const successResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********9999'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
          ),
        );

        // 执行测试
        final result = await repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());
      });

      test('应该处理空电话号码', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '';
        const successResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': ''},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
          ),
        );

        // 执行测试
        final result = await repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone);

        // 验证结果
        expect(result, isA<GetTokenResultModel>());
      });
    });

    group('异常处理测试', () {
      test('应该处理网络连接异常', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 网络连接异常
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
            type: DioExceptionType.connectionError,
            error: 'Connection failed',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<BusinessException>()),
        );
      });

      test('应该处理超时异常', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 超时异常
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
            type: DioExceptionType.connectionTimeout,
            error: 'Connection timeout',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<BusinessException>()),
        );
      });

      test('应该处理404错误', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 404错误
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Not Found'},
            statusCode: 404,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<SystemException>()),
        );
      });

      test('应该处理401未授权错误', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 401错误
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Unauthorized'},
            statusCode: 401,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<SystemException>()),
        );
      });

      test('应该处理403禁止访问错误', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 403错误
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Forbidden'},
            statusCode: 403,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<SystemException>()),
        );
      });

      test('应该处理数据解析异常', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 返回无效JSON数据
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: 'invalid json data',
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<SystemException>()),
        );
      });

      test('应该处理空响应数据', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 返回空数据
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: null,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<SystemException>()),
        );
      });

      test('应该处理业务异常响应', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 设置 Mock 行为 - 业务异常响应（DioException）
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
            message: 'Invalid ticket',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.loginTenant(ticket: ticket, tenantId: tenantId, isBiometrics: isBiometrics),
          throwsA(isA<BusinessException>()),
        );
      });

      test('应该处理验证码发送失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';

        // 设置 Mock 行为 - 验证码发送失败（DioException）
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
            message: 'SMS service unavailable',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone),
          throwsA(isA<BusinessException>()),
        );
      });

      test('应该处理手机绑定失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';
        const userName = 'test_user';
        const password = 'test_password';
        const code = '123456';
        const tenantId = 'test_tenant';
        const userId = 1;

        // 设置 Mock 行为 - 手机绑定失败（DioException）
        when(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.userBindPhone),
            message: 'Invalid verification code',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getUserBindPhone(
            ticket: ticket,
            countryCode: countryCode,
            phone: phone,
            userName: userName,
            password: password,
            code: code,
            tenantId: tenantId,
            userId: userId,
          ),
          throwsA(isA<BusinessException>()),
        );
      });

      test('应该处理邮件验证码发送失败', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const email = '<EMAIL>';

        // 设置 Mock 行为 - 邮件验证码发送失败（DioException）
        when(
          mockDioUtil.get(GlobalVariable.getEmailVerificationCode, queryParams: {'ticket': ticket, 'userName': email}),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.getEmailVerificationCode),
            message: 'Email service unavailable',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getSendMfaMailVerifyCode(ticket: ticket, email: email),
          throwsA(isA<BusinessException>()),
        );
      });
    });

    group('集成测试', () {
      test('应该能够完成完整的租户登录流程', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;
        const code = 'test_code';

        // 第一步：租户登录
        const loginResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1', 'code': code},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: loginResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 第二步：获取用户信息
        const userInfoResponse = {
          'code': 200,
          'msg': 'success',
          'userInfo': {'id': 1, 'name': 'test_user', 'email': '<EMAIL>'},
        };
        when(mockDioUtil.get(GlobalVariable.getFullUserInfo)).thenAnswer(
          (_) async => Response(
            data: userInfoResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getFullUserInfo),
          ),
        );

        // 执行测试
        final loginResult = await repository.loginTenant(
          ticket: ticket,
          tenantId: tenantId,
          isBiometrics: isBiometrics,
          code: code,
        );
        final userInfoResult = await repository.getFullUserInfo();

        // 验证结果
        expect(loginResult, isA<GetTokenResultModel>());
        expect(userInfoResult, isA<GetTokenResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1', 'code': code},
          ),
        ).called(1);
        verify(mockDioUtil.get(GlobalVariable.getFullUserInfo)).called(1);
      });

      test('应该能够完成手机验证码流程', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const phone = '090-1234-5678';
        const userName = 'test_user';
        const password = 'test_password';
        const code = '123456';
        const tenantId = 'test_tenant';
        const userId = 1;

        // 第一步：获取验证码
        const verificationResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};
        when(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: verificationResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
          ),
        );

        // 第二步：绑定手机
        const bindResponse = {'code': 200, 'msg': 'success', 'bindResult': true};
        when(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).thenAnswer(
          (_) async => Response(
            data: bindResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.userBindPhone),
          ),
        );

        // 执行测试
        final verificationResult = await repository.getVerificationCode(
          ticket: ticket,
          countryCode: countryCode,
          phone: phone,
        );
        final bindResult = await repository.getUserBindPhone(
          ticket: ticket,
          countryCode: countryCode,
          phone: phone,
          userName: userName,
          password: password,
          code: code,
          tenantId: tenantId,
          userId: userId,
        );

        // 验证结果
        expect(verificationResult, isA<GetTokenResultModel>());
        expect(bindResult, isA<GetTokenResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(
            GlobalVariable.getVerificationCode,
            queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': '***********'},
          ),
        ).called(1);
        verify(
          mockDioUtil.post(
            GlobalVariable.userBindPhone,
            data: {
              'userName': userName,
              'password': password,
              'code': code,
              'phone': phone,
              'countryCode': countryCode,
              'userId': userId,
              'tenantId': tenantId,
              'ticket': ticket,
            },
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该能够处理流程中的异常中断', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;

        // 第一步：租户登录成功
        const loginResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};
        when(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).thenAnswer(
          (_) async => Response(
            data: loginResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
          ),
        );

        // 第二步：获取用户信息失败
        when(mockDioUtil.get(GlobalVariable.getFullUserInfo)).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.getFullUserInfo),
            message: 'User info service unavailable',
          ),
        );

        // 执行测试
        final loginResult = await repository.loginTenant(
          ticket: ticket,
          tenantId: tenantId,
          isBiometrics: isBiometrics,
        );

        // 验证登录成功
        expect(loginResult, isA<GetTokenResultModel>());

        // 验证获取用户信息失败
        expect(() => repository.getFullUserInfo(), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(
            GlobalVariable.secureSignInTenant,
            queryParams: {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'},
          ),
        ).called(1);
        verify(mockDioUtil.get(GlobalVariable.getFullUserInfo)).called(1);
      });
    });

    group('特殊业务逻辑测试', () {
      test('应该正确处理电话号码格式化的各种情况', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const countryCode = '+81';
        const successResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};

        // 测试各种电话号码格式
        final testCases = [
          '090-1234-5678', // 标准格式
          '***********', // 无连字符
          '090-1234-5678-9', // 多连字符
          '090--1234--5678', // 连续连字符
          '090-1234-5678-', // 末尾连字符
          '-090-1234-5678', // 开头连字符
        ];

        for (final phone in testCases) {
          final expectedPhone = phone.replaceAll('-', '');

          when(
            mockDioUtil.get(
              GlobalVariable.getVerificationCode,
              queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': expectedPhone},
            ),
          ).thenAnswer(
            (_) async => Response(
              data: successResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
            ),
          );

          final result = await repository.getVerificationCode(ticket: ticket, countryCode: countryCode, phone: phone);

          expect(result, isA<GetTokenResultModel>());

          verify(
            mockDioUtil.get(
              GlobalVariable.getVerificationCode,
              queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': expectedPhone},
            ),
          ).called(1);
        }
      });

      test('应该正确处理生物识别标志的各种组合', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 测试生物识别标志的各种情况
        final testCases = [
          {'isBiometrics': true, 'expectedFlag': '1'},
          {'isBiometrics': false, 'expectedFlag': '0'},
        ];

        for (final testCase in testCases) {
          when(
            mockDioUtil.get(
              GlobalVariable.secureSignInTenant,
              queryParams: {
                'ticket': ticket,
                'tenantId': tenantId,
                'biometricsFlg': testCase['expectedFlag'] as String,
              },
            ),
          ).thenAnswer(
            (_) async => Response(
              data: successResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
            ),
          );

          final result = await repository.loginTenant(
            ticket: ticket,
            tenantId: tenantId,
            isBiometrics: testCase['isBiometrics'] as bool,
          );

          expect(result, isA<GetTokenResultModel>());

          verify(
            mockDioUtil.get(
              GlobalVariable.secureSignInTenant,
              queryParams: {
                'ticket': ticket,
                'tenantId': tenantId,
                'biometricsFlg': testCase['expectedFlag'] as String,
              },
            ),
          ).called(1);
        }
      });

      test('应该正确处理可选参数 code 的各种情况', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const tenantId = 'test_tenant_id';
        const isBiometrics = true;
        const successResponse = {'code': 200, 'msg': 'success', 'token': 'test_token'};

        // 测试 code 参数的各种情况
        final testCases = [
          {'code': 'test_code', 'shouldInclude': true},
          {'code': '', 'shouldInclude': false},
          {'code': null, 'shouldInclude': false},
        ];

        for (final testCase in testCases) {
          final code = testCase['code'] as String?;
          final shouldInclude = testCase['shouldInclude'] as bool;

          final expectedParams = {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': '1'};

          if (shouldInclude) {
            expectedParams['code'] = code!;
          }

          when(mockDioUtil.get(GlobalVariable.secureSignInTenant, queryParams: expectedParams)).thenAnswer(
            (_) async => Response(
              data: successResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureSignInTenant),
            ),
          );

          final result = await repository.loginTenant(
            ticket: ticket,
            tenantId: tenantId,
            isBiometrics: isBiometrics,
            code: code,
          );

          expect(result, isA<GetTokenResultModel>());

          verify(mockDioUtil.get(GlobalVariable.secureSignInTenant, queryParams: expectedParams)).called(1);
        }
      });

      test('应该正确处理不同国家的电话号码格式', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const successResponse = {'code': 200, 'msg': 'success', 'verificationCode': '123456'};

        // 测试不同国家的电话号码格式
        final testCases = [
          {'countryCode': '+81', 'phone': '090-1234-5678', 'expected': '***********'},
          {'countryCode': '+86', 'phone': '138-8888-8888', 'expected': '13888888888'},
          {'countryCode': '+1', 'phone': '************', 'expected': '5551234567'},
          {'countryCode': '+44', 'phone': '20-7946-0958', 'expected': '2079460958'},
        ];

        for (final testCase in testCases) {
          when(
            mockDioUtil.get(
              GlobalVariable.getVerificationCode,
              queryParams: {
                'ticket': ticket,
                'countryCode': testCase['countryCode'] as String,
                'phone': testCase['expected'] as String,
              },
            ),
          ).thenAnswer(
            (_) async => Response(
              data: successResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.getVerificationCode),
            ),
          );

          final result = await repository.getVerificationCode(
            ticket: ticket,
            countryCode: testCase['countryCode'] as String,
            phone: testCase['phone'] as String,
          );

          expect(result, isA<GetTokenResultModel>());

          verify(
            mockDioUtil.get(
              GlobalVariable.getVerificationCode,
              queryParams: {
                'ticket': ticket,
                'countryCode': testCase['countryCode'] as String,
                'phone': testCase['expected'] as String,
              },
            ),
          ).called(1);
        }
      });

      test('应该正确处理邮件地址的各种格式', () async {
        // 准备测试数据
        const ticket = 'test_ticket';
        const successResponse = {'code': 200, 'msg': 'success', 'emailSent': true};

        // 测试各种邮件地址格式
        final testCases = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in testCases) {
          when(
            mockDioUtil.get(
              GlobalVariable.getEmailVerificationCode,
              queryParams: {'ticket': ticket, 'userName': email},
            ),
          ).thenAnswer(
            (_) async => Response(
              data: successResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.getEmailVerificationCode),
            ),
          );

          final result = await repository.getSendMfaMailVerifyCode(ticket: ticket, email: email);

          expect(result, isA<GetTokenResultModel>());

          verify(
            mockDioUtil.get(
              GlobalVariable.getEmailVerificationCode,
              queryParams: {'ticket': ticket, 'userName': email},
            ),
          ).called(1);
        }
      });
    });
  });
}
