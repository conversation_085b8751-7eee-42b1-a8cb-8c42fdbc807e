import 'dart:async';

import 'package:build/build.dart';
import 'package:dart_style/dart_style.dart';
import 'package:glob/glob.dart';
import 'package:path/path.dart' as p;

import 'route_item.dart';

class RouteTableBuilder extends Builder {
  @override
  FutureOr<void> build(BuildStep buildStep) async {
    final routes = <RouteItem>[]; // 改为List存储原始顺序
    final Set<String> imports = {};
    final Set<String> routeNames = {}; // 用于路由名称重复检测

    // 读取所有路由配置
    await for (final asset in buildStep.findAssets(Glob("**.t.json"))) {
      final item = RouteItem.fromJson(await buildStep.readAsString(asset));

      // 路由名称重复检测
      if (routeNames.contains(item.routeName)) {
        _printError('''
❌❌❌ 发现重复路由名称: ${item.routeName} 解决方案: 使用 @GetRoutePage(routeName: "唯一路径") 指定不同路径''');
        // continue; // 跳过重复项
      }

      routes.add(item);
      imports.addAll(item.import);
      routeNames.add(item.routeName);
    }

    if (routes.isEmpty) {
      _printWarning('未发现有效路由配置');
      return;
    }

    final StringBuffer buffer = StringBuffer();
    final Set<String> generatedNames = {};

    List<String> listImports = imports.toList();
    listImports.sort((a, b) => a.compareTo(b));
    // 生成文件头
    buffer.writeln('''
// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:get/get.dart';
''');
    listImports.forEach(buffer.writeln);
    buffer.writeln('\nclass AutoRoutes {\n  AutoRoutes._();\n');

    final nameMap = {};

    // 按照路由名称排序
    routes.sort((RouteItem a, RouteItem b) => a.routeName.compareTo(b.routeName));

    // 生成路由常量
    for (final item in routes) {
      final varName = _generateVarName(item.routeName, item.className);
      if (generatedNames.contains(varName)) {
        _printError('''
❌❌❌ 路由变量名冲突: $varName  解决方案: 修改路径格式或手动指定唯一名称''');
        // continue;
      }

      buffer.writeln('  static const String $varName = \'${item.routeName}\';');
      nameMap[item.routeName] = varName;
      generatedNames.add(varName);
    }

    // 生成页面列表（保留所有路由） 有重复的也不管， 就是要抛出去。
    buffer.writeln('\n  static final List<GetPage> pages = [');
    for (final item in routes) {
      buffer.write('''
    GetPage(
      name: ${nameMap[item.routeName]},
      page: () {''');

      // 如果有参数，生成参数传递代码
      if (item.parameters.isNotEmpty) {
        buffer.writeln();
        for (String param in item.parameters) {
          buffer.writeln('        final $param = Get.parameters[\'$param\']??\'\';');
        }
        buffer.write('        return ${item.isConst ? 'const ' : ''}${item.className}(');
        buffer.writeln(item.parameters.map((p) => '$p: $p').join(', ') + ');');
      } else {
        buffer.writeln('return ${item.isConst ? 'const ' : ''}${item.className}();');
      }
      buffer.writeln('      },');

      // 生成单个 binding
      if (item.binding != null) {
        //   if (item.parameters.isNotEmpty) {
        //     buffer.writeln('''
        // binding: ${item.binding}(${item.parameters.map((p) => '$p: Get.parameters[\'$p\']??\'\' ').join(', ')}),''');
        //   } else {
        buffer.writeln('''
      binding: ${item.binding}(),''');
        // }
      }

      if (item.bindings.isNotEmpty) {
        buffer.write('bindings: [');
        for (String binding in item.bindings) {
          // if (item.parameters.isNotEmpty) {
          //   buffer.writeln('$binding(${item.parameters.map((p) => '$p: Get.parameters[\'$p\']??\'\' ').join(', ')}),');
          // } else {
          buffer.writeln('$binding(),');
          // }
        }
        buffer.writeln('],');
      }

      buffer.writeln('popGesture: false,');
      buffer.writeln('),');
    }
    buffer.writeln('  ];\n}');

    // 输出格式化代码
    await _writeOutput(buildStep, buffer);

    _printInfo('运行结束。${DateTime.now().toString()}');
  }

  String _generateVarName(String route, String className) {
    if (route == "/") return 'mainPage';

    // 增强型命名规则
    final segments = route
        .replaceAll(RegExp(r'_+'), '/') // 将里面的_, + 全给转成 / 然后做分割。
        .split('/')
        .where((s) => s.isNotEmpty)
        .map((s) => s.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_'))
        .map((s) => s.replaceAll(RegExp(r'_+'), '_'))
        .map((s) => s.replaceAll(RegExp(r'^_|_$'), ''))
        .where((s) => s.isNotEmpty)
        .toList();

    // 备用类名方案
    if (segments.isEmpty) {
      return className.replaceAll(RegExp(r'Page$'), '').replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();
    }

    // 智能驼峰转换
    final camelCase = segments
        .map((s) => s[0].toUpperCase() + s.substring(1))
        .join()
        .replaceFirstMapped(RegExp(r'^[A-Z]'), (m) => m[0]?.toLowerCase() ?? '');

    // 处理数字开头 , 保留数字但添加前缀
    return camelCase.replaceFirstMapped(RegExp(r'^\d+'), (m) => 'n${m[0]}');
  }

  void _printError(String message) {
    print('\x1B[31m get_auto_router: $message\x1B[0m');
  }

  void _printWarning(String message) {
    print('\x1B[33m get_auto_router: $message\x1B[0m');
  }

  void _printInfo(String message) {
    print('\x1B[29m get_auto_router: $message\x1B[0m');
  }

  Future<void> _writeOutput(BuildStep step, StringBuffer buffer) async {
    try {
      final formatted = DartFormatter(
        languageVersion: DartFormatter.latestLanguageVersion,
        pageWidth: 120,
      ).format(buffer.toString());
      await step.writeAsString(
        AssetId(step.inputId.package, p.join('lib', 'core', 'routes', 'auto_route.dart')),
        formatted,
      );
    } catch (e) {
      _printError('代码格式化失败: $e');
    }
  }

  @override
  Map<String, List<String>> get buildExtensions => {
    r'$lib$': ['core/routes/auto_route.dart'],
  };
}
