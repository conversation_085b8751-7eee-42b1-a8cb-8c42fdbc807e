import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/domain/usecase/get_asset_type_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/controllers/asset_type_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 Mock 类
@GenerateNiceMocks([MockSpec<GetAssetTypeListUseCase>()])
import 'asset_type_controller_test.mocks.dart';

// 测试专用的 AssetTypeController，避免影响原逻辑
class TestAssetTypeController extends AssetTypeController {
  TestAssetTypeController({required super.getAssetTypeUseCase});

  bool exceptionHandled = false;

  @override
  void onReady() {
    // 在测试中不自动加载数据，让测试手动控制loadData的调用时机
    // 不调用 super.onReady() 来避免自动触发 loadData()
  }

  @override
  Future<void> showLoading() async {
    // 在测试中跳过Loading UI显示
  }

  @override
  void hideLoading() {
    // 在测试中跳过Loading UI隐藏
  }

  @override
  Future<void> handleException(dynamic exception, [StackTrace? stackTrace, ErrorHandlingMode? errorHandlingMode]) {
    exceptionHandled = true;
    return Future<void>.value();
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late TestAssetTypeController controller;
  late MockGetAssetTypeListUseCase mockUseCase;

  final testAssetTypes = [
    AssetTypeListModel(assetTypeId: 1, assetTypeName: 'Type A'),
    AssetTypeListModel(assetTypeId: 2, assetTypeName: 'Type B'),
  ];

  setUp(() {
    mockUseCase = MockGetAssetTypeListUseCase();
    controller = TestAssetTypeController(getAssetTypeUseCase: mockUseCase);
    Get.testMode = true;
    Get.put(controller);
    controller.onReady();
  });

  tearDown(() {
    Get.reset();
  });

  /// ----------------------------
  /// AssetTypeController Tests
  /// ----------------------------
  /// 初始状态测试
  group('Initial State Tests', () {
    test('The initial state should be empty', () {
      // 初始状态应为空
      expect(controller.state.allAssetTypes.isEmpty, true);
      expect(controller.state.filteredAssetTypes.isEmpty, true);
      expect(controller.state.isEmpty.value, false);
      expect(controller.state.isAllDataDisplayed.value, false);
      expect(controller.state.searchQuery.value, '');
    });
  });

  /// 数据加载测试
  group('Data Loading Tests', () {
    test('loadData should update state when successful', () async {
      // loadData 成功时应更新状态
      // Arrange
      when(
        mockUseCase.call(const NoParams()),
      ).thenAnswer((_) async => GetAssetTypeListResult(assetTypeList: testAssetTypes));

      // Act
      await controller.loadData();

      // Assert
      verify(mockUseCase.call(const NoParams())).called(1);
      expect(controller.state.allAssetTypes.length, 2);
      expect(controller.state.filteredAssetTypes.length, 2);
      expect(controller.state.isEmpty.value, false);
      expect(controller.state.isAllDataDisplayed.value, true);
    });

    test('loadData should handle exception on failure', () async {
      // loadData 在调用失败时应正确处理异常
      // Arrange
      when(mockUseCase.call(const NoParams())).thenThrow(BusinessException('Error'));

      // Act
      await controller.loadData();

      // Assert
      verify(mockUseCase.call(const NoParams())).called(1);
      expect(controller.state.allAssetTypes.isEmpty, true);
      expect(controller.state.filteredAssetTypes.isEmpty, true);
      expect(controller.exceptionHandled, isTrue);
    });

    test('loadData should handle empty list', () async {
      // loadData 应能处理返回空列表的情况
      // Arrange
      when(mockUseCase.call(const NoParams())).thenAnswer((_) async => GetAssetTypeListResult(assetTypeList: []));

      // Act
      await controller.loadData();

      // Assert
      expect(controller.state.allAssetTypes.isEmpty, true);
      expect(controller.state.isEmpty.value, true);
    });

    test('loadData should handle concurrent calls correctly', () async {
      // loadData 应能正确处理并发调用
      // Arrange
      when(
        mockUseCase.call(const NoParams()),
      ).thenAnswer((_) async => GetAssetTypeListResult(assetTypeList: testAssetTypes));

      // Act
      await Future.wait([controller.loadData(), controller.loadData()]);

      // Assert
      expect(controller.state.allAssetTypes.length, 2);
    });
  });

  /// 搜索功能测试
  group('Search Functionality Tests', () {
    setUp(() {
      // 设置初始数据
      controller.state.allAssetTypes.value = testAssetTypes;
      controller.state.filteredAssetTypes.value = testAssetTypes;
    });

    test('Search should update query string and filter matching items', () {
      // 搜索应更新查询字符串并过滤匹配的资产类型
      // Act
      controller.updateSearch('Type A');

      // Assert
      expect(controller.state.searchQuery.value, 'Type A');
      expect(controller.state.filteredAssetTypes.length, 1);
      expect(controller.state.filteredAssetTypes.first.assetTypeName, 'Type A');
    });

    test('Clearing search should display all asset types', () {
      // 清空搜索时应显示所有资产类型
      // Act
      controller.updateSearch('Type A');
      controller.updateSearch('');

      // Assert
      expect(controller.state.filteredAssetTypes.length, 2);
      expect(controller.state.isAllDataDisplayed.value, true);
    });

    test('Search should be case-insensitive', () {
      // 搜索应忽略大小写
      // Act
      controller.updateSearch('type a');

      // Assert
      expect(controller.state.filteredAssetTypes.length, 1);
      expect(controller.state.filteredAssetTypes.first.assetTypeName, 'Type A');
    });

    test('Search should handle null or empty strings gracefully', () {
      // 搜索应能正确处理 null 或空字符串
      // Act
      controller.updateSearch('   ');

      // Assert
      expect(controller.state.filteredAssetTypes, controller.state.allAssetTypes);
    });
  });

  /// 资产类型选择测试
  group('Asset Type Selection Tests', () {
    final testAssetType = AssetTypeListModel(assetTypeId: 1, assetTypeName: 'Test Type');

    testWidgets('selectAssetType should succeed when authority is valid', (tester) async {
      // selectAssetType 在权限通过时应成功
      // Arrange
      await tester.pumpWidget(const GetMaterialApp(home: Material(child: Text('Test'))));
      when(mockUseCase.checkAuthority(assetTypeId: 1)).thenAnswer((_) async => true);

      // Act
      await controller.selectAssetType(testAssetType);
      await tester.pumpAndSettle();

      // Assert
      verify(mockUseCase.checkAuthority(assetTypeId: 1)).called(1);
    });

    testWidgets('selectAssetType should show dialog when authority is invalid', (tester) async {
      // selectAssetType 在权限失败时应显示对话框
      // Arrange
      await tester.pumpWidget(const GetMaterialApp(home: Material(child: Text('Test'))));
      when(mockUseCase.checkAuthority(assetTypeId: 1)).thenAnswer((_) async => false);

      // Act
      await controller.selectAssetType(testAssetType);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('注意'), findsOneWidget);
    });
  });

  /// 异常处理测试
  group('Exception Handling Tests', () {
    final testAssetType = AssetTypeListModel(assetTypeId: 1, assetTypeName: 'Test Type');
    test('selectAssetType should handle exceptions correctly', () async {
      // selectAssetType 在异常时应正确处理
      // Arrange
      when(mockUseCase.checkAuthority(assetTypeId: 1)).thenThrow(Exception('Test exception'));

      // Act
      await controller.selectAssetType(testAssetType);

      // Assert
      expect(controller.exceptionHandled, isTrue);
    });
  });
}
