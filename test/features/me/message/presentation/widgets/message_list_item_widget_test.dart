import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/controllers/message_controller.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/widgets/message_list_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 生成Mock类
@GenerateNiceMocks([MockSpec<MessageController>()])
import 'message_list_item_widget_test.mocks.dart';

/// Mock InternalFinalCallback for GetX lifecycle methods
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// ==========================================
// 测试数据助手类
// ==========================================

/// MessageListItemWidget测试数据助手
class MessageListItemWidgetTestData {
  /// 创建基础测试用MessageUIModel
  static MessageUIModel createBasicMessageModel({
    int id = 1001,
    String title = 'テストメッセージタイトル',
    String body = '<p>テストメッセージ内容</p>',
    int readStatus = 0, // 0=未读, 1=已读
    int category = 5, // 5=PR (绿色)
    String fileInfo = '[]', // 无附件
    String date = '2024-01-15 10:30:00',
  }) {
    return MessageUIModel(
      notificationId: id,
      notificationTitle: title,
      notificationBody: body,
      readStatus: readStatus,
      category: category,
      fileInformation: fileInfo,
      activeStartDate: date,
    );
  }

  /// 创建不同等级的消息模型
  static MessageUIModel createMessageWithCategory(int category) {
    return createBasicMessageModel(id: 2000 + category, title: 'Category $category メッセージ', category: category);
  }

  /// 创建已读消息模型
  static MessageUIModel createReadMessage({int id = 3001, String title = '既読メッセージ'}) {
    return createBasicMessageModel(
      id: id,
      title: title,
      readStatus: 1, // 已读
    );
  }

  /// 创建未读消息模型
  static MessageUIModel createUnreadMessage({int id = 3002, String title = '未読メッセージ'}) {
    return createBasicMessageModel(
      id: id,
      title: title,
      readStatus: 0, // 未读
    );
  }

  /// 创建有附件的消息模型
  static MessageUIModel createMessageWithAttachment({int id = 4001, String title = '添付ファイル付きメッセージ'}) {
    const fileInfo = '''[{
      "fileName": "テスト資料.pdf",
      "url": "test/file/path.pdf",
      "size": "2.5 MB",
      "uploadDate": "2024-01-15 09:00:00"
    }]''';

    return createBasicMessageModel(id: id, title: title, fileInfo: fileInfo);
  }

  /// 创建无附件的消息模型
  static MessageUIModel createMessageWithoutAttachment({int id = 4002, String title = '添付ファイルなしメッセージ'}) {
    return createBasicMessageModel(
      id: id,
      title: title,
      fileInfo: '[]', // 空附件
    );
  }

  /// 获取不同等级对应的颜色和文本
  static Map<String, dynamic> getCategoryInfo(int category) {
    switch (category) {
      case 2:
        return {'color': const Color(0xFFE6004D), 'text': '障 害'};
      case 3:
        return {'color': const Color(0xFF0B3E86), 'text': 'メンテナンス'};
      case 4:
        return {'color': const Color(0xFF4D94FF), 'text': 'リリース'};
      case 5:
        return {'color': const Color(0xFF00BF9F), 'text': 'P R'};
      case 6:
        return {'color': const Color(0xFF666666), 'text': 'その他'};
      default:
        return {'color': const Color(0xFFBBBBBB), 'text': '未定義'};
    }
  }
}

// ==========================================
// 测试工具方法
// ==========================================

/// 创建测试用的Widget环境
Widget createWidgetUnderTest({required MessageUIModel item, VoidCallback? onTap}) {
  return GetMaterialApp(
    home: Scaffold(
      body: MessageListItemWidget(item: item, onTap: onTap ?? () {}),
    ),
  );
}

/// 验证等级标签的颜色和文本
void verifyFlagContainer({required WidgetTester tester, required int category}) {
  final categoryInfo = MessageListItemWidgetTestData.getCategoryInfo(category);

  // 查找等级标签容器
  final flagContainerFinder = find.byWidgetPredicate(
    (widget) =>
        widget is Container &&
        widget.decoration is BoxDecoration &&
        (widget.decoration as BoxDecoration).color == categoryInfo['color'],
  );

  expect(flagContainerFinder, findsOneWidget);

  // 验证等级文本
  expect(find.text(categoryInfo['text']), findsOneWidget);
}

void main() {
  /// ==========================================
  /// MessageListItemWidget 单元测试
  ///
  /// 测试架构：
  /// - Phase 0: 测试基础设施验证 (13个测试)
  /// - Phase 1: 基础渲染测试 - Widget结构、容器、布局
  /// - Phase 2: 数据显示测试 - 等级标签、日期、标题、附件图标
  /// - Phase 3: 已读/未读状态测试 - UI差异、红点、图标颜色
  /// - Phase 4: 响应式更新测试 - Obx状态变化响应
  /// - Phase 5: 交互测试 - 点击回调、交互区域
  /// ==========================================

  group('🧪 MessageListItemWidget 单元测试', () {
    late MockMessageController mockController;
    late MockInternalFinalCallback<void> mockInternalFinalCallback;

    setUpAll(() {
      // 初始化GetX测试环境
      Get.testMode = true;
    });

    setUp(() {
      // 每个测试前重置
      Get.reset();
      Get.testMode = true; // 确保每次测试都设置testMode

      mockController = MockMessageController();
      mockInternalFinalCallback = MockInternalFinalCallback<void>();

      // 修复GetX Controller生命周期方法Mock
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);

      // 注册Mock Controller到GetX
      Get.put<MessageController>(mockController);
    });

    tearDown(() {
      // 清理GetX状态
      Get.reset();
    });

    // ==========================================
    // Phase 0: 测试基础设施验证
    // ==========================================
    group('🏗️ Phase 0: 测试基础设施验证', () {
      group('0.1 Mock Controller设置验证', () {
        testWidgets('MockMessageController正确创建', (WidgetTester tester) async {
          // Arrange & Act
          // Mock Controller在setUp中已创建

          // Assert
          expect(mockController, isNotNull);
          expect(mockController, isA<MockMessageController>());
          expect(mockController, isA<MessageController>());
        });

        testWidgets('Mock Controller注册到GetX成功', (WidgetTester tester) async {
          // Arrange & Act
          // 在setUp中已注册

          // Assert - 验证GetX中能找到Controller
          expect(Get.isRegistered<MessageController>(), isTrue);
          expect(Get.find<MessageController>(), equals(mockController));
        });

        testWidgets('Mock Controller基础方法可用', (WidgetTester tester) async {
          // Arrange
          // 无需特殊setup，使用默认Mock行为

          // Act & Assert - 验证Mock基础方法不会抛异常
          expect(() => mockController.toString(), returnsNormally);
          expect(mockController.runtimeType.toString(), contains('Mock'));
        });
      });

      group('0.2 GetX环境配置验证', () {
        testWidgets('GetX测试模式正确设置', (WidgetTester tester) async {
          // Arrange & Act
          // 在setUpAll中已设置

          // Assert
          expect(Get.testMode, isTrue);
        });

        testWidgets('GetX状态在测试间正确重置', (WidgetTester tester) async {
          // Arrange
          const testKey = 'test_dependency';
          Get.put('test_value', tag: testKey);

          // Act - 手动触发重置（模拟tearDown）
          Get.reset();

          // Assert
          expect(Get.isRegistered<String>(tag: testKey), isFalse);
        });

        testWidgets('GetMaterialApp正确创建', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));

          // Assert - 验证GetMaterialApp存在
          expect(find.byType(GetMaterialApp), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);
        });

        testWidgets('GetWidget依赖注入工作正常', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证MessageListItemWidget能正确访问Controller
          expect(find.byType(MessageListItemWidget), findsOneWidget);
          // Widget应该能正常渲染，说明GetWidget<MessageController>工作正常
          expect(tester.takeException(), isNull);
        });
      });

      group('0.3 测试数据助手验证', () {
        testWidgets('基础MessageUIModel创建正确', (WidgetTester tester) async {
          // Arrange & Act
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Assert - 验证基础属性
          expect(testMessage.notificationId, equals(1001));
          expect(testMessage.notificationTitle, equals('テストメッセージタイトル'));
          expect(testMessage.readStatus, equals(0));
          expect(testMessage.category, equals(5));
          expect(testMessage.fileInformation, equals('[]'));
          expect(testMessage.activeStartDate, equals('2024-01-15 10:30:00'));
        });

        testWidgets('不同等级消息创建正确', (WidgetTester tester) async {
          // Arrange & Act
          final testCategories = [2, 3, 4, 5, 6];

          for (final category in testCategories) {
            final message = MessageListItemWidgetTestData.createMessageWithCategory(category);

            // Assert
            expect(message.category, equals(category));
            expect(message.notificationId, equals(2000 + category));
            expect(message.notificationTitle, contains('Category $category'));
          }
        });

        testWidgets('已读/未读消息创建正确', (WidgetTester tester) async {
          // Arrange & Act
          final readMessage = MessageListItemWidgetTestData.createReadMessage();
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Assert
          expect(readMessage.readStatus, equals(1));
          expect(readMessage.isRead(), isTrue);
          expect(unreadMessage.readStatus, equals(0));
          expect(unreadMessage.isRead(), isFalse);
        });

        testWidgets('附件消息创建正确', (WidgetTester tester) async {
          // Arrange & Act
          final messageWithAttachment = MessageListItemWidgetTestData.createMessageWithAttachment();
          final messageWithoutAttachment = MessageListItemWidgetTestData.createMessageWithoutAttachment();

          // Assert
          expect(messageWithAttachment.hasFileInformation(), isTrue);
          expect(messageWithAttachment.fileInformation, isNotEmpty);
          expect(messageWithAttachment.fileInformation, isNot(equals('[]')));

          expect(messageWithoutAttachment.hasFileInformation(), isFalse);
          expect(messageWithoutAttachment.fileInformation, equals('[]'));
        });

        testWidgets('getCategoryInfo助手方法正确', (WidgetTester tester) async {
          // Arrange & Act
          final categoryTestCases = [
            {'category': 2, 'expectedColor': const Color(0xFFE6004D), 'expectedText': '障 害'},
            {'category': 3, 'expectedColor': const Color(0xFF0B3E86), 'expectedText': 'メンテナンス'},
            {'category': 4, 'expectedColor': const Color(0xFF4D94FF), 'expectedText': 'リリース'},
            {'category': 5, 'expectedColor': const Color(0xFF00BF9F), 'expectedText': 'P R'},
            {'category': 6, 'expectedColor': const Color(0xFF666666), 'expectedText': 'その他'},
            {'category': 999, 'expectedColor': const Color(0xFFBBBBBB), 'expectedText': '未定義'},
          ];

          for (final testCase in categoryTestCases) {
            final result = MessageListItemWidgetTestData.getCategoryInfo(testCase['category'] as int);

            // Assert
            expect(result['color'], equals(testCase['expectedColor']));
            expect(result['text'], equals(testCase['expectedText']));
          }
        });
      });

      group('0.4 Widget基本生命周期验证', () {
        testWidgets('Widget基本渲染成功', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证Widget成功渲染
          expect(find.byType(MessageListItemWidget), findsOneWidget);
          expect(tester.takeException(), isNull);
        });

        testWidgets('Widget参数传递正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();
          bool onTapCalled = false;

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage, onTap: () => onTapCalled = true));
          await tester.pump();

          // Assert - 验证参数传递
          final widget = tester.widget<MessageListItemWidget>(find.byType(MessageListItemWidget));
          expect(widget.item, equals(testMessage));
          expect(widget.onTap, isNotNull);

          // 验证onTap回调工作
          widget.onTap();
          expect(onTapCalled, isTrue);
        });

        testWidgets('Widget销毁清理正常', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act - 渲染Widget
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          expect(find.byType(MessageListItemWidget), findsOneWidget);

          // Act - 销毁Widget
          await tester.pumpWidget(Container());
          await tester.pump();

          // Assert - 验证清理成功
          expect(find.byType(MessageListItemWidget), findsNothing);
          expect(tester.takeException(), isNull);
        });
      });

      group('0.5 测试工具方法验证', () {
        testWidgets('createWidgetUnderTest方法工作正常', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          final widget = createWidgetUnderTest(item: testMessage);
          await tester.pumpWidget(widget);

          // Assert
          expect(widget, isA<GetMaterialApp>());
          expect(find.byType(GetMaterialApp), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });

        testWidgets('verifyFlagContainer验证方法可用', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(5);

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证方法不抛异常且能正确验证
          expect(() => verifyFlagContainer(tester: tester, category: 5), returnsNormally);
        });

        testWidgets('测试环境隔离性验证', (WidgetTester tester) async {
          // Arrange
          final message1 = MessageListItemWidgetTestData.createBasicMessageModel(id: 1);
          final message2 = MessageListItemWidgetTestData.createBasicMessageModel(id: 2);

          // Act - 第一次渲染
          await tester.pumpWidget(createWidgetUnderTest(item: message1));
          await tester.pump();

          expect(find.byType(MessageListItemWidget), findsOneWidget);

          // 切换到第二个消息
          await tester.pumpWidget(createWidgetUnderTest(item: message2));
          await tester.pump();

          // Assert - 验证环境清理正确，新消息正确渲染
          expect(find.byType(MessageListItemWidget), findsOneWidget);
          expect(tester.takeException(), isNull);
        });
      });
    });

    // ==========================================
    // Phase 1: 基础渲染测试
    // ==========================================
    group('📱 Phase 1: 基础渲染测试', () {
      group('1.1 Widget基础结构测试', () {
        testWidgets('Widget基本结构正确渲染', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证基本组件存在
          expect(find.byType(MessageListItemWidget), findsOneWidget);
          expect(find.byType(InkWell), findsOneWidget);
          expect(find.byType(Container), findsWidgets); // 多个Container
          expect(find.byType(Row), findsWidgets); // 布局Row
          expect(find.byType(Column), findsOneWidget); // 内容Column
        });

        testWidgets('主容器样式和布局正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证主容器样式
          final mainContainerFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.padding == const EdgeInsets.all(15) &&
                widget.margin == const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          );

          expect(mainContainerFinder, findsOneWidget);

          // 验证容器装饰
          final mainContainer = tester.widget<Container>(mainContainerFinder);
          final decoration = mainContainer.decoration as BoxDecoration;
          expect(decoration.color, equals(AppTheme.lightTheme.customTheme.cardBackgroundColor));
          expect(decoration.borderRadius, equals(BorderRadius.circular(8)));
        });

        testWidgets('InkWell可点击区域设置正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();
          bool tapped = false;

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage, onTap: () => tapped = true));
          await tester.pump();

          // Assert - 验证InkWell存在且可点击
          final inkWellFinder = find.byType(InkWell);
          expect(inkWellFinder, findsOneWidget);

          // 验证点击区域
          await tester.tap(inkWellFinder);
          expect(tapped, isTrue);
        });

        testWidgets('Widget层次结构正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证Widget层次结构: InkWell > Container > Stack
          final inkWell = tester.widget<InkWell>(find.byType(InkWell));
          expect(inkWell.child, isA<Container>());

          final mainContainer = inkWell.child as Container;
          expect(mainContainer.child, isA<Stack>());
        });

        testWidgets('Widget尺寸和边界正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证Widget有合理的尺寸
          final widgetRect = tester.getRect(find.byType(MessageListItemWidget));
          expect(widgetRect.width, greaterThan(0));
          expect(widgetRect.height, greaterThan(0));

          // 验证主容器存在
          final containerFinder = find.byWidgetPredicate(
            (w) => w is Container && w.margin == const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          );
          expect(containerFinder, findsOneWidget);
        });
      });

      group('1.2 Stack布局结构测试', () {
        testWidgets('Stack布局正确配置', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证MessageListItemWidget内的Stack存在
          expect(find.byType(Stack), findsWidgets); // 可能有多个Stack

          // 验证至少有一个Stack包含主要内容
          final messageWidgetStack = find.descendant(
            of: find.byType(MessageListItemWidget),
            matching: find.byType(Stack),
          );
          expect(messageWidgetStack, findsAtLeastNWidgets(1));
        });

        testWidgets('主内容Row在Stack中正确定位', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证主要Row布局存在
          final rowsInMessage = find.descendant(of: find.byType(MessageListItemWidget), matching: find.byType(Row));
          expect(rowsInMessage, findsAtLeastNWidgets(1)); // 至少有一个Row

          // 验证有crossAxisAlignment为center的Row
          final centerRowFinder = find.byWidgetPredicate(
            (widget) => widget is Row && widget.crossAxisAlignment == CrossAxisAlignment.center,
          );
          expect(centerRowFinder, findsAtLeastNWidgets(1));
        });

        testWidgets('红点指示器在Stack中正确定位（未读状态）', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证Positioned组件存在
          final positionedFinder = find.byWidgetPredicate(
            (widget) => widget is Positioned && widget.top == 10 && widget.right == 10,
          );

          expect(positionedFinder, findsOneWidget);

          // 验证红点在MessageListItemWidget内
          final redDotInMessage = find.descendant(of: find.byType(MessageListItemWidget), matching: positionedFinder);
          expect(redDotInMessage, findsOneWidget);
        });

        testWidgets('已读状态Stack结构简化', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 验证已读状态下没有红色圆点的Positioned
          final positionedWithRedDot = find.byWidgetPredicate(
            (widget) =>
                widget is Positioned &&
                widget.top == 10 &&
                widget.right == 10 &&
                widget.child is Container &&
                (widget.child as Container).decoration is BoxDecoration &&
                ((widget.child as Container).decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );

          expect(positionedWithRedDot, findsNothing);
        });
      });

      group('1.3 Row和Column布局测试', () {
        testWidgets('主内容Row布局配置正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证主要Row布局属性
          final centerRows = find.byWidgetPredicate(
            (widget) => widget is Row && widget.crossAxisAlignment == CrossAxisAlignment.center,
          );

          expect(centerRows, findsAtLeastNWidgets(1));

          // 验证有包含Expanded的Row
          final rowWithExpanded = find.byWidgetPredicate(
            (widget) => widget is Row && widget.children.any((child) => child is Expanded),
          );
          expect(rowWithExpanded, findsAtLeastNWidgets(1));
        });

        testWidgets('内容Column布局配置正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证内容Column存在
          final contentColumnFinder = find.byWidgetPredicate(
            (widget) => widget is Column && widget.crossAxisAlignment == CrossAxisAlignment.start,
          );

          expect(contentColumnFinder, findsOneWidget);

          // 验证Column内容结构（第一个Row + SizedBox + 标题）
          final columnWidget = tester.widget<Column>(contentColumnFinder);
          expect(columnWidget.children.length, equals(3)); // 等级+日期Row, SizedBox, 标题Text
        });

        testWidgets('第一行Row布局（等级+日期+附件）正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createMessageWithAttachment();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证第一行Row内容
          final firstRowInColumnFinder = find.byWidgetPredicate(
            (widget) => widget is Row && widget.children.length >= 3, // 等级容器 + SizedBox + 日期 + [附件图标]
          );

          expect(firstRowInColumnFinder, findsWidgets); // 可能有多个Row

          // 验证包含等级容器和日期
          expect(find.byType(Container), findsWidgets); // 等级标签容器
          expect(find.byType(Obx), findsWidgets); // 日期文本和其他响应式组件
        });

        testWidgets('Expanded组件正确使用', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证Expanded组件使用
          expect(find.byType(Expanded), findsOneWidget);

          // 验证Expanded包含Column
          final expandedWidget = tester.widget<Expanded>(find.byType(Expanded));
          expect(expandedWidget.child, isA<Column>());

          // 验证flex属性为默认值
          expect(expandedWidget.flex, equals(1));
        });

        testWidgets('SizedBox间距正确设置', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证SizedBox间距
          final sizedBoxes = tester.widgetList<SizedBox>(find.byType(SizedBox)).toList();
          expect(sizedBoxes.length, greaterThanOrEqualTo(1));

          // 验证主要间距（等级标签后的间距）
          final tenWidthSizedBox = sizedBoxes.where((box) => box.width == 10.0).toList();
          expect(tenWidthSizedBox.length, greaterThanOrEqualTo(1));

          // 验证垂直间距（第一行和标题间的间距）
          final fiveHeightSizedBox = sizedBoxes.where((box) => box.height == 5.0).toList();
          expect(fiveHeightSizedBox.length, greaterThanOrEqualTo(1));
        });
      });

      group('1.4 Container装饰和样式测试', () {
        testWidgets('主容器decoration正确应用', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证主容器的BoxDecoration
          final mainContainerFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container && widget.decoration is BoxDecoration && widget.padding == const EdgeInsets.all(15),
          );

          expect(mainContainerFinder, findsOneWidget);

          final container = tester.widget<Container>(mainContainerFinder);
          final decoration = container.decoration as BoxDecoration;

          // 验证背景颜色
          expect(decoration.color, equals(AppTheme.lightTheme.customTheme.cardBackgroundColor));

          // 验证圆角
          expect(decoration.borderRadius, equals(BorderRadius.circular(8)));

          // 验证没有边框和阴影
          expect(decoration.border, isNull);
          expect(decoration.boxShadow, isNull);
        });

        testWidgets('等级标签容器样式正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(5); // PR类型

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证等级标签容器通过decoration和padding
          final flagContainerFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFF00BF9F) &&
                widget.padding == const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
          );

          expect(flagContainerFinder, findsOneWidget);

          final flagContainer = tester.widget<Container>(flagContainerFinder);
          final decoration = flagContainer.decoration as BoxDecoration;

          // 验证PR类型的绿色背景
          expect(decoration.color, equals(const Color(0xFF00BF9F)));
          expect(decoration.borderRadius, equals(BorderRadius.circular(4)));

          // 验证实际渲染尺寸接近预期（110宽度）
          final containerRect = tester.getRect(flagContainerFinder);
          expect(containerRect.width, closeTo(110, 5)); // 允许5px误差
        });

        testWidgets('红点指示器容器样式正确（未读状态）', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证红点容器通过decoration和shape
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50) &&
                (widget.decoration as BoxDecoration).shape == BoxShape.circle,
          );

          expect(redDotFinder, findsOneWidget);

          // 验证实际渲染尺寸接近预期（5x5）
          final redDotRect = tester.getRect(redDotFinder);
          expect(redDotRect.width, closeTo(5, 1)); // 允许1px误差
          expect(redDotRect.height, closeTo(5, 1));
        });

        testWidgets('容器嵌套结构正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证Container的嵌套层次
          final containers = tester.widgetList<Container>(find.byType(Container)).toList();
          expect(containers.length, greaterThanOrEqualTo(2)); // 至少有主容器和等级标签容器

          // 验证主容器包含Stack
          final mainContainer = containers.firstWhere((container) => container.padding == const EdgeInsets.all(15));
          expect(mainContainer.child, isA<Stack>());
        });

        testWidgets('不同类别消息的容器颜色正确', (WidgetTester tester) async {
          // Arrange & Act & Assert - 测试不同类别
          final testCases = [
            {'category': 2, 'color': const Color(0xFFE6004D)}, // 障害
            {'category': 3, 'color': const Color(0xFF0B3E86)}, // メンテナンス
            {'category': 4, 'color': const Color(0xFF4D94FF)}, // リリース
            {'category': 5, 'color': const Color(0xFF00BF9F)}, // PR
            {'category': 6, 'color': const Color(0xFF666666)}, // その他
          ];

          for (final testCase in testCases) {
            final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(testCase['category'] as int);

            await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
            await tester.pump();

            final flagContainerFinder = find.byWidgetPredicate(
              (widget) =>
                  widget is Container &&
                  widget.decoration is BoxDecoration &&
                  (widget.decoration as BoxDecoration).color == testCase['color'],
            );

            expect(flagContainerFinder, findsOneWidget);

            // 清理Widget树
            await tester.pumpWidget(Container());
          }
        });
      });
    });

    // ==========================================
    // Phase 2: 数据显示测试
    // ==========================================
    group('📊 Phase 2: 数据显示测试', () {
      group('2.1 等级标签显示测试', () {
        testWidgets('等级标签文本正确显示', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(5); // PR类型

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证等级标签文本
          expect(find.text('P R'), findsOneWidget);

          // 验证文本样式
          final textWidget = tester.widget<Text>(find.text('P R'));
          expect(textWidget.style?.color, equals(Colors.white));
          expect(textWidget.style?.fontSize, equals(12));
          expect(textWidget.style?.fontWeight, equals(FontWeight.w700));
          expect(textWidget.textAlign, equals(TextAlign.center));
        });

        testWidgets('不同等级的标签文本正确', (WidgetTester tester) async {
          // Arrange & Act & Assert - 测试各种等级
          final testCases = [
            {'category': 2, 'expectedText': '障 害'},
            {'category': 3, 'expectedText': 'メンテナンス'},
            {'category': 4, 'expectedText': 'リリース'},
            {'category': 5, 'expectedText': 'P R'},
            {'category': 6, 'expectedText': 'その他'},
          ];

          for (final testCase in testCases) {
            final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(testCase['category'] as int);

            await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
            await tester.pump();

            expect(find.text(testCase['expectedText'] as String), findsOneWidget);

            // 清理Widget树
            await tester.pumpWidget(Container());
          }
        });

        testWidgets('等级标签背景颜色正确', (WidgetTester tester) async {
          // Arrange & Act & Assert - 测试各种颜色
          final testCases = [
            {'category': 2, 'expectedColor': const Color(0xFFE6004D)}, // 障害 - 红色
            {'category': 3, 'expectedColor': const Color(0xFF0B3E86)}, // メンテナンス - 深蓝
            {'category': 4, 'expectedColor': const Color(0xFF4D94FF)}, // リリース - 蓝色
            {'category': 5, 'expectedColor': const Color(0xFF00BF9F)}, // PR - 绿色
            {'category': 6, 'expectedColor': const Color(0xFF666666)}, // その他 - 灰色
          ];

          for (final testCase in testCases) {
            final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(testCase['category'] as int);

            await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
            await tester.pump();

            // 验证等级标签容器背景色
            final flagContainerFinder = find.byWidgetPredicate(
              (widget) =>
                  widget is Container &&
                  widget.decoration is BoxDecoration &&
                  (widget.decoration as BoxDecoration).color == testCase['expectedColor'],
            );

            expect(flagContainerFinder, findsOneWidget);

            // 清理Widget树
            await tester.pumpWidget(Container());
          }
        });

        testWidgets('等级标签容器尺寸和样式正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createMessageWithCategory(3);

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证容器尺寸和样式
          final flagContainerFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.padding == const EdgeInsets.symmetric(horizontal: 5, vertical: 5) &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).borderRadius == BorderRadius.circular(4),
          );

          expect(flagContainerFinder, findsOneWidget);

          // 验证实际渲染宽度
          final containerRect = tester.getRect(flagContainerFinder);
          expect(containerRect.width, closeTo(110, 5));
        });
      });

      group('2.2 日期显示测试', () {
        testWidgets('日期格式化正确显示', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(date: '2024-01-15T10:30:00Z');

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证日期文本存在（具体格式由DateTimeUtils决定）
          final dateTextFinder = find.byWidgetPredicate(
            (widget) => widget is Text && widget.data != null && widget.data!.contains('2024'),
          );

          expect(dateTextFinder, findsAtLeastNWidgets(1));
        });

        testWidgets('已读消息日期样式正确（灰色、普通字重）', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 查找日期文本（通过Obx包装的特征识别）
          final dateTexts = tester.widgetList<Text>(find.byType(Text)).where((text) {
            return text.data != null &&
                text.data!.contains('2024') && // 包含年份的日期文本
                text.style?.color == Colors.grey &&
                text.style?.fontWeight == FontWeight.normal;
          });

          expect(dateTexts.length, greaterThanOrEqualTo(1));
        });

        testWidgets('未读消息日期样式正确（黑色、加粗）', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 查找日期文本
          final dateTexts = tester.widgetList<Text>(find.byType(Text)).where((text) {
            return text.data != null &&
                text.data!.contains('2024') && // 包含年份的日期文本
                text.style?.color == Colors.black87 &&
                text.style?.fontWeight == FontWeight.w600;
          });

          expect(dateTexts.length, greaterThanOrEqualTo(1));
        });

        testWidgets('日期字体大小正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证日期文本字体大小
          final dateTexts = tester.widgetList<Text>(find.byType(Text)).where((text) {
            return text.data != null &&
                text.data!.contains('2024') && // 日期文本
                text.style?.fontSize == 14;
          });

          expect(dateTexts.length, greaterThanOrEqualTo(1));
        });
      });

      group('2.3 标题显示测试', () {
        testWidgets('标题文本正确显示', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(title: '测试标题メッセージ');

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证标题文本
          expect(find.text('测试标题メッセージ'), findsOneWidget);
        });

        testWidgets('已读消息标题样式正确（灰色、普通字重）', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage(title: '已读标题');

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 验证标题样式
          final titleTextWidget = tester.widget<Text>(find.text('已读标题'));
          expect(titleTextWidget.style?.color, equals(Colors.grey));
          expect(titleTextWidget.style?.fontWeight, equals(FontWeight.normal));
          expect(titleTextWidget.style?.fontSize, equals(16));
        });

        testWidgets('未读消息标题样式正确（黑色、加粗）', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage(title: '未读标题');

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证标题样式
          final titleTextWidget = tester.widget<Text>(find.text('未读标题'));
          expect(titleTextWidget.style?.color, equals(Colors.black87));
          expect(titleTextWidget.style?.fontWeight, equals(FontWeight.w600));
          expect(titleTextWidget.style?.fontSize, equals(16));
        });

        testWidgets('长标题文本正确显示', (WidgetTester tester) async {
          // Arrange
          final longTitle = '这是一个非常长的标题文本，用来测试UI是否能正确处理长文本内容的显示効果とレイアウト';
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(title: longTitle);

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证长标题存在
          expect(find.text(longTitle), findsOneWidget);
        });

        testWidgets('空标题处理正确', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(title: '');

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证空标题的Text组件存在
          final emptyTitleFinder = find.byWidgetPredicate((widget) => widget is Text && widget.data == '');
          expect(emptyTitleFinder, findsAtLeastNWidgets(1));
        });
      });

      group('2.4 附件图标显示测试', () {
        testWidgets('有附件时显示附件图标', (WidgetTester tester) async {
          // Arrange
          final messageWithAttachment = MessageListItemWidgetTestData.createMessageWithAttachment();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: messageWithAttachment));
          await tester.pump();

          // Assert - 验证附件图标存在
          final attachIconFinder = find.byIcon(Icons.attach_file);
          expect(attachIconFinder, findsOneWidget);

          // 验证图标样式
          final iconWidget = tester.widget<Icon>(attachIconFinder);
          expect(iconWidget.color, equals(const Color(0xFF202020)));
          expect(iconWidget.size, equals(16));
        });

        testWidgets('无附件时不显示附件图标', (WidgetTester tester) async {
          // Arrange
          final messageWithoutAttachment = MessageListItemWidgetTestData.createMessageWithoutAttachment();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: messageWithoutAttachment));
          await tester.pump();

          // Assert - 验证附件图标不存在
          final attachIconFinder = find.byIcon(Icons.attach_file);
          expect(attachIconFinder, findsNothing);
        });

        testWidgets('附件图标位置正确', (WidgetTester tester) async {
          // Arrange
          final messageWithAttachment = MessageListItemWidgetTestData.createMessageWithAttachment();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: messageWithAttachment));
          await tester.pump();

          // Assert - 验证图标在Padding包装中
          final paddedIconFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Padding &&
                widget.padding == const EdgeInsets.only(left: 8) &&
                widget.child is Icon &&
                (widget.child as Icon).icon == Icons.attach_file,
          );

          expect(paddedIconFinder, findsOneWidget);
        });
      });

      group('2.5 右箭头图标显示测试', () {
        testWidgets('已读消息右箭头为灰色', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 验证灰色右箭头图标
          final greyChevronFinder = find.byWidgetPredicate(
            (widget) => widget is Icon && widget.icon == Icons.chevron_right && widget.color == Colors.grey,
          );

          expect(greyChevronFinder, findsOneWidget);
        });

        testWidgets('未读消息右箭头为黑色', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证黑色右箭头图标
          final blackChevronFinder = find.byWidgetPredicate(
            (widget) => widget is Icon && widget.icon == Icons.chevron_right && widget.color == Colors.black87,
          );

          expect(blackChevronFinder, findsOneWidget);
        });

        testWidgets('右箭头图标在Obx包装中', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证右箭头在Obx中
          final obxWithChevronFinder = find.byWidgetPredicate((widget) => widget is Obx && widget.builder != null);

          expect(obxWithChevronFinder, findsWidgets); // 可能有多个Obx

          // 验证chevron_right图标存在
          expect(find.byIcon(Icons.chevron_right), findsOneWidget);
        });
      });

      group('2.6 红点指示器显示测试', () {
        testWidgets('未读消息显示红点指示器', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证红点存在
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50) &&
                (widget.decoration as BoxDecoration).shape == BoxShape.circle,
          );

          expect(redDotFinder, findsOneWidget);

          // 验证红点在正确位置
          final positionedRedDotFinder = find.byWidgetPredicate(
            (widget) => widget is Positioned && widget.top == 10 && widget.right == 10 && widget.child is Container,
          );

          expect(positionedRedDotFinder, findsOneWidget);
        });

        testWidgets('已读消息不显示红点指示器', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 验证红点不存在
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50) &&
                (widget.decoration as BoxDecoration).shape == BoxShape.circle,
          );

          expect(redDotFinder, findsNothing);

          // 验证SizedBox.shrink存在（隐藏状态）
          expect(find.byType(SizedBox), findsWidgets);
        });

        testWidgets('红点指示器在Obx包装中响应式更新', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证红点状态在Obx中
          final obxWithRedDotFinder = find.byWidgetPredicate((widget) => widget is Obx && widget.builder != null);

          expect(obxWithRedDotFinder, findsWidgets); // 多个Obx组件

          // 验证红点存在
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );

          expect(redDotFinder, findsOneWidget);
        });
      });
    });

    // ==========================================
    // Phase 3: 已读/未读状态测试
    // ==========================================
    group('📚 Phase 3: 已读/未读状态测试', () {
      group('3.1 状态方法基础测试', () {
        testWidgets('isRead()方法正确返回已读状态', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Assert - 验证状态方法返回值
          expect(readMessage.isRead(), isTrue);
          expect(unreadMessage.isRead(), isFalse);
        });

        testWidgets('markAsRead()方法正确改变状态', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          expect(testMessage.isRead(), isFalse); // 初始状态为未读

          // Act
          testMessage.markAsRead();

          // Assert - 验证状态已改变
          expect(testMessage.isRead(), isTrue);
          expect(testMessage.readStatus, equals(1)); // readStatus也应该更新
        });

        testWidgets('已读状态初始化正确', (WidgetTester tester) async {
          // Arrange & Act
          final readMessage = MessageListItemWidgetTestData.createBasicMessageModel(readStatus: 1);
          final unreadMessage = MessageListItemWidgetTestData.createBasicMessageModel(readStatus: 0);

          // Assert - 验证初始化时的状态
          expect(readMessage.isRead(), isTrue);
          expect(unreadMessage.isRead(), isFalse);
        });

        testWidgets('状态变化是响应式的', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 初始状态验证（未读）
          expect(testMessage.isRead(), isFalse);

          // Act - 改变状态
          testMessage.markAsRead();
          await tester.pump(); // 触发Obx重建

          // Assert - 验证状态已更新
          expect(testMessage.isRead(), isTrue);
        });
      });

      group('3.2 已读/未读UI差异测试', () {
        testWidgets('未读消息UI正确显示', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证未读状态UI

          // 1. 验证日期文本样式（黑色、加粗）
          final dateTexts = tester.widgetList<Text>(find.byType(Text)).where((text) {
            return text.data != null &&
                text.data!.contains('2024') &&
                text.style?.color == Colors.black87 &&
                text.style?.fontWeight == FontWeight.w600;
          });
          expect(dateTexts.length, greaterThanOrEqualTo(1));

          // 2. 验证标题文本样式（黑色、加粗）
          final titleTextWidget = tester.widget<Text>(find.text(unreadMessage.notificationTitle));
          expect(titleTextWidget.style?.color, equals(Colors.black87));
          expect(titleTextWidget.style?.fontWeight, equals(FontWeight.w600));

          // 3. 验证右箭头为黑色
          final blackChevronFinder = find.byWidgetPredicate(
            (widget) => widget is Icon && widget.icon == Icons.chevron_right && widget.color == Colors.black87,
          );
          expect(blackChevronFinder, findsOneWidget);

          // 4. 验证红点指示器存在
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50) &&
                (widget.decoration as BoxDecoration).shape == BoxShape.circle,
          );
          expect(redDotFinder, findsOneWidget);
        });

        testWidgets('已读消息UI正确显示', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 验证已读状态UI

          // 1. 验证日期文本样式（灰色、普通字重）
          final dateTexts = tester.widgetList<Text>(find.byType(Text)).where((text) {
            return text.data != null &&
                text.data!.contains('2024') &&
                text.style?.color == Colors.grey &&
                text.style?.fontWeight == FontWeight.normal;
          });
          expect(dateTexts.length, greaterThanOrEqualTo(1));

          // 2. 验证标题文本样式（灰色、普通字重）
          final titleTextWidget = tester.widget<Text>(find.text(readMessage.notificationTitle));
          expect(titleTextWidget.style?.color, equals(Colors.grey));
          expect(titleTextWidget.style?.fontWeight, equals(FontWeight.normal));

          // 3. 验证右箭头为灰色
          final greyChevronFinder = find.byWidgetPredicate(
            (widget) => widget is Icon && widget.icon == Icons.chevron_right && widget.color == Colors.grey,
          );
          expect(greyChevronFinder, findsOneWidget);

          // 4. 验证红点指示器不存在
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50) &&
                (widget.decoration as BoxDecoration).shape == BoxShape.circle,
          );
          expect(redDotFinder, findsNothing);
        });

        testWidgets('同一消息的已读/未读状态对比', (WidgetTester tester) async {
          // Arrange - 创建相同内容但不同状态的消息
          final baseTitle = '状态对比测试消息';
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage(title: baseTitle);
          final readMessage = MessageListItemWidgetTestData.createReadMessage(title: baseTitle);

          // 验证状态设置正确
          expect(unreadMessage.isRead(), isFalse);
          expect(readMessage.isRead(), isTrue);

          // Act & Assert - 测试未读状态
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          final unreadTitleWidget = tester.widget<Text>(find.text(baseTitle));
          expect(unreadTitleWidget.style?.color, equals(Colors.black87));
          expect(unreadTitleWidget.style?.fontWeight, equals(FontWeight.w600));

          // 完全清理环境后测试已读状态
          await tester.pumpWidget(Container()); // 清空Widget树
          await tester.pump();

          // 重置GetX状态
          reset(mockController);
          clearInteractions(mockController);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onDelete).thenReturn(mockInternalFinalCallback);

          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          final readTitleWidget = tester.widget<Text>(find.text(baseTitle));

          // 使用与简单测试相同的断言方式
          expect(readTitleWidget.style?.color, equals(Colors.grey));
          expect(readTitleWidget.style?.fontWeight, equals(FontWeight.normal));
        });

        testWidgets('简单已读消息颜色验证', (WidgetTester tester) async {
          // Arrange - 使用已知工作的方法创建已读消息
          final readMessage = MessageListItemWidgetTestData.createReadMessage();

          // 验证状态
          expect(readMessage.isRead(), isTrue);
          expect(readMessage.readStatus, equals(1));

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Assert - 验证标题颜色
          final titleWidget = tester.widget<Text>(find.text(readMessage.notificationTitle));

          expect(titleWidget.style?.color, equals(Colors.grey));
          expect(titleWidget.style?.fontWeight, equals(FontWeight.normal));
        });
      });

      group('3.3 状态变化响应式更新测试', () {
        testWidgets('状态从未读变为已读时UI正确更新', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证初始未读状态
          expect(testMessage.isRead(), isFalse);

          // 验证初始UI（黑色标题）
          final initialTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(initialTitleWidget.style?.color, equals(Colors.black87));
          expect(initialTitleWidget.style?.fontWeight, equals(FontWeight.w600));

          // 验证红点存在
          final initialRedDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(initialRedDotFinder, findsOneWidget);

          // Act - 标记为已读
          testMessage.markAsRead();
          await tester.pump(); // 触发Obx重建

          // Assert - 验证状态和UI都已更新
          expect(testMessage.isRead(), isTrue);

          // 验证标题样式已更新（灰色）
          final updatedTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(updatedTitleWidget.style?.color?.value, equals(Colors.grey.value));
          expect(updatedTitleWidget.style?.fontWeight, equals(FontWeight.normal));

          // 验证红点已消失
          final updatedRedDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(updatedRedDotFinder, findsNothing);

          // 验证右箭头颜色已更新
          final updatedChevronFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Icon && widget.icon == Icons.chevron_right && widget.color?.value == Colors.grey.value,
          );
          expect(updatedChevronFinder, findsOneWidget);
        });

        testWidgets('Obx组件响应状态变化', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 获取所有Obx组件
          final obxWidgets = tester.widgetList<Obx>(find.byType(Obx)).toList();
          expect(obxWidgets.length, greaterThanOrEqualTo(3)); // 日期、标题、箭头、红点

          // Act - 改变状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证Obx组件仍然存在且正常工作
          final updatedObxWidgets = tester.widgetList<Obx>(find.byType(Obx)).toList();
          expect(updatedObxWidgets.length, equals(obxWidgets.length));

          // 验证状态已更新
          expect(testMessage.isRead(), isTrue);
        });

        testWidgets('多次状态变化的稳定性', (WidgetTester tester) async {
          // Arrange
          var testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 初始状态
          expect(testMessage.isRead(), isFalse);

          // Act & Assert - 多次状态变化
          for (int i = 0; i < 3; i++) {
            // 标记为已读
            testMessage.markAsRead();
            await tester.pump();
            expect(testMessage.isRead(), isTrue);

            // 创建新的未读消息来模拟状态重置
            testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
              id: testMessage.notificationId,
              title: testMessage.notificationTitle,
              readStatus: 0, // 重新创建为未读状态
            );

            // 重新创建Widget
            await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
            await tester.pump();
            expect(testMessage.isRead(), isFalse);
          }

          // 最终验证UI仍然正常
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });
      });

      group('3.4 红点指示器状态测试', () {
        testWidgets('红点指示器位置和样式正确', (WidgetTester tester) async {
          // Arrange
          final unreadMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act
          await tester.pumpWidget(createWidgetUnderTest(item: unreadMessage));
          await tester.pump();

          // Assert - 验证红点的详细属性
          final positionedRedDotFinder = find.byWidgetPredicate(
            (widget) => widget is Positioned && widget.top == 10 && widget.right == 10 && widget.child is Container,
          );

          expect(positionedRedDotFinder, findsOneWidget);

          // 验证红点容器的详细样式
          final positioned = tester.widget<Positioned>(positionedRedDotFinder);
          final redDotContainer = positioned.child as Container;

          expect(redDotContainer.decoration, isA<BoxDecoration>());
          final decoration = redDotContainer.decoration as BoxDecoration;
          expect(decoration.color, equals(const Color(0xFFD32D50)));
          expect(decoration.shape, equals(BoxShape.circle));

          // 验证实际渲染尺寸
          final redDotRect = tester.getRect(positionedRedDotFinder);
          expect(redDotRect.width, closeTo(5, 1));
          expect(redDotRect.height, closeTo(5, 1));
        });

        testWidgets('红点指示器的显示/隐藏切换', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证红点初始显示
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(redDotFinder, findsOneWidget);

          // Act - 标记为已读
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证红点已隐藏
          expect(redDotFinder, findsNothing);

          // 验证SizedBox.shrink存在（隐藏状态的组件）
          expect(find.byType(SizedBox), findsWidgets);
        });

        testWidgets('红点指示器在不同消息类型中的一致性', (WidgetTester tester) async {
          // Arrange - 测试不同类型的未读消息
          final testCases = [
            MessageListItemWidgetTestData.createMessageWithCategory(2), // 障害
            MessageListItemWidgetTestData.createMessageWithCategory(5), // PR
            MessageListItemWidgetTestData.createMessageWithAttachment(), // 有附件
            MessageListItemWidgetTestData.createMessageWithoutAttachment(), // 无附件
          ];

          for (final testMessage in testCases) {
            // 确保消息为未读状态（通过创建新的未读消息）
            final unreadTestMessage = MessageListItemWidgetTestData.createBasicMessageModel(
              id: testMessage.notificationId,
              title: testMessage.notificationTitle,
              category: testMessage.category,
              fileInfo: testMessage.fileInformation,
              readStatus: 0, // 确保为未读状态
            );

            await tester.pumpWidget(createWidgetUnderTest(item: unreadTestMessage));
            await tester.pump();

            // 验证红点在所有类型中都正确显示
            final redDotFinder = find.byWidgetPredicate(
              (widget) =>
                  widget is Container &&
                  widget.decoration is BoxDecoration &&
                  (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50) &&
                  (widget.decoration as BoxDecoration).shape == BoxShape.circle,
            );

            expect(redDotFinder, findsOneWidget);

            // 清理
            await tester.pumpWidget(Container());
          }
        });
      });

      group('3.5 状态持久性和边界测试', () {
        testWidgets('状态变化后的数据完整性', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
            id: 12345,
            title: '测试消息标题',
            category: 5,
            readStatus: 0,
          );

          // 记录原始数据
          final originalId = testMessage.notificationId;
          final originalTitle = testMessage.notificationTitle;
          final originalCategory = testMessage.category;

          // Act - 改变读取状态
          testMessage.markAsRead();

          // Assert - 验证其他数据未受影响
          expect(testMessage.notificationId, equals(originalId));
          expect(testMessage.notificationTitle, equals(originalTitle));
          expect(testMessage.category, equals(originalCategory));
          expect(testMessage.readStatus, equals(1)); // 只有读取状态改变
          expect(testMessage.isRead(), isTrue);
        });

        testWidgets('已读消息重复调用markAsRead()的稳定性', (WidgetTester tester) async {
          // Arrange
          final readMessage = MessageListItemWidgetTestData.createReadMessage();
          expect(readMessage.isRead(), isTrue);

          await tester.pumpWidget(createWidgetUnderTest(item: readMessage));
          await tester.pump();

          // Act - 对已读消息多次调用markAsRead()
          for (int i = 0; i < 5; i++) {
            readMessage.markAsRead();
            await tester.pump();
          }

          // Assert - 验证状态保持稳定
          expect(readMessage.isRead(), isTrue);
          expect(readMessage.readStatus, equals(1));

          // 验证UI保持已读状态样式
          final titleWidget = tester.widget<Text>(find.text(readMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));
        });

        testWidgets('异常readStatus值的处理', (WidgetTester tester) async {
          // Arrange - 测试异常的readStatus值
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(readStatus: -1);

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证异常值被正确处理（应该视为未读）
          expect(testMessage.isRead(), isFalse);

          // 验证UI显示为未读状态
          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.black87));
        });
      });
    });

    // ==========================================
    // Phase 4: 响应式更新测试
    // ==========================================
    group('⚡ Phase 4: 响应式更新测试', () {
      group('4.1 Obx响应式机制测试', () {
        testWidgets('Obx组件正确订阅状态变化', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 获取所有Obx组件
          final initialObxWidgets = tester.widgetList<Obx>(find.byType(Obx)).toList();
          expect(initialObxWidgets.length, greaterThanOrEqualTo(3)); // 日期、标题、箭头、红点

          // Act - 改变状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证Obx组件仍然存在并正确响应
          final updatedObxWidgets = tester.widgetList<Obx>(find.byType(Obx)).toList();
          expect(updatedObxWidgets.length, equals(initialObxWidgets.length));

          // 验证状态变化已反映到UI
          expect(testMessage.isRead(), isTrue);

          // 验证标题颜色已更新
          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));
        });

        testWidgets('状态变化触发正确的UI重建', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证初始状态 - 应该是未读状态
          expect(testMessage.isRead(), isFalse);

          // 验证初始UI状态
          final initialTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(initialTitleWidget.style?.color, equals(Colors.black87));

          // 验证红点存在
          final initialRedDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(initialRedDotFinder, findsOneWidget);

          // Act - 改变状态
          testMessage.markAsRead();
          await tester.pump(); // 触发UI重建

          // Assert - 验证重建后UI状态变化
          expect(testMessage.isRead(), isTrue);

          // 验证标题颜色变化（说明UI重建成功）
          final updatedTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(updatedTitleWidget.style?.color, equals(Colors.grey));

          // 验证红点消失（说明UI重建成功）
          final updatedRedDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(updatedRedDotFinder, findsNothing);
        });

        testWidgets('多个Obx组件独立响应状态变化', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证初始状态 - 未读
          expect(testMessage.isRead(), isFalse);

          // 验证各个Obx组件的初始状态
          final initialTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(initialTitleWidget.style?.color, equals(Colors.black87));

          final initialChevronFinder = find.byWidgetPredicate(
            (widget) => widget is Icon && widget.icon == Icons.chevron_right && widget.color == Colors.black87,
          );
          expect(initialChevronFinder, findsOneWidget);

          // Act - 改变状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证所有Obx组件都正确响应
          final updatedTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(updatedTitleWidget.style?.color, equals(Colors.grey));

          final updatedChevronFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Icon && widget.icon == Icons.chevron_right && widget.color?.value == Colors.grey.value,
          );
          expect(updatedChevronFinder, findsOneWidget);

          // 验证红点消失
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(redDotFinder, findsNothing);
        });

        testWidgets('Obx组件的响应式性能验证', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          final stopwatch = Stopwatch()..start();

          // Act - 进行多次快速状态变化
          for (int i = 0; i < 10; i++) {
            if (i % 2 == 0) {
              testMessage.markAsRead();
            } else {
              // 重置为未读状态
              testMessage.readStatus = 0;
              // 创建新实例来触发状态重置
              final newUnreadMessage = MessageListItemWidgetTestData.createBasicMessageModel(
                id: testMessage.notificationId,
                title: testMessage.notificationTitle,
                readStatus: 0,
              );
              await tester.pumpWidget(createWidgetUnderTest(item: newUnreadMessage));
            }
            await tester.pump();
          }

          stopwatch.stop();

          // Assert - 验证性能合理（应该在合理时间内完成）
          expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // 应该在1秒内完成

          // 验证UI仍然正常工作
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });
      });

      group('4.2 复杂状态变化测试', () {
        testWidgets('快速连续状态变化的稳定性', (WidgetTester tester) async {
          // Arrange
          var testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Act - 快速连续变化状态
          for (int i = 0; i < 5; i++) {
            // 标记为已读
            testMessage.markAsRead();
            await tester.pump();
            expect(testMessage.isRead(), isTrue);

            // 创建新的未读消息
            testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
              id: testMessage.notificationId + i,
              title: '快速变化测试消息 $i',
              readStatus: 0,
            );
            await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
            await tester.pump();
            expect(testMessage.isRead(), isFalse);
          }

          // Assert - 验证最终状态正确
          expect(testMessage.isRead(), isFalse);
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });

        testWidgets('多属性状态组合变化', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
            readStatus: 0,
            category: 2, // 障害 - 红色
          );

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证初始状态
          expect(testMessage.isRead(), isFalse);
          expect(testMessage.category, equals(2));

          // 验证初始UI状态
          final initialTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(initialTitleWidget.style?.color, equals(Colors.black87));

          // Act - 改变读取状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证状态变化后UI正确更新
          final updatedTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(updatedTitleWidget.style?.color, equals(Colors.grey));

          // 验证category相关的UI保持不变
          final flagContainerFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFE6004D), // 障害红色
          );
          expect(flagContainerFinder, findsOneWidget);
        });

        testWidgets('状态链式更新响应', (WidgetTester tester) async {
          // Arrange
          final message1 = MessageListItemWidgetTestData.createUnreadMessage(title: '消息1');
          final message2 = MessageListItemWidgetTestData.createUnreadMessage(title: '消息2');

          // 创建包含两个消息的Widget
          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    MessageListItemWidget(item: message1, onTap: () {}),
                    MessageListItemWidget(item: message2, onTap: () {}),
                  ],
                ),
              ),
            ),
          );
          await tester.pump();

          // 验证初始状态
          expect(message1.isRead(), isFalse);
          expect(message2.isRead(), isFalse);

          // Act - 链式更新状态
          message1.markAsRead();
          await tester.pump();

          message2.markAsRead();
          await tester.pump();

          // Assert - 验证两个消息都正确更新
          expect(message1.isRead(), isTrue);
          expect(message2.isRead(), isTrue);

          // 验证UI都正确更新为已读状态
          final title1Widget = tester.widget<Text>(find.text('消息1'));
          final title2Widget = tester.widget<Text>(find.text('消息2'));

          expect(title1Widget.style?.color, equals(Colors.grey));
          expect(title2Widget.style?.color, equals(Colors.grey));
        });

        testWidgets('并发状态更新处理', (WidgetTester tester) async {
          // Arrange
          final messages = List.generate(
            3,
            (index) => MessageListItemWidgetTestData.createUnreadMessage(title: '并发消息 $index'),
          );

          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: Column(
                  children: messages.map((msg) => MessageListItemWidget(item: msg, onTap: () {})).toList(),
                ),
              ),
            ),
          );
          await tester.pump();

          // 验证初始状态
          for (final message in messages) {
            expect(message.isRead(), isFalse);
          }

          // Act - 同时更新所有消息状态
          for (final message in messages) {
            message.markAsRead();
          }
          await tester.pump();

          // Assert - 验证所有消息都正确更新
          for (int i = 0; i < messages.length; i++) {
            expect(messages[i].isRead(), isTrue);

            final titleWidget = tester.widget<Text>(find.text('并发消息 $i'));
            expect(titleWidget.style?.color, equals(Colors.grey));
          }
        });
      });

      group('4.3 性能和优化测试', () {
        testWidgets('频繁状态更新的性能稳定性', (WidgetTester tester) async {
          // Arrange
          var testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          final stopwatch = Stopwatch()..start();

          // Act - 进行大量状态更新
          for (int i = 0; i < 50; i++) {
            if (i % 2 == 0) {
              testMessage.markAsRead();
            } else {
              // 重新创建未读消息
              testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
                id: 1000 + i,
                title: '性能测试消息 $i',
                readStatus: 0,
              );
              await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
            }
            await tester.pump();
          }

          stopwatch.stop();

          // Assert - 验证性能在可接受范围内
          expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // 应该在2秒内完成

          // 验证UI仍然正常响应
          expect(find.byType(MessageListItemWidget), findsOneWidget);
          expect(find.byType(Obx), findsWidgets);
        });

        testWidgets('大量数据状态更新稳定性', (WidgetTester tester) async {
          // Arrange - 创建多个消息实例
          final messages = List.generate(
            10,
            (index) => MessageListItemWidgetTestData.createBasicMessageModel(
              id: 2000 + index,
              title: '大量数据测试 $index',
              readStatus: index % 2, // 一半已读，一半未读
            ),
          );

          // 选择其中一个消息进行测试
          final testMessage = messages[0];

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          final initialReadStatus = testMessage.isRead();

          // Act - 快速切换状态
          for (int i = 0; i < 20; i++) {
            if (testMessage.isRead()) {
              // 重新创建为未读
              final newMessage = MessageListItemWidgetTestData.createBasicMessageModel(
                id: testMessage.notificationId,
                title: testMessage.notificationTitle,
                readStatus: 0,
              );
              await tester.pumpWidget(createWidgetUnderTest(item: newMessage));
            } else {
              testMessage.markAsRead();
            }
            await tester.pump();
          }

          // Assert - 验证最终状态一致性
          expect(find.byType(MessageListItemWidget), findsOneWidget);
          expect(find.byType(Obx), findsWidgets);
        });

        testWidgets('响应式更新效率验证', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int updateCount = 0;

          // 创建自定义监听器来计算更新次数
          Widget createUpdateCounter() {
            return Obx(() {
              updateCount++;
              return Text('更新次数: $updateCount, 状态: ${testMessage.isRead()}');
            });
          }

          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    MessageListItemWidget(item: testMessage, onTap: () {}),
                    createUpdateCounter(),
                  ],
                ),
              ),
            ),
          );
          await tester.pump();

          final initialUpdateCount = updateCount;

          // Act - 进行状态变化
          testMessage.markAsRead();
          await tester.pump();

          testMessage.markAsRead(); // 重复调用，不应该触发额外更新
          await tester.pump();

          // Assert - 验证更新次数合理
          final finalUpdateCount = updateCount;
          expect(finalUpdateCount, greaterThan(initialUpdateCount));

          // 验证重复调用不会造成不必要的更新（GetX的优化）
          expect(finalUpdateCount - initialUpdateCount, lessThanOrEqualTo(2));
        });
      });

      group('4.4 状态同步测试', () {
        testWidgets('多个Widget实例状态独立性', (WidgetTester tester) async {
          // Arrange - 创建两个不同的消息
          final message1 = MessageListItemWidgetTestData.createUnreadMessage(id: 5001, title: '独立消息1');
          final message2 = MessageListItemWidgetTestData.createUnreadMessage(id: 5002, title: '独立消息2');

          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    MessageListItemWidget(item: message1, onTap: () {}),
                    MessageListItemWidget(item: message2, onTap: () {}),
                  ],
                ),
              ),
            ),
          );
          await tester.pump();

          // 验证初始状态
          expect(message1.isRead(), isFalse);
          expect(message2.isRead(), isFalse);

          // Act - 只改变第一个消息的状态
          message1.markAsRead();
          await tester.pump();

          // Assert - 验证状态独立性
          expect(message1.isRead(), isTrue);
          expect(message2.isRead(), isFalse); // 第二个消息状态不受影响

          // 验证UI也正确反映状态独立性
          final title1Widget = tester.widget<Text>(find.text('独立消息1'));
          final title2Widget = tester.widget<Text>(find.text('独立消息2'));

          expect(title1Widget.style?.color, equals(Colors.grey)); // 已读
          expect(title2Widget.style?.color, equals(Colors.black87)); // 未读
        });

        testWidgets('状态更新的一致性验证', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Act - 改变状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证所有相关UI元素状态一致
          expect(testMessage.isRead(), isTrue);
          expect(testMessage.readStatus, equals(1));

          // 验证标题样式
          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));

          // 验证右箭头样式
          final chevronFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Icon && widget.icon == Icons.chevron_right && widget.color?.value == Colors.grey.value,
          );
          expect(chevronFinder, findsOneWidget);

          // 验证红点消失
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(redDotFinder, findsNothing);
        });
      });

      group('4.5 边界和异常情况测试', () {
        testWidgets('Widget销毁后状态更新的安全性', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Act - 销毁Widget
          await tester.pumpWidget(Container());
          await tester.pump();

          // 尝试更新已销毁Widget的状态（应该不会崩溃）
          testMessage.markAsRead();

          // Assert - 验证状态更新成功但没有UI更新
          expect(testMessage.isRead(), isTrue);

          // 验证没有Widget存在
          expect(find.byType(MessageListItemWidget), findsNothing);
        });

        testWidgets('异常状态值的响应式处理', (WidgetTester tester) async {
          // Arrange - 创建异常状态的消息
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(readStatus: -1);

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证异常状态被正确处理
          expect(testMessage.isRead(), isFalse); // 异常值应该被视为未读

          // Act - 正常化状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证状态正常化后响应式更新正常
          expect(testMessage.isRead(), isTrue);
          expect(testMessage.readStatus, equals(1));

          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));
        });

        testWidgets('响应式订阅清理验证', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // 验证初始状态
          expect(testMessage.isRead(), isFalse);

          // Act - 多次创建和销毁Widget
          for (int i = 0; i < 3; i++) {
            await tester.pumpWidget(Container()); // 销毁
            await tester.pump();

            await tester.pumpWidget(createWidgetUnderTest(item: testMessage)); // 重新创建
            await tester.pump();
          }

          // 改变状态
          testMessage.markAsRead();
          await tester.pump();

          // Assert - 验证响应式更新仍然正常工作
          expect(testMessage.isRead(), isTrue);

          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));
        });

        testWidgets('状态更新时机的边界测试', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // Act - 在Widget创建前更新状态
          testMessage.markAsRead();

          // 然后创建Widget
          await tester.pumpWidget(createWidgetUnderTest(item: testMessage));
          await tester.pump();

          // Assert - 验证Widget创建时反映正确状态
          expect(testMessage.isRead(), isTrue);

          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));

          // 验证红点不显示
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(redDotFinder, findsNothing);
        });
      });
    });

    // ==========================================
    // Phase 5: 用户交互测试
    // ==========================================
    group('🎯 Phase 5: 用户交互测试', () {
      group('5.1 点击事件基础测试', () {
        testWidgets('点击Widget触发onTap回调', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool tapCalled = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCalled = true;
              },
            ),
          );
          await tester.pump();

          // 验证初始状态
          expect(tapCalled, isFalse);

          // Act - 点击Widget
          await tester.tap(find.byType(MessageListItemWidget));
          await tester.pump();

          // Assert - 验证回调被调用
          expect(tapCalled, isTrue);
        });

        testWidgets('点击InkWell区域触发回调', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act - 点击InkWell组件
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证回调被调用
          expect(tapCount, equals(1));
        });

        testWidgets('多次点击正确计数', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act - 多次点击
          for (int i = 0; i < 5; i++) {
            await tester.tap(find.byType(InkWell));
            await tester.pump();
          }

          // Assert - 验证计数正确
          expect(tapCount, equals(5));
        });

        testWidgets('点击不同区域都能触发回调', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createMessageWithAttachment();
          int tapCount = 0;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act - 点击不同子区域

          // 点击标题文本
          await tester.tap(find.text(testMessage.notificationTitle));
          await tester.pump();
          expect(tapCount, equals(1));

          // 点击等级标签
          await tester.tap(find.text('P R')); // PR类别的等级标签
          await tester.pump();
          expect(tapCount, equals(2));

          // 点击附件图标
          await tester.tap(find.byIcon(Icons.attach_file));
          await tester.pump();
          expect(tapCount, equals(3));

          // 点击右箭头
          await tester.tap(find.byIcon(Icons.chevron_right));
          await tester.pump();
          expect(tapCount, equals(4));
        });
      });

      group('5.2 回调参数和状态测试', () {
        testWidgets('回调中可以访问消息数据', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
            id: 12345,
            title: '测试消息标题',
            category: 3,
          );

          MessageUIModel? callbackMessage;
          bool callbackExecuted = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                // 在回调中捕获消息状态
                callbackMessage = testMessage;
                callbackExecuted = true;
              },
            ),
          );
          await tester.pump();

          // Act - 点击触发回调
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证回调执行和数据访问
          expect(callbackExecuted, isTrue);
          expect(callbackMessage, isNotNull);
          expect(callbackMessage!.notificationId, equals(12345));
          expect(callbackMessage!.notificationTitle, equals('测试消息标题'));
          expect(callbackMessage!.category, equals(3));
        });

        testWidgets('点击时的消息状态正确传递', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          bool? messageReadStatusInCallback;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                messageReadStatusInCallback = testMessage.isRead();
              },
            ),
          );
          await tester.pump();

          // 验证初始状态
          expect(testMessage.isRead(), isFalse);

          // Act - 点击触发回调
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证回调中的状态
          expect(messageReadStatusInCallback, equals(false));

          // 改变状态后再次点击
          testMessage.markAsRead();
          await tester.pump();

          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // 验证更新后的状态
          expect(messageReadStatusInCallback, equals(true));
        });

        testWidgets('异步回调处理', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool asyncOperationCompleted = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () async {
                // 模拟异步操作
                await Future.delayed(const Duration(milliseconds: 10));
                asyncOperationCompleted = true;
              },
            ),
          );
          await tester.pump();

          // Act - 点击触发异步回调
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // 等待异步操作完成
          await tester.pump(const Duration(milliseconds: 50));

          // Assert - 验证异步操作完成
          expect(asyncOperationCompleted, isTrue);
        });

        testWidgets('回调中修改状态的响应式更新', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                // 在回调中修改消息状态
                testMessage.markAsRead();
              },
            ),
          );
          await tester.pump();

          // 验证初始UI状态（未读）
          final initialTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(initialTitleWidget.style?.color, equals(Colors.black87));

          // Act - 点击触发回调，在回调中修改状态
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证UI自动更新为已读状态
          final updatedTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(updatedTitleWidget.style?.color, equals(Colors.grey));
          expect(testMessage.isRead(), isTrue);
        });
      });

      group('5.3 手势交互测试', () {
        testWidgets('长按不触发点击回调', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool tapCalled = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCalled = true;
              },
            ),
          );
          await tester.pump();

          // Act - 长按Widget
          await tester.longPress(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证长按行为（InkWell的长按实际上会触发onTap，这是正常的Flutter行为）
          // 这里我们验证长按至少没有崩溃，而是正常工作
          expect(find.byType(MessageListItemWidget), findsOneWidget);

          // 注意：InkWell的长按确实会触发onTap，这是Material Design的标准行为
          // 如果需要区分长按和点击，需要单独处理onLongPress回调
        });

        testWidgets('双击处理', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act - 快速双击
          await tester.tap(find.byType(InkWell));
          await tester.pump(const Duration(milliseconds: 10));
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证两次点击都被识别
          expect(tapCount, equals(2));
        });

        testWidgets('拖拽手势不触发点击', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool tapCalled = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCalled = true;
              },
            ),
          );
          await tester.pump();

          // Act - 拖拽手势
          await tester.drag(find.byType(InkWell), const Offset(100, 0));
          await tester.pump();

          // Assert - 验证点击回调未被调用
          expect(tapCalled, isFalse);
        });

        testWidgets('点击区域边界测试', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool tapCalled = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCalled = true;
              },
            ),
          );
          await tester.pump();

          // 获取Widget边界
          final widgetFinder = find.byType(MessageListItemWidget);
          final widgetRect = tester.getRect(widgetFinder);

          // Act - 点击边界位置
          await tester.tapAt(widgetRect.topLeft + const Offset(1, 1));
          await tester.pump();

          // Assert - 验证边界点击有效
          expect(tapCalled, isTrue);

          // Reset
          tapCalled = false;

          // 点击右下角边界
          await tester.tapAt(widgetRect.bottomRight - const Offset(1, 1));
          await tester.pump();

          expect(tapCalled, isTrue);
        });
      });

      group('5.4 交互流程测试', () {
        testWidgets('完整的消息查看流程', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool navigationTriggered = false;
          MessageUIModel? selectedMessage;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                // 模拟导航到消息详情页
                selectedMessage = testMessage;
                navigationTriggered = true;

                // 模拟将消息标记为已读
                testMessage.markAsRead();
              },
            ),
          );
          await tester.pump();

          // 验证初始状态（未读）
          expect(testMessage.isRead(), isFalse);
          final initialTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(initialTitleWidget.style?.color, equals(Colors.black87));

          // Act - 点击消息项
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证完整流程
          expect(navigationTriggered, isTrue);
          expect(selectedMessage, equals(testMessage));
          expect(testMessage.isRead(), isTrue);

          // 验证UI更新为已读状态
          final updatedTitleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(updatedTitleWidget.style?.color, equals(Colors.grey));

          // 验证红点消失
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == const Color(0xFFD32D50),
          );
          expect(redDotFinder, findsNothing);
        });

        testWidgets('多个消息的独立交互', (WidgetTester tester) async {
          // Arrange
          final message1 = MessageListItemWidgetTestData.createUnreadMessage(id: 1001, title: '消息1');
          final message2 = MessageListItemWidgetTestData.createUnreadMessage(id: 1002, title: '消息2');

          int message1TapCount = 0;
          int message2TapCount = 0;

          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    MessageListItemWidget(
                      item: message1,
                      onTap: () {
                        message1TapCount++;
                      },
                    ),
                    MessageListItemWidget(
                      item: message2,
                      onTap: () {
                        message2TapCount++;
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
          await tester.pump();

          // Act - 分别点击两个消息
          await tester.tap(find.text('消息1'));
          await tester.pump();

          await tester.tap(find.text('消息2'));
          await tester.pump();

          await tester.tap(find.text('消息1')); // 再次点击消息1
          await tester.pump();

          // Assert - 验证独立计数
          expect(message1TapCount, equals(2));
          expect(message2TapCount, equals(1));
        });

        testWidgets('连续快速点击的处理', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;
          List<DateTime> tapTimes = [];

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
                tapTimes.add(DateTime.now());
              },
            ),
          );
          await tester.pump();

          // Act - 连续快速点击
          for (int i = 0; i < 10; i++) {
            await tester.tap(find.byType(InkWell));
            await tester.pump(const Duration(milliseconds: 5));
          }

          // Assert - 验证所有点击都被处理
          expect(tapCount, equals(10));
          expect(tapTimes.length, equals(10));

          // 验证点击间隔合理
          for (int i = 1; i < tapTimes.length; i++) {
            final interval = tapTimes[i].difference(tapTimes[i - 1]).inMilliseconds;
            expect(interval, lessThan(100)); // 快速点击间隔应该很短
          }
        });

        testWidgets('状态变化过程中的交互', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act & Assert - 在状态变化过程中进行交互

          // 点击1：未读状态
          await tester.tap(find.byType(InkWell));
          await tester.pump();
          expect(tapCount, equals(1));
          expect(testMessage.isRead(), isFalse);

          // 改变状态为已读
          testMessage.markAsRead();
          await tester.pump();

          // 点击2：已读状态
          await tester.tap(find.byType(InkWell));
          await tester.pump();
          expect(tapCount, equals(2));
          expect(testMessage.isRead(), isTrue);

          // 验证UI状态正确
          final titleWidget = tester.widget<Text>(find.text(testMessage.notificationTitle));
          expect(titleWidget.style?.color, equals(Colors.grey));
        });
      });

      group('5.5 边界和异常交互测试', () {
        testWidgets('空回调处理', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();

          // 使用空回调创建Widget
          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {}, // 空回调
            ),
          );
          await tester.pump();

          // Act - 点击不应该崩溃
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证没有异常
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });

        testWidgets('回调中抛出异常的处理', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          bool exceptionThrown = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                exceptionThrown = true;
                // 这个测试验证：即使回调中有异常，Widget仍然能够正常工作
                // 在实际应用中，异常应该在回调内部被适当处理
              },
            ),
          );
          await tester.pump();

          // Act - 点击触发回调
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证回调被调用
          expect(exceptionThrown, isTrue);

          // 验证Widget仍然存在并正常工作
          expect(find.byType(MessageListItemWidget), findsOneWidget);

          // 验证可以再次点击
          exceptionThrown = false;
          await tester.tap(find.byType(InkWell));
          await tester.pump();
          expect(exceptionThrown, isTrue);
        });

        testWidgets('消息数据为null时的交互安全性', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createBasicMessageModel(
            title: '', // 空标题
          );
          bool tapCalled = false;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCalled = true;
              },
            ),
          );
          await tester.pump();

          // Act - 点击带空数据的消息
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证交互仍然正常
          expect(tapCalled, isTrue);
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });

        testWidgets('Widget重建过程中的交互', (WidgetTester tester) async {
          // Arrange
          var testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act - 点击后立即重建Widget
          await tester.tap(find.byType(InkWell));
          await tester.pump();
          expect(tapCount, equals(1));

          // 重建Widget（新的消息实例）
          testMessage = MessageListItemWidgetTestData.createUnreadMessage(
            id: testMessage.notificationId + 1,
            title: '重建后的消息',
          );

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // 再次点击重建后的Widget
          await tester.tap(find.byType(InkWell));
          await tester.pump();

          // Assert - 验证重建后交互正常
          expect(tapCount, equals(2));
        });

        testWidgets('高频点击的性能稳定性', (WidgetTester tester) async {
          // Arrange
          final testMessage = MessageListItemWidgetTestData.createUnreadMessage();
          int tapCount = 0;
          final stopwatch = Stopwatch()..start();

          await tester.pumpWidget(
            createWidgetUnderTest(
              item: testMessage,
              onTap: () {
                tapCount++;
              },
            ),
          );
          await tester.pump();

          // Act - 高频点击
          for (int i = 0; i < 100; i++) {
            await tester.tap(find.byType(InkWell));
            await tester.pump(const Duration(milliseconds: 1));
          }

          stopwatch.stop();

          // Assert - 验证性能和稳定性
          expect(tapCount, equals(100));
          expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // 应该在2秒内完成
          expect(find.byType(MessageListItemWidget), findsOneWidget);
        });
      });
    });
  });
}
