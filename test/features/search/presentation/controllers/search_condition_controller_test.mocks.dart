// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/search/presentation/controllers/search_condition_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:ui' as _i8;

import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i7;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/search/domain/usecases/get_search_conditions_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i9;
import 'package:flutter/cupertino.dart' as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetSearchIdUseCase_0 extends _i1.SmartFake
    implements _i2.GetSearchIdUseCase {
  _FakeGetSearchIdUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetSearchConditionResult_1 extends _i1.SmartFake
    implements _i3.GetSearchConditionResult {
  _FakeGetSearchConditionResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetSearchConditionsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetSearchConditionsUseCase extends _i1.Mock
    implements _i3.GetSearchConditionsUseCase {
  MockGetSearchConditionsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.GetSearchIdUseCase get getSearchIdUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getSearchIdUseCase),
            returnValue: _FakeGetSearchIdUseCase_0(
              this,
              Invocation.getter(#getSearchIdUseCase),
            ),
          )
          as _i2.GetSearchIdUseCase);

  @override
  _i4.Future<_i3.GetSearchConditionResult> call(
    _i3.GetSearchConditionParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i4.Future<_i3.GetSearchConditionResult>.value(
              _FakeGetSearchConditionResult_1(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i4.Future<_i3.GetSearchConditionResult>);

  @override
  _i4.Future<List<String>> saveHistorySearchInput({
    required String? inputKey,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#saveHistorySearchInput, [], {
              #inputKey: inputKey,
            }),
            returnValue: _i4.Future<List<String>>.value(<String>[]),
          )
          as _i4.Future<List<String>>);

  @override
  _i4.Future<void> clearAllHistorySearchInput() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllHistorySearchInput, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i5.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i4.Future<dynamic>.value(),
          )
          as _i4.Future<dynamic>);

  @override
  _i4.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i4.Future<dynamic>.value(),
          )
          as _i4.Future<dynamic>);

  @override
  _i4.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<dynamic> toAssetDetail(_i6.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i4.Future<dynamic>.value(),
          )
          as _i4.Future<dynamic>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i7.DialogService {
  MockDialogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i8.VoidCallback? onConfirm,
    _i8.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i9.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i8.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i10.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i9.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}
