import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_list_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/services/category_count_processing_service_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/repositories/category_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_count_processing_service.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_count_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'category_count_processing_service_impl_test.mocks.dart';

@GenerateMocks([CategoryRepository])
void main() {
  late CategoryCountProcessingService countProcessingService;
  late MockCategoryRepository repository;
  late GetCategoryCountUseCase getCategoryCountUseCase;
  setUp(() {
    LogUtil.initialize();
    repository = MockCategoryRepository();
    getCategoryCountUseCase = GetCategoryCountUseCase(repository);
    countProcessingService = CategoryCountProcessingServiceImpl(getCategoryCountUseCase: getCategoryCountUseCase);
  });

  group('description', () {
    test('case 1', () {
      when(
        repository.getCategoryCount(
          params: anyNamed('params'),
          searchId: anyNamed('searchId'),
          keyword: anyNamed('keyword'),
        ),
      ).thenAnswer(
        (_) async => CategoryItemValueListResponse(
          categoryItemValueList: [
            CategoryItemValueModel(categoryItemValue: 'すべて', count: 239, homeImageMobileDisplayFlg: false),
            CategoryItemValueModel(categoryItemValue: '-10000.00001', count: 1, homeImageMobileDisplayFlg: false),
            CategoryItemValueModel(categoryItemValue: '', count: 238, homeImageMobileDisplayFlg: false),
          ],
        ),
      );
      countProcessingService.fetchCategoryCount(
        GetCategoryAllListParams(
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
            ),
          ],
          categoryList: [
            CategoryData(
              titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              categoryInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
              categorySelectedData: CategorySelectedData(
                categoryItemId: 12493079,
                itemType: 'number',
                index: 0,
                itFinished: false,
              ),
            ),
          ],
        ),
        [
          CategoryData(
            titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
            categoryInfo: const CategoryModel(
              itemId: 12493079,
              itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              itemName: 'itemName',
              itemType: 'itemType',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 12493079,
              categoryItemValue: '321',
              itemType: 'number',
              index: 0,
              itFinished: false,
            ),
          ),
          CategoryData(
            titleVal: 'FXH_Master_All(数字_小数点桁数12)',
            categoryInfo: const CategoryModel(
              itemId: 12493082,
              itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
              itemName: 'FXH_Master_All',
              itemType: 'master',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemValue: '123',
              categoryItemId: 12493082,
              categorySubItemId: 962060,
              itemType: 'master',
              index: 1,
              itFinished: false,
            ),
          ),
        ],
      );
    });

    test('case 2 repository return []', () {
      when(
        repository.getCategoryCount(
          params: anyNamed('params'),
          searchId: anyNamed('searchId'),
          keyword: anyNamed('keyword'),
        ),
      ).thenAnswer((_) async => CategoryItemValueListResponse(categoryItemValueList: []));
      countProcessingService.fetchCategoryCount(
        GetCategoryAllListParams(
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
            ),
          ],
          categoryList: [
            CategoryData(
              titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              categoryInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
              categorySelectedData: CategorySelectedData(
                categoryItemId: 12493079,
                itemType: 'number',
                index: 0,
                itFinished: false,
              ),
            ),
          ],
        ),
        [
          CategoryData(
            titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
            categoryInfo: const CategoryModel(
              itemId: 12493079,
              itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              itemName: 'itemName',
              itemType: 'itemType',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 12493079,
              categoryItemValue: '321',
              itemType: 'number',
              index: 0,
              itFinished: false,
            ),
          ),
          CategoryData(
            titleVal: 'FXH_Master_All(数字_小数点桁数12)',
            categoryInfo: const CategoryModel(
              itemId: 12493082,
              itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
              itemName: 'FXH_Master_All',
              itemType: 'master',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemValue: '123',
              categoryItemId: 12493082,
              categorySubItemId: 962060,
              itemType: 'master',
              index: 1,
              itFinished: false,
            ),
          ),
        ],
      );
    });

    test('case 3 has number', () {
      when(
        repository.getCategoryCount(
          params: anyNamed('params'),
          searchId: anyNamed('searchId'),
          keyword: anyNamed('keyword'),
        ),
      ).thenAnswer(
        (_) async => CategoryItemValueListResponse(
          categoryItemValueList: [
            CategoryItemValueModel(categoryItemValue: 'すべて', count: 239, homeImageMobileDisplayFlg: false),
            CategoryItemValueModel(categoryItemValue: '-10000.00001', count: 1, homeImageMobileDisplayFlg: false),
            CategoryItemValueModel(categoryItemValue: '2', count: 238, homeImageMobileDisplayFlg: false),
          ],
        ),
      );
      countProcessingService.fetchCategoryCount(
        GetCategoryAllListParams(
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
            ),
          ],
          categoryList: [
            CategoryData(
              titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              categoryInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
              categorySelectedData: CategorySelectedData(
                categoryItemId: 12493079,
                itemType: 'number',
                index: 0,
                itFinished: false,
              ),
            ),
          ],
        ),
        [
          CategoryData(
            titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
            categoryInfo: const CategoryModel(
              itemId: 12493079,
              itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              itemName: 'itemName',
              itemType: 'itemType',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 12493079,
              categoryItemValue: '321',
              itemType: 'number',
              index: 0,
              itFinished: false,
            ),
          ),
          CategoryData(
            titleVal: 'FXH_Master_All(数字_小数点桁数12)',
            categoryInfo: const CategoryModel(
              itemId: 12493082,
              itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
              itemName: 'FXH_Master_All',
              itemType: 'master',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemValue: '123',
              categoryItemId: 12493082,
              categorySubItemId: 962060,
              itemType: 'master',
              index: 1,
              itFinished: false,
            ),
          ),
        ],
      );
    });
  });
}
