import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_detail_usecase.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/controllers/message_content_controller.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_file_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_ui_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/controllers/download_controller.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:get/get.dart';
import 'dart:async';

// 自动生成 mock 类
@GenerateMocks([MessageDetailUsecase, DownloadController, S3Repository, NavigationService])
import 'message_content_controller_test.mocks.dart';

/// 测试数据辅助类
class MessageContentControllerTestData {
  /// 创建测试用的MessageUIModel
  static MessageUIModel createTestUIModel({
    String? title,
    String? date,
    String? body,
    String? fileInfo,
    int? notificationId,
    int? readStatus,
    int? category,
  }) {
    return MessageUIModel(
      activeStartDate: date ?? '2024-01-15 10:30:00',
      notificationTitle: title ?? 'Test Notification Title',
      notificationBody: body ?? 'Test notification body content',
      fileInformation: fileInfo ?? '[]',
      readStatus: readStatus ?? 0,
      notificationId: notificationId ?? 12345,
      category: category ?? 1,
    );
  }

  /// 创建不同类型的MessageUIModel用于测试
  static List<MessageUIModel> createVariousUIModels() {
    return [
      createTestUIModel(title: 'Empty Title', date: '', body: ''),
      createTestUIModel(title: 'Long Title' * 10, date: '2024-01-15 10:30:00'),
      createTestUIModel(title: 'Special Chars Title 🎉', fileInfo: '["file1.pdf"]'),
      createTestUIModel(title: 'Normal Message', date: '2024-12-31 23:59:59'),
    ];
  }

  /// 创建notificationId测试用例
  static List<int> createNotificationIdTestCases() {
    return [
      0, // 边界情况 - 零
      1, // 最小有效值
      12345, // 正常值
      999999, // 大值
      -1, // 负数（无效但需要处理）
    ];
  }

  /// 创建日期格式化测试用例
  static Map<String, String> createDateFormatTestCases() {
    return {
      '2024-01-15 10:30:00': '2024-01-15', // 标准格式
      '2024-12-31 23:59:59': '2024-12-31', // 年末日期
      '2024-01-15': '2024-01-15', // 仅日期部分
      '2024-01': '2024-01', // 短格式
      '2024': '2024', // 仅年份
      '': '', // 空字符串
      'invalid-date': 'invalid-da', // 无效格式，取前10位
    };
  }

  /// 创建下载文件测试数据
  static Map<String, String> createDownloadTestCases() {
    return {
      'https://example.com/file.pdf': 'document.pdf',
      'https://test.com/image.jpg': 'photo.jpg',
      'https://domain.com/data.xlsx': 'spreadsheet.xlsx',
      '': 'empty_url_file.txt',
    };
  }

  /// 创建带文件数据的MessageUIModel用于onTapFileItem测试
  static MessageUIModel createModelWithFiles({int fileCount = 3}) {
    final files = List.generate(
      fileCount,
      (index) => MessageFileUIModel(
        url: 'file_$index.pdf',
        fileName: 'Document_$index.pdf',
        uploadDate: '2024-01-${15 + index}',
      ),
    );

    return MessageUIModel(
      activeStartDate: '2024-01-15 10:30:00',
      notificationTitle: 'Test Message with Files',
      notificationBody: 'Test content with file attachments',
      readStatus: 0,
      notificationId: 12345,
      category: 1,
      files: files,
    );
  }

  /// 创建空文件列表的MessageUIModel
  static MessageUIModel createModelWithoutFiles() {
    return MessageUIModel(
      activeStartDate: '2024-01-15 10:30:00',
      notificationTitle: 'Test Message without Files',
      notificationBody: 'Test content without file attachments',
      readStatus: 0,
      notificationId: 12345,
      category: 1,
      files: [],
    );
  }
}

void main() {
  group('MessageContentController', () {
    late MessageContentController controller;
    late MockMessageDetailUsecase mockMessageDetailUsecase;
    late MockDownloadController mockDownloadController;
    late MockS3Repository mockS3Repository;
    late MockNavigationService mockNavigationService;

    setUp(() {
      // 初始化GetX测试环境
      Get.testMode = true;
      LogUtil.initialize();

      // 创建Mock对象
      mockMessageDetailUsecase = MockMessageDetailUsecase();
      mockDownloadController = MockDownloadController();
      mockS3Repository = MockS3Repository();
      mockNavigationService = MockNavigationService();

      // 创建Controller实例
      controller = MessageContentController(
        messageDetailUsecase: mockMessageDetailUsecase,
        downloadController: mockDownloadController,
        s3Repository: mockS3Repository,
        navigationService: mockNavigationService,
      );
    });

    tearDown(() {
      // 清理Mock和GetX状态
      reset(mockMessageDetailUsecase);
      reset(mockDownloadController);
      reset(mockS3Repository);
      reset(mockNavigationService);
      Get.reset();
    });

    // Phase 1: 基础设施测试
    group('Phase 1: 基础设施测试', () {
      group('1.1 Controller创建和依赖注入', () {
        test('应该能够创建MessageContentController实例', () {
          // Assert
          expect(controller, isNotNull);
          expect(controller, isA<MessageContentController>());
        });

        test('应该正确注入MessageDetailUsecase依赖', () {
          // Assert
          expect(controller.messageDetailUsecase, equals(mockMessageDetailUsecase));
          expect(controller.messageDetailUsecase, isA<MessageDetailUsecase>());
        });

        test('应该正确注入DownloadController依赖', () {
          // Assert
          expect(controller.downloadController, equals(mockDownloadController));
          expect(controller.downloadController, isA<DownloadController>());
        });

        test('应该正确注入S3Repository依赖', () {
          // Assert
          expect(controller.s3Repository, equals(mockS3Repository));
          expect(controller.s3Repository, isA<S3Repository>());
        });

        test('应该正确注入NavigationService依赖', () {
          // Assert
          expect(controller.navigationService, equals(mockNavigationService));
          expect(controller.navigationService, isA<NavigationService>());
        });

        test('应该继承BaseController', () {
          // Assert
          expect(controller, isA<BaseController>());
        });

        test('构造函数应该要求所有必需的依赖', () {
          // Arrange & Act & Assert - 验证构造函数参数为required
          expect(
            () => MessageContentController(
              messageDetailUsecase: mockMessageDetailUsecase,
              downloadController: mockDownloadController,
              s3Repository: mockS3Repository,
              navigationService: mockNavigationService,
            ),
            returnsNormally,
          );
        });

        test('不同实例应该有独立的依赖引用', () {
          // Arrange
          final usecase1 = MockMessageDetailUsecase();
          final download1 = MockDownloadController();
          final s3Repo1 = MockS3Repository();
          final nav1 = MockNavigationService();
          final usecase2 = MockMessageDetailUsecase();
          final download2 = MockDownloadController();
          final s3Repo2 = MockS3Repository();
          final nav2 = MockNavigationService();

          // Act
          final controller1 = MessageContentController(
            messageDetailUsecase: usecase1,
            downloadController: download1,
            s3Repository: s3Repo1,
            navigationService: nav1,
          );
          final controller2 = MessageContentController(
            messageDetailUsecase: usecase2,
            downloadController: download2,
            s3Repository: s3Repo2,
            navigationService: nav2,
          );

          // Assert
          expect(controller1.messageDetailUsecase, equals(usecase1));
          expect(controller1.downloadController, equals(download1));
          expect(controller1.s3Repository, equals(s3Repo1));
          expect(controller1.navigationService, equals(nav1));
          expect(controller2.messageDetailUsecase, equals(usecase2));
          expect(controller2.downloadController, equals(download2));
          expect(controller2.s3Repository, equals(s3Repo2));
          expect(controller2.navigationService, equals(nav2));
          expect(controller1.messageDetailUsecase, isNot(equals(controller2.messageDetailUsecase)));
          expect(controller1.downloadController, isNot(equals(controller2.downloadController)));
          expect(controller1.s3Repository, isNot(equals(controller2.s3Repository)));
          expect(controller1.navigationService, isNot(equals(controller2.navigationService)));
        });
      });

      group('1.2 初始状态验证', () {
        test('应该有正确的初始状态', () {
          // Assert - 验证初始状态
          expect(controller.uiModel.value.activeStartDate, equals(''));
          expect(controller.uiModel.value.notificationTitle, equals(''));
          expect(controller.isLoading.value, isTrue);
        });

        test('初始uiModel应该是有效的空对象', () {
          // Assert
          final initialModel = controller.uiModel.value;
          expect(initialModel, isA<MessageUIModel>());
          expect(initialModel.activeStartDate, equals(''));
          expect(initialModel.notificationTitle, equals(''));
          expect(initialModel.notificationBody, equals('')); // 默认值是空字符串，不是null
          expect(initialModel.notificationId, equals(0)); // 默认值是0，不是null
        });

        test('初始isLoading应该为true', () {
          // Assert
          expect(controller.isLoading.value, isTrue);
          expect(controller.isLoading.value, isA<bool>());
        });

        test('状态应该具有合理的默认值', () {
          // Assert - 验证所有重要状态的初始值
          expect(controller.uiModel.value.activeStartDate, isNotNull);
          expect(controller.uiModel.value.notificationTitle, isNotNull);
          expect(controller.isLoading.value, isNotNull);
        });
      });

      group('1.3 响应式状态验证', () {
        test('uiModel应该是响应式的', () {
          // Arrange
          final testModel = MessageContentControllerTestData.createTestUIModel();

          // Act
          controller.uiModel.value = testModel;

          // Assert
          expect(controller.uiModel.value, equals(testModel));
          expect(controller.uiModel.value.notificationTitle, equals('Test Notification Title'));
        });

        test('isLoading应该是响应式的', () {
          // Arrange
          final originalValue = controller.isLoading.value;

          // Act
          controller.isLoading.value = !originalValue;

          // Assert
          expect(controller.isLoading.value, equals(!originalValue));
        });

        test('状态变化应该可以被观察', () {
          // Arrange
          bool uiModelChanged = false;
          bool isLoadingChanged = false;

          // 监听状态变化
          controller.uiModel.listen((_) => uiModelChanged = true);
          controller.isLoading.listen((_) => isLoadingChanged = true);

          // Act
          controller.uiModel.value = MessageContentControllerTestData.createTestUIModel();
          controller.isLoading.value = false;

          // Assert
          expect(uiModelChanged, isTrue);
          expect(isLoadingChanged, isTrue);
        });

        test('uiModel可以设置为不同的值', () {
          // Arrange
          final models = MessageContentControllerTestData.createVariousUIModels();

          for (final model in models) {
            // Act
            controller.uiModel.value = model;

            // Assert
            expect(controller.uiModel.value, equals(model));
            expect(controller.uiModel.value.notificationTitle, equals(model.notificationTitle));
          }
        });

        test('isLoading可以在true和false之间切换', () {
          // Arrange & Act & Assert
          controller.isLoading.value = true;
          expect(controller.isLoading.value, isTrue);

          controller.isLoading.value = false;
          expect(controller.isLoading.value, isFalse);

          controller.isLoading.value = true;
          expect(controller.isLoading.value, isTrue);
        });
      });

      group('1.4 方法签名验证', () {
        test('应该具有正确的方法签名', () {
          // Assert - 验证方法存在且签名正确
          expect(controller.initializeWithNotificationId, isA<Future<void> Function(int)>());
          expect(controller.loadData, isA<Future<void> Function(int)>());
          expect(controller.downloadFile, isA<Future<void> Function(String, String)>());
          expect(controller.getFormattedDate, isA<String Function(String)>());
        });

        test('方法应该返回正确的类型', () {
          // Arrange - 为异步方法调用配置mock
          when(
            mockMessageDetailUsecase.call(any),
          ).thenAnswer((_) async => MessageContentControllerTestData.createTestUIModel());
          when(mockDownloadController.downloadFile(any, any)).thenAnswer((_) async {});

          // Act & Assert
          expect(controller.getFormattedDate('2024-01-15 10:30:00'), isA<String>());
          expect(controller.initializeWithNotificationId(123), isA<Future<void>>());
          expect(controller.loadData(123), isA<Future<void>>());
          expect(controller.downloadFile('url', 'file'), isA<Future<void>>());
        });

        test('Controller应该具有预期的公开接口', () {
          // Assert - 验证公开属性和方法
          expect(controller.messageDetailUsecase, isNotNull);
          expect(controller.downloadController, isNotNull);
          expect(controller.s3Repository, isNotNull);
          expect(controller.navigationService, isNotNull);
          expect(controller.uiModel, isNotNull);
          expect(controller.isLoading, isNotNull);
        });
      });
    });

    // Phase 2: 核心业务逻辑测试
    group('Phase 2: 核心业务逻辑测试', () {
      group('2.1 initializeWithNotificationId方法测试', () {
        test('应该调用loadData方法', () async {
          // Arrange
          final notificationId = 12345;
          final testModel = MessageContentControllerTestData.createTestUIModel();
          when(mockMessageDetailUsecase.call(notificationId)).thenAnswer((_) async => testModel);

          // Act
          await controller.initializeWithNotificationId(notificationId);

          // Assert
          verify(mockMessageDetailUsecase.call(notificationId)).called(1);
        });

        test('应该处理有效的notificationId', () async {
          // Arrange
          final testCases = MessageContentControllerTestData.createNotificationIdTestCases();
          final testModel = MessageContentControllerTestData.createTestUIModel();

          for (final notificationId in testCases) {
            when(mockMessageDetailUsecase.call(notificationId)).thenAnswer((_) async => testModel);

            // Act
            await controller.initializeWithNotificationId(notificationId);

            // Assert
            verify(mockMessageDetailUsecase.call(notificationId)).called(1);
            expect(controller.uiModel.value, equals(testModel));
          }
        });

        test('应该处理UseCase异常', () async {
          // Arrange
          final notificationId = 12345;
          when(mockMessageDetailUsecase.call(notificationId)).thenThrow(Exception('UseCase failed'));

          // Act & Assert - loadData方法会传播异常
          expect(() => controller.initializeWithNotificationId(notificationId), throwsA(isA<Exception>()));
        });

        test('应该是异步方法', () {
          // Arrange
          final notificationId = 12345;
          when(
            mockMessageDetailUsecase.call(notificationId),
          ).thenAnswer((_) async => MessageContentControllerTestData.createTestUIModel());

          // Act
          final result = controller.initializeWithNotificationId(notificationId);

          // Assert
          expect(result, isA<Future<void>>());
        });
      });

      group('2.2 loadData方法测试', () {
        test('成功加载应该更新uiModel并设置isLoading为false', () async {
          // Arrange
          final notificationId = 12345;
          final testModel = MessageContentControllerTestData.createTestUIModel(
            title: 'Loaded Message',
            date: '2024-01-15 10:30:00',
          );
          when(mockMessageDetailUsecase.call(notificationId)).thenAnswer((_) async => testModel);

          // Act
          await controller.loadData(notificationId);

          // Assert
          expect(controller.uiModel.value, equals(testModel));
          expect(controller.uiModel.value.notificationTitle, equals('Loaded Message'));
          expect(controller.isLoading.value, isFalse);
          verify(mockMessageDetailUsecase.call(notificationId)).called(1);
        });

        test('加载过程中isLoading应该为true', () async {
          // Arrange
          final notificationId = 12345;
          bool loadingWasTrue = false;

          // 模拟延迟的UseCase调用
          when(mockMessageDetailUsecase.call(notificationId)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 50));
            return MessageContentControllerTestData.createTestUIModel();
          });

          // 监听isLoading变化
          controller.isLoading.listen((value) {
            if (value == true) loadingWasTrue = true;
          });

          // Act
          final future = controller.loadData(notificationId);

          // Assert - 加载过程中
          expect(controller.isLoading.value, isTrue);

          // 等待完成
          await future;

          // Assert - 加载完成后
          expect(controller.isLoading.value, isFalse);
          expect(loadingWasTrue, isTrue);
        });

        test('UseCase异常时应该设置isLoading为false', () async {
          // Arrange
          final notificationId = 12345;
          when(mockMessageDetailUsecase.call(notificationId)).thenThrow(Exception('Network error'));

          // Act & Assert - 异常会传播，但finally块确保isLoading为false
          try {
            await controller.loadData(notificationId);
          } catch (e) {
            // 预期异常
          }

          // Assert
          expect(controller.isLoading.value, isFalse);
          verify(mockMessageDetailUsecase.call(notificationId)).called(1);
        });

        test('应该处理不同类型的异常', () async {
          // Arrange
          final notificationId = 12345;
          final exceptions = [
            Exception('General exception'),
            ArgumentError('Invalid argument'),
            StateError('State error'),
            FormatException('Format error'),
          ];

          for (final exception in exceptions) {
            when(mockMessageDetailUsecase.call(notificationId)).thenThrow(exception);

            // Act & Assert - 异常会传播
            expect(() => controller.loadData(notificationId), throwsA(equals(exception)));
          }
        });

        test('多次调用应该产生多次UseCase调用', () async {
          // Arrange
          final notificationId = 12345;
          when(
            mockMessageDetailUsecase.call(notificationId),
          ).thenAnswer((_) async => MessageContentControllerTestData.createTestUIModel());

          // Act
          await controller.loadData(notificationId);
          await controller.loadData(notificationId);
          await controller.loadData(notificationId);

          // Assert
          verify(mockMessageDetailUsecase.call(notificationId)).called(3);
        });

        test('应该处理不同的notificationId值', () async {
          // Arrange
          final testCases = MessageContentControllerTestData.createNotificationIdTestCases();

          for (final notificationId in testCases) {
            final expectedModel = MessageContentControllerTestData.createTestUIModel(
              notificationId: notificationId,
              title: 'Message $notificationId',
            );
            when(mockMessageDetailUsecase.call(notificationId)).thenAnswer((_) async => expectedModel);

            // Act
            await controller.loadData(notificationId);

            // Assert
            expect(controller.uiModel.value, equals(expectedModel));
            expect(controller.uiModel.value.notificationId, equals(notificationId));
            verify(mockMessageDetailUsecase.call(notificationId)).called(1);
          }
        });

        test('finally块应该确保isLoading始终设置为false', () async {
          // Arrange
          final notificationId = 12345;
          when(mockMessageDetailUsecase.call(notificationId)).thenThrow(Exception('Test exception'));

          // 验证初始状态
          controller.isLoading.value = true;

          // Act & Assert - 异常会传播
          try {
            await controller.loadData(notificationId);
          } catch (e) {
            // 预期异常
          }

          // Assert - 即使异常，isLoading也应该为false
          expect(controller.isLoading.value, isFalse);
        });
      });

      group('2.3 getFormattedDate纯函数测试', () {
        test('应该正确格式化标准日期字符串', () {
          // Arrange & Act & Assert
          final testCases = MessageContentControllerTestData.createDateFormatTestCases();

          testCases.forEach((input, expected) {
            final result = controller.getFormattedDate(input);
            expect(result, equals(expected), reason: 'Input: $input should return: $expected');
          });
        });

        test('应该提取YYYY-MM-DD格式', () {
          // Arrange
          final testCases = {
            '2024-01-15 10:30:00': '2024-01-15',
            '2024-12-31 23:59:59': '2024-12-31',
            '2023-06-20 14:45:30': '2023-06-20',
            '2025-02-28 00:00:00': '2025-02-28',
          };

          testCases.forEach((input, expected) {
            // Act
            final result = controller.getFormattedDate(input);

            // Assert
            expect(result, equals(expected));
            expect(result.length, equals(10));
            expect(result, matches(r'^\d{4}-\d{2}-\d{2}$'));
          });
        });

        test('应该处理长度不足10的字符串', () {
          // Arrange
          final shortInputs = [
            '2024-01', // 7 chars
            '2024', // 4 chars
            '20', // 2 chars
            '', // 0 chars
          ];

          for (final input in shortInputs) {
            // Act
            final result = controller.getFormattedDate(input);

            // Assert
            expect(result, equals(input)); // 应该返回原字符串
          }
        });

        test('应该处理边界情况', () {
          // Arrange
          final boundaryInputs = {
            '1234567890': '1234567890', // 正好10位
            '12345678901': '1234567890', // 超过10位，取前10位
            '0000-00-00 00:00:00': '0000-00-00',
            '9999-99-99 99:99:99': '9999-99-99',
          };

          boundaryInputs.forEach((input, expected) {
            // Act
            final result = controller.getFormattedDate(input);

            // Assert
            expect(result, equals(expected));
          });
        });

        test('应该是纯函数（无副作用）', () {
          // Arrange
          final input = '2024-01-15 10:30:00';
          final originalUIModel = controller.uiModel.value;
          final originalIsLoading = controller.isLoading.value;

          // Act
          final result1 = controller.getFormattedDate(input);
          final result2 = controller.getFormattedDate(input);

          // Assert
          expect(result1, equals(result2)); // 相同输入产生相同输出
          expect(controller.uiModel.value, equals(originalUIModel)); // 状态未改变
          expect(controller.isLoading.value, equals(originalIsLoading)); // 状态未改变
        });

        test('应该处理特殊字符和Unicode', () {
          // Arrange
          final specialInputs = {
            '2024-01-15🎉10:30:00': '2024-01-15',
            '2024/01/15 10:30:00': '2024/01/15',
            '2024.01.15 10:30:00': '2024.01.15',
            'date-2024-01-15': 'date-2024-',
          };

          specialInputs.forEach((input, expected) {
            // Act
            final result = controller.getFormattedDate(input);

            // Assert
            expect(result, equals(expected));
          });
        });
      });

      group('2.4 downloadFile方法测试', () {
        test('应该调用DownloadController的downloadFile方法', () async {
          // Arrange
          final url = 'https://example.com/file.pdf';
          final fileName = 'document.pdf';
          when(mockDownloadController.downloadFile(url, fileName)).thenAnswer((_) async {});

          // Act
          await controller.downloadFile(url, fileName);

          // Assert
          verify(mockDownloadController.downloadFile(url, fileName)).called(1);
        });

        test('应该处理各种URL和文件名组合', () async {
          // Arrange
          final testCases = MessageContentControllerTestData.createDownloadTestCases();
          when(mockDownloadController.downloadFile(any, any)).thenAnswer((_) async {});

          // Act & Assert
          for (final entry in testCases.entries) {
            final url = entry.key;
            final fileName = entry.value;

            await controller.downloadFile(url, fileName);
            verify(mockDownloadController.downloadFile(url, fileName)).called(1);
          }
        });

        test('应该传递DownloadController的异常', () async {
          // Arrange
          final url = 'https://example.com/file.pdf';
          final fileName = 'document.pdf';
          final testException = Exception('Download failed');
          when(mockDownloadController.downloadFile(url, fileName)).thenThrow(testException);

          // Act & Assert
          expect(() => controller.downloadFile(url, fileName), throwsA(testException));
          verify(mockDownloadController.downloadFile(url, fileName)).called(1);
        });

        test('应该处理不同类型的DownloadController异常', () async {
          // Arrange
          final url = 'https://example.com/file.pdf';
          final fileName = 'document.pdf';
          final exceptions = [
            Exception('Network error'),
            ArgumentError('Invalid URL'),
            StateError('Download state error'),
            TimeoutException('Download timeout'),
          ];

          for (final exception in exceptions) {
            when(mockDownloadController.downloadFile(url, fileName)).thenThrow(exception);

            // Act & Assert
            expect(() => controller.downloadFile(url, fileName), throwsA(exception));
          }
        });

        test('应该是异步方法', () {
          // Arrange
          final url = 'https://example.com/file.pdf';
          final fileName = 'document.pdf';
          when(mockDownloadController.downloadFile(url, fileName)).thenAnswer((_) async {});

          // Act
          final result = controller.downloadFile(url, fileName);

          // Assert
          expect(result, isA<Future<void>>());
        });

        test('不应该影响Controller状态', () async {
          // Arrange
          final url = 'https://example.com/file.pdf';
          final fileName = 'document.pdf';
          final originalUIModel = controller.uiModel.value;
          final originalIsLoading = controller.isLoading.value;
          when(mockDownloadController.downloadFile(url, fileName)).thenAnswer((_) async {});

          // Act
          await controller.downloadFile(url, fileName);

          // Assert
          expect(controller.uiModel.value, equals(originalUIModel));
          expect(controller.isLoading.value, equals(originalIsLoading));
        });
      });

      group('2.5 状态管理集成测试', () {
        test('完整的初始化流程应该正确管理状态', () async {
          // Arrange
          final notificationId = 12345;
          final testModel = MessageContentControllerTestData.createTestUIModel(title: 'Integration Test');
          final stateChanges = <bool>[];

          // 监听状态变化
          controller.isLoading.listen((value) => stateChanges.add(value));

          when(mockMessageDetailUsecase.call(notificationId)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 50));
            return testModel;
          });

          // Act
          await controller.initializeWithNotificationId(notificationId);

          // Assert
          expect(controller.uiModel.value, equals(testModel));
          expect(controller.isLoading.value, isFalse);
          expect(stateChanges, contains(true)); // 加载过程中为true
          expect(stateChanges.last, isFalse); // 最终为false
        });

        test('异常情况下状态应该正确恢复', () async {
          // Arrange
          final notificationId = 12345;
          when(mockMessageDetailUsecase.call(notificationId)).thenThrow(Exception('Test error'));

          // Act & Assert - 异常会传播
          try {
            await controller.initializeWithNotificationId(notificationId);
          } catch (e) {
            // 预期异常
          }

          // Assert
          expect(controller.isLoading.value, isFalse);
          // uiModel应该保持初始状态
          expect(controller.uiModel.value.notificationTitle, equals(''));
        });

        test('并发调用应该正确处理', () async {
          // Arrange
          final notificationId1 = 123;
          final notificationId2 = 456;
          final model1 = MessageContentControllerTestData.createTestUIModel(title: 'Model 1');
          final model2 = MessageContentControllerTestData.createTestUIModel(title: 'Model 2');

          when(mockMessageDetailUsecase.call(notificationId1)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 100));
            return model1;
          });
          when(mockMessageDetailUsecase.call(notificationId2)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 50));
            return model2;
          });

          // Act - 并发调用
          final future1 = controller.loadData(notificationId1);
          final future2 = controller.loadData(notificationId2);

          await Future.wait([future1, future2]);

          // Assert
          expect(controller.isLoading.value, isFalse);
          // 最后完成的调用应该设置最终的uiModel值
          verify(mockMessageDetailUsecase.call(notificationId1)).called(1);
          verify(mockMessageDetailUsecase.call(notificationId2)).called(1);
        });
      });
    });

    // Phase 3: 新增业务逻辑测试
    group('Phase 3: 新增业务逻辑测试', () {
      group('3.1 goBack方法测试', () {
        test('应该调用NavigationService的goBack方法', () {
          // Arrange
          when(mockNavigationService.goBack()).thenReturn(null);

          // Act
          controller.goBack();

          // Assert
          verify(mockNavigationService.goBack()).called(1);
        });

        test('应该只调用一次NavigationService.goBack', () {
          // Arrange
          when(mockNavigationService.goBack()).thenReturn(null);

          // Act
          controller.goBack();

          // Assert
          verify(mockNavigationService.goBack()).called(1);
          verifyNoMoreInteractions(mockNavigationService);
        });

        test('NavigationService异常时应该传播异常', () {
          // Arrange
          final testException = Exception('Navigation failed');
          when(mockNavigationService.goBack()).thenThrow(testException);

          // Act & Assert
          expect(() => controller.goBack(), throwsA(testException));
          verify(mockNavigationService.goBack()).called(1);
        });

        test('不应该影响Controller状态', () {
          // Arrange
          final originalUIModel = controller.uiModel.value;
          final originalIsLoading = controller.isLoading.value;
          when(mockNavigationService.goBack()).thenReturn(null);

          // Act
          controller.goBack();

          // Assert
          expect(controller.uiModel.value, equals(originalUIModel));
          expect(controller.isLoading.value, equals(originalIsLoading));
          verify(mockNavigationService.goBack()).called(1);
        });

        test('多次调用应该产生多次NavigationService调用', () {
          // Arrange
          when(mockNavigationService.goBack()).thenReturn(null);

          // Act
          controller.goBack();
          controller.goBack();
          controller.goBack();

          // Assert
          verify(mockNavigationService.goBack()).called(3);
        });

        test('应该处理不同类型的NavigationService异常', () {
          // Arrange
          final exceptions = [
            Exception('General navigation error'),
            ArgumentError('Invalid navigation state'),
            StateError('Navigation state error'),
          ];

          for (final exception in exceptions) {
            reset(mockNavigationService);
            when(mockNavigationService.goBack()).thenThrow(exception);

            // Act & Assert
            expect(() => controller.goBack(), throwsA(exception));
            verify(mockNavigationService.goBack()).called(1);
          }
        });
      });

      group('3.2 getTurl方法测试', () {
        test('应该调用S3Repository的getTurl方法', () async {
          // Arrange
          final filePath = 'test/path/file.pdf';
          final expectedUrl = 'https://s3.example.com/signed-url';
          when(mockS3Repository.getTurl(filePath)).thenAnswer((_) async => expectedUrl);

          // Act
          final result = await controller.getTurl(filePath);

          // Assert
          verify(mockS3Repository.getTurl(filePath)).called(1);
          expect(result, equals(expectedUrl));
        });

        test('应该返回S3Repository返回的URL', () async {
          // Arrange
          final testCases = {
            'documents/file1.pdf': 'https://s3.aws.com/signed-url-1',
            'images/photo.jpg': 'https://s3.aws.com/signed-url-2',
            'data/spreadsheet.xlsx': 'https://s3.aws.com/signed-url-3',
            'videos/movie.mp4': 'https://s3.aws.com/signed-url-4',
          };

          for (final entry in testCases.entries) {
            final filePath = entry.key;
            final expectedUrl = entry.value;
            when(mockS3Repository.getTurl(filePath)).thenAnswer((_) async => expectedUrl);

            // Act
            final result = await controller.getTurl(filePath);

            // Assert
            expect(result, equals(expectedUrl));
            verify(mockS3Repository.getTurl(filePath)).called(1);
          }
        });

        test('应该传递正确的filePath参数', () async {
          // Arrange
          final testPaths = [
            'simple.txt',
            'folder/subfolder/file.pdf',
            'path with spaces/file.doc',
            'special-chars_file.123.txt',
            'very/long/path/to/some/deep/nested/file.extension',
          ];

          for (final filePath in testPaths) {
            when(mockS3Repository.getTurl(filePath)).thenAnswer((_) async => 'mock-url');

            // Act
            await controller.getTurl(filePath);

            // Assert
            verify(mockS3Repository.getTurl(filePath)).called(1);
          }
        });

        test('S3Repository异常时应该传播异常', () async {
          // Arrange
          final filePath = 'test/file.pdf';
          final testException = Exception('S3 access denied');
          when(mockS3Repository.getTurl(filePath)).thenThrow(testException);

          // Act & Assert
          expect(() => controller.getTurl(filePath), throwsA(testException));
          verify(mockS3Repository.getTurl(filePath)).called(1);
        });

        test('应该处理不同类型的S3Repository异常', () async {
          // Arrange
          final filePath = 'test/file.pdf';
          final exceptions = [
            Exception('Network error'),
            ArgumentError('Invalid file path'),
            StateError('S3 service unavailable'),
            TimeoutException('Request timeout'),
          ];

          for (final exception in exceptions) {
            reset(mockS3Repository);
            when(mockS3Repository.getTurl(filePath)).thenThrow(exception);

            // Act & Assert
            expect(() => controller.getTurl(filePath), throwsA(exception));
            verify(mockS3Repository.getTurl(filePath)).called(1);
          }
        });

        test('应该处理边界情况的filePath值', () async {
          // Arrange
          final edgeCases = {
            '': 'empty-path-url',
            '/': 'root-path-url',
            '../../relative/path': 'relative-path-url',
            'file.with.multiple.dots.txt': 'multiple-dots-url',
          };

          for (final entry in edgeCases.entries) {
            final filePath = entry.key;
            final expectedUrl = entry.value;
            when(mockS3Repository.getTurl(filePath)).thenAnswer((_) async => expectedUrl);

            // Act
            final result = await controller.getTurl(filePath);

            // Assert
            expect(result, equals(expectedUrl));
            verify(mockS3Repository.getTurl(filePath)).called(1);
          }
        });

        test('应该是异步方法', () {
          // Arrange
          final filePath = 'test/file.pdf';
          when(mockS3Repository.getTurl(filePath)).thenAnswer((_) async => 'test-url');

          // Act
          final result = controller.getTurl(filePath);

          // Assert
          expect(result, isA<Future<String>>());
        });

        test('不应该影响Controller状态', () async {
          // Arrange
          final filePath = 'test/file.pdf';
          final originalUIModel = controller.uiModel.value;
          final originalIsLoading = controller.isLoading.value;
          when(mockS3Repository.getTurl(filePath)).thenAnswer((_) async => 'test-url');

          // Act
          await controller.getTurl(filePath);

          // Assert
          expect(controller.uiModel.value, equals(originalUIModel));
          expect(controller.isLoading.value, equals(originalIsLoading));
          verify(mockS3Repository.getTurl(filePath)).called(1);
        });
      });

      group('3.3 onTapFileItem方法测试', () {
        test('有效索引应该成功下载文件', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 3);
          controller.uiModel.value = testModel;

          final index = 1;
          final file = testModel.files[index];
          final expectedUrl = 'https://s3.example.com/signed-url';

          when(mockS3Repository.getTurl(file.url)).thenAnswer((_) async => expectedUrl);
          when(mockDownloadController.downloadFile(expectedUrl, file.fileName)).thenAnswer((_) async {});

          // Act
          await controller.onTapFileItem(index);

          // Assert
          verify(mockS3Repository.getTurl(file.url)).called(1);
          verify(mockDownloadController.downloadFile(expectedUrl, file.fileName)).called(1);
        });

        test('负数索引应该记录警告并退出', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 3);
          controller.uiModel.value = testModel;

          // Act
          await controller.onTapFileItem(-1);

          // Assert - 不应该调用任何依赖方法
          verifyNever(mockS3Repository.getTurl(any));
          verifyNever(mockDownloadController.downloadFile(any, any));
        });

        test('超出范围的索引应该记录警告并退出', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 3);
          controller.uiModel.value = testModel;

          // Act
          await controller.onTapFileItem(3); // 索引超出范围

          // Assert - 不应该调用任何依赖方法
          verifyNever(mockS3Repository.getTurl(any));
          verifyNever(mockDownloadController.downloadFile(any, any));
        });

        test('空文件列表时任何索引都应该退出', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithoutFiles();
          controller.uiModel.value = testModel;

          // Act
          await controller.onTapFileItem(0);

          // Assert - 不应该调用任何依赖方法
          verifyNever(mockS3Repository.getTurl(any));
          verifyNever(mockDownloadController.downloadFile(any, any));
        });

        test('边界索引应该正常工作', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 5);
          controller.uiModel.value = testModel;

          when(mockS3Repository.getTurl(any)).thenAnswer((_) async => 'test-url');
          when(mockDownloadController.downloadFile(any, any)).thenAnswer((_) async {});

          // Act & Assert - 测试第一个索引
          await controller.onTapFileItem(0);
          verify(mockS3Repository.getTurl(testModel.files[0].url)).called(1);
          verify(mockDownloadController.downloadFile('test-url', testModel.files[0].fileName)).called(1);

          // 重置Mock
          reset(mockS3Repository);
          reset(mockDownloadController);
          when(mockS3Repository.getTurl(any)).thenAnswer((_) async => 'test-url-2');
          when(mockDownloadController.downloadFile(any, any)).thenAnswer((_) async {});

          // Act & Assert - 测试最后一个索引
          await controller.onTapFileItem(4);
          verify(mockS3Repository.getTurl(testModel.files[4].url)).called(1);
          verify(mockDownloadController.downloadFile('test-url-2', testModel.files[4].fileName)).called(1);
        });

        test('getTurl失败应该捕获异常并记录错误', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 2);
          controller.uiModel.value = testModel;

          final index = 0;
          final file = testModel.files[index];
          final testException = Exception('S3 getTurl failed');

          when(mockS3Repository.getTurl(file.url)).thenThrow(testException);

          // Act - 应该不抛出异常
          await controller.onTapFileItem(index);

          // Assert
          verify(mockS3Repository.getTurl(file.url)).called(1);
          // downloadFile不应该被调用，因为getTurl失败了
          verifyNever(mockDownloadController.downloadFile(any, any));
        });

        test('downloadFile失败应该捕获异常并记录错误', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 2);
          controller.uiModel.value = testModel;

          final index = 1;
          final file = testModel.files[index];
          final expectedUrl = 'https://s3.example.com/signed-url';
          final testException = Exception('Download failed');

          when(mockS3Repository.getTurl(file.url)).thenAnswer((_) async => expectedUrl);
          when(mockDownloadController.downloadFile(expectedUrl, file.fileName)).thenThrow(testException);

          // Act - 应该不抛出异常
          await controller.onTapFileItem(index);

          // Assert
          verify(mockS3Repository.getTurl(file.url)).called(1);
          verify(mockDownloadController.downloadFile(expectedUrl, file.fileName)).called(1);
        });

        test('应该处理不同类型的异常', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 3);
          controller.uiModel.value = testModel;

          final exceptions = [
            Exception('General error'),
            ArgumentError('Invalid argument'),
            StateError('State error'),
            TimeoutException('Timeout'),
          ];

          for (int i = 0; i < exceptions.length; i++) {
            final index = i % testModel.files.length;
            final file = testModel.files[index];

            reset(mockS3Repository);
            reset(mockDownloadController);

            when(mockS3Repository.getTurl(file.url)).thenThrow(exceptions[i]);

            // Act - 应该不抛出异常
            await controller.onTapFileItem(index);

            // Assert
            verify(mockS3Repository.getTurl(file.url)).called(1);
          }
        });

        test('不应该影响Controller状态', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 2);
          controller.uiModel.value = testModel;

          final originalUIModel = controller.uiModel.value;
          final originalIsLoading = controller.isLoading.value;

          when(mockS3Repository.getTurl(any)).thenAnswer((_) async => 'test-url');
          when(mockDownloadController.downloadFile(any, any)).thenAnswer((_) async {});

          // Act
          await controller.onTapFileItem(0);

          // Assert
          expect(controller.uiModel.value, equals(originalUIModel));
          expect(controller.isLoading.value, equals(originalIsLoading));
        });

        test('完整集成测试：从索引到下载成功', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 3);
          controller.uiModel.value = testModel;

          final index = 2;
          final file = testModel.files[index];
          final expectedUrl = 'https://s3.example.com/integration-test-url';

          when(mockS3Repository.getTurl(file.url)).thenAnswer((_) async => expectedUrl);
          when(mockDownloadController.downloadFile(expectedUrl, file.fileName)).thenAnswer((_) async {});

          // Act
          await controller.onTapFileItem(index);

          // Assert - 验证完整的调用链
          verifyInOrder([
            mockS3Repository.getTurl(file.url),
            mockDownloadController.downloadFile(expectedUrl, file.fileName),
          ]);

          expect(file.url, equals('file_2.pdf'));
          expect(file.fileName, equals('Document_2.pdf'));
        });

        test('多次调用不同索引应该正确处理', () async {
          // Arrange
          final testModel = MessageContentControllerTestData.createModelWithFiles(fileCount: 3);
          controller.uiModel.value = testModel;

          when(
            mockS3Repository.getTurl(any),
          ).thenAnswer((invocation) async => 'signed-url-for-${invocation.positionalArguments[0]}');
          when(mockDownloadController.downloadFile(any, any)).thenAnswer((_) async {});

          // Act
          await controller.onTapFileItem(0);
          await controller.onTapFileItem(2);
          await controller.onTapFileItem(1);

          // Assert
          verify(mockS3Repository.getTurl('file_0.pdf')).called(1);
          verify(mockS3Repository.getTurl('file_2.pdf')).called(1);
          verify(mockS3Repository.getTurl('file_1.pdf')).called(1);

          verify(mockDownloadController.downloadFile('signed-url-for-file_0.pdf', 'Document_0.pdf')).called(1);
          verify(mockDownloadController.downloadFile('signed-url-for-file_2.pdf', 'Document_2.pdf')).called(1);
          verify(mockDownloadController.downloadFile('signed-url-for-file_1.pdf', 'Document_1.pdf')).called(1);
        });
      });
    });
  });
}
