import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/models/reset_password_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/repositories/reset_password_repository_impl.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../../../../core/utils/dio_utils_test.mocks.dart';

void main() {
  late ResetPasswordRepositoryImpl repository;
  late MockDioUtil mockDioUtil;

  setUp(() {
    // 初始化LogUtil
    LogUtil.initialize();
    mockDioUtil = MockDioUtil();
    repository = ResetPasswordRepositoryImpl(dioUtil: mockDioUtil);
  });

  group('setUpdatePassword', () {
    test('should return ResetPasswordResultModel when password reset is successful', () async {
      // 成功重置密码时应返回ResetPasswordResultModel
      // Arrange
      const userName = '<EMAIL>';
      const oldPassword = 'oldPassword123';
      const newPassword = 'newPassword123';
      const tenantId = 'tenant123';
      const zoneId = 'zone123';

      final successResponse = Response(
        data: {
          'code': 0,
          'msg': 'Success',
          'data': {'userId': 123, 'userName': userName, 'tenantId': tenantId},
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/api/updatePassword'),
      );

      when(
        mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
      ).thenAnswer((_) async => successResponse);

      // Act
      final result = await repository.setUpdatePassword(
        userName: userName,
        oldPassword: oldPassword,
        newPassword: newPassword,
        tenantId: tenantId,
        zoneId: zoneId,
      );

      // Assert
      expect(result, isA<ResetPasswordResultModel>());
      expect(result.code, 0);
      expect(result.msg, 'Success');

      verify(
        mockDioUtil.post(
          any,
          data: {
            'zoneId': zoneId,
            'userName': userName,
            'oldPassword': oldPassword,
            'newPassword': newPassword,
            'tenantId': tenantId,
          },
          useFormUrlEncoded: true,
        ),
      ).called(1);
    });

    test('should throw DataException when API returns business failure', () async {
      // API业务失败时应抛出DataException
      // Arrange
      const userName = '<EMAIL>';
      const oldPassword = 'oldPassword123';
      const newPassword = 'newPassword123';
      const tenantId = 'tenant123';
      const zoneId = 'zone123';

      final failureResponse = Response(
        data: {'code': 400, 'msg': 'Bad Request', 'error': 'Invalid old password'},
        statusCode: 400,
        requestOptions: RequestOptions(path: '/api/updatePassword'),
      );

      when(
        mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
      ).thenAnswer((_) async => failureResponse);

      // Act & Assert
      expect(
        () => repository.setUpdatePassword(
          userName: userName,
          oldPassword: oldPassword,
          newPassword: newPassword,
          tenantId: tenantId,
          zoneId: zoneId,
        ),
        throwsA(
          isA<BusinessException>().having(
            (e) => e.message,
            'message',
            'Failed to update password due to API response status: 400',
          ),
        ),
      );

      verify(
        mockDioUtil.post(
          any,
          data: {
            'zoneId': zoneId,
            'userName': userName,
            'oldPassword': oldPassword,
            'newPassword': newPassword,
            'tenantId': tenantId,
          },
          useFormUrlEncoded: true,
        ),
      ).called(1);
    });

    test('should throw BusinessException when network error occurs', () async {
      // 网络异常时应抛出BusinessException
      // Arrange
      const userName = '<EMAIL>';
      const oldPassword = 'oldPassword123';
      const newPassword = 'newPassword123';
      const tenantId = 'tenant123';
      const zoneId = 'zone123';

      final dioException = DioException(
        requestOptions: RequestOptions(path: '/api/updatePassword'),
        error: 'Network error',
        message: 'Connection timeout',
      );

      when(
        mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
      ).thenThrow(dioException);

      // Act & Assert
      expect(
        () => repository.setUpdatePassword(
          userName: userName,
          oldPassword: oldPassword,
          newPassword: newPassword,
          tenantId: tenantId,
          zoneId: zoneId,
        ),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', 'Connection timeout')),
      );

      verify(
        mockDioUtil.post(
          any,
          data: {
            'zoneId': zoneId,
            'userName': userName,
            'oldPassword': oldPassword,
            'newPassword': newPassword,
            'tenantId': tenantId,
          },
          useFormUrlEncoded: true,
        ),
      ).called(1);
    });

    group('参数验证异常', () {
      test('should throw SystemException when userName is empty', () async {
        // userName为空字符串时应抛出SystemException
        // Arrange
        final failureResponse = Response(
          data: {'code': 400, 'msg': 'Bad Request', 'error': 'Invalid userName'},
          statusCode: 400,
          requestOptions: RequestOptions(path: '/api/updatePassword'),
        );

        when(
          mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
        ).thenAnswer((_) async => failureResponse);

        // Act & Assert
        expect(
          () => repository.setUpdatePassword(
            userName: '',
            oldPassword: 'oldPassword123',
            newPassword: 'newPassword123',
            tenantId: 'tenant123',
            zoneId: 'zone123',
          ),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update password due to API response status: 400',
            ),
          ),
        );
      });

      test('should throw SystemException when oldPassword is empty', () async {
        // oldPassword为空字符串时应抛出SystemException
        // Arrange
        final failureResponse = Response(
          data: {'code': 400, 'msg': 'Bad Request', 'error': 'Invalid oldPassword'},
          statusCode: 400,
          requestOptions: RequestOptions(path: '/api/updatePassword'),
        );

        when(
          mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
        ).thenAnswer((_) async => failureResponse);

        // Act & Assert
        expect(
          () => repository.setUpdatePassword(
            userName: '<EMAIL>',
            oldPassword: '',
            newPassword: 'newPassword123',
            tenantId: 'tenant123',
            zoneId: 'zone123',
          ),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update password due to API response status: 400',
            ),
          ),
        );
      });

      test('should throw SystemException when newPassword is empty', () async {
        // newPassword为空字符串时应抛出SystemException
        // Arrange
        final failureResponse = Response(
          data: {'code': 400, 'msg': 'Bad Request', 'error': 'Invalid newPassword'},
          statusCode: 400,
          requestOptions: RequestOptions(path: '/api/updatePassword'),
        );

        when(
          mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
        ).thenAnswer((_) async => failureResponse);

        // Act & Assert
        expect(
          () => repository.setUpdatePassword(
            userName: '<EMAIL>',
            oldPassword: 'oldPassword123',
            newPassword: '',
            tenantId: 'tenant123',
            zoneId: 'zone123',
          ),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update password due to API response status: 400',
            ),
          ),
        );
      });

      test('should throw SystemException when tenantId is empty', () async {
        // tenantId为空字符串时应抛出SystemException
        // Arrange
        final failureResponse = Response(
          data: {'code': 400, 'msg': 'Bad Request', 'error': 'Invalid tenantId'},
          statusCode: 400,
          requestOptions: RequestOptions(path: '/api/updatePassword'),
        );

        when(
          mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
        ).thenAnswer((_) async => failureResponse);

        // Act & Assert
        expect(
          () => repository.setUpdatePassword(
            userName: '<EMAIL>',
            oldPassword: 'oldPassword123',
            newPassword: 'newPassword123',
            tenantId: '',
            zoneId: 'zone123',
          ),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update password due to API response status: 400',
            ),
          ),
        );
      });

      test('should throw SystemException when zoneId is empty', () async {
        // zoneId为空字符串时应抛出SystemException
        // Arrange
        final failureResponse = Response(
          data: {'code': 400, 'msg': 'Bad Request', 'error': 'Invalid zoneId'},
          statusCode: 400,
          requestOptions: RequestOptions(path: '/api/updatePassword'),
        );

        when(
          mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
        ).thenAnswer((_) async => failureResponse);

        // Act & Assert
        expect(
          () => repository.setUpdatePassword(
            userName: '<EMAIL>',
            oldPassword: 'oldPassword123',
            newPassword: 'newPassword123',
            tenantId: 'tenant123',
            zoneId: '',
          ),
          throwsA(
            isA<BusinessException>().having(
              (e) => e.message,
              'message',
              'Failed to update password due to API response status: 400',
            ),
          ),
        );
      });
    });

    test('should throw SystemException when response data parsing fails', () async {
      // 响应数据解析失败时应抛出SystemException
      // Arrange
      const userName = '<EMAIL>';
      const oldPassword = 'oldPassword123';
      const newPassword = 'newPassword123';
      const tenantId = 'tenant123';
      const zoneId = 'zone123';

      // 创建一个会导致JSON解析失败的响应
      // 使用一个无效的JSON结构，比如code字段是字符串而不是数字
      final invalidResponse = Response(
        data: {
          'code': 'invalid_code', // 应该是数字，但这里是字符串
          'msg': 'Success',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/api/updatePassword'),
      );

      when(
        mockDioUtil.post(any, data: anyNamed('data'), useFormUrlEncoded: anyNamed('useFormUrlEncoded')),
      ).thenAnswer((_) async => invalidResponse);

      // Act & Assert
      expect(
        () => repository.setUpdatePassword(
          userName: userName,
          oldPassword: oldPassword,
          newPassword: newPassword,
          tenantId: tenantId,
          zoneId: zoneId,
        ),
        throwsA(
          isA<SystemException>().having((e) => e.technicalMessage, 'technicalMessage', 'Failed to update password'),
        ),
      );

      verify(
        mockDioUtil.post(
          any,
          data: {
            'zoneId': zoneId,
            'userName': userName,
            'oldPassword': oldPassword,
            'newPassword': newPassword,
            'tenantId': tenantId,
          },
          useFormUrlEncoded: true,
        ),
      ).called(1);
    });
  });
}
