import 'dart:convert';
import 'package:collection/collection.dart';
import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/google_location_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/dpp_info_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/password_policy_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_use_status_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:geolocator_platform_interface/geolocator_platform_interface.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import '../../../../../mocks/storage_utils_mock.dart';
import 'user_info_handle_base_usecase_test.mocks.dart';

/// Mock 地理位置服务，用于测试环境
class MockGeolocatorPlatform extends Mock with MockPlatformInterfaceMixin implements GeolocatorPlatform {
  @override
  Future<bool> isLocationServiceEnabled() async => true;

  @override
  Future<LocationPermission> checkPermission() async => LocationPermission.always;

  @override
  Future<LocationPermission> requestPermission() async => LocationPermission.always;

  @override
  Future<Position> getCurrentPosition({LocationSettings? locationSettings}) async {
    return Position(
      latitude: 35.6762,
      longitude: 139.6503,
      timestamp: DateTime.now(),
      accuracy: 0.0,
      altitude: 0.0,
      altitudeAccuracy: 0.0,
      heading: 0.0,
      headingAccuracy: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
    );
  }

  @override
  Future<LocationAccuracyStatus> getLocationAccuracy() async => LocationAccuracyStatus.precise;

  @override
  Future<LocationAccuracyStatus> requestTemporaryFullAccuracy({required String purposeKey}) async =>
      LocationAccuracyStatus.precise;

  @override
  Stream<Position> getPositionStream({LocationSettings? locationSettings}) {
    return Stream.value(
      Position(
        latitude: 35.6762,
        longitude: 139.6503,
        timestamp: DateTime.now(),
        accuracy: 0.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      ),
    );
  }

  @override
  Future<bool> openAppSettings() async => true;

  @override
  Future<bool> openLocationSettings() async => true;

  @override
  double distanceBetween(double startLatitude, double startLongitude, double endLatitude, double endLongitude) {
    return 0.0;
  }

  @override
  double bearingBetween(double startLatitude, double startLongitude, double endLatitude, double endLongitude) {
    return 0.0;
  }
}

// 生成 Mock 类
@GenerateMocks([TenantRepository, UserRepository, GoogleLocationService])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('UserInfoHandleBaseUseCase', () {
    late TestUserInfoHandleUseCase useCase;
    late MockTenantRepository mockTenantRepository;
    late MockUserRepository mockUserRepository;
    late MockStorageUtils mockStorageUtils;
    late MockGoogleLocationService mockGoogleLocationService;

    setUp(() {
      LogUtil.initialize();

      // 初始化 dotenv 以避免 NotInitializedError
      // 使用测试环境的最小配置
      dotenv.testLoad(
        mergeWith: {
          'APP_MODEL': 'test',
          'APP_VERSION': '1.0.0-test',
          'GUIDE_BASE_URL': 'https://test.example.com',
          'SUPPORT_FAQ': 'https://test.example.com/faq',
          'LIVE_BASE_URL': 'https://test.example.com',
          'LIVE_API_BASE_URL': 'https://api.test.example.com',
          'API_GW_HOST': 'https://gateway.test.example.com',
          'SOCKET_HOST': 'https://socket.test.example.com',
          'REPORT_HOST': 'https://report.test.example.com',
          'API_HOST': 'https://api.test.example.com',
          'API_AUTH_HOST': 'https://auth.test.example.com',
          'TEXT_OCR_TARGET_TENANT': 'test_tenant',
          'CAR_NUMBER_OCR_TARGET_TENANT': 'test_tenant',
          'UNIVERSAL_SERIAL_OCR_TARGET_TENANT': 'test_tenant',
        },
      );

      // 设置 Mock 地理位置服务
      GeolocatorPlatform.instance = MockGeolocatorPlatform();

      mockTenantRepository = MockTenantRepository();
      mockUserRepository = MockUserRepository();
      mockStorageUtils = MockStorageUtils();
      mockGoogleLocationService = MockGoogleLocationService();

      // 配置 MockGoogleLocationService 行为
      when(mockGoogleLocationService.toSetLocation()).thenAnswer((_) async {
        // 模拟成功的地理位置设置，不做任何实际操作
        return;
      });

      useCase = TestUserInfoHandleUseCase(
        tenantRepository: mockTenantRepository,
        userRepository: mockUserRepository,
        storageUtils: mockStorageUtils,
        mockGoogleLocationService: mockGoogleLocationService,
      );

      // Mock设置：直接使用mockStorageUtils，不需要设置静态实现

      // 设置环境
      EnvHelper.environment = 'dev';
    });

    tearDown(() {
      // 测试后清理
      reset(mockGoogleLocationService);
    });

    group('基础功能测试', () {
      test('应该能够创建 UseCase 实例', () {
        expect(useCase, isNotNull);
        expect(useCase, isA<TestUserInfoHandleUseCase>());
      });

      test('应该实现 UseCase 接口', () {
        expect(useCase, isA<UserInfoHandleBaseUseCase>());
      });
    });

    group('正常登录流程测试 (code = 0)', () {
      test('应该成功处理正常登录并保存用户信息', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'test-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        final tenantInfo = SharedUserTenantModel(
          code: 0,
          msg: 'success',
          tenantUseStatesList: [SharedTenantUseStatusModel(fieldText: '{"uploadFileSizeMax": "50"}')],
        );

        when(mockUserRepository.getUserTenant()).thenAnswer((_) async => tenantInfo);

        // Act
        final actualResult = await useCase.call(params);

        // Assert
        expect(actualResult.code, equals(0));
        expect(actualResult.mapToken?.accessToken, equals('test-access-token'));
        expect(actualResult.user?.userName, equals('testuser'));

        // 验证存储调用
        expect(mockStorageUtils.setCalls, contains(StorageKeys.zoneId));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.token));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.userId));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.userName));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.firstName));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.lastName));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.enableTwoStep));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.tel + 'testuser'));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.nationCode + 'testuser'));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.printEnableFlg));
        expect(mockStorageUtils.setCalls, contains(StorageKeys.barcodeExtraction));
      });

      test('应该处理密码过期情况 (code = 7158)', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'password-expired-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act
        final actualResult = await useCase.call(params);

        // Assert
        expect(actualResult.code, equals(7158)); // 密码重置代码
        expect(actualResult.policy, isNotNull);
      });

      test('应该处理 DPP 信息存储', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'dpp-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        final tenantInfo = SharedUserTenantModel(
          code: 0,
          msg: 'success',
          tenantUseStatesList: [SharedTenantUseStatusModel(fieldText: '{"uploadFileSizeMax": "50"}')],
        );

        when(mockUserRepository.getUserTenant()).thenAnswer((_) async => tenantInfo);

        // Act
        await useCase.call(params);

        // Assert
        expect(mockStorageUtils.setCalls, contains(StorageKeys.ocrInfo));
        final storedDppInfo = mockStorageUtils.getValue<String>(StorageKeys.ocrInfo);
        expect(storedDppInfo, isNotNull);
        expect(storedDppInfo, contains('dppId'));
        expect(storedDppInfo, contains('test-dpp-id'));
      });

      test('应该处理文件大小限制获取', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'file-size-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        final tenantInfo = SharedUserTenantModel(
          code: 0,
          msg: 'success',
          tenantUseStatesList: [SharedTenantUseStatusModel(fieldText: '{"uploadFileSizeMax": "50"}')],
        );

        when(mockUserRepository.getUserTenant()).thenAnswer((_) async => tenantInfo);

        // Act
        await useCase.call(params);

        // Assert
        expect(mockStorageUtils.setCalls, contains(StorageKeys.selectFileMaxSize));
        final maxFileSize = mockStorageUtils.getValue<int>(StorageKeys.selectFileMaxSize);
        expect(maxFileSize, equals(50));
      });
    });

    group('双重认证流程测试', () {
      test('应该处理新规手机号双重认证 (code = 3)', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'sms-new-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act
        final actualResult = await useCase.call(params);

        // Assert
        expect(actualResult.code, equals(3));
        // 不应该调用存储相关方法
        expect(mockStorageUtils.setCalls, contains(StorageKeys.zoneId));
        expect(mockStorageUtils.setCalls, isNot(contains(StorageKeys.token)));
      });

      test('应该处理手机号双重认证 (code = 4)', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'sms-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act
        final actualResult = await useCase.call(params);

        // Assert
        expect(actualResult.code, equals(4));
      });

      test('应该处理邮箱双重认证 (code = 6)', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'email-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act
        final actualResult = await useCase.call(params);

        // Assert
        expect(actualResult.code, equals(6));
      });
    });

    group('异常处理测试', () {
      test('应该处理业务异常 (code = 1)', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'business-error-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<BusinessException>()));
      });

      test('应该处理用户过期异常', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'expired-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<BusinessException>()));
      });

      test('应该处理系统异常 (无错误信息)', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'system-error-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<SystemException>()));
      });

      test('应该处理空错误信息异常', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'empty-error-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<SystemException>()));
      });
    });

    group('存储验证测试', () {
      test('应该处理 mapToken 为空的情况', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'null-maptoken-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<SystemException>()));
      });

      test('应该处理 accessToken 为空的情况', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'null-token-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<SystemException>()));
      });

      test('应该处理 user 为空的情况', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'null-user-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<SystemException>()));
      });

      test('应该处理 tenantId 为空的情况', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'empty-tenant-id-tenant', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<SystemException>()));
      });

      test('应该处理 TENANT_ADMIN 租户异常', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealTenant: DealTenantModel(ticket: 'test-ticket', tenantId: 'TENANT_ADMIN', isBiometrics: false),
          dealDeeplink: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.call(params), throwsA(isA<BusinessException>()));
      });
    });

    group('LoginCodeStatus 枚举测试', () {
      test('应该正确比较枚举值', () {
        expect(LoginCodeStatus.codeNormal.equals(0), isTrue);
        expect(LoginCodeStatus.codeNewSms.equals(3), isTrue);
        expect(LoginCodeStatus.codeSms.equals(4), isTrue);
        expect(LoginCodeStatus.codeEmail.equals(6), isTrue);
        expect(LoginCodeStatus.codeResetPassword.equals(7158), isTrue);
        expect(LoginCodeStatus.codeNormal.equals(1), isFalse);
      });

      test('应该正确检查枚举列表包含', () {
        final codeList = [LoginCodeStatus.codeNormal, LoginCodeStatus.codeSms];
        expect(LoginCodeStatus.contains(codeEnumList: codeList, value: 0), isTrue);
        expect(LoginCodeStatus.contains(codeEnumList: codeList, value: 4), isTrue);
        expect(LoginCodeStatus.contains(codeEnumList: codeList, value: 6), isFalse);
        expect(LoginCodeStatus.contains(codeEnumList: codeList, value: null), isFalse);
      });
    });
  });
}

/// 测试用的具体实现类
class TestUserInfoHandleUseCase extends UserInfoHandleBaseUseCase {
  final GoogleLocationService? mockGoogleLocationService;

  TestUserInfoHandleUseCase({
    required super.tenantRepository,
    required super.userRepository,
    required super.storageUtils,
    this.mockGoogleLocationService,
  });

  @override
  Future<GetTokenResultModel> call(SelectTenantModel params) async {
    final String zoneId = params.zoneId;
    await storageUtils.setValue<String>(StorageKeys.zoneId, zoneId);
    await envHelper.setHost(zoneId);
    final GetTokenResultModel result = await getBasicInfo(params);

    switch (result.code) {
      case 0: // 正常登录
        if (result.policy != null) {
          // 密码过期后重置密码后台没有制定特殊的code，移动端特殊制定
          result.code = LoginCodeStatus.codeResetPassword.value;
          return result;
        }
        await _testSaveStorageUtils(result: result);
        await _testGetUploadFileSizeMax();
        return result;
      case 3: // SMS 用户的这个tenant开启了双重验证，但是没有绑定双重认证信息
      case 4: // SMS 用户的这个tenant开启了双重验证，开始双重认证校验
      case 6: // Email 用户的这个tenant开启了双重验证，开始双重认证校验
        return result;
      default: // 错误
        final String? signInResult = result.signInResult;
        // 后台code没有传过来0，并且没有传过来错误信息
        if (signInResult == null || signInResult.isEmpty) {
          throw SystemException();
        }
        // 显示后台给出的错误
        if (result.code == 1) {
          throw BusinessException(signInResult);
        }
        // 用户过期
        if (result.signInResult == userApiAccountExpiration) {
          throw BusinessException(userShowAccountExpiration);
        }
        throw SystemException();
    }
  }

  /// 测试专用的存储方法，避免调用真实的GoogleLocationService
  Future<void> _testSaveStorageUtils({required GetTokenResultModel result}) async {
    final MapTokenModel? mapToken = result.mapToken;

    if (mapToken == null) {
      throw SystemException();
    }
    final String? token = mapToken.accessToken;
    if (token == null || token.isEmpty) {
      throw SystemException();
    }
    final SharedUserModel? user = result.user;
    if (user == null) {
      throw SystemException();
    }

    final String refreshToken = mapToken.refreshToken ?? '';
    final int userId = user.userId ?? 0;
    final String userName = user.userName ?? '';
    final String firstName = user.firstName ?? '';
    final String lastName = user.lastName ?? '';
    final String tel = user.tel ?? '';
    final String nationCode = user.nationCode ?? '';
    final String location = user.location ?? '';
    final String tenantId = user.tenantId ?? '';

    final bool tenantEnableTwoStep = result.tenantEnableTwoStep ?? false;
    final String printEnableFlg = result.printEnableFlg ?? '';
    final String barcodeExtraction = result.barcodeExtraction ?? '';
    if (tenantId.isEmpty) {
      throw SystemException();
    }
    if (tenantId == 'TENANT_ADMIN') {
      throw BusinessException('モバイルアプリでは「テナント管理者用メニュー」にログインすることは出来ません。ウェブブラウザからのログインをお願いいたします。');
    }
    await storageUtils.setValue<String>(StorageKeys.tenantId, tenantId);
    await storageUtils.setValue<String>(StorageKeys.token, token);
    await storageUtils.setValue<String>(StorageKeys.refreshToken, refreshToken);
    await storageUtils.setValue<int>(StorageKeys.userId, userId);
    await storageUtils.setValue<String>(StorageKeys.userName, userName);

    await storageUtils.setValue<String>(StorageKeys.firstName, firstName);
    await storageUtils.setValue<String>(StorageKeys.lastName, lastName);
    await storageUtils.setValue<bool>(StorageKeys.enableTwoStep, tenantEnableTwoStep);

    await storageUtils.setValue<String>(StorageKeys.tel + userName, tel);
    await storageUtils.setValue<String>(StorageKeys.nationCode + userName, nationCode);
    await storageUtils.setValue<String>(StorageKeys.printEnableFlg, printEnableFlg);
    await storageUtils.setValue<String>(StorageKeys.barcodeExtraction, barcodeExtraction);
    final DppInfoModel? dppInfo = result.dppInfo;
    if (dppInfo != null) {
      final String dppInfoStr = jsonEncode(dppInfo.toJson());
      await storageUtils.setValue<String>(StorageKeys.ocrInfo, dppInfoStr);
    }
    // 测试环境中使用Mock GoogleLocationService 或跳过地理位置服务调用
    if (mockGoogleLocationService != null) {
      mockGoogleLocationService!.toSetLocation();
    }
    // 从后台获取到的场所信息储存 （获取 方式 userName + StorageKeys.location）
    await storageUtils.setAssetScanLocation(assetLocation: location);
  }

  /// 测试专用的文件大小限制获取方法
  Future<void> _testGetUploadFileSizeMax() async {
    final SharedUserTenantModel tenantInfo = await userRepository.getUserTenant();
    final SharedTenantUseStatusModel? tenantUseStates = tenantInfo.tenantUseStatesList?.firstOrNull;
    final String? fieldText = tenantUseStates?.fieldText;
    if (fieldText == null || fieldText.isEmpty) {
      return;
    }
    final Map<String, dynamic> fieldTextObj = jsonDecode(fieldText);
    final int maxFileSizeInM = int.tryParse(fieldTextObj['uploadFileSizeMax']) ?? 0;
    if (maxFileSizeInM > 0) {
      await storageUtils.setValue<int>(StorageKeys.selectFileMaxSize, maxFileSizeInM);
    }
  }

  @override
  Future<GetTokenResultModel> getBasicInfo(SelectTenantModel params) async {
    // 根据不同的 tenantId 返回不同的测试结果
    final tenantId = params.dealTenant?.tenantId;

    switch (tenantId) {
      case 'test-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(
            userId: 123,
            userName: 'testuser',
            firstName: 'Test',
            lastName: 'User',
            tel: '1234567890',
            nationCode: '+81',
            location: 'Tokyo, Japan',
            tenantId: 'test-tenant',
          ),
          tenantEnableTwoStep: false,
          printEnableFlg: '1',
          barcodeExtraction: '1',
        );

      case 'password-expired-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          policy: PasswordPolicyModel(
            policyId: 1,
            tenantId: 'password-expired-tenant',
            mustContain: 'upper,lower,number',
            length: 8,
          ),
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'password-expired-tenant'),
        );

      case 'dpp-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'dpp-tenant'),
          dppInfo: DppInfoModel(
            dppId: 'test-dpp-id',
            eunomiaID: 'test-eunomia-id',
            dppAccessKey: 'test-dpp-access-key',
            eunomiaAccessKey: 'test-eunomia-access-key',
          ),
        );

      case 'file-size-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'file-size-tenant'),
        );

      case 'sms-new-tenant':
        return GetTokenResultModel(
          code: 3,
          msg: 'new sms verification required',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'sms-new-tenant'),
        );

      case 'sms-tenant':
        return GetTokenResultModel(
          code: 4,
          msg: 'sms verification required',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'sms-tenant'),
        );

      case 'email-tenant':
        return GetTokenResultModel(
          code: 6,
          msg: 'email verification required',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'email-tenant'),
        );

      case 'business-error-tenant':
        return GetTokenResultModel(code: 1, msg: 'business error', signInResult: '用户名或密码错误');

      case 'expired-tenant':
        return GetTokenResultModel(
          code: 999,
          msg: 'user expired',
          signInResult: 'assetforceをご利用いただきありがとうございます。\n有効期限が過ぎているためご利用になれません。\nテナント管理者へご連絡お願いします。',
        );

      case 'system-error-tenant':
        return GetTokenResultModel(code: 999, msg: 'system error', signInResult: null);

      case 'empty-error-tenant':
        return GetTokenResultModel(code: 999, msg: 'empty error', signInResult: '');

      case 'null-maptoken-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: null,
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'null-maptoken-tenant'),
        );

      case 'null-token-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: null, refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'null-token-tenant'),
        );

      case 'null-user-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: null,
        );

      case 'empty-tenant-id-tenant':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: ''),
        );

      case 'TENANT_ADMIN':
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'TENANT_ADMIN'),
        );

      default:
        return GetTokenResultModel(
          code: 0,
          msg: 'success',
          mapToken: MapTokenModel(accessToken: 'test-access-token', refreshToken: 'test-refresh-token'),
          user: SharedUserModel(userId: 123, userName: 'testuser', tenantId: 'default-tenant'),
        );
    }
  }
}
