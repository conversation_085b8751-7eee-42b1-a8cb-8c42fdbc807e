import 'package:asset_force_mobile_v2/features/login/tenant/presentation/controllers/tenant_controller.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/pages/tenant_page.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 Mock 类
@GenerateNiceMocks([MockSpec<TenantController>()])
import 'tenant_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockTenantController mockController;

  // 测试数据准备
  final testTenantList = [
    SharedTenantModel(tenantId: 'tenant1', tenantName: 'テナントA', zoneId: 'zone1'),
    SharedTenantModel(tenantId: 'tenant2', tenantName: 'テナントB', zoneId: 'zone2'),
    SharedTenantModel(tenantId: 'tenant3', tenantName: 'LongTenantName', zoneId: 'zone3'),
  ];

  final emptyTenantList = <SharedTenantModel>[];

  final tenantWithNullName = [SharedTenantModel(tenantId: 'tenant_null', tenantName: null, zoneId: 'zone_null')];

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: TenantPage());
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    mockController = MockTenantController();

    // 设置 Mock Controller 的默认返回值
    when(mockController.tenantTitle).thenReturn('テナント選択'.obs);
    when(mockController.tenantsList).thenReturn(<SharedTenantModel>[].obs);
    when(mockController.onTenantClick(any)).thenAnswer((_) async {});

    // 设置生命周期方法的 stub
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 注入 Mock Controller
    Get.put<TenantController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
    clearInteractions(mockController);
  });

  // ================================
  // UI ELEMENT TESTS - Phase 1
  // ================================
  group('UI ELEMENT TESTS', () {
    testWidgets('Displays correct AppBar title', (tester) async {
      // Arrange
      when(mockController.tenantTitle).thenReturn('テナント選択'.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('テナント選択'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('Displays back button in AppBar', (tester) async {
      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
      expect(find.byType(IconButton), findsOneWidget);
    });

    testWidgets('Displays tenant list when data is available', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);
      expect(find.text('テナントB'), findsOneWidget);
      expect(find.text('LongTenantName'), findsOneWidget);
    });

    testWidgets('Displays arrow icons for each tenant item', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.arrow_forward_ios), findsNWidgets(testTenantList.length));
    });

    testWidgets('Displays empty list when no tenants available', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(emptyTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('テナントA'), findsNothing);
      expect(find.text('テナントB'), findsNothing);
    });

    testWidgets('Handles tenant with null name gracefully', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(tenantWithNullName.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(ListView), findsOneWidget);
      // 应该显示空字符串而不是 "null"
      expect(find.text('null'), findsNothing);
      // 验证容器仍然存在
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('Displays correct UI structure and styling', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(Stack), findsWidgets); // 可能有多个 Stack，这是正常的
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);

      // 验证 GestureDetector 数量至少等于租户数量（页面可能有额外的 GestureDetector）
      final gestureDetectors = find.byType(GestureDetector);
      expect(gestureDetectors.evaluate().length, greaterThanOrEqualTo(testTenantList.length));

      // 验证 Container 存在（数量可能因布局复杂度而变化）
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('Displays text with correct overflow handling', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      final textWidgets = find.byType(Text);
      expect(textWidgets, findsWidgets);

      // 验证长文本存在
      expect(find.text('LongTenantName'), findsOneWidget);
    });
  });

  // ================================
  // BASIC RESPONSIVENESS TESTS
  // ================================
  group('BASIC RESPONSIVENESS TESTS', () {
    testWidgets('Updates AppBar title when tenantTitle changes', (tester) async {
      // Arrange
      final titleObs = 'Initial Title'.obs;
      when(mockController.tenantTitle).thenReturn(titleObs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert initial state
      expect(find.text('Initial Title'), findsOneWidget);

      // Act - Change title
      titleObs.value = 'Updated Title';
      await tester.pump();

      // Assert updated state
      expect(find.text('Updated Title'), findsOneWidget);
      expect(find.text('Initial Title'), findsNothing);
    });

    testWidgets('Updates tenant list when tenantsList changes', (tester) async {
      // Arrange
      final tenantsObs = <SharedTenantModel>[].obs;
      when(mockController.tenantsList).thenReturn(tenantsObs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert initial empty state
      expect(find.text('テナントA'), findsNothing);

      // Act - Add tenants
      tenantsObs.assignAll(testTenantList);
      await tester.pump();

      // Assert updated state
      expect(find.text('テナントA'), findsOneWidget);
      expect(find.text('テナントB'), findsOneWidget);
    });
  });

  // ================================
  // INTERACTION TESTS
  // ================================
  group('INTERACTION TESTS', () {
    testWidgets('点击租户项应该调用 onTenantClick', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 点击第一个租户
      await tester.tap(find.text('テナントA'));
      await tester.pumpAndSettle();

      // Verify
      verify(mockController.onTenantClick(testTenantList[0])).called(1);
    });

    testWidgets('点击不同租户项应该传递正确参数', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 点击第二个租户
      await tester.tap(find.text('テナントB'));
      await tester.pumpAndSettle();

      // Verify
      verify(mockController.onTenantClick(testTenantList[1])).called(1);
    });

    testWidgets('点击 GestureDetector 区域应该触发点击事件', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 直接点击 GestureDetector（使用 finder）
      final gestureDetectors = find.byType(GestureDetector);
      await tester.tap(gestureDetectors.first);
      await tester.pumpAndSettle();

      // Verify
      verify(mockController.onTenantClick(any)).called(1);
    });
  });

  // ================================
  // PHASE 2: ADVANCED TESTS
  // ================================

  // ================================
  // ERROR HANDLING TESTS
  // ================================
  group('ERROR HANDLING TESTS', () {
    testWidgets('处理网络错误状态', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(<SharedTenantModel>[].obs);
      when(mockController.tenantTitle).thenReturn('ネットワークエラー'.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('ネットワークエラー'), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
    });

    testWidgets('处理异常的租户数据结构', (tester) async {
      // Arrange - 创建包含异常数据的租户列表
      final corruptedTenantList = [
        SharedTenantModel(tenantId: '', tenantName: '', zoneId: ''),
        SharedTenantModel(tenantId: 'valid_id', tenantName: null, zoneId: 'valid_zone'),
      ].obs;

      when(mockController.tenantsList).thenReturn(corruptedTenantList);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 应该不崩溃并正常渲染
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(GestureDetector), findsWidgets);
    });

    testWidgets('处理异步操作错误状态', (tester) async {
      // Arrange - 模拟异步操作失败的状态
      when(mockController.tenantsList).thenReturn(testTenantList.obs);
      when(mockController.tenantTitle).thenReturn('エラーが発生しました'.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 页面应该显示错误状态但不崩溃
      expect(find.text('エラーが発生しました'), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);

      // 即使在错误状态，点击功能也应该正常工作
      await tester.tap(find.text('テナントA'));
      await tester.pumpAndSettle();

      verify(mockController.onTenantClick(testTenantList[0])).called(1);
    });
  });

  // ================================
  // LOADING STATE TESTS
  // ================================
  group('LOADING STATE TESTS', () {
    testWidgets('显示加载状态', (tester) async {
      // Arrange - 模拟加载状态
      when(mockController.tenantsList).thenReturn(<SharedTenantModel>[].obs);
      when(mockController.tenantTitle).thenReturn('読み込み中...'.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('読み込み中...'), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
    });

    testWidgets('从加载状态过渡到数据状态', (tester) async {
      // Arrange
      final titleObs = '読み込み中...'.obs;
      final tenantsObs = <SharedTenantModel>[].obs;

      when(mockController.tenantTitle).thenReturn(titleObs);
      when(mockController.tenantsList).thenReturn(tenantsObs);

      // Act - 初始加载状态
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert initial loading state
      expect(find.text('読み込み中...'), findsOneWidget);
      expect(find.text('テナントA'), findsNothing);

      // Act - 数据加载完成
      titleObs.value = 'テナント選択';
      tenantsObs.assignAll(testTenantList);
      await tester.pump();

      // Assert data loaded state
      expect(find.text('テナント選択'), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);
      expect(find.text('読み込み中...'), findsNothing);
    });
  });

  // ================================
  // EDGE CASE TESTS
  // ================================
  group('EDGE CASE TESTS', () {
    testWidgets('处理大量租户数据', (tester) async {
      // Arrange - 创建大量租户数据
      final largeTenantList = List.generate(
        50,
        (index) => SharedTenantModel(tenantId: 'tenant_$index', tenantName: 'テナント$index', zoneId: 'zone_$index'),
      ).obs;

      when(mockController.tenantsList).thenReturn(largeTenantList);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('テナント0'), findsOneWidget);
      expect(find.text('テナント49'), findsNothing); // 不在可视区域

      // 滚动到底部
      await tester.fling(find.byType(ListView), const Offset(0, -5000), 3000);
      await tester.pumpAndSettle();

      // 验证可以滚动到底部项目
      expect(find.text('テナント49'), findsOneWidget);
    });

    testWidgets('处理超长租户名称', (tester) async {
      // Arrange
      final longNameTenant = [
        SharedTenantModel(
          tenantId: 'long_tenant',
          tenantName: 'この租户名称非常长，需要测试文本省略号和换行处理功能，确保UI不会溢出或崩溃',
          zoneId: 'zone_long',
        ),
      ].obs;

      when(mockController.tenantsList).thenReturn(longNameTenant);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 应该正常渲染，不出现溢出
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(Text), findsWidgets);

      // 验证可以点击
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pumpAndSettle();

      verify(mockController.onTenantClick(any)).called(1);
    });

    testWidgets('处理快速连续点击', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 快速连续点击同一个租户
      await tester.tap(find.text('テナントA'));
      await tester.tap(find.text('テナントA'));
      await tester.tap(find.text('テナントA'));
      await tester.pumpAndSettle();

      // Verify - 应该调用3次
      verify(mockController.onTenantClick(testTenantList[0])).called(3);
    });
  });

  // ================================
  // LIFECYCLE TESTS
  // ================================
  group('LIFECYCLE TESTS', () {
    testWidgets('页面创建时应该调用 onStart', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Verify - onStart 应该被调用
      verify(mockController.onStart).called(1);
    });

    testWidgets('页面销毁时清理资源', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act - 创建页面
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Act - 销毁页面
      await tester.pumpWidget(const SizedBox());
      await tester.pumpAndSettle();

      // 页面已被销毁，控制器应该被清理
      // 注意：这个测试主要验证没有内存泄漏
      expect(tester.takeException(), isNull);
    });
  });

  // ================================
  // ACCESSIBILITY TESTS
  // ================================
  group('ACCESSIBILITY TESTS', () {
    testWidgets('支持语义化标签', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 验证关键元素有适当的语义
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(GestureDetector), findsWidgets);
    });

    testWidgets('支持触摸目标大小', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 验证点击区域足够大
      final gestureDetectors = find.byType(GestureDetector);
      expect(gestureDetectors, findsWidgets);

      // 验证可以成功点击
      await tester.tap(gestureDetectors.first);
      await tester.pumpAndSettle();

      verify(mockController.onTenantClick(any)).called(1);
    });
  });

  // ================================
  // PHASE 3: INTEGRATION & ADVANCED SCENARIO TESTS
  // ================================

  // ================================
  // NAVIGATION INTEGRATION TESTS
  // ================================
  group('NAVIGATION INTEGRATION TESTS', () {
    testWidgets('正确处理路由参数传递', (tester) async {
      // Arrange - 模拟从不同路由传来的参数
      when(mockController.tenantsList).thenReturn(testTenantList.obs);
      when(mockController.tenantTitle).thenReturn('特定环境租户'.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('特定环境租户'), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
    });

    testWidgets('返回按钮导航行为验证', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 点击返回按钮
      final backButton = find.byIcon(Icons.chevron_left);
      expect(backButton, findsOneWidget);

      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // 应该触发某种导航行为（在真实场景中会退出页面）
      // 这里我们验证UI仍然稳定
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('租户选择后的导航流程', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 选择租户
      await tester.tap(find.text('テナントA'));
      await tester.pumpAndSettle();

      // Verify - 应该调用控制器方法并可能触发导航
      verify(mockController.onTenantClick(testTenantList[0])).called(1);
    });
  });

  // ================================
  // STATE MANAGEMENT INTEGRATION TESTS
  // ================================
  group('STATE MANAGEMENT INTEGRATION TESTS', () {
    testWidgets('复杂状态变化序列测试', (tester) async {
      // Arrange
      final titleObs = '初始状态'.obs;
      final tenantsObs = <SharedTenantModel>[].obs;

      when(mockController.tenantTitle).thenReturn(titleObs);
      when(mockController.tenantsList).thenReturn(tenantsObs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert initial state
      expect(find.text('初始状态'), findsOneWidget);

      // State sequence: Loading -> Error -> Success
      titleObs.value = '読み込み中...';
      await tester.pump();
      expect(find.text('読み込み中...'), findsOneWidget);

      titleObs.value = 'エラーが発生しました';
      await tester.pump();
      expect(find.text('エラーが発生しました'), findsOneWidget);

      titleObs.value = 'テナント選択';
      tenantsObs.assignAll(testTenantList);
      await tester.pump();

      expect(find.text('テナント選択'), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);
    });

    testWidgets('多个状态同时更新', (tester) async {
      // Arrange
      final titleObs = 'タイトルA'.obs;
      final tenantsObs = testTenantList.obs;

      when(mockController.tenantTitle).thenReturn(titleObs);
      when(mockController.tenantsList).thenReturn(tenantsObs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 同时更新标题和租户列表
      titleObs.value = 'タイトルB';
      tenantsObs.clear();
      tenantsObs.add(SharedTenantModel(tenantId: 'new_tenant', tenantName: 'NewTenant', zoneId: 'new_zone'));

      await tester.pump();

      // Assert both changes reflected
      expect(find.text('タイトルB'), findsOneWidget);
      expect(find.text('NewTenant'), findsOneWidget);
      expect(find.text('テナントA'), findsNothing);
    });

    testWidgets('内存泄漏防护测试', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act - 多次创建和销毁页面
      for (int i = 0; i < 5; i++) {
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        await tester.pumpWidget(const SizedBox());
        await tester.pumpAndSettle();
      }

      // Assert - 没有异常抛出表示没有内存泄漏
      expect(tester.takeException(), isNull);
    });
  });

  // ================================
  // PERFORMANCE TESTS
  // ================================
  group('PERFORMANCE TESTS', () {
    testWidgets('大量数据渲染性能测试', (tester) async {
      // Arrange - 创建100个租户
      final largeTenantList = List.generate(
        100,
        (index) => SharedTenantModel(
          tenantId: 'perf_tenant_$index',
          tenantName: 'パフォーマンステナント$index',
          zoneId: 'perf_zone_$index',
        ),
      ).obs;

      when(mockController.tenantsList).thenReturn(largeTenantList);

      // Act & Measure
      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Assert - 渲染应该在合理时间内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5秒内
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('パフォーマンステナント0'), findsOneWidget);
    });

    testWidgets('快速滚动性能测试', (tester) async {
      // Arrange
      final largeTenantList = List.generate(
        50,
        (index) => SharedTenantModel(
          tenantId: 'scroll_tenant_$index',
          tenantName: 'スクロール$index',
          zoneId: 'scroll_zone_$index',
        ),
      ).obs;

      when(mockController.tenantsList).thenReturn(largeTenantList);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 快速滚动测试
      final listView = find.byType(ListView);

      // 向下滚动
      await tester.fling(listView, const Offset(0, -3000), 5000);
      await tester.pumpAndSettle();

      // 向上滚动
      await tester.fling(listView, const Offset(0, 3000), 5000);
      await tester.pumpAndSettle();

      // Assert - 滚动后页面仍然正常
      expect(find.byType(ListView), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('频繁状态更新性能测试', (tester) async {
      // Arrange
      final titleObs = 'Initial'.obs;
      when(mockController.tenantTitle).thenReturn(titleObs);
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 频繁更新状态
      final stopwatch = Stopwatch()..start();

      for (int i = 0; i < 20; i++) {
        titleObs.value = 'Title$i';
        await tester.pump();
      }

      stopwatch.stop();

      // Assert - 频繁更新应该能正常处理
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      expect(find.text('Title19'), findsOneWidget);
    });
  });

  // ================================
  // DEVICE ADAPTATION TESTS
  // ================================
  group('DEVICE ADAPTATION TESTS', () {
    testWidgets('小屏幕设备适配', (tester) async {
      // Arrange - 设置小屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(320, 568)); // iPhone SE size
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 在小屏幕上也应该正常显示
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);
      expect(tester.takeException(), isNull);

      // 恢复默认尺寸
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('大屏幕设备适配', (tester) async {
      // Arrange - 设置大屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(1024, 1366)); // iPad size
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 在大屏幕上也应该正常显示
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);

      // 恢复默认尺寸
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('横屏模式适配', (tester) async {
      // Arrange - 设置横屏尺寸
      await tester.binding.setSurfaceSize(const Size(812, 375)); // iPhone X landscape
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 横屏模式下也应该正常工作
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);

      // 恢复默认尺寸
      await tester.binding.setSurfaceSize(null);
    });
  });

  // ================================
  // USER EXPERIENCE FLOW TESTS
  // ================================
  group('USER EXPERIENCE FLOW TESTS', () {
    testWidgets('完整用户选择流程测试', (tester) async {
      // Arrange - 模拟真实用户流程
      final titleObs = '読み込み中...'.obs;
      final tenantsObs = <SharedTenantModel>[].obs;

      when(mockController.tenantTitle).thenReturn(titleObs);
      when(mockController.tenantsList).thenReturn(tenantsObs);

      // Act - Step 1: 页面加载
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();
      expect(find.text('読み込み中...'), findsOneWidget);

      // Step 2: 数据加载完成
      titleObs.value = 'テナント選択';
      tenantsObs.assignAll(testTenantList);
      await tester.pump();

      // Step 3: 用户浏览选项
      expect(find.text('テナント選択'), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);
      expect(find.text('テナントB'), findsOneWidget);

      // Step 4: 用户做出选择
      await tester.tap(find.text('テナントB'));
      await tester.pumpAndSettle();

      // Verify - 完整流程验证
      verify(mockController.onTenantClick(testTenantList[1])).called(1);
    });

    testWidgets('用户返回操作流程', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 用户进入页面，然后决定返回
      expect(find.byType(ListView), findsOneWidget);

      // 点击返回按钮
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pumpAndSettle();

      // 页面应该仍然稳定（在真实应用中会导航回上一页）
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('用户犹豫不决场景（多次点击不同选项）', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 用户点击多个不同选项
      await tester.tap(find.text('テナントA'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('テナントB'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('LongTenantName'));
      await tester.pumpAndSettle();

      // Verify - 每次点击都应该被正确处理
      verify(mockController.onTenantClick(testTenantList[0])).called(1);
      verify(mockController.onTenantClick(testTenantList[1])).called(1);
      verify(mockController.onTenantClick(testTenantList[2])).called(1);
    });
  });

  // ================================
  // REGRESSION TESTS
  // ================================
  group('REGRESSION TESTS', () {
    testWidgets('防止UI布局回归', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 关键UI元素的黄金测试
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(GestureDetector), findsWidgets);
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsNWidgets(testTenantList.length));
    });

    testWidgets('防止数据绑定回归', (tester) async {
      // Arrange
      final titleObs = 'Regression Test'.obs;
      final tenantsObs = testTenantList.obs;

      when(mockController.tenantTitle).thenReturn(titleObs);
      when(mockController.tenantsList).thenReturn(tenantsObs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 数据绑定正确性
      expect(find.text('Regression Test'), findsOneWidget);
      expect(find.text('テナントA'), findsOneWidget);
      expect(find.text('テナントB'), findsOneWidget);
      expect(find.text('LongTenantName'), findsOneWidget);
    });

    testWidgets('防止交互功能回归', (tester) async {
      // Arrange
      when(mockController.tenantsList).thenReturn(testTenantList.obs);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // 测试每个交互点
      await tester.tap(find.text('テナントA'));
      await tester.tap(find.text('テナントB'));
      await tester.tap(find.text('LongTenantName'));
      await tester.pumpAndSettle();

      // Verify - 所有交互都应该正常工作
      verify(mockController.onTenantClick(any)).called(3);
    });
  });
}
