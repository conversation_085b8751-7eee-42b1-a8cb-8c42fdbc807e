import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/services/category_validation_service_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late CategoryValidationService categoryValidationService;
  setUp(() {
    LogUtil.initialize();
    categoryValidationService = CategoryValidationServiceImpl();
  });

  group('checkCompletionStatus', () {
    test('checkCompletionStatus', () {
      categoryValidationService.checkCompletionStatus([
        CategoryData(
          titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
          categoryInfo: const CategoryModel(
            itemId: 12493079,
            itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
            itemName: 'itemName',
            itemType: 'itemType',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 12493079,
            categoryItemValue: '321',
            itemType: 'number',
            index: 0,
            itFinished: false,
          ),
        ),
        CategoryData(
          titleVal: 'FXH_Master_All(数字_小数点桁数12)',
          categoryInfo: const CategoryModel(
            itemId: 12493082,
            itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
            itemName: 'FXH_Master_All',
            itemType: 'master',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemValue: '123',
            categoryItemId: 12493082,
            categorySubItemId: 962060,
            itemType: 'master',
            index: 1,
            itFinished: false,
          ),
        ),
      ]);
    });
  });

  group('isEmptyCategory', () {
    test('isEmptyCategory', () {
      categoryValidationService.isEmptyCategory([
        CategoryItemValueModel(categoryItemValue: 'すべて', count: 239, homeImageMobileDisplayFlg: false),
        CategoryItemValueModel(categoryItemValue: '-10000.00001', count: 1, homeImageMobileDisplayFlg: false),
        CategoryItemValueModel(categoryItemValue: '', count: 238, homeImageMobileDisplayFlg: false),
      ]);
    });
  });
}
