import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_model_and_unread_hint_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/get_authority_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'get_home_model_and_unread_hint_usecase_test.mocks.dart';

@GenerateMocks([HomeRepository, GetAuthorityRepository])
void main() {
  LogUtil.initialize();
  late MockHomeRepository mockHomeRepository;
  late MockGetAuthorityRepository mockGetAuthorityRepository;
  late GetHomeModelAndUnreadHintUseCase useCase;

  setUp(() {
    mockHomeRepository = MockHomeRepository();
    mockGetAuthorityRepository = MockGetAuthorityRepository();
    useCase = GetHomeModelAndUnreadHintUseCase(
      homeRepository: mockHomeRepository,
      getAuthorityRepository: mockGetAuthorityRepository,
    );
  });

  group('GetHomeModelAndUnreadHintUseCase Tests', () {
    test('正常情况下返回正确的数据', () async {
      // 准备测试数据
      final homeModel = HomeModel(
        talkBadgeNum: 0,
        saveTemporaryActionCount: 10,
        unApproveProcessCount: 5,
        saveTemporaryProcessCount: 3,
        saveTemporaryActionDisplayCount: '10',
        saveTemporaryProcessDisplayCount: '3',
        unApproveProcessDisplayCount: '5',
        saveTemporaryActionLatestDate: null,
        saveTemporaryProcessLatestDate: null,
        unApproveProcessLatestDate: null,
        assetActionList: null,
        workflowList: null,
      );

      // 模拟Repository的行为
      when(mockHomeRepository.getHomePageProcessSummary()).thenAnswer((_) async => homeModel);
      when(
        mockHomeRepository.httpGetNotificationAnyNew(),
      ).thenAnswer((_) async => {'code': 0, 'anyNewNotifications': true});
      when(
        mockGetAuthorityRepository.hasFunctionPermission(GetAuthorityRepository.talkFunctionPermissionId),
      ).thenAnswer((_) async => true);

      // 执行用例
      final result = await useCase(null);

      // 验证结果
      expect(result.homeModel, equals(homeModel));
      expect(result.talkAuthority, isTrue);
      expect(result.showWorkflowUnreadHintSubject, isTrue);
      expect(result.showActionTaskUnreadHintSubject, isTrue);
      expect(result.showHomeMessageUnreadHintSubject, isTrue);

      // 验证显示计数的格式化是否正确
      expect(homeModel.saveTemporaryActionDisplayCount, equals('10'));
      expect(homeModel.unApproveProcessDisplayCount, equals('5'));
      expect(homeModel.saveTemporaryProcessDisplayCount, equals('3'));
    });

    test('当计数超过99时，显示"99+"', () async {
      // 准备测试数据
      final homeModel = HomeModel(
        talkBadgeNum: 0,
        saveTemporaryActionCount: 100,
        unApproveProcessCount: 150,
        saveTemporaryProcessCount: 200,
        saveTemporaryActionDisplayCount: '99+',
        saveTemporaryProcessDisplayCount: '99+',
        unApproveProcessDisplayCount: '99+',
        saveTemporaryActionLatestDate: null,
        saveTemporaryProcessLatestDate: null,
        unApproveProcessLatestDate: null,
        assetActionList: null,
        workflowList: null,
      );

      // 模拟Repository的行为
      when(mockHomeRepository.getHomePageProcessSummary()).thenAnswer((_) async => homeModel);
      when(
        mockHomeRepository.httpGetNotificationAnyNew(),
      ).thenAnswer((_) async => {'code': 0, 'anyNewNotifications': false});
      when(mockGetAuthorityRepository.hasFunctionPermission(any)).thenAnswer((_) async => false);

      // 执行用例
      final result = await useCase(null);

      // 验证显示计数的格式化是否正确
      expect(homeModel.saveTemporaryActionDisplayCount, equals('99+'));
      expect(homeModel.unApproveProcessDisplayCount, equals('99+'));
      expect(homeModel.saveTemporaryProcessDisplayCount, equals('99+'));

      // 验证标志
      expect(result.showWorkflowUnreadHintSubject, isTrue);
      expect(result.showActionTaskUnreadHintSubject, isTrue);
      expect(result.showHomeMessageUnreadHintSubject, isFalse);
    });

    test('当homePageProcessSummary为null时的行为', () async {
      // 模拟Repository的行为
      when(mockHomeRepository.getHomePageProcessSummary()).thenAnswer((_) async => null);
      when(
        mockHomeRepository.httpGetNotificationAnyNew(),
      ).thenAnswer((_) async => {'code': 0, 'anyNewNotifications': true});
      when(mockGetAuthorityRepository.hasFunctionPermission(any)).thenAnswer((_) async => true);

      // 执行用例
      final result = await useCase(null);

      // 验证结果
      expect(result.homeModel, isNull);
      expect(result.talkAuthority, isTrue);
      expect(result.showWorkflowUnreadHintSubject, isFalse);
      expect(result.showActionTaskUnreadHintSubject, isFalse);
      expect(result.showHomeMessageUnreadHintSubject, isFalse);
    });

    test('当通知接口返回code不为0时的行为', () async {
      // 准备测试数据
      final homeModel = HomeModel(
        talkBadgeNum: 0,
        saveTemporaryActionCount: 0,
        unApproveProcessCount: 0,
        saveTemporaryProcessCount: 0,
        saveTemporaryActionDisplayCount: '0',
        saveTemporaryProcessDisplayCount: '0',
        unApproveProcessDisplayCount: '0',
        saveTemporaryActionLatestDate: null,
        saveTemporaryProcessLatestDate: null,
        unApproveProcessLatestDate: null,
        assetActionList: null,
        workflowList: null,
      );

      // 模拟Repository的行为
      when(mockHomeRepository.getHomePageProcessSummary()).thenAnswer((_) async => homeModel);
      when(
        mockHomeRepository.httpGetNotificationAnyNew(),
      ).thenAnswer((_) async => {'code': 1, 'anyNewNotifications': true});
      when(mockGetAuthorityRepository.hasFunctionPermission(any)).thenAnswer((_) async => true);

      // 执行用例
      final result = await useCase(null);

      // 验证结果
      expect(result.showWorkflowUnreadHintSubject, isFalse);
      expect(result.showActionTaskUnreadHintSubject, isFalse);
      expect(result.showHomeMessageUnreadHintSubject, isFalse);
    });

    test('当发生SystemException时应该重新抛出异常', () async {
      // 模拟Repository抛出SystemException
      when(mockHomeRepository.getHomePageProcessSummary()).thenThrow(SystemException());

      // 执行用例并验证是否抛出原始的SystemException
      expect(() => useCase(null), throwsA(isA<SystemException>()));
    });
  });
}
