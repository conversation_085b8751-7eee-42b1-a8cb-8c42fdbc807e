import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/system_mobile_deploy_manage_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/usecases/login_traditional_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'dart:async';

import 'login_traditional_usecase_test.mocks.dart';

@GenerateMocks([LoginTraditionalRepository, IEnvHelper])
void main() {
  group('LoginTraditionalUseCase', () {
    late LoginTraditionalUseCase useCase;
    late MockLoginTraditionalRepository mockRepository;
    late MockIEnvHelper mockEnvHelper;

    setUp(() {
      mockRepository = MockLoginTraditionalRepository();
      mockEnvHelper = MockIEnvHelper();
    });

    group('Phase 0: 基础设施测试', () {
      group('构造函数测试', () {
        test('应该正确创建实例 - 提供所有参数', () {
          // Act
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);

          // Assert
          expect(useCase, isNotNull);
          expect(useCase.loginRepository, equals(mockRepository));
          expect(useCase.envHelper, equals(mockEnvHelper));
        });

        test('应该正确创建实例 - 仅提供必需参数', () {
          // Act
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository);

          // Assert
          expect(useCase, isNotNull);
          expect(useCase.loginRepository, equals(mockRepository));
          expect(useCase.envHelper, isA<EnvHelperImpl>());
        });

        test('应该使用默认的EnvHelperImpl当envHelper为null', () {
          // Act
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: null);

          // Assert
          expect(useCase, isNotNull);
          expect(useCase.loginRepository, equals(mockRepository));
          expect(useCase.envHelper, isA<EnvHelperImpl>());
        });
      });

      group('依赖注入验证', () {
        test('应该正确注入LoginTraditionalRepository', () {
          // Act
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);

          // Assert
          expect(useCase.loginRepository, isA<LoginTraditionalRepository>());
          expect(useCase.loginRepository, equals(mockRepository));
        });

        test('应该正确注入IEnvHelper', () {
          // Act
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);

          // Assert
          expect(useCase.envHelper, isA<IEnvHelper>());
          expect(useCase.envHelper, equals(mockEnvHelper));
        });

        test('应该在envHelper为null时使用EnvHelperImpl实例', () {
          // Act
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository);

          // Assert
          expect(useCase.envHelper, isA<EnvHelperImpl>());
          expect(useCase.envHelper, isNot(equals(mockEnvHelper)));
        });
      });

      group('Mock对象验证', () {
        setUp(() {
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
        });

        test('MockLoginTraditionalRepository应该正确创建', () {
          // Assert
          expect(mockRepository, isA<LoginTraditionalRepository>());
          expect(mockRepository, isA<MockLoginTraditionalRepository>());
        });

        test('MockIEnvHelper应该正确创建', () {
          // Assert
          expect(mockEnvHelper, isA<IEnvHelper>());
          expect(mockEnvHelper, isA<MockIEnvHelper>());
        });

        test('应该能够设置Repository mock行为', () {
          // Arrange
          final expectedResult = LoginResultModel();

          // Act & Assert
          expect(
            () => when(
              mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
            ).thenAnswer((_) async => expectedResult),
            returnsNormally,
          );
        });

        test('应该能够设置EnvHelper mock行为', () {
          // Act & Assert
          expect(() => when(mockEnvHelper.isIOS()).thenReturn(true), returnsNormally);
          expect(() => when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0'), returnsNormally);
        });
      });

      group('类型和接口验证', () {
        setUp(() {
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
        });

        test('应该实现UseCase接口', () {
          // Assert
          expect(useCase.call, isA<Function>());
        });

        test('应该具有正确的泛型类型', () {
          // Assert
          expect(useCase, isA<LoginTraditionalUseCase>());
        });

        test('LoginParams应该正确创建', () {
          // Act
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');

          // Assert
          expect(params, isNotNull);
          expect(params.userName, equals('<EMAIL>'));
          expect(params.password, equals('password123'));
        });

        test('LoginParams应该要求必需参数', () {
          // Act & Assert
          expect(() => LoginParams(userName: '<EMAIL>', password: 'password123'), returnsNormally);
        });
      });

      group('初始状态验证', () {
        setUp(() {
          useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
        });

        test('loginRepository应该不为null', () {
          // Assert
          expect(useCase.loginRepository, isNotNull);
        });

        test('envHelper应该不为null', () {
          // Assert
          expect(useCase.envHelper, isNotNull);
        });

        test('应该能够访问所有公共属性', () {
          // Assert
          expect(() => useCase.loginRepository, returnsNormally);
          expect(() => useCase.envHelper, returnsNormally);
        });
      });
    });

    group('Phase 1: 纯函数测试', () {
      setUp(() {
        useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
      });

      group('版本号比较测试（通过移动端版本检查间接测试）', () {
        group('版本号相等情况', () {
          test('相同版本号应该不抛出异常', () async {
            // Arrange
            const testVersion = '1.0.0';
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });

          test('完全相同的复杂版本号', () async {
            // Arrange
            const testVersion = '2.1.3';
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });
        });

        group('第一个版本大于第二个版本', () {
          test('主版本号更大应该不抛出异常', () async {
            // Arrange
            const apiVersion = '1.0.0'; // 较小的API版本
            const appVersion = '2.0.0'; // 较大的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });

          test('次版本号更大应该不抛出异常', () async {
            // Arrange
            const apiVersion = '1.1.0'; // 较小的API版本
            const appVersion = '1.2.0'; // 较大的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });

          test('修订版本号更大应该不抛出异常', () async {
            // Arrange
            const apiVersion = '1.0.1'; // 较小的API版本
            const appVersion = '1.0.2'; // 较大的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });
        });

        group('第一个版本小于第二个版本', () {
          test('主版本号更小应该抛出版本过期异常', () async {
            // Arrange
            const apiVersion = '2.0.0'; // 较大的API版本
            const appVersion = '1.0.0'; // 较小的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });

          test('次版本号更小应该抛出版本过期异常', () async {
            // Arrange
            const apiVersion = '1.2.0'; // 较大的API版本
            const appVersion = '1.1.0'; // 较小的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });

          test('修订版本号更小应该抛出版本过期异常', () async {
            // Arrange
            const apiVersion = '1.0.2'; // 较大的API版本
            const appVersion = '1.0.1'; // 较小的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });
        });

        group('不同长度版本号比较', () {
          test('较短版本与较长版本比较 - 短版本更小', () async {
            // Arrange
            const apiVersion = '*******'; // 较长的API版本
            const appVersion = '1.0.0'; // 较短的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });

          test('较长版本与较短版本比较 - 长版本更大', () async {
            // Arrange
            const apiVersion = '1.0.0'; // 较短的API版本
            const appVersion = '*******'; // 较长的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });

          test('单位数版本与多位数版本比较', () async {
            // Arrange
            const apiVersion = '10.0.0'; // 两位数版本
            const appVersion = '2.0.0'; // 单位数版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });
        });

        group('边界情况和特殊版本号', () {
          test('零版本号比较', () async {
            // Arrange
            const apiVersion = '0.0.1'; // 较大的API版本
            const appVersion = '0.0.0'; // 较小的APP版本
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });

          test('大版本号比较', () async {
            // Arrange
            const apiVersion = '99.99.99'; // 大版本号
            const appVersion = '100.0.0'; // 更大的版本号
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act & Assert
            expect(() => useCase.versionCheck(), returnsNormally);
          });

          test('前缀相同但长度不同的版本号', () async {
            // Arrange
            const apiVersion = '*******.5'; // 更长的API版本
            const appVersion = '1.2.3'; // 较短的APP版本（前缀相同）
            when(mockEnvHelper.isIOS()).thenReturn(true);
            when(mockEnvHelper.isAndroid()).thenReturn(false);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

            // Act & Assert
            expect(
              () => useCase.versionCheck(),
              throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
            );
          });
        });

        group('Android平台版本比较', () {
          test('Android平台应该使用clientType=2', () async {
            // Arrange
            const testVersion = '1.0.0';
            when(mockEnvHelper.isIOS()).thenReturn(false);
            when(mockEnvHelper.isAndroid()).thenReturn(true);
            when(mockEnvHelper.isWindows()).thenReturn(false);
            when(mockEnvHelper.isLinux()).thenReturn(false);
            when(mockEnvHelper.isMacOS()).thenReturn(false);
            when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
            when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
            when(
              mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
            when(
              mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 2),
            ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
            when(mockEnvHelper.isDev()).thenReturn(false);
            when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

            // Act
            await useCase.versionCheck();

            // Assert
            verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2)).called(1);
            verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 2)).called(1);
          });
        });
      });
    });

    group('Phase 2: 核心业务逻辑测试', () {
      setUp(() {
        useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
      });

      group('call方法 - 正常成功流程', () {
        test('完整的成功登录流程应该返回原始响应', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final expectedTenants = [SharedTenantModel(tenantId: '1', tenantName: 'Test Tenant', plan: '1')];
          final expectedMapToken = MapTokenModel(ticket: 'valid_ticket_123');
          final expectedResponse = LoginResultModel(tenants: expectedTenants, mapToken: expectedMapToken);

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => expectedResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(expectedResponse));
          expect(result.tenants, equals(expectedTenants));
          expect(result.mapToken, equals(expectedMapToken));
          verify(mockRepository.login(userName: '<EMAIL>', password: 'password123')).called(1);
        });

        test('多个tenant的成功登录流程', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password456');
          final expectedTenants = [
            SharedTenantModel(tenantId: '1', tenantName: 'Tenant 1', plan: '1'),
            SharedTenantModel(tenantId: '2', tenantName: 'Tenant 2', plan: '2'),
            SharedTenantModel(tenantId: '3', tenantName: 'Tenant 3', plan: '1'),
          ];
          final expectedMapToken = MapTokenModel(ticket: 'multi_tenant_ticket');
          final expectedResponse = LoginResultModel(tenants: expectedTenants, mapToken: expectedMapToken);

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => expectedResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(expectedResponse));
          expect(result.tenants?.length, equals(3));
          expect(result.mapToken?.ticket, equals('multi_tenant_ticket'));
        });

        test('包含复杂数据的成功登录流程', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'ComplexP@ssw0rd!');
          final expectedTenants = [
            SharedTenantModel(
              tenantId: 'complex_tenant_123',
              tenantName: 'Complex Tenant Name 複雑なテナント名',
              plan: 'enterprise',
            ),
          ];
          final expectedMapToken = MapTokenModel(ticket: 'very_long_ticket_with_special_chars_!@#\$%^&*()');
          final expectedResponse = LoginResultModel(tenants: expectedTenants, mapToken: expectedMapToken);

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => expectedResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(expectedResponse));
          verify(
            mockRepository.login(userName: '<EMAIL>', password: 'ComplexP@ssw0rd!'),
          ).called(1);
        });
      });

      group('call方法 - tenantList为null的失败场景', () {
        test('tenantList为null时应该返回空的LoginResultModel', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final responseWithNullTenants = LoginResultModel(
            tenants: null, // tenantList为null
            mapToken: MapTokenModel(ticket: 'valid_ticket'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithNullTenants);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
          verify(mockRepository.login(userName: '<EMAIL>', password: 'password123')).called(1);
        });

        test('repository返回完全空的响应时应该处理正确', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password');
          final emptyResponse = LoginResultModel(); // 所有字段都为null/空

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => emptyResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
        });
      });

      group('call方法 - tenantList为空数组的失败场景', () {
        test('tenantList为空数组时应该返回空的LoginResultModel', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final responseWithEmptyTenants = LoginResultModel(
            tenants: [], // tenantList为空数组
            mapToken: MapTokenModel(ticket: 'valid_ticket'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithEmptyTenants);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
          verify(mockRepository.login(userName: '<EMAIL>', password: 'password123')).called(1);
        });

        test('tenantList确实为空数组时的边界条件', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'test123');
          final responseWithExplicitEmptyList = LoginResultModel(
            tenants: <SharedTenantModel>[], // 明确指定空列表类型
            mapToken: MapTokenModel(ticket: 'boundary_ticket'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithExplicitEmptyList);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
        });
      });

      group('call方法 - mapToken为null的失败场景', () {
        test('mapToken为null时应该返回空的LoginResultModel', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final responseWithNullMapToken = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: null, // mapToken为null
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithNullMapToken);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
          verify(mockRepository.login(userName: '<EMAIL>', password: 'password123')).called(1);
        });

        test('有效tenantList但mapToken为null的情况', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'secure_password');
          final validTenants = [
            SharedTenantModel(tenantId: 'tenant1', tenantName: 'Valid Tenant', plan: 'premium'),
            SharedTenantModel(tenantId: 'tenant2', tenantName: 'Another Tenant', plan: 'basic'),
          ];
          final responseWithValidTenantsButNullToken = LoginResultModel(tenants: validTenants, mapToken: null);

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithValidTenantsButNullToken);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
        });
      });

      group('call方法 - ticket为null的失败场景', () {
        test('ticket为null时应该返回空的LoginResultModel', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final responseWithNullTicket = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: null), // ticket为null
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithNullTicket);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
          verify(mockRepository.login(userName: '<EMAIL>', password: 'password123')).called(1);
        });

        test('ticket为空字符串时应该返回原始响应（空字符串被认为是有效的）', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final expectedTenants = [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')];
          final expectedMapToken = MapTokenModel(ticket: ''); // ticket为空字符串
          final responseWithEmptyTicket = LoginResultModel(tenants: expectedTenants, mapToken: expectedMapToken);

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithEmptyTicket);

          // Act
          final result = await useCase.call(params);

          // Assert - 空字符串的ticket被认为是有效的，所以返回原始响应
          expect(result, equals(responseWithEmptyTicket));
          expect(result.tenants, equals(expectedTenants));
          expect(result.mapToken, equals(expectedMapToken));
          expect(result.mapToken?.ticket, equals(''));
        });

        test('有效数据但ticket为null的复杂场景', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'complex_pass');
          final validTenants = [
            SharedTenantModel(tenantId: 'valid1', tenantName: 'Valid Tenant 1', plan: 'enterprise'),
          ];
          final mapTokenWithNullTicket = MapTokenModel(
            ticket: null,
            // 可能还有其他字段，但ticket为null
          );
          final responseWithValidDataButNullTicket = LoginResultModel(
            tenants: validTenants,
            mapToken: mapTokenWithNullTicket,
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => responseWithValidDataButNullTicket);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, isA<LoginResultModel>());
          expect(result.tenants, isNull);
          expect(result.mapToken, isNull);
        });
      });

      group('call方法 - 参数验证和边界情况', () {
        test('应该正确传递用户名和密码给repository', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'specific_password');
          final successResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'valid_ticket'),
          );

          when(
            mockRepository.login(userName: '<EMAIL>', password: 'specific_password'),
          ).thenAnswer((_) async => successResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          verify(mockRepository.login(userName: '<EMAIL>', password: 'specific_password')).called(1);
          expect(result, equals(successResponse));
        });

        test('应该处理包含特殊字符的用户名和密码', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'P@ssw0rd!#\$%^&*()');
          final successResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'special_char_ticket'),
          );

          when(
            mockRepository.login(userName: '<EMAIL>', password: 'P@ssw0rd!#\$%^&*()'),
          ).thenAnswer((_) async => successResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          verify(mockRepository.login(userName: '<EMAIL>', password: 'P@ssw0rd!#\$%^&*()')).called(1);
          expect(result, equals(successResponse));
        });

        test('应该处理长用户名和密码', () async {
          // Arrange
          const longUserName =
              '<EMAIL>';
          const longPassword = 'VeryLongPasswordWithManyCharactersAndSpecialSymbols!@#\$%^&*()_+{}|:<>?[]\\;\'",./`~';
          final params = LoginParams(userName: longUserName, password: longPassword);
          final successResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'long_credentials_ticket'),
          );

          when(
            mockRepository.login(userName: longUserName, password: longPassword),
          ).thenAnswer((_) async => successResponse);

          // Act
          final result = await useCase.call(params);

          // Assert
          verify(mockRepository.login(userName: longUserName, password: longPassword)).called(1);
          expect(result, equals(successResponse));
        });

        test('repository调用应该只发生一次', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final successResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'single_call_ticket'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => successResponse);

          // Act
          await useCase.call(params);

          // Assert
          verify(mockRepository.login(userName: '<EMAIL>', password: 'password123')).called(1);
          verifyNoMoreInteractions(mockRepository);
        });
      });
    });

    group('Phase 3: 平台检测逻辑测试', () {
      setUp(() {
        useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
      });

      group('versionCheck方法 - 桌面平台处理', () {
        test('Windows平台应该调用updateServerEnv后直接返回', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(true);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
          verifyNever(mockRepository.getMobileDeployManageApiHostVersion(appClientType: anyNamed('appClientType')));
          verifyNever(
            mockRepository.getMobileDeployMobileVersion(
              mobileVersion: anyNamed('mobileVersion'),
              appClientType: anyNamed('appClientType'),
            ),
          );
        });

        test('Linux平台应该调用updateServerEnv后直接返回', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(true);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
          verifyNever(mockRepository.getMobileDeployManageApiHostVersion(appClientType: anyNamed('appClientType')));
        });

        test('macOS平台应该调用updateServerEnv后直接返回', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(true);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
          verifyNever(mockRepository.getMobileDeployManageApiHostVersion(appClientType: anyNamed('appClientType')));
        });

        test('多个桌面平台同时为true的边界情况', () async {
          // Arrange - 模拟异常情况：多个桌面平台同时为true
          when(mockEnvHelper.isWindows()).thenReturn(true);
          when(mockEnvHelper.isLinux()).thenReturn(true);
          when(mockEnvHelper.isMacOS()).thenReturn(true);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert - 应该仍然执行桌面平台逻辑
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
          verifyNever(mockRepository.getMobileDeployManageApiHostVersion(appClientType: anyNamed('appClientType')));
        });
      });

      group('versionCheck方法 - 移动平台处理', () {
        test('iOS平台应该调用_deployMobileVersion逻辑', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).called(1);
          verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1)).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
        });

        test('Android平台应该调用_deployMobileVersion逻辑', () async {
          // Arrange
          const testVersion = '2.1.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 2),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'production', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2)).called(1);
          verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 2)).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
        });

        test('移动平台同时为true的边界情况', () async {
          // Arrange - 模拟异常情况：iOS和Android同时为true
          const testVersion = '1.5.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert - 应该使用iOS逻辑（因为条件检查顺序）
          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).called(1);
          verifyNever(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2));
        });
      });

      group('versionCheck方法 - 版本检查成功场景', () {
        test('版本号相等时应该成功完成检查', () async {
          // Arrange
          const sameVersion = '3.2.1';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(sameVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: sameVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: sameVersion, appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'staging', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act & Assert
          expect(() => useCase.versionCheck(), returnsNormally);
          await useCase.versionCheck();

          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).called(2);
          verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: sameVersion, appClientType: 1)).called(2);
        });

        test('APP版本更新时应该成功完成检查', () async {
          // Arrange
          const apiVersion = '2.0.0'; // 较旧的API版本
          const appVersion = '2.1.0'; // 较新的APP版本
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 2),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'development', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(true);
          when(mockEnvHelper.getEnvironment()).thenReturn('local');
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.getEnvironment()).called(1);
        });
      });

      group('versionCheck方法 - 版本检查失败场景', () {
        test('APP版本过旧时应该抛出BusinessException 109', () async {
          // Arrange
          const apiVersion = '3.0.0'; // 较新的API版本
          const appVersion = '2.9.0'; // 较旧的APP版本
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: apiVersion));

          // Act & Assert
          expect(
            () => useCase.versionCheck(),
            throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
          );
        });

        test('获取发布信息为null时应该抛出BusinessException 108', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
          ).thenAnswer((_) async => null); // 返回null

          // Act & Assert
          expect(
            () => useCase.versionCheck(),
            throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(108))),
          );
        });

        test('版本过旧且处于维护模式时应该先抛出版本过旧错误（109）', () async {
          // Arrange - 测试业务逻辑：版本检查会先于维护模式检查
          const apiVersion = '3.0.0'; // 较新的API版本
          const appVersion = '2.9.0'; // 较旧的APP版本
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2)).thenAnswer(
            (_) async => SystemMobileDeployManageResultModel(
              mobileVersion: apiVersion, // API版本更新
            ),
          );
          // 注意：维护模式的检查不会被执行，因为版本检查会先失败

          // Act & Assert - 应该抛出版本过旧错误，而不是维护模式错误
          expect(
            () => useCase.versionCheck(),
            throwsA(
              isA<BusinessException>().having(
                (e) => e.code,
                'code',
                equals(109), // 版本过旧错误优先级更高
              ),
            ),
          );
        });
      });

      group('_deployMobileVersion方法 - 环境变量处理', () {
        test('空的env时应该直接返回不设置环境变量', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1)).thenAnswer(
            (_) async => SystemMobileDeployManageResultModel(
              env: '', // 空的环境变量
              appDeployStatus: '1',
            ),
          );

          // Act
          await useCase.versionCheck();

          // Assert
          verifyNever(mockEnvHelper.isDev());
          verifyNever(mockEnvHelper.getEnvironment());
          verifyNever(mockEnvHelper.updateServerEnv()); // 当env为空时，不应该调用updateServerEnv
        });

        test('开发环境时应该使用本地环境变量', () async {
          // Arrange
          const testVersion = '1.0.0';
          const localEnv = 'local_development';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1)).thenAnswer(
            (_) async => SystemMobileDeployManageResultModel(
              env: 'production', // API返回production
              appDeployStatus: '1',
            ),
          );
          when(mockEnvHelper.isDev()).thenReturn(true);
          when(mockEnvHelper.getEnvironment()).thenReturn(localEnv);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.isDev()).called(1);
          verify(mockEnvHelper.getEnvironment()).called(1);
          verify(mockEnvHelper.updateServerEnv()).called(1);
        });

        test('生产环境时应该使用API返回的环境变量', () async {
          // Arrange
          const testVersion = '1.0.0';
          const apiEnv = 'staging';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 2),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 2),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: apiEnv, appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.isDev()).called(1);
          verifyNever(mockEnvHelper.getEnvironment());
          verify(mockEnvHelper.updateServerEnv()).called(1);
        });
      });

      group('updateAuthHost调用验证', () {
        test('所有平台都应该首先调用updateAuthHost', () async {
          // Arrange - 测试桌面平台
          when(mockEnvHelper.isWindows()).thenReturn(true);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verify(mockEnvHelper.updateAuthHost()).called(1);
        });

        test('updateAuthHost调用应该在其他操作之前执行', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act
          await useCase.versionCheck();

          // Assert
          verifyInOrder([
            mockEnvHelper.updateAuthHost(),
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
            mockEnvHelper.updateServerEnv(),
          ]);
        });
      });
    });

    group('Phase 4: 异常处理和边界条件测试', () {
      setUp(() {
        useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
      });

      group('网络异常和连接错误处理', () {
        test('call方法遇到网络异常时应该正确传播异常', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final networkError = Exception('Network connection failed');

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenThrow(networkError);

          // Act & Assert
          expect(() => useCase.call(params), throwsA(equals(networkError)));
        });

        test('versionCheck遇到API异常时应该正确传播异常', () async {
          // Arrange
          const testVersion = '1.0.0';
          final apiError = Exception('API server unavailable');
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).thenThrow(apiError);

          // Act & Assert
          expect(() => useCase.versionCheck(), throwsA(equals(apiError)));
        });

        test('updateAuthHost失败时应该正确传播异常', () async {
          // Arrange
          final authError = Exception('Authentication host unreachable');
          when(mockEnvHelper.isWindows()).thenReturn(true);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.updateAuthHost()).thenThrow(authError);

          // Act & Assert
          expect(() => useCase.versionCheck(), throwsA(equals(authError)));
        });

        test('updateServerEnv失败时应该正确传播异常', () async {
          // Arrange
          final serverError = Exception('Server environment update failed');
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0');
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: '1.0.0'));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: '1.0.0', appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'prod', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenThrow(serverError);

          // Act & Assert
          expect(() => useCase.versionCheck(), throwsA(equals(serverError)));
        });
      });

      group('超时处理和异步错误', () {
        test('login方法超时时应该正确处理', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');

          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            await Future.delayed(Duration(seconds: 30)); // 模拟超长等待
            return LoginResultModel();
          });

          // Act & Assert - 使用timeout来测试超时处理
          expect(() => useCase.call(params).timeout(Duration(milliseconds: 100)), throwsA(isA<TimeoutException>()));
        });

        test('versionCheck API调用超时时应该正确处理', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).thenAnswer((_) async {
            await Future.delayed(Duration(seconds: 30)); // 模拟超长等待
            return SystemMobileDeployManageResultModel(mobileVersion: testVersion);
          });

          // Act & Assert
          expect(() => useCase.versionCheck().timeout(Duration(milliseconds: 100)), throwsA(isA<TimeoutException>()));
        });

        test('异步操作被取消时应该正确处理', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final completer = Completer<LoginResultModel>();

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) => completer.future);

          // Act
          final future = useCase.call(params);
          completer.completeError(Exception('Operation cancelled'));

          // Assert
          expect(() => future, throwsA(isA<Exception>()));
        });
      });

      group('并发调用和竞态条件', () {
        test('多次并发调用call方法应该独立处理', () async {
          // Arrange
          final params1 = LoginParams(userName: '<EMAIL>', password: 'pass1');
          final params2 = LoginParams(userName: '<EMAIL>', password: 'pass2');
          final params3 = LoginParams(userName: '<EMAIL>', password: 'pass3');

          final response1 = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Tenant1', plan: '1')],
            mapToken: MapTokenModel(ticket: 'ticket1'),
          );
          final response2 = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '2', tenantName: 'Tenant2', plan: '2')],
            mapToken: MapTokenModel(ticket: 'ticket2'),
          );
          final response3 = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '3', tenantName: 'Tenant3', plan: '3')],
            mapToken: MapTokenModel(ticket: 'ticket3'),
          );

          when(mockRepository.login(userName: '<EMAIL>', password: 'pass1')).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 100));
            return response1;
          });
          when(mockRepository.login(userName: '<EMAIL>', password: 'pass2')).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 50));
            return response2;
          });
          when(mockRepository.login(userName: '<EMAIL>', password: 'pass3')).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 150));
            return response3;
          });

          // Act - 并发执行
          final futures = await Future.wait([useCase.call(params1), useCase.call(params2), useCase.call(params3)]);

          // Assert - 结果应该按照预期返回，不受并发影响
          expect(futures[0], equals(response1));
          expect(futures[1], equals(response2));
          expect(futures[2], equals(response3));

          // 验证每个调用都执行了
          verify(mockRepository.login(userName: '<EMAIL>', password: 'pass1')).called(1);
          verify(mockRepository.login(userName: '<EMAIL>', password: 'pass2')).called(1);
          verify(mockRepository.login(userName: '<EMAIL>', password: 'pass3')).called(1);
        });

        test('多次并发调用versionCheck应该独立处理', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 10));
          });
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 50));
            return SystemMobileDeployManageResultModel(mobileVersion: testVersion);
          });
          when(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1)).thenAnswer((
            _,
          ) async {
            await Future.delayed(Duration(milliseconds: 30));
            return SystemMobileDeployManageResultModel(env: 'test', appDeployStatus: '1');
          });
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 20));
          });

          // Act - 并发执行3次versionCheck
          await Future.wait([useCase.versionCheck(), useCase.versionCheck(), useCase.versionCheck()]);

          // Assert - 每个调用都应该完整执行
          verify(mockEnvHelper.updateAuthHost()).called(3);
          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).called(3);
          verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1)).called(3);
          verify(mockEnvHelper.updateServerEnv()).called(3);
        });

        test('一个调用失败不应该影响其他并发调用', () async {
          // Arrange
          final goodParams = LoginParams(userName: '<EMAIL>', password: 'good');
          final badParams = LoginParams(userName: '<EMAIL>', password: 'bad');

          final goodResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Good', plan: '1')],
            mapToken: MapTokenModel(ticket: 'good_ticket'),
          );

          when(
            mockRepository.login(userName: '<EMAIL>', password: 'good'),
          ).thenAnswer((_) async => goodResponse);
          when(
            mockRepository.login(userName: '<EMAIL>', password: 'bad'),
          ).thenThrow(Exception('Authentication failed'));

          // Act & Assert
          final results = await Future.wait([
            useCase.call(goodParams),
            useCase.call(badParams).catchError((e) => LoginResultModel()),
          ]);

          expect(results[0], equals(goodResponse));
          expect(results[1], isA<LoginResultModel>()); // 失败的调用被catchError处理，返回空的LoginResultModel
        });
      });

      group('极端边界情况和异常数据', () {
        test('极长的用户名和密码应该正确处理', () async {
          // Arrange
          final longUserName = 'a' * 10000; // 10000个字符的用户名
          final longPassword = 'b' * 10000; // 10000个字符的密码
          final params = LoginParams(userName: longUserName, password: longPassword);
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'valid_ticket'),
          );

          when(mockRepository.login(userName: longUserName, password: longPassword)).thenAnswer((_) async => response);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(response));
          verify(mockRepository.login(userName: longUserName, password: longPassword)).called(1);
        });

        test('包含特殊Unicode字符的凭据应该正确处理', () async {
          // Arrange
          const unicodeUserName = '用户名测试👨‍💻🌍🚀@例え.テスト';
          const unicodePassword = 'パスワード🔒💎🎯测试密码🌟';
          final params = LoginParams(userName: unicodeUserName, password: unicodePassword);
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '🏢', tenantName: '国际化公司 🌏', plan: 'プレミアム')],
            mapToken: MapTokenModel(ticket: 'unicode_ticket_🎫'),
          );

          when(
            mockRepository.login(userName: unicodeUserName, password: unicodePassword),
          ).thenAnswer((_) async => response);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(response));
          expect(result.tenants?.first.tenantName, contains('国际化'));
          expect(result.mapToken?.ticket, contains('🎫'));
        });

        test('API返回超大数量的tenant时应该正确处理', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          // 创建1000个tenant
          final massiveTenants = List.generate(
            1000,
            (index) => SharedTenantModel(
              tenantId: 'tenant_$index',
              tenantName: 'Tenant Number $index',
              plan: 'plan_${index % 5}',
            ),
          );
          final response = LoginResultModel(
            tenants: massiveTenants,
            mapToken: MapTokenModel(ticket: 'massive_ticket'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => response);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(response));
          expect(result.tenants?.length, equals(1000));
          expect(result.tenants?.first.tenantId, equals('tenant_0'));
          expect(result.tenants?.last.tenantId, equals('tenant_999'));
        });

        test('超长版本号字符串应该正确处理', () async {
          // Arrange
          const extremelyLongVersion =
              '999999999.888888888.777777777.666666666.555555555.444444444.333333333.222222222.111111111.000000000';
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: extremelyLongVersion));

          // Act & Assert - 应该抛出版本过旧异常
          expect(
            () => useCase.versionCheck(),
            throwsA(isA<BusinessException>().having((e) => e.code, 'code', equals(109))),
          );
        });

        test('版本号包含非数字字符时应该正确处理解析错误', () async {
          // Arrange
          const invalidVersion = '1.2.abc.def';
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: invalidVersion));

          // Act & Assert - 应该抛出业务异常（因为版本号解析失败会导致业务逻辑错误）
          expect(() => useCase.versionCheck(), throwsA(isA<BusinessException>()));
        });
      });

      group('内存压力和资源清理', () {
        test('大量快速调用不应该导致内存泄露', () async {
          // Arrange
          final baseResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'test_ticket'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => baseResponse);

          // Act - 快速连续调用100次
          final futures = <Future<LoginResultModel>>[];
          for (int i = 0; i < 100; i++) {
            final params = LoginParams(userName: 'user$<EMAIL>', password: 'password$i');
            futures.add(useCase.call(params));
          }

          final results = await Future.wait(futures);

          // Assert - 所有调用都应该成功完成
          expect(results.length, equals(100));
          for (final result in results) {
            expect(result, equals(baseResponse));
          }

          // 验证没有额外的副作用
          verify(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).called(100);
        });

        test('异常情况下资源应该正确清理', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          var callCount = 0;

          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            callCount++;
            if (callCount == 1) {
              throw Exception('First call fails');
            }
            return LoginResultModel(
              tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
              mapToken: MapTokenModel(ticket: 'cleanup_ticket'),
            );
          });

          // Act & Assert - 第一次调用失败
          expect(() => useCase.call(params), throwsA(isA<Exception>()));

          // 第二次调用应该成功，证明没有状态污染
          final result = await useCase.call(params);
          expect(result.tenants, isNotNull);
          expect(result.mapToken, isNotNull);
        });
      });

      group('错误恢复和重试机制验证', () {
        test('UseCase本身不应该实现重试，而是让调用方处理', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          var attemptCount = 0;

          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            attemptCount++;
            throw Exception('Simulated failure #$attemptCount');
          });

          // Act & Assert - UseCase不应该实现自动重试
          expect(
            () => useCase.call(params),
            throwsA(allOf([isA<Exception>(), predicate<Exception>((e) => e.toString().contains('#1'))])),
          );

          // 再次调用仍然失败，证明没有内部重试机制
          expect(
            () => useCase.call(params),
            throwsA(allOf([isA<Exception>(), predicate<Exception>((e) => e.toString().contains('#2'))])),
          );

          expect(attemptCount, equals(2));
        });

        test('调用方可以实现自定义重试逻辑', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          var attemptCount = 0;
          final successResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Test', plan: '1')],
            mapToken: MapTokenModel(ticket: 'retry_success'),
          );

          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            attemptCount++;
            if (attemptCount < 3) {
              throw Exception('Retry attempt #$attemptCount failed');
            }
            return successResponse;
          });

          // Act - 实现调用方的重试逻辑
          LoginResultModel? result;
          for (int retry = 0; retry < 3; retry++) {
            try {
              result = await useCase.call(params);
              break;
            } catch (e) {
              if (retry == 2) rethrow; // 最后一次重试仍失败则抛出异常
              await Future.delayed(Duration(milliseconds: 10)); // 重试延迟
            }
          }

          // Assert
          expect(result, equals(successResponse));
          expect(attemptCount, equals(3));
        });

        test('部分失败的versionCheck应该正确报告错误位置', () async {
          // Arrange
          const testVersion = '1.0.0';
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(testVersion);
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: testVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1),
          ).thenThrow(Exception('Mobile version API failed'));

          // Act & Assert
          try {
            await useCase.versionCheck();
            fail('应该抛出异常');
          } catch (e) {
            expect(e, isA<Exception>());
            expect(e.toString(), contains('Mobile version API failed'));
          }

          // 验证部分调用成功执行
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).called(1);
          verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: testVersion, appClientType: 1)).called(1);
          // updateServerEnv不应该被调用，因为之前的步骤失败了
          verifyNever(mockEnvHelper.updateServerEnv());
        });
      });
    });

    group('Phase 5: 集成测试和完整业务流程验证', () {
      setUp(() {
        useCase = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
      });

      group('完整的登录到版本检查业务流程', () {
        test('完整的桌面平台流程应该按正确顺序执行', () async {
          // Arrange - 设置桌面平台
          final params = LoginParams(userName: '<EMAIL>', password: 'password123');
          final loginResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: '1', tenantName: 'Desktop Corp', plan: 'enterprise')],
            mapToken: MapTokenModel(ticket: 'desktop_ticket_12345'),
          );

          // Mock桌面平台检测
          when(mockEnvHelper.isWindows()).thenReturn(true);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(false);
          when(mockEnvHelper.isAndroid()).thenReturn(false);

          // Mock登录流程
          when(
            mockRepository.login(userName: '<EMAIL>', password: 'password123'),
          ).thenAnswer((_) async => loginResponse);

          // Mock版本检查流程
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});

          // Act - 执行完整流程
          final loginResult = await useCase.call(params);
          await useCase.versionCheck();

          // Assert - 验证登录结果
          expect(loginResult, equals(loginResponse));
          expect(loginResult.tenants?.length, equals(1));
          expect(loginResult.tenants?.first.tenantName, equals('Desktop Corp'));
          expect(loginResult.mapToken?.ticket, equals('desktop_ticket_12345'));

          // 验证调用顺序和频次
          verifyInOrder([
            mockRepository.login(userName: '<EMAIL>', password: 'password123'),
            mockEnvHelper.updateAuthHost(),
            mockEnvHelper.isWindows(),
            mockEnvHelper.updateServerEnv(),
          ]);

          // 验证桌面平台特定行为：不调用移动端API
          verifyNever(mockRepository.getMobileDeployManageApiHostVersion(appClientType: anyNamed('appClientType')));
        });

        test('完整的移动平台流程应该包含所有移动端特定步骤', () async {
          // Arrange - 设置移动平台
          final params = LoginParams(userName: '<EMAIL>', password: 'mobile_pass');
          final loginResponse = LoginResultModel(
            tenants: [
              SharedTenantModel(tenantId: 'mob1', tenantName: 'Mobile Corp', plan: 'premium'),
              SharedTenantModel(tenantId: 'mob2', tenantName: 'Mobile Dev', plan: 'basic'),
            ],
            mapToken: MapTokenModel(ticket: 'mobile_secure_token_xyz'),
          );

          const appVersion = '2.1.0';
          const serverVersion = '2.1.0';

          // Mock移动平台检测
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn(appVersion);

          // Mock登录流程
          when(
            mockRepository.login(userName: '<EMAIL>', password: 'mobile_pass'),
          ).thenAnswer((_) async => loginResponse);

          // Mock完整的移动版本检查流程
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: serverVersion));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(env: 'production', appDeployStatus: '1'));
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async => {});

          // Act - 执行完整流程
          final loginResult = await useCase.call(params);
          await useCase.versionCheck();

          // Assert - 验证登录结果
          expect(loginResult, equals(loginResponse));
          expect(loginResult.tenants?.length, equals(2));
          expect(loginResult.tenants?.map((t) => t.tenantName), containsAll(['Mobile Corp', 'Mobile Dev']));

          // 验证移动端完整调用链
          verifyInOrder([
            mockRepository.login(userName: '<EMAIL>', password: 'mobile_pass'),
            mockEnvHelper.updateAuthHost(),
            mockEnvHelper.isWindows(),
            mockEnvHelper.isLinux(),
            mockEnvHelper.isMacOS(),
            mockEnvHelper.isIOS(),
            mockEnvHelper.getAppVersion(),
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
            mockRepository.getMobileDeployMobileVersion(mobileVersion: appVersion, appClientType: 1),
            mockEnvHelper.isDev(),
            mockEnvHelper.updateServerEnv(),
          ]);
        });

        test('从登录失败到重试成功的完整恢复流程', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'retry_password');
          final successResponse = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'retry1', tenantName: 'Retry Corp', plan: 'standard')],
            mapToken: MapTokenModel(ticket: 'retry_success_token'),
          );

          var attemptCount = 0;
          when(mockRepository.login(userName: '<EMAIL>', password: 'retry_password')).thenAnswer((_) async {
            attemptCount++;
            if (attemptCount == 1) {
              throw Exception('Network timeout on first attempt');
            } else if (attemptCount == 2) {
              throw Exception('Server overload on second attempt');
            }
            return successResponse;
          });

          // Act - 模拟应用层的重试逻辑
          LoginResultModel? finalResult;
          Exception? lastError;

          for (int retry = 0; retry < 3; retry++) {
            try {
              finalResult = await useCase.call(params);
              break;
            } catch (e) {
              lastError = e as Exception;
              if (retry == 2) rethrow;
              await Future.delayed(Duration(milliseconds: 10));
            }
          }

          // Assert
          expect(finalResult, isNotNull);
          expect(finalResult, equals(successResponse));
          expect(attemptCount, equals(3));

          // 验证最后一次成功调用
          verify(mockRepository.login(userName: '<EMAIL>', password: 'retry_password')).called(3);
        });
      });

      group('数据一致性和状态同步验证', () {
        test('多次调用应该维持数据一致性', () async {
          // Arrange
          final params1 = LoginParams(userName: '<EMAIL>', password: 'pass1');
          final params2 = LoginParams(userName: '<EMAIL>', password: 'pass2');

          final response1 = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'tenant1', tenantName: 'Company 1', plan: 'enterprise')],
            mapToken: MapTokenModel(ticket: 'ticket1'),
          );
          final response2 = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'tenant2', tenantName: 'Company 2', plan: 'basic')],
            mapToken: MapTokenModel(ticket: 'ticket2'),
          );

          when(
            mockRepository.login(userName: '<EMAIL>', password: 'pass1'),
          ).thenAnswer((_) async => response1);
          when(
            mockRepository.login(userName: '<EMAIL>', password: 'pass2'),
          ).thenAnswer((_) async => response2);

          // Act - 连续调用多次
          final result1a = await useCase.call(params1);
          final result2a = await useCase.call(params2);
          final result1b = await useCase.call(params1);
          final result2b = await useCase.call(params2);

          // Assert - 相同参数应该返回相同结果
          expect(result1a, equals(result1b));
          expect(result2a, equals(result2b));
          expect(result1a, isNot(equals(result2a)));

          // 验证每个用户的数据一致性
          expect(result1a.tenants?.first.tenantId, equals('tenant1'));
          expect(result1b.tenants?.first.tenantId, equals('tenant1'));
          expect(result2a.tenants?.first.tenantId, equals('tenant2'));
          expect(result2b.tenants?.first.tenantId, equals('tenant2'));
        });

        test('UseCase实例状态应该保持无状态设计', () async {
          // Arrange
          final useCase1 = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);
          final useCase2 = LoginTraditionalUseCase(loginRepository: mockRepository, envHelper: mockEnvHelper);

          final params = LoginParams(userName: '<EMAIL>', password: 'password');
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'stateless', tenantName: 'Stateless Corp', plan: 'pro')],
            mapToken: MapTokenModel(ticket: 'stateless_token'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => response);

          // Act - 使用不同实例调用
          final result1 = await useCase1.call(params);
          final result2 = await useCase2.call(params);

          // Assert - 不同实例应该产生相同结果
          expect(result1, equals(result2));
          expect(result1.tenants?.first.tenantId, equals(result2.tenants?.first.tenantId));

          // 验证UseCase实例本身没有状态污染
          expect(useCase1.loginRepository, equals(useCase2.loginRepository));
          expect(useCase1.envHelper, equals(useCase2.envHelper));
        });

        test('大量数据处理时的内存和状态稳定性', () async {
          // Arrange - 创建大量tenant数据
          final massiveTenants = List.generate(
            500,
            (index) => SharedTenantModel(
              tenantId: 'tenant_$index',
              tenantName: 'Large Corporation $index',
              plan: 'plan_${index % 10}',
            ),
          );

          final params = LoginParams(userName: '<EMAIL>', password: 'bigdata_pass');
          final massiveResponse = LoginResultModel(
            tenants: massiveTenants,
            mapToken: MapTokenModel(ticket: 'massive_data_token_' + 'x' * 1000), // 大型token
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => massiveResponse);

          // Act - 多次处理大量数据
          final results = <LoginResultModel>[];
          for (int i = 0; i < 10; i++) {
            final result = await useCase.call(params);
            results.add(result);

            // 验证每次结果的完整性
            expect(result.tenants?.length, equals(500));
            expect(result.mapToken?.ticket?.length, greaterThan(1000));
          }

          // Assert - 所有结果应该一致
          for (int i = 1; i < results.length; i++) {
            expect(results[i], equals(results[0]));
            expect(results[i].tenants?.length, equals(results[0].tenants?.length));
          }

          // 验证数据完整性
          final firstResult = results[0];
          expect(firstResult.tenants?.first.tenantId, equals('tenant_0'));
          expect(firstResult.tenants?.last.tenantId, equals('tenant_499'));
          expect(firstResult.tenants?.map((t) => t.plan).toSet().length, equals(10)); // 10种不同计划
        });
      });

      group('复杂错误传播和恢复链验证', () {
        test('多级错误传播应该保持错误上下文信息', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'error_pass');
          final chainedError = Exception(
            'Database connection failed -> Auth service unavailable -> Login request rejected',
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenThrow(chainedError);

          // Act & Assert
          try {
            await useCase.call(params);
            fail('应该抛出链式错误');
          } catch (e) {
            expect(e, equals(chainedError));
            expect(e.toString(), contains('Database connection failed'));
            expect(e.toString(), contains('Auth service unavailable'));
            expect(e.toString(), contains('Login request rejected'));
          }
        });

        test('版本检查多点失败的错误传播路径', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0');

          // 设置失败点1: updateAuthHost失败
          final authError = Exception('Authentication host connection failed: DNS resolution error');
          when(mockEnvHelper.updateAuthHost()).thenThrow(authError);

          // Act & Assert - 第一个失败点
          try {
            await useCase.versionCheck();
            fail('应该在updateAuthHost处失败');
          } catch (e) {
            expect(e, equals(authError));
            expect(e.toString(), contains('DNS resolution error'));
          }

          // 重新设置 - 让updateAuthHost成功，但API调用失败
          reset(mockEnvHelper);
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0');
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});

          final apiError = Exception('Version API endpoint returned 503: Service temporarily unavailable');
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).thenThrow(apiError);

          // Act & Assert - 第二个失败点
          try {
            await useCase.versionCheck();
            fail('应该在API调用处失败');
          } catch (e) {
            expect(e, equals(apiError));
            expect(e.toString(), contains('503'));
            expect(e.toString(), contains('temporarily unavailable'));
          }
        });

        test('业务异常的详细诊断信息应该被保留', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0');
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});

          // 模拟版本过旧的情况
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).thenAnswer(
            (_) async => SystemMobileDeployManageResultModel(
              mobileVersion: '2.0.0', // 服务器要求更高版本
            ),
          );

          // Act & Assert
          try {
            await useCase.versionCheck();
            fail('应该抛出版本过旧异常');
          } catch (e) {
            expect(e, isA<BusinessException>());
            final businessException = e as BusinessException;
            expect(businessException.code, equals(109));

            // 验证业务异常包含了诊断所需的上下文信息
            expect(businessException.toString(), isNotEmpty);
          }
        });
      });

      group('性能基准和资源使用验证', () {
        test('单次登录调用应该在合理时间内完成', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'perf_pass');
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'perf1', tenantName: 'Performance Corp', plan: 'enterprise')],
            mapToken: MapTokenModel(ticket: 'performance_token'),
          );

          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            await Future.delayed(Duration(milliseconds: 50)); // 模拟网络延迟
            return response;
          });

          // Act
          final stopwatch = Stopwatch()..start();
          final result = await useCase.call(params);
          stopwatch.stop();

          // Assert
          expect(result, equals(response));
          expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // 应该在1秒内完成
          expect(stopwatch.elapsedMilliseconds, greaterThan(40)); // 但至少需要模拟的延迟时间
        });

        test('版本检查性能应该符合预期基准', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0');

          // 模拟各步骤的延迟
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 20));
          });
          when(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 30));
            return SystemMobileDeployManageResultModel(mobileVersion: '1.0.0');
          });
          when(mockRepository.getMobileDeployMobileVersion(mobileVersion: '1.0.0', appClientType: 1)).thenAnswer((
            _,
          ) async {
            await Future.delayed(Duration(milliseconds: 25));
            return SystemMobileDeployManageResultModel(env: 'prod', appDeployStatus: '1');
          });
          when(mockEnvHelper.isDev()).thenReturn(false);
          when(mockEnvHelper.updateServerEnv()).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 15));
          });

          // Act
          final stopwatch = Stopwatch()..start();
          await useCase.versionCheck();
          stopwatch.stop();

          // Assert
          expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // 应该在2秒内完成
          expect(stopwatch.elapsedMilliseconds, greaterThan(80)); // 至少需要所有步骤的累计时间
        });

        test('大量并发调用的性能稳定性', () async {
          // Arrange
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'concurrent', tenantName: 'Concurrent Corp', plan: 'premium')],
            mapToken: MapTokenModel(ticket: 'concurrent_token'),
          );

          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            await Future.delayed(Duration(milliseconds: 10));
            return response;
          });

          // Act - 50个并发调用
          final stopwatch = Stopwatch()..start();
          final futures = List.generate(
            50,
            (index) => useCase.call(LoginParams(userName: 'concurrent$<EMAIL>', password: 'concurrent_pass')),
          );

          final results = await Future.wait(futures);
          stopwatch.stop();

          // Assert
          expect(results.length, equals(50));
          expect(results.every((r) => r.tenants?.first.tenantId == 'concurrent'), isTrue);

          // 并发执行应该比串行执行快得多（串行需要500ms，并发应该接近10ms）
          expect(stopwatch.elapsedMilliseconds, lessThan(200));
          expect(stopwatch.elapsedMilliseconds, greaterThan(5));
        });
      });

      group('安全性和数据保护验证', () {
        test('敏感数据不应该被意外暴露', () async {
          // Arrange
          const sensitivePassword = 'TopSecret123!@#';
          final params = LoginParams(userName: '<EMAIL>', password: sensitivePassword);
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'sec1', tenantName: 'Security Corp', plan: 'enterprise')],
            mapToken: MapTokenModel(ticket: 'secure_token_xyz'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => response);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(response));

          // 验证UseCase本身不存储敏感数据
          expect(useCase.toString(), isNot(contains(sensitivePassword)));

          // 验证返回结果不包含原始密码
          expect(result.toString(), isNot(contains(sensitivePassword)));
          expect(result.tenants?.toString(), isNot(contains(sensitivePassword)));
          expect(result.mapToken?.toString(), isNot(contains(sensitivePassword)));
        });

        test('输入验证应该阻止潜在的注入攻击', () async {
          // Arrange - 尝试各种潜在的恶意输入
          final maliciousInputs = [
            "'; DROP TABLE users; --",
            '<script>alert("xss")</script>',
            '\${env:HOME}/malicious_path',
            '../../../etc/passwd',
            'null\x00byte',
          ];

          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'safe', tenantName: 'Safe Corp', plan: 'secure')],
            mapToken: MapTokenModel(ticket: 'validated_token'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => response);

          // Act & Assert - 所有恶意输入都应该被安全处理
          for (final maliciousInput in maliciousInputs) {
            final params = LoginParams(userName: maliciousInput, password: maliciousInput);

            // UseCase应该将输入传递给Repository，由Repository层处理验证
            final result = await useCase.call(params);
            expect(result, equals(response));

            // 验证恶意输入被正确传递给了mock，而不是被UseCase层过滤
            verify(mockRepository.login(userName: maliciousInput, password: maliciousInput)).called(1);
          }
        });

        test('错误信息不应该泄露系统内部信息', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'secure_pass');

          // 模拟包含内部信息的错误
          final internalError = Exception('Database connection failed at server 192.168.1.100:5432 with user admin');
          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenThrow(internalError);

          // Act & Assert
          try {
            await useCase.call(params);
            fail('应该抛出异常');
          } catch (e) {
            // UseCase应该透明地传播异常，错误消息过滤应该在上层处理
            expect(e, equals(internalError));
            expect(e.toString(), contains('Database connection failed'));

            // 注意：在真实应用中，应该在Presentation层或Service层过滤敏感信息
            // UseCase层保持透明，便于调试和错误追踪
          }
        });
      });

      group('可观测性和诊断信息验证', () {
        test('成功流程应该提供完整的执行轨迹', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'trace_pass');
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'trace1', tenantName: 'Traceable Corp', plan: 'premium')],
            mapToken: MapTokenModel(ticket: 'traceable_token'),
          );

          when(
            mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password')),
          ).thenAnswer((_) async => response);

          // Act
          final result = await useCase.call(params);

          // Assert
          expect(result, equals(response));

          // 验证可以追踪到具体的方法调用
          verify(mockRepository.login(userName: '<EMAIL>', password: 'trace_pass')).called(1);

          // 验证返回的数据包含足够的诊断信息
          expect(result.tenants?.first.tenantId, isNotEmpty);
          expect(result.mapToken?.ticket, isNotEmpty);
          expect(result.tenants?.first.tenantName, isNotEmpty);
        });

        test('失败流程应该提供明确的失败原因和位置', () async {
          // Arrange
          when(mockEnvHelper.isWindows()).thenReturn(false);
          when(mockEnvHelper.isLinux()).thenReturn(false);
          when(mockEnvHelper.isMacOS()).thenReturn(false);
          when(mockEnvHelper.isIOS()).thenReturn(true);
          when(mockEnvHelper.isAndroid()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('1.0.0');
          when(mockEnvHelper.updateAuthHost()).thenAnswer((_) async => {});

          // 在第二个API调用处设置失败点
          when(
            mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1),
          ).thenAnswer((_) async => SystemMobileDeployManageResultModel(mobileVersion: '1.0.0'));
          when(
            mockRepository.getMobileDeployMobileVersion(mobileVersion: '1.0.0', appClientType: 1),
          ).thenThrow(Exception('Deployment API failure at step 2'));

          // Act & Assert
          try {
            await useCase.versionCheck();
            fail('应该在第二个API调用处失败');
          } catch (e) {
            expect(e.toString(), contains('Deployment API failure at step 2'));
          }

          // 验证可以确定失败发生在哪个具体步骤
          verify(mockEnvHelper.updateAuthHost()).called(1);
          verify(mockRepository.getMobileDeployManageApiHostVersion(appClientType: 1)).called(1);
          verify(mockRepository.getMobileDeployMobileVersion(mobileVersion: '1.0.0', appClientType: 1)).called(1);
          verifyNever(mockEnvHelper.updateServerEnv()); // 这一步不应该被调用
        });

        test('性能诊断信息应该可以被收集', () async {
          // Arrange
          final params = LoginParams(userName: '<EMAIL>', password: 'perf_pass');
          final response = LoginResultModel(
            tenants: [SharedTenantModel(tenantId: 'perf1', tenantName: 'Performance Corp', plan: 'enterprise')],
            mapToken: MapTokenModel(ticket: 'performance_token'),
          );

          var callStartTime = DateTime.now();
          when(mockRepository.login(userName: anyNamed('userName'), password: anyNamed('password'))).thenAnswer((
            _,
          ) async {
            await Future.delayed(Duration(milliseconds: 100));
            return response;
          });

          // Act
          final executionStartTime = DateTime.now();
          final result = await useCase.call(params);
          final executionEndTime = DateTime.now();
          final executionDuration = executionEndTime.difference(executionStartTime);

          // Assert
          expect(result, equals(response));

          // 验证可以测量执行时间
          expect(executionDuration.inMilliseconds, greaterThan(90));
          expect(executionDuration.inMilliseconds, lessThan(1000));

          // 验证可以关联调用参数和结果用于性能分析
          expect(result.tenants?.first.tenantId, equals('perf1'));
          verify(mockRepository.login(userName: '<EMAIL>', password: 'perf_pass')).called(1);
        });
      });
    });
  });
}
