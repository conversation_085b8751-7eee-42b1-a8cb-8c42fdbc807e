// category_search_page_test.dart

import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/enums/navigation_action_enum.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/pages/category_search_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/states/asset_category_ui_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/widgets/category_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 生成 Mock 类
@GenerateNiceMocks([MockSpec<CategoryController>()])
import 'category_search_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// 辅助方法
CategoryData createCategoryData({
  required String titleVal,
  required int itemId,
  required String itemDisplayName,
  required String itemName,
  required String itemType,
  required int categoryItemId,
  required String itemTypeSelected,
  required int index,
  required bool itFinished,
}) {
  return CategoryData(
    titleVal: titleVal,
    categoryInfo: CategoryModel(
      itemId: itemId,
      itemDisplayName: itemDisplayName,
      itemName: itemName,
      itemType: itemType,
    ),
    categorySelectedData: CategorySelectedData(
      categoryItemId: categoryItemId,
      itemType: itemTypeSelected,
      index: index,
      itFinished: itFinished,
    ),
  );
}

CategoryItemValueModel createCategoryItemValueModel({
  required String categoryText,
  required int count,
  required bool homeImageMobileDisplayFlg,
  Map<String, dynamic>? assetMobileSetting,
  String? arColor,
  Map<String, dynamic>? asset,
}) {
  return CategoryItemValueModel(
    categoryText: categoryText,
    count: count,
    homeImageMobileDisplayFlg: homeImageMobileDisplayFlg,
    assetMobileSetting: assetMobileSetting,
    arColor: arColor,
    asset: asset,
    categoryItemValue: categoryText, // 假设 categoryItemValue 与 categoryText 相同
  );
}

void main() {
  late MockCategoryController mockController;
  late AssetCategoryUIState mockState;

  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: CategorySearchPage());
  }

  setUp(() {
    // 初始化 Mock Controller 和 State
    mockController = MockCategoryController();
    mockState = AssetCategoryUIState();

    // Stub getters
    when(mockController.state).thenReturn(mockState);

    // Stub 控制器的方法
    when(mockController.backBtnClick()).thenReturn(null);
    when(mockController.handleNavigation(any, payload: anyNamed('payload'))).thenReturn(null);

    // 其他需要 stub 的方法
    when(mockController.handleNavigation(NavigationActionEnum.cancel, payload: anyNamed('payload'))).thenReturn(null);
    when(mockController.handleNavigation(NavigationActionEnum.back, payload: anyNamed('payload'))).thenReturn(null);
    when(mockController.handleNavigation(NavigationActionEnum.forward, payload: anyNamed('payload'))).thenReturn(null);

    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 将 Mock Controller 放入依赖中
    Get.put<CategoryController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
  });

  // ================================
  // UI ELEMENT TESTS
  // ================================
  group('UI ELEMENT TESTS', () {
    testWidgets('Displays AppBar with correct title and back button', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('カテゴリから選択'), findsOneWidget);
      expect(find.byKey(const Key('category-header-back-button')), findsOneWidget);
    });

    testWidgets('Displays CategoryBottom with cancel button', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byKey(const Key('category-bottom-cancel-button')), findsOneWidget);
      expect(find.text('キャンセル'), findsOneWidget);
    });

    testWidgets('Displays CategoryPath with "一覧" button when categoryList is empty', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byKey(const Key('category-path-all-button')), findsOneWidget);
      expect(find.text('一覧'), findsOneWidget);
    });

    testWidgets('Displays selected category path buttons when categoryList is not empty', (tester) async {
      // Arrange
      final categories = [
        createCategoryData(
          titleVal: 'カテゴリA',
          itemId: 1,
          itemDisplayName: 'カテゴリA',
          itemName: 'カテゴリA',
          itemType: 'typeA',
          categoryItemId: 1,
          itemTypeSelected: 'typeA',
          index: 0,
          itFinished: true,
        ),
        createCategoryData(
          titleVal: 'カテゴリB',
          itemId: 2,
          itemDisplayName: 'カテゴリB',
          itemName: 'カテゴリB',
          itemType: 'typeB',
          categoryItemId: 2,
          itemTypeSelected: 'typeB',
          index: 1,
          itFinished: true,
        ),
      ];
      mockState.categoryList.assignAll(categories);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byKey(const Key('category-path-all-button')), findsOneWidget);
      expect(find.byKey(const Key('category-path-button-カテゴリA')), findsOneWidget);
      expect(find.byKey(const Key('category-path-button-カテゴリB')), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsNWidgets(categories.length)); // 分隔箭头数量应为 categories.length
    });

    testWidgets('Displays CategoryList with "データがありません" when categoryCountList is empty', (tester) async {
      // Arrange
      mockState.categoryCountList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('データがありません'), findsOneWidget);
      expect(find.byType(ListView), findsNothing);
    });

    testWidgets('Displays CategoryList with CategoryItem widgets when categoryCountList is not empty', (tester) async {
      // Arrange
      final categoryCountList = [
        createCategoryItemValueModel(categoryText: 'カテゴリA', count: 10, homeImageMobileDisplayFlg: true),
        createCategoryItemValueModel(categoryText: 'カテゴリB', count: 20, homeImageMobileDisplayFlg: false),
      ];
      mockState.categoryCountList.assignAll(categoryCountList);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(CategoryItem), findsNWidgets(2));
      expect(find.byKey(const Key('category-item-カテゴリA')), findsOneWidget);
      expect(find.byKey(const Key('category-item-カテゴリB')), findsOneWidget);
    });
  });

  // ================================
  // INTERACTION TESTS
  // ================================
  group('INTERACTION TESTS', () {
    testWidgets('Tapping back button calls backBtnClick()', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Tap back button
      await tester.tap(find.byKey(const Key('category-header-back-button')));
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.backBtnClick()).called(1);
    });

    testWidgets('Tapping cancel button calls handleNavigation with cancel action', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Tap cancel button
      await tester.tap(find.byKey(const Key('category-bottom-cancel-button')));
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.handleNavigation(NavigationActionEnum.cancel, payload: null)).called(1);
    });

    testWidgets('Tapping "一覧" button in CategoryPath calls handleNavigation with back action', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Tap "一覧" button
      await tester.tap(find.byKey(const Key('category-path-all-button')));
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.handleNavigation(NavigationActionEnum.back, payload: null)).called(1);
    });

    testWidgets('Tapping selected category path button calls handleNavigation with back action and payload', (
      tester,
    ) async {
      // Arrange
      final categories = [
        createCategoryData(
          titleVal: 'カテゴリA',
          itemId: 1,
          itemDisplayName: 'カテゴリA',
          itemName: 'カテゴリA',
          itemType: 'typeA',
          categoryItemId: 1,
          itemTypeSelected: 'typeA',
          index: 0,
          itFinished: true,
        ),
        createCategoryData(
          titleVal: 'カテゴリB',
          itemId: 2,
          itemDisplayName: 'カテゴリB',
          itemName: 'カテゴリB',
          itemType: 'typeB',
          categoryItemId: 2,
          itemTypeSelected: 'typeB',
          index: 1,
          itFinished: true,
        ),
      ];
      mockState.categoryList.assignAll(categories);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Tap "カテゴリA" button
      await tester.tap(find.byKey(const Key('category-path-button-カテゴリA')));
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.handleNavigation(NavigationActionEnum.back, payload: categories[0])).called(1);
    });

    testWidgets('Tapping CategoryItem calls handleNavigation with forward action and payload', (tester) async {
      // Arrange
      final categoryCountList = [
        createCategoryItemValueModel(categoryText: 'カテゴリA', count: 10, homeImageMobileDisplayFlg: true),
      ];
      mockState.categoryCountList.assignAll(categoryCountList);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Tap CategoryItem
      await tester.tap(find.byKey(const Key('category-item-カテゴリA')));
      await tester.pumpAndSettle();

      // Assert
      verify(mockController.handleNavigation(NavigationActionEnum.forward, payload: categoryCountList[0])).called(1);
    });
  });

  // ================================
  // CONDITIONAL RENDERING TESTS
  // ================================
  group('CONDITIONAL RENDERING TESTS', () {
    testWidgets('CategoryPath shows separator arrows only when categoryList has items', (tester) async {
      // Arrange
      // Case 1: categoryList is empty
      mockState.categoryList.assignAll([]);

      // Act & Assert
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // No separator arrows
      expect(find.byIcon(Icons.chevron_right), findsNothing);

      // Case 2: categoryList has items
      final categories = [
        createCategoryData(
          titleVal: 'カテゴリA',
          itemId: 1,
          itemDisplayName: 'カテゴリA',
          itemName: 'カテゴリA',
          itemType: 'typeA',
          categoryItemId: 1,
          itemTypeSelected: 'typeA',
          index: 0,
          itFinished: true,
        ),
        createCategoryData(
          titleVal: 'カテゴリB',
          itemId: 2,
          itemDisplayName: 'カテゴリB',
          itemName: 'カテゴリB',
          itemType: 'typeB',
          categoryItemId: 2,
          itemTypeSelected: 'typeB',
          index: 1,
          itFinished: true,
        ),
      ];
      mockState.categoryList.assignAll(categories);

      // Rebuild widget
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Separator arrows should be present
      expect(find.byIcon(Icons.chevron_right), findsNWidgets(categories.length));
    });
  });

  // ================================
  // DEBUGGING TESTS
  // ================================
  group('DEBUGGING TESTS', () {
    testWidgets('Prints widget tree for debugging', (tester) async {
      // Arrange
      mockState.categoryList.assignAll([]);

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Print Widget 树
      debugDumpApp();

      // Assert: 基本的断言以确保测试通过
      expect(find.byType(CategorySearchPage), findsOneWidget);
    });
  });
}
