import 'dart:async';

import 'package:analyzer/dart/element/element.dart';
import 'package:analyzer/dart/element/type.dart';
import 'package:build/build.dart';
import 'package:path/path.dart' as p;
import 'package:source_gen/source_gen.dart';

import 'route_item.dart';

class AnnotationScanBuilder implements Builder {
  @override
  FutureOr<void> build(BuildStep buildStep) async {
    if (!await buildStep.resolver.isLibrary(buildStep.inputId)) return;

    var library = await buildStep.resolver.libraryFor(buildStep.inputId);
    var reader = LibraryReader(library);

    var annotations = reader.classes.where((c) => c.metadata.any((m) => m.element?.displayName == 'GetRoutePage'));

    for (var classElement in annotations) {
      var annotation = classElement.metadata.firstWhere((m) => m.element?.displayName == 'GetRoutePage');

      var reader = ConstantReader(annotation.computeConstantValue());

      var routeName = reader.read('routeName').stringValue;
      routeName = routeName.startsWith('/') ? routeName : '/$routeName';

      var isConst = reader.read('isConst').boolValue;

      final bindingValue = reader.peek('binding');
      final bindingListValue = reader.read('bindings').listValue;
      final parametersValue = reader.peek('parameters')?.listValue ?? [];

      Set<String> imports = {};
      String? singleBinding;
      Set<String> multiBindings = {};
      List<String> parameters = [];

      // 处理注解中的参数列表
      for (var param in parametersValue) {
        parameters.add(param.toStringValue() ?? '');
      }

      // 从路由路径中提取参数
      parameters.addAll(_extractRouteParams(routeName));

      // 处理单个 binding
      if (bindingValue != null && !bindingValue.isNull) {
        final bindingType = bindingValue.typeValue;
        singleBinding = bindingType.getDisplayString();
        _addImport(bindingType, imports);
      }

      // 获取当前文件的路径
      final normalizedPath = _normalizeImportPath(buildStep.inputId.package, classElement.source.uri.path);

      // 添加当前文件的import
      imports.add("import 'package:${buildStep.inputId.package}/$normalizedPath';");

      // 处理bindings列表
      for (var bindingConst in bindingListValue) {
        final bindingType = bindingConst.toTypeValue();
        if (bindingType != null) {
          final bindingName = bindingType.getDisplayString();
          multiBindings.add(bindingName);
          _addImport(bindingType, imports);
        }
      }

      var item = RouteItem(
        routeName: routeName,
        import: imports,
        className: classElement.name,
        isConst: isConst,
        binding: singleBinding,
        bindings: multiBindings,
        parameters: parameters,
      );

      await buildStep.writeAsString(buildStep.inputId.changeExtension('.t.json'), item.toJson());
    }
  }

  /// 从路由路径中提取参数
  Set<String> _extractRouteParams(String routePath) {
    final params = <String>{};
    final segments = routePath.split('/');

    for (final segment in segments) {
      if (segment.startsWith(':')) {
        params.add(segment.substring(1));
      }
    }

    return params;
  }

  String _normalizeImportPath(String package, String filePath) {
    // 移除包名前缀（如果存在）
    if (filePath.startsWith(package)) {
      filePath = filePath.substring(package.length);
    }

    // 移除开头的斜杠（如果存在）
    while (filePath.startsWith('/')) {
      filePath = filePath.substring(1);
    }

    // 如果路径中包含 lib/，从 lib/ 开始取
    final libIndex = filePath.indexOf('lib/');
    if (libIndex != -1) {
      filePath = filePath.substring(libIndex + 4);
    }

    return filePath;
  }

  void _addImport(DartType type, Set<String> imports) {
    final element = type.element;
    if (element == null || element.library == null) return;

    final uri = element.library!.source.uri;
    if (uri.scheme.isEmpty || uri.scheme == 'dart') return;

    final importPath = uri.scheme == 'package' ? uri.toString() : _convertRelativeToPackageUri(uri, element.library!);

    if (importPath.isNotEmpty) {
      imports.add("import '$importPath';");
    }
  }

  String _convertRelativeToPackageUri(Uri uri, LibraryElement library) {
    final libSource = library.source;
    final libUri = libSource.uri;

    if (!libUri.isScheme('package')) return uri.toString();

    final packageName = libUri.pathSegments.first;
    final relativePath = p.relative(uri.toString(), from: p.dirname(libSource.fullName)).replaceAll('../', '');

    return 'package:$packageName/$relativePath';
  }

  @override
  Map<String, List<String>> get buildExtensions => {
    '.dart': ['.t.json'],
  };
}
