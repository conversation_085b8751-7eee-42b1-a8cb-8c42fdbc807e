import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/pages/search_condition_page.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/states/search_condition_ui_state.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_history_list_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_input_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_save_conditions_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 生成 Mock 类
@GenerateNiceMocks([MockSpec<SearchConditionController>()])
import './search_condition_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// **辅助方法：创建搜索条件模型**
SearchConditionModel createSearchCondition({required String searchName, required int id}) {
  return SearchConditionModel(searchName: searchName, searchId: id);
}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockSearchConditionController mockController;
  late SearchConditionUIState mockState;
  late TextEditingController realTextEditingController;

  // **创建测试用 Widget**
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: SearchConditionPage());
  }

  setUp(() {
    // 初始化 Mock Controller 和状态
    mockController = MockSearchConditionController();
    mockState = SearchConditionUIState();

    // 创建一个实际的 TextEditingController
    realTextEditingController = TextEditingController();

    // 当 controller.state 被调用时，返回 mockState
    when(mockController.state).thenReturn(mockState);

    // 当 controller.textEditingController 被调用时，返回实际的 TextEditingController
    when(mockController.textEditingController).thenReturn(realTextEditingController);

    // 添加桩值，确保页面能显示预期的组件和文本
    when(mockController.getTitle()).thenReturn('検索条件設定');
    when(mockController.isFromAssetList()).thenReturn(true);

    // Stub controller 的方法
    when(mockController.goBackPage()).thenReturn(null);
    when(mockController.onClickClearTheConditions()).thenReturn(null);
    when(mockController.onClickSearch()).thenReturn(null);
    when(mockController.onChangeUserSearchInput(any)).thenReturn(null);
    when(mockController.onClickClearAllRecordList()).thenReturn(null);
    when(mockController.onClickSearchConditionItemToSearch(any)).thenReturn(null);
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 将 mockController 放入 GetX 依赖中
    Get.put<SearchConditionController>(mockController);
  });

  tearDown(() {
    // 重置 GetX 依赖
    Get.reset();
    reset(mockController);
    realTextEditingController.dispose();
  });

  group('SearchConditionPage Widget Tests', () {
    // **UI 元素测试**
    group('UI ELEMENT TESTS', () {
      testWidgets('show appbar title and back button', (tester) async {
        // Arrange
        mockState.searchHistoryList.assignAll([]);
        mockState.searchInput.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert: 检查 AppBar 标题、输入框、历史列表、保存的条件和底部按钮
        expect(find.text('検索条件設定'), findsOneWidget);
        expect(find.byType(SearchInputBarWidget), findsOneWidget);
        expect(find.byType(SearchHistoryWidget), findsOneWidget);
        expect(find.byType(SavedConditionsWidget), findsOneWidget);
        expect(find.byType(BottomButtonsWidget), findsOneWidget);
      });

      testWidgets('显示搜索历史列表当有历史记录时', (tester) async {
        // Arrange
        final historyList = ['Flutter', 'Dart'];
        mockState.searchHistoryList.assignAll(historyList);
        mockState.searchInput.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SearchHistoryWidget), findsOneWidget);
        expect(find.text('過去に検索したキーワード'), findsOneWidget);
        expect(find.text('履歴をクリア'), findsOneWidget);
        for (var history in historyList) {
          expect(find.text(history), findsOneWidget);
        }
      });

      testWidgets('do not show search history list when history list is empty', (tester) async {
        // Arrange
        mockState.searchHistoryList.assignAll([]);

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        final historyWidgetFinder = find.byType(SearchHistoryWidget);
        expect(historyWidgetFinder, findsOneWidget); // 组件仍存在
        expect(find.text('過去に検索したキーワード'), findsNothing); // 标题不显示
      });

      testWidgets('显示底部按钮', (tester) async {
        // Arrange
        mockState.searchInput.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(BottomButtonsWidget), findsOneWidget);
        expect(find.text('条件をクリア'), findsOneWidget);
        expect(find.text('検索'), findsOneWidget);
      });

      testWidgets('Displays saved search conditions when available', (tester) async {
        // Arrange
        final savedConditions = [
          createSearchCondition(searchName: 'Condition 1', id: 1),
          createSearchCondition(searchName: 'Condition 2', id: 2),
        ];
        mockState.searchConditions.assignAll(savedConditions);
        mockState.selectedRadioCondition.value = savedConditions[0];

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert: 检查保存条件标题及每个保存条件
        expect(find.text('保存された検索条件'), findsOneWidget);
        for (var condition in savedConditions) {
          expect(find.text(condition.searchName ?? ''), findsOneWidget);
        }

        // 检查 Radio 按钮状态
        final radioFinder = find.byType(Radio<SearchConditionModel>);
        expect(radioFinder, findsNWidgets(savedConditions.length));

        for (int i = 0; i < savedConditions.length; i++) {
          final radioWidget = tester.widget<Radio<SearchConditionModel>>(radioFinder.at(i));
          final condition = savedConditions[i];
          if (condition == mockState.selectedRadioCondition.value) {
            expect(radioWidget.groupValue, equals(radioWidget.value));
          } else {
            expect(radioWidget.groupValue, isNot(equals(radioWidget.value)));
          }
        }
      });
    });

    // **用户交互测试**
    group('INTERACTION TESTS', () {
      testWidgets('点击返回按钮调用 goBackPage', (tester) async {
        // Arrange
        mockState.searchInput.value = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 点击返回按钮
        await tester.tap(find.byIcon(Icons.chevron_left));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.goBackPage()).called(1);
      });

      testWidgets('点击“条件をクリア”按钮调用 onClickClearTheConditions', (tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 点击“条件をクリア”按钮
        await tester.tap(find.text('条件をクリア'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onClickClearTheConditions()).called(1);
      });

      testWidgets('点击“検索”按钮调用 onClickSearch', (tester) async {
        // Arrange
        mockState.searchInput.value = 'Flutter';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 点击“検索”按钮
        await tester.tap(find.text('検索'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onClickSearch()).called(1);
      });

      testWidgets('在搜索输入框输入内容调用 onChangeUserSearchInput', (tester) async {
        // Arrange
        const searchText = 'Dart';

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 输入文本到搜索框（假设页面中只有一个 TextField）
        await tester.enterText(find.byType(TextField), searchText);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onChangeUserSearchInput(searchText)).called(1);
      });

      testWidgets('点击搜索历史项调用 onChangeUserSearchInput', (tester) async {
        // Arrange
        final historyList = ['Flutter', 'Dart'];
        mockState.searchHistoryList.assignAll(historyList);

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 点击 'Flutter'
        await tester.tap(find.text('Flutter'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onChangeUserSearchInput('Flutter')).called(1);
      });

      testWidgets('Triggers onClickSearch when submitting the search input', (tester) async {
        // Arrange
        const searchText = 'Flutter';
        mockState.searchInput.value = searchText;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 使用 TextField 查找搜索输入框
        await tester.enterText(find.byType(TextField), searchText);

        // 模拟回车键（Search 操作）
        await tester.testTextInput.receiveAction(TextInputAction.search);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onClickSearch()).called(1);
      });

      testWidgets('Calls onClickSearchConditionItemToSearch when tapping a saved search condition', (tester) async {
        // Arrange
        final savedConditions = [
          createSearchCondition(searchName: 'Condition 1', id: 1),
          createSearchCondition(searchName: 'Condition 2', id: 2),
        ];
        mockState.searchConditions.assignAll(savedConditions);
        mockState.selectedRadioCondition.value = savedConditions[0];

        when(mockController.onClickSearchConditionItemToSearch(any)).thenReturn(null);

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 点击 'Condition 2'
        await tester.tap(find.text('Condition 2'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onClickSearchConditionItemToSearch(savedConditions[1])).called(1);
      });

      testWidgets('test onClickSearchConditionItemToSearch', (tester) async {
        // Arrange
        final savedConditions = [
          createSearchCondition(searchName: '条件1', id: 1),
          createSearchCondition(searchName: '条件2', id: 2),
        ];
        mockState.searchConditions.assignAll(savedConditions);
        mockState.selectedRadioCondition.value = savedConditions[0];

        when(mockController.onClickSearchConditionItemToSearch(any)).thenReturn(null);

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 触发 onChanged 事件，点击第二个 Radio 按钮
        await tester.tap(find.byType(Radio<SearchConditionModel>).last);
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.onClickSearchConditionItemToSearch(savedConditions[1])).called(1);
      });
    });
  });
}
