import 'dart:convert';

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/me/message/data/models/notification_detail_model.dart';
import 'package:asset_force_mobile_v2/features/me/message/domain/repositories/message_repository.dart';
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_detail_usecase.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_file_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
@GenerateMocks([MessageRepository])
import 'message_detail_usecase_test.mocks.dart';

/// 测试数据辅助类
class MessageDetailTestData {
  /// 创建基础有效模型
  static NotificationDetailModel createValidModel({
    int? notificationId,
    String? notificationTitle,
    String? notificationBody,
    String? activeStartDate,
    String? category,
    String? fileInformation,
  }) {
    return NotificationDetailModel(
      notificationId: notificationId ?? 123,
      notificationTitle: notificationTitle ?? 'Test Title',
      notificationBody: notificationBody ?? '<p>Test Body</p>',
      activeStartDate: activeStartDate ?? '2024-01-15 10:30:00',
      category: category ?? '5',
      fileInformation: fileInformation,
      createdById: '1',
      createdDate: '2024-01-15 10:00:00',
      modifiedById: '1',
      lastEditorName: 'Test User',
      modifiedDate: '2024-01-15 10:00:00',
      zoneList: '["zone1", "zone2"]',
    );
  }

  /// 创建包含文件信息的有效JSON字符串
  static String createValidFileInfoJson() {
    return json.encode([
      {
        'fileName': 'document.pdf',
        'url': 'files/documents/document.pdf',
        'uploadDate': '2024-01-15 10:00',
        'size': '1.5 MB',
        'turl': 'https://example.com/temp/document.pdf',
      },
      {
        'fileName': 'image.png',
        'url': 'files/images/image.png',
        'uploadDate': '2024-01-15 10:01',
        'size': '2.3 MB',
        'turl': 'https://example.com/temp/image.png',
      },
    ]);
  }

  /// 创建单个文件的有效JSON字符串
  static String createSingleFileInfoJson() {
    return json.encode([
      {
        'fileName': 'single.doc',
        'url': 'files/single.doc',
        'uploadDate': '2024-01-15 12:00',
        'size': '500 KB',
        'turl': 'https://example.com/temp/single.doc',
      },
    ]);
  }

  /// 创建期望的文件UI模型列表
  static List<MessageFileUIModel> createExpectedFileModels() {
    return [
      MessageFileUIModel(
        fileName: 'document.pdf',
        url: 'files/documents/document.pdf',
        uploadDate: '2024-01-15 10:00',
        size: '1.5 MB',
        turl: 'https://example.com/temp/document.pdf',
      ),
      MessageFileUIModel(
        fileName: 'image.png',
        url: 'files/images/image.png',
        uploadDate: '2024-01-15 10:01',
        size: '2.3 MB',
        turl: 'https://example.com/temp/image.png',
      ),
    ];
  }

  /// 创建期望的UI模型
  static MessageUIModel createExpectedUIModel({
    List<MessageFileUIModel>? files,
    int? notificationId,
    String? notificationTitle,
    String? notificationBody,
    String? activeStartDate,
    int? category,
    String? fileInformation,
  }) {
    return MessageUIModel(
      notificationId: notificationId ?? 123,
      notificationTitle: notificationTitle ?? 'Test Title',
      notificationBody: notificationBody ?? '<p>Test Body</p>',
      activeStartDate: activeStartDate ?? '2024-01-15 10:30:00',
      category: category ?? 5,
      fileInformation: fileInformation ?? '',
      files: files ?? [],
    );
  }

  /// 无效JSON字符串示例
  static List<String> invalidJsonSamples() {
    return [
      'invalid json {[',
      '{broken: json}',
      '{"incomplete": "json"',
      '[invalid, array]',
      'not json at all',
      '{"validJson": "but not array"}',
      '{}',
      'null',
      '',
    ];
  }

  /// 边界值测试数据
  static Map<String, String?> boundaryTestData() {
    return {'null': null, 'empty': '', 'whitespace': '   ', 'newlines': '\n\t\r', 'very_long': 'a' * 10000};
  }
}

void main() {
  group('MessageDetailUsecase', () {
    late MessageDetailUsecase usecase;
    late MockMessageRepository mockMessageRepository;

    setUp(() {
      LogUtil.initialize();
      mockMessageRepository = MockMessageRepository();
      usecase = MessageDetailUsecase(messageRepository: mockMessageRepository);
    });

    tearDown(() {
      reset(mockMessageRepository);
    });

    // Phase 1: 基础设施测试
    group('Phase 1: 基础设施测试', () {
      test('应该能够创建 MessageDetailUsecase 实例', () {
        // Assert
        expect(usecase, isNotNull);
        expect(usecase, isA<MessageDetailUsecase>());
      });

      test('应该实现 UseCase 接口', () {
        // Assert
        expect(usecase, isA<UseCase<MessageUIModel, int>>());
      });

      test('应该正确注入 MessageRepository 依赖', () {
        // Arrange
        final testRepository = MockMessageRepository();

        // Act
        final testUsecase = MessageDetailUsecase(messageRepository: testRepository);

        // Assert
        expect(testUsecase.messageRepository, equals(testRepository));
      });

      test('构造函数应该要求 MessageRepository 参数', () {
        // Act & Assert - 验证构造函数参数为required
        expect(() => MessageDetailUsecase(messageRepository: mockMessageRepository), returnsNormally);
      });

      test('应该具有正确的方法签名', () {
        // Assert - 验证call方法签名
        expect(usecase.call, isA<Future<MessageUIModel> Function(int)>());
      });

      test('应该能够访问messageRepository属性', () {
        // Assert
        expect(usecase.messageRepository, equals(mockMessageRepository));
        expect(usecase.messageRepository, isA<MessageRepository>());
      });

      test('不同实例应该有独立的repository引用', () {
        // Arrange
        final repository1 = MockMessageRepository();
        final repository2 = MockMessageRepository();

        // Act
        final usecase1 = MessageDetailUsecase(messageRepository: repository1);
        final usecase2 = MessageDetailUsecase(messageRepository: repository2);

        // Assert
        expect(usecase1.messageRepository, equals(repository1));
        expect(usecase2.messageRepository, equals(repository2));
        expect(usecase1.messageRepository, isNot(equals(usecase2.messageRepository)));
      });

      test('应该继承UseCase的所有特性', () {
        // Assert - 验证泛型参数正确
        expect(usecase, isA<UseCase<MessageUIModel, int>>());

        // 验证可以作为UseCase使用
        UseCase<MessageUIModel, int> genericUsecase = usecase;
        expect(genericUsecase, isNotNull);
      });
    });

    // Phase 2: 核心业务逻辑测试
    group('Phase 2: 核心业务逻辑测试', () {
      group('2.1 正常流程测试', () {
        test('应该正确获取消息详情并转换为UI模型 - 包含文件信息', () async {
          // Arrange
          final fileInfoJson = MessageDetailTestData.createValidFileInfoJson();
          final model = MessageDetailTestData.createValidModel(fileInformation: fileInfoJson);

          when(mockMessageRepository.getNotificationDetail(notificationId: 123)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(123);

          // Assert
          expect(result, isA<MessageUIModel>());
          expect(result.notificationId, equals(123));
          expect(result.notificationTitle, equals('Test Title'));
          expect(result.notificationBody, equals('<p>Test Body</p>'));
          expect(result.activeStartDate, equals('2024-01-15 10:30:00'));
          expect(result.category, equals(5));
          expect(result.fileInformation, equals(fileInfoJson));
          expect(result.files, hasLength(2));
          expect(result.files[0].fileName, equals('document.pdf'));
          expect(result.files[1].fileName, equals('image.png'));

          // 验证repository被正确调用
          verify(mockMessageRepository.getNotificationDetail(notificationId: 123)).called(1);
        });

        test('应该正确获取消息详情并转换为UI模型 - 不含文件信息', () async {
          // Arrange
          final model = MessageDetailTestData.createValidModel(fileInformation: null);

          when(mockMessageRepository.getNotificationDetail(notificationId: 456)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(456);

          // Assert
          expect(result, isA<MessageUIModel>());
          expect(result.notificationId, equals(123));
          expect(result.notificationTitle, equals('Test Title'));
          expect(result.files, isEmpty);
          expect(result.fileInformation, equals(''));

          verify(mockMessageRepository.getNotificationDetail(notificationId: 456)).called(1);
        });

        test('应该正确处理所有字段的默认值', () async {
          // Arrange - 创建所有字段都为null的模型
          final model = NotificationDetailModel();

          when(mockMessageRepository.getNotificationDetail(notificationId: 789)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(789);

          // Assert - 验证所有默认值都正确设置
          expect(result.notificationId, equals(0));
          expect(result.notificationTitle, equals(''));
          expect(result.notificationBody, equals(''));
          expect(result.activeStartDate, equals(''));
          expect(result.category, equals(0));
          expect(result.fileInformation, equals(''));
          expect(result.files, isEmpty);
        });

        test('应该正确处理单个文件的情况', () async {
          // Arrange
          final fileInfoJson = MessageDetailTestData.createSingleFileInfoJson();
          final model = MessageDetailTestData.createValidModel(fileInformation: fileInfoJson);

          when(mockMessageRepository.getNotificationDetail(notificationId: 100)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(100);

          // Assert
          expect(result.files, hasLength(1));
          expect(result.files[0].fileName, equals('single.doc'));
          expect(result.files[0].url, equals('files/single.doc'));
        });
      });

      group('2.2 JSON解析测试', () {
        test('有效JSON数组应正确解析为文件模型列表', () async {
          // Arrange
          final validJson = MessageDetailTestData.createValidFileInfoJson();
          final model = MessageDetailTestData.createValidModel(fileInformation: validJson);

          when(mockMessageRepository.getNotificationDetail(notificationId: 200)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(200);

          // Assert
          expect(result.files, hasLength(2));
          expect(result.files[0], isA<MessageFileUIModel>());
          expect(result.files[1], isA<MessageFileUIModel>());
        });

        test('无效JSON格式应返回空列表不抛异常', () async {
          // Arrange & Act & Assert - 测试多种无效JSON格式
          final invalidJsonSamples = MessageDetailTestData.invalidJsonSamples();

          for (int i = 0; i < invalidJsonSamples.length; i++) {
            final invalidJson = invalidJsonSamples[i];
            final model = MessageDetailTestData.createValidModel(fileInformation: invalidJson);

            when(mockMessageRepository.getNotificationDetail(notificationId: 300 + i)).thenAnswer((_) async => model);

            final result = await usecase.call(300 + i);

            expect(result.files, isEmpty, reason: 'Invalid JSON "$invalidJson" should result in empty files list');
            expect(result.fileInformation, equals(invalidJson));
          }
        });

        test('JSON为空数组应返回空列表', () async {
          // Arrange
          final model = MessageDetailTestData.createValidModel(fileInformation: '[]');

          when(mockMessageRepository.getNotificationDetail(notificationId: 400)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(400);

          // Assert
          expect(result.files, isEmpty);
          expect(result.fileInformation, equals('[]'));
        });

        test('JSON为非数组格式应返回空列表', () async {
          // Arrange
          final model = MessageDetailTestData.createValidModel(fileInformation: '{"validJson": "but not array"}');

          when(mockMessageRepository.getNotificationDetail(notificationId: 500)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(500);

          // Assert
          expect(result.files, isEmpty);
        });
      });

      group('2.3 边界条件测试', () {
        test('fileInformation边界值测试', () async {
          final boundaryData = MessageDetailTestData.boundaryTestData();

          int notificationId = 600;
          for (final entry in boundaryData.entries) {
            final testCase = entry.key;
            final value = entry.value;

            // Arrange
            final model = MessageDetailTestData.createValidModel(fileInformation: value);

            when(
              mockMessageRepository.getNotificationDetail(notificationId: notificationId),
            ).thenAnswer((_) async => model);

            // Act
            final result = await usecase.call(notificationId);

            // Assert
            expect(result.files, isEmpty, reason: 'Test case "$testCase" should result in empty files list');
            expect(result.fileInformation, equals(value ?? ''));

            notificationId++;
          }
        });

        test('category字段类型转换测试', () async {
          final categoryTestCases = {
            'null': null,
            'empty': '',
            'zero': '0',
            'positive': '5',
            'negative': '-1',
            'decimal': '5.5',
            'text': 'abc',
            'mixed': '5abc',
          };

          // 根据代码逻辑: int.tryParse(model.category ?? '0') ?? 0
          // int.tryParse 只接受纯整数字符串，小数和混合字符都会返回null
          final expectedResults = {
            'null': 0, // null -> '0' -> 0
            'empty': 0, // '' -> int.tryParse('') -> null -> 0
            'zero': 0, // '0' -> 0
            'positive': 5, // '5' -> 5
            'negative': -1, // '-1' -> -1
            'decimal': 0, // '5.5' -> null -> 0 (int.tryParse不支持小数)
            'text': 0, // 'abc' -> null -> 0
            'mixed': 0, // '5abc' -> null -> 0 (int.tryParse不支持混合字符)
          };

          int notificationId = 700;
          for (final entry in categoryTestCases.entries) {
            final testCase = entry.key;
            final categoryValue = entry.value;
            final expectedResult = expectedResults[testCase]!;

            // Arrange - 直接创建模型，避免默认值干扰
            final model = NotificationDetailModel(
              notificationId: 123,
              notificationTitle: 'Test Title',
              notificationBody: '<p>Test Body</p>',
              activeStartDate: '2024-01-15 10:30:00',
              category: categoryValue, // 直接使用测试值，不经过默认值处理
              fileInformation: '',
              createdById: '1',
              createdDate: '2024-01-15 10:00:00',
              modifiedById: '1',
              lastEditorName: 'Test User',
              modifiedDate: '2024-01-15 10:00:00',
              zoneList: '["zone1", "zone2"]',
            );

            when(
              mockMessageRepository.getNotificationDetail(notificationId: notificationId),
            ).thenAnswer((_) async => model);

            // Act
            final result = await usecase.call(notificationId);

            // Assert
            expect(
              result.category,
              equals(expectedResult),
              reason: 'Category test case "$testCase" with value "$categoryValue" should result in $expectedResult',
            );

            notificationId++;
          }
        });
      });

      group('2.4 异常处理测试', () {
        test('Repository抛出异常应传播异常', () async {
          // Arrange
          when(mockMessageRepository.getNotificationDetail(notificationId: 999)).thenThrow(Exception('Network error'));

          // Act & Assert
          expect(() => usecase.call(999), throwsA(isA<Exception>()));

          verify(mockMessageRepository.getNotificationDetail(notificationId: 999)).called(1);
        });

        test('Repository抛出特定异常类型应正确传播', () async {
          // Arrange
          final testException = ArgumentError('Invalid notification ID');
          when(mockMessageRepository.getNotificationDetail(notificationId: 888)).thenThrow(testException);

          // Act & Assert
          try {
            await usecase.call(888);
            fail('Expected exception was not thrown');
          } catch (e) {
            expect(e, equals(testException));
            expect(e, isA<ArgumentError>());
          }
        });

        test('Repository返回null应能正常处理', () async {
          // 注意：根据实际实现，这个测试可能需要调整
          // 如果repository永远不返回null，可以移除这个测试
        });
      });

      group('2.5 数据一致性测试', () {
        test('输入的notificationId应正确传递给repository', () async {
          // Arrange
          final testIds = [1, 100, 999, 12345, -1, 0];

          for (final testId in testIds) {
            final model = MessageDetailTestData.createValidModel(notificationId: testId);

            when(mockMessageRepository.getNotificationDetail(notificationId: testId)).thenAnswer((_) async => model);

            // Act
            await usecase.call(testId);

            // Assert
            verify(mockMessageRepository.getNotificationDetail(notificationId: testId)).called(1);
          }
        });

        test('fileInformation原始数据应完整保留在结果中', () async {
          // Arrange
          final originalFileInfo = MessageDetailTestData.createValidFileInfoJson();
          final model = MessageDetailTestData.createValidModel(fileInformation: originalFileInfo);

          when(mockMessageRepository.getNotificationDetail(notificationId: 1111)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(1111);

          // Assert
          expect(result.fileInformation, equals(originalFileInfo));
          // 同时验证解析结果正确
          expect(result.files, hasLength(2));
        });

        test('所有字段映射应该保持数据完整性', () async {
          // Arrange
          final model = MessageDetailTestData.createValidModel(
            notificationId: 2222,
            notificationTitle: 'Complete Title Test',
            notificationBody: '<div>Complete Body Test</div>',
            activeStartDate: '2024-12-31 23:59:59',
            category: '7',
            fileInformation: '[]',
          );

          when(mockMessageRepository.getNotificationDetail(notificationId: 2222)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(2222);

          // Assert - 验证所有字段都正确映射
          expect(result.notificationId, equals(2222));
          expect(result.notificationTitle, equals('Complete Title Test'));
          expect(result.notificationBody, equals('<div>Complete Body Test</div>'));
          expect(result.activeStartDate, equals('2024-12-31 23:59:59'));
          expect(result.category, equals(7));
          expect(result.fileInformation, equals('[]'));
          expect(result.files, isEmpty);
        });

        test('文件解析失败不应影响其他字段的正确性', () async {
          // Arrange
          final model = MessageDetailTestData.createValidModel(
            notificationId: 3333,
            notificationTitle: 'Title with broken JSON',
            notificationBody: 'Body with broken JSON',
            fileInformation: 'completely broken json {[}',
          );

          when(mockMessageRepository.getNotificationDetail(notificationId: 3333)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(3333);

          // Assert - 其他字段应该正常，只有files为空
          expect(result.notificationId, equals(3333));
          expect(result.notificationTitle, equals('Title with broken JSON'));
          expect(result.notificationBody, equals('Body with broken JSON'));
          expect(result.fileInformation, equals('completely broken json {[}'));
          expect(result.files, isEmpty); // 只有这个字段受影响
        });
      });

      group('2.6 性能和资源测试', () {
        test('处理大量文件信息应该正常工作', () async {
          // Arrange - 创建包含多个文件的大JSON
          final largeFileList = List.generate(
            100,
            (index) => {
              'fileName': 'file_$index.pdf',
              'url': 'files/file_$index.pdf',
              'uploadDate': '2024-01-15 10:${index.toString().padLeft(2, '0')}',
              'size': '${index + 1} MB',
              'turl': 'https://example.com/temp/file_$index.pdf',
            },
          );

          final largeJsonString = json.encode(largeFileList);
          final model = MessageDetailTestData.createValidModel(fileInformation: largeJsonString);

          when(mockMessageRepository.getNotificationDetail(notificationId: 4444)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(4444);

          // Assert
          expect(result.files, hasLength(100));
          expect(result.files[0].fileName, equals('file_0.pdf'));
          expect(result.files[99].fileName, equals('file_99.pdf'));
        });

        test('处理包含特殊字符的数据应该正常工作', () async {
          // Arrange
          final specialCharsJson = json.encode([
            {
              'fileName': 'файл с русскими символами.pdf',
              'url': 'files/файл.pdf',
              'uploadDate': '2024-01-15 10:00',
              'size': '1 MB',
              'turl': 'https://example.com/temp/файл.pdf',
            },
            {
              'fileName': '特殊字符文件.doc',
              'url': 'files/特殊字符.doc',
              'uploadDate': '2024-01-15 10:01',
              'size': '2 MB',
              'turl': 'https://example.com/temp/特殊字符.doc',
            },
          ]);

          final model = MessageDetailTestData.createValidModel(
            notificationTitle: 'Title with 特殊字符 and émojis 🚀',
            notificationBody: '<p>Body with ñ, ü, and other спец символы</p>',
            fileInformation: specialCharsJson,
          );

          when(mockMessageRepository.getNotificationDetail(notificationId: 5555)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(5555);

          // Assert
          expect(result.notificationTitle, contains('特殊字符'));
          expect(result.notificationTitle, contains('🚀'));
          expect(result.notificationBody, contains('спец символы'));
          expect(result.files, hasLength(2));
          expect(result.files[0].fileName, equals('файл с русскими символами.pdf'));
          expect(result.files[1].fileName, equals('特殊字符文件.doc'));
        });
      });
    });

    // Phase 3: 高级场景和集成测试
    group('Phase 3: 高级场景和集成测试', () {
      group('3.1 并发调用测试', () {
        test('多个并发调用应该正确处理', () async {
          // Arrange
          final models = List.generate(
            10,
            (index) => MessageDetailTestData.createValidModel(
              notificationId: 1000 + index,
              notificationTitle: 'Concurrent Test $index',
            ),
          );

          for (int i = 0; i < models.length; i++) {
            when(
              mockMessageRepository.getNotificationDetail(notificationId: 1000 + i),
            ).thenAnswer((_) async => models[i]);
          }

          // Act - 并发执行多个调用
          final futures = List.generate(10, (index) => usecase.call(1000 + index));
          final results = await Future.wait(futures);

          // Assert
          expect(results, hasLength(10));
          for (int i = 0; i < results.length; i++) {
            expect(results[i].notificationTitle, equals('Concurrent Test $i'));
            expect(results[i].notificationId, equals(1000 + i));
          }

          // 验证每个调用都被执行了
          for (int i = 0; i < 10; i++) {
            verify(mockMessageRepository.getNotificationDetail(notificationId: 1000 + i)).called(1);
          }
        });

        test('并发调用不应该互相干扰', () async {
          // Arrange - 一个成功，一个失败
          final successModel = MessageDetailTestData.createValidModel(notificationId: 2000);
          when(mockMessageRepository.getNotificationDetail(notificationId: 2000)).thenAnswer((_) async => successModel);
          when(mockMessageRepository.getNotificationDetail(notificationId: 2001)).thenThrow(Exception('Network error'));

          // Act
          final futures = [usecase.call(2000), usecase.call(2001)];

          try {
            await Future.wait(futures);
            fail('Expected exception was not thrown');
          } catch (e) {
            // 预期的异常
          }

          // Assert - 成功的调用应该能单独执行
          final successResult = await usecase.call(2000);
          expect(successResult.notificationId, equals(2000));
        });
      });

      group('3.2 真实业务场景模拟', () {
        test('模拟完整的消息查看流程', () async {
          // Arrange - 模拟一个真实的消息通知
          final realWorldJson = json.encode([
            {
              'fileName': '重要通知.pdf',
              'url': 'notifications/2024/01/important-notice.pdf',
              'uploadDate': '2024-01-15 14:30',
              'size': '1.2 MB',
              'turl': 'https://storage.example.com/temp/notice-12345',
            },
            {
              'fileName': '添付資料.xlsx',
              'url': 'notifications/2024/01/attachment.xlsx',
              'uploadDate': '2024-01-15 14:35',
              'size': '850 KB',
              'turl': 'https://storage.example.com/temp/attachment-67890',
            },
          ]);

          final realWorldModel = NotificationDetailModel(
            notificationId: 607,
            notificationTitle: 'assetforceが 日本能率協会コンサルティング「IoT7つ道具」認定を取得しました',
            notificationBody:
                '<h1><strong><u><span style="font-size: 48px;">assetforceが 日本能率協会コンサルティング「IoT7つ道具」認定を取得しました</span></u></strong></h1><p>この度、弊社のassetforceが...</p>',
            activeStartDate: '2024-01-15 15:53:43',
            category: '5', // PR カテゴリ
            fileInformation: realWorldJson,
            createdById: '1',
            createdDate: '2024-01-15 06:52:41',
            modifiedById: '1',
            lastEditorName: '高陽陽',
            modifiedDate: '2024-01-15 06:52:41',
            zoneList: '["0ahjf2d99","44ba554e1","9dhas5sw2"]',
          );

          when(
            mockMessageRepository.getNotificationDetail(notificationId: 607),
          ).thenAnswer((_) async => realWorldModel);

          // Act
          final result = await usecase.call(607);

          // Assert - 真实场景的完整验证
          expect(result.notificationId, equals(607));
          expect(result.notificationTitle, contains('assetforce'));
          expect(result.notificationTitle, contains('認定'));
          expect(result.notificationBody, contains('<h1>'));
          expect(result.category, equals(5)); // PR
          expect(result.activeStartDate, equals('2024-01-15 15:53:43'));
          expect(result.files, hasLength(2));
          expect(result.files[0].fileName, equals('重要通知.pdf'));
          expect(result.files[1].fileName, equals('添付資料.xlsx'));

          // 验证文件信息解析正确
          expect(result.files[0].size, equals('1.2 MB'));
          expect(result.files[1].size, equals('850 KB'));
          expect(result.files[0].turl, contains('storage.example.com'));
        });

        test('模拟系统维护通知场景', () async {
          // Arrange - 系统维护通知（无附件）
          final maintenanceModel = NotificationDetailModel(
            notificationId: 801,
            notificationTitle: 'システムメンテナンスのお知らせ',
            notificationBody:
                '<p>下記の日程でシステムメンテナンスを実施いたします。</p><ul><li>日時: 2024年1月20日 02:00〜06:00</li><li>影響範囲: 全機能</li></ul>',
            activeStartDate: '2024-01-18 17:00:00',
            category: '3', // メンテナンス
            fileInformation: null, // 添付ファイルなし
            createdById: 'system',
            createdDate: '2024-01-18 17:00:00',
            modifiedById: 'system',
            lastEditorName: 'システム管理者',
            modifiedDate: '2024-01-18 17:00:00',
          );

          when(
            mockMessageRepository.getNotificationDetail(notificationId: 801),
          ).thenAnswer((_) async => maintenanceModel);

          // Act
          final result = await usecase.call(801);

          // Assert
          expect(result.notificationId, equals(801));
          expect(result.notificationTitle, equals('システムメンテナンスのお知らせ'));
          expect(result.category, equals(3)); // メンテナンス
          expect(result.files, isEmpty); // 添付ファイルなし
          expect(result.fileInformation, equals(''));
          expect(result.notificationBody, contains('メンテナンス'));
        });

        test('模拟紧急故障通知场景', () async {
          // Arrange - 紧急故障通知
          final emergencyModel = NotificationDetailModel(
            notificationId: 902,
            notificationTitle: '【緊急】システム障害発生のお知らせ',
            notificationBody: '<div style="color: red; font-weight: bold;">現在、システムに障害が発生しており、一部機能がご利用いただけません。</div>',
            activeStartDate: '2024-01-19 09:15:23',
            category: '2', // 障害
            fileInformation: '[]', // 空の配列
            createdById: 'emergency',
            createdDate: '2024-01-19 09:15:23',
            modifiedById: 'emergency',
            lastEditorName: '緊急対応チーム',
            modifiedDate: '2024-01-19 09:15:23',
          );

          when(
            mockMessageRepository.getNotificationDetail(notificationId: 902),
          ).thenAnswer((_) async => emergencyModel);

          // Act
          final result = await usecase.call(902);

          // Assert
          expect(result.notificationId, equals(902));
          expect(result.notificationTitle, contains('緊急'));
          expect(result.notificationTitle, contains('障害'));
          expect(result.category, equals(2)); // 障害
          expect(result.files, isEmpty);
          expect(result.fileInformation, equals('[]'));
          expect(result.notificationBody, contains('color: red'));
        });
      });

      group('3.3 极端边界场景测试', () {
        test('处理超大JSON数据应该正常工作', () async {
          // Arrange - 创建1000个文件的超大JSON
          final massiveFileList = List.generate(
            1000,
            (index) => {
              'fileName': 'file_${index.toString().padLeft(4, '0')}.pdf',
              'url': 'files/batch_2024/file_${index.toString().padLeft(4, '0')}.pdf',
              'uploadDate':
                  '2024-01-15 ${(10 + index % 14).toString().padLeft(2, '0')}:${(index % 60).toString().padLeft(2, '0')}',
              'size': '${(index % 50 + 1)} MB',
              'turl': 'https://cdn.example.com/temp/file_${index}_${DateTime.now().millisecondsSinceEpoch}',
            },
          );

          final massiveJsonString = json.encode(massiveFileList);
          final model = MessageDetailTestData.createValidModel(
            notificationId: 3000,
            fileInformation: massiveJsonString,
          );

          when(mockMessageRepository.getNotificationDetail(notificationId: 3000)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(3000);

          // Assert
          expect(result.files, hasLength(1000));
          expect(result.files.first.fileName, equals('file_0000.pdf'));
          expect(result.files.last.fileName, equals('file_0999.pdf'));
          expect(result.fileInformation, equals(massiveJsonString));
        });

        test('处理包含复杂嵌套JSON的fileInformation', () async {
          // Arrange - 包含复杂数据结构的JSON（虽然解析会失败，但应该优雅处理）
          final complexJson = json.encode({
            'files': [
              {
                'name': 'file1.pdf',
                'meta': {
                  'tags': ['important', 'urgent'],
                },
              },
            ],
            'metadata': {'version': '2.0', 'timestamp': DateTime.now().toIso8601String()},
          });

          final model = MessageDetailTestData.createValidModel(fileInformation: complexJson);

          when(mockMessageRepository.getNotificationDetail(notificationId: 4000)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(4000);

          // Assert - 复杂JSON应该被视为无效，返回空列表
          expect(result.files, isEmpty);
          expect(result.fileInformation, equals(complexJson));
        });

        test('处理极长字符串字段', () async {
          // Arrange - 极长的标题和内容
          final veryLongTitle = 'Very Long Title ' * 1000; // 非常长的标题
          final veryLongBody = '<p>' + ('This is a very long notification body content. ' * 2000) + '</p>';

          final model = MessageDetailTestData.createValidModel(
            notificationTitle: veryLongTitle,
            notificationBody: veryLongBody,
            notificationId: 5000,
          );

          when(mockMessageRepository.getNotificationDetail(notificationId: 5000)).thenAnswer((_) async => model);

          // Act
          final result = await usecase.call(5000);

          // Assert - 应该能正常处理极长字符串
          expect(result.notificationTitle, equals(veryLongTitle));
          expect(result.notificationBody, equals(veryLongBody));
          expect(result.notificationTitle.length, greaterThan(10000));
          expect(result.notificationBody.length, greaterThan(50000));
        });
      });

      group('3.4 Repository行为边界测试', () {
        test('Repository响应延迟测试', () async {
          // Arrange - 模拟慢速响应
          final model = MessageDetailTestData.createValidModel();
          when(mockMessageRepository.getNotificationDetail(notificationId: 6000)).thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 100)); // 模拟网络延迟
            return model;
          });

          // Act
          final stopwatch = Stopwatch()..start();
          final result = await usecase.call(6000);
          stopwatch.stop();

          // Assert
          expect(result, isA<MessageUIModel>());
          expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(100));
          verify(mockMessageRepository.getNotificationDetail(notificationId: 6000)).called(1);
        });

        test('Repository多次调用同一ID应该每次都执行', () async {
          // Arrange
          final model = MessageDetailTestData.createValidModel(notificationId: 7000);
          when(mockMessageRepository.getNotificationDetail(notificationId: 7000)).thenAnswer((_) async => model);

          // Act - 多次调用同一个ID
          await usecase.call(7000);
          await usecase.call(7000);
          await usecase.call(7000);

          // Assert - 每次都应该调用repository
          verify(mockMessageRepository.getNotificationDetail(notificationId: 7000)).called(3);
        });
      });

      group('3.5 内存和资源管理测试', () {
        test('大量连续调用不应该导致内存问题', () async {
          // Arrange
          for (int i = 8000; i < 8100; i++) {
            final model = MessageDetailTestData.createValidModel(notificationId: i);
            when(mockMessageRepository.getNotificationDetail(notificationId: i)).thenAnswer((_) async => model);
          }

          // Act - 大量连续调用
          final results = <MessageUIModel>[];
          for (int i = 8000; i < 8100; i++) {
            final result = await usecase.call(i);
            results.add(result);
          }

          // Assert
          expect(results, hasLength(100));
          expect(results.first.notificationId, equals(8000));
          expect(results.last.notificationId, equals(8099));

          // 验证每个调用都被执行
          for (int i = 8000; i < 8100; i++) {
            verify(mockMessageRepository.getNotificationDetail(notificationId: i)).called(1);
          }
        });

        test('JSON解析失败不应该影响后续调用', () async {
          // Arrange
          final modelWithBadJson = MessageDetailTestData.createValidModel(
            notificationId: 9000,
            fileInformation: 'completely broken json {[}',
          );
          final modelWithGoodJson = MessageDetailTestData.createValidModel(
            notificationId: 9001,
            fileInformation: MessageDetailTestData.createValidFileInfoJson(),
          );

          when(
            mockMessageRepository.getNotificationDetail(notificationId: 9000),
          ).thenAnswer((_) async => modelWithBadJson);
          when(
            mockMessageRepository.getNotificationDetail(notificationId: 9001),
          ).thenAnswer((_) async => modelWithGoodJson);

          // Act
          final result1 = await usecase.call(9000); // 坏JSON
          final result2 = await usecase.call(9001); // 好JSON

          // Assert - 第一次失败不应该影响第二次
          expect(result1.files, isEmpty);
          expect(result1.fileInformation, equals('completely broken json {[}'));

          expect(result2.files, hasLength(2));
          expect(result2.files[0].fileName, equals('document.pdf'));
        });
      });
    });
  });
}
