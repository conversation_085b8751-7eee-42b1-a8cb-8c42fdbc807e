// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_traditional/presentation/pages/login_traditional_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i14;
import 'dart:ui' as _i18;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i5;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i16;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i11;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/usecases/login_traditional_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/controllers/login_traditional_controller.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/states/login_traditional_ui_state.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart'
    as _i15;
import 'package:flutter/cupertino.dart' as _i7;
import 'package:get/get.dart' as _i12;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i17;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLoginTraditionalUseCase_0 extends _i1.SmartFake
    implements _i2.LoginTraditionalUseCase {
  _FakeLoginTraditionalUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBiometricsSwitchUtil_1 extends _i1.SmartFake
    implements _i3.BiometricsSwitchUtil {
  _FakeBiometricsSwitchUtil_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginSsoUseCase_2 extends _i1.SmartFake
    implements _i4.LoginSsoUseCase {
  _FakeLoginSsoUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_3 extends _i1.SmartFake implements _i5.IEnvHelper {
  _FakeIEnvHelper_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_4 extends _i1.SmartFake implements _i6.IStorageUtils {
  _FakeIStorageUtils_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFocusNode_5 extends _i1.SmartFake implements _i7.FocusNode {
  _FakeFocusNode_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);

  @override
  String toString({_i7.DiagnosticLevel? minLevel = _i7.DiagnosticLevel.info}) =>
      super.toString();
}

class _FakeTextEditingController_6 extends _i1.SmartFake
    implements _i7.TextEditingController {
  _FakeTextEditingController_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginTraditionalUiState_7 extends _i1.SmartFake
    implements _i8.LoginTraditionalUiState {
  _FakeLoginTraditionalUiState_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTenantUseCase_8 extends _i1.SmartFake implements _i9.TenantUseCase {
  _FakeTenantUseCase_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_9 extends _i1.SmartFake implements _i10.DialogService {
  _FakeDialogService_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_10 extends _i1.SmartFake
    implements _i11.NavigationService {
  _FakeNavigationService_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxString_11 extends _i1.SmartFake implements _i12.RxString {
  _FakeRxString_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_12 extends _i1.SmartFake implements _i12.RxBool {
  _FakeRxBool_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_13<T> extends _i1.SmartFake
    implements _i12.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LoginTraditionalController].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginTraditionalController extends _i1.Mock
    implements _i13.LoginTraditionalController {
  @override
  _i2.LoginTraditionalUseCase get loginUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#loginUseCase),
            returnValue: _FakeLoginTraditionalUseCase_0(
              this,
              Invocation.getter(#loginUseCase),
            ),
            returnValueForMissingStub: _FakeLoginTraditionalUseCase_0(
              this,
              Invocation.getter(#loginUseCase),
            ),
          )
          as _i2.LoginTraditionalUseCase);

  @override
  _i3.BiometricsSwitchUtil get biometricsSwitchUtil =>
      (super.noSuchMethod(
            Invocation.getter(#biometricsSwitchUtil),
            returnValue: _FakeBiometricsSwitchUtil_1(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
            returnValueForMissingStub: _FakeBiometricsSwitchUtil_1(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
          )
          as _i3.BiometricsSwitchUtil);

  @override
  _i4.LoginSsoUseCase get loginSsoUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#loginSsoUseCase),
            returnValue: _FakeLoginSsoUseCase_2(
              this,
              Invocation.getter(#loginSsoUseCase),
            ),
            returnValueForMissingStub: _FakeLoginSsoUseCase_2(
              this,
              Invocation.getter(#loginSsoUseCase),
            ),
          )
          as _i4.LoginSsoUseCase);

  @override
  _i5.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_3(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_3(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i5.IEnvHelper);

  @override
  _i6.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i6.IStorageUtils);

  @override
  _i7.FocusNode get focusNodePassWord =>
      (super.noSuchMethod(
            Invocation.getter(#focusNodePassWord),
            returnValue: _FakeFocusNode_5(
              this,
              Invocation.getter(#focusNodePassWord),
            ),
            returnValueForMissingStub: _FakeFocusNode_5(
              this,
              Invocation.getter(#focusNodePassWord),
            ),
          )
          as _i7.FocusNode);

  @override
  _i7.TextEditingController get controllerPassWord =>
      (super.noSuchMethod(
            Invocation.getter(#controllerPassWord),
            returnValue: _FakeTextEditingController_6(
              this,
              Invocation.getter(#controllerPassWord),
            ),
            returnValueForMissingStub: _FakeTextEditingController_6(
              this,
              Invocation.getter(#controllerPassWord),
            ),
          )
          as _i7.TextEditingController);

  @override
  _i7.FocusNode get focusNodeEmail =>
      (super.noSuchMethod(
            Invocation.getter(#focusNodeEmail),
            returnValue: _FakeFocusNode_5(
              this,
              Invocation.getter(#focusNodeEmail),
            ),
            returnValueForMissingStub: _FakeFocusNode_5(
              this,
              Invocation.getter(#focusNodeEmail),
            ),
          )
          as _i7.FocusNode);

  @override
  _i7.TextEditingController get controllerEmail =>
      (super.noSuchMethod(
            Invocation.getter(#controllerEmail),
            returnValue: _FakeTextEditingController_6(
              this,
              Invocation.getter(#controllerEmail),
            ),
            returnValueForMissingStub: _FakeTextEditingController_6(
              this,
              Invocation.getter(#controllerEmail),
            ),
          )
          as _i7.TextEditingController);

  @override
  set controllerEmail(_i7.TextEditingController? _controllerEmail) =>
      super.noSuchMethod(
        Invocation.setter(#controllerEmail, _controllerEmail),
        returnValueForMissingStub: null,
      );

  @override
  _i8.LoginTraditionalUiState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeLoginTraditionalUiState_7(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeLoginTraditionalUiState_7(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i8.LoginTraditionalUiState);

  @override
  _i9.TenantUseCase get tenantUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#tenantUseCase),
            returnValue: _FakeTenantUseCase_8(
              this,
              Invocation.getter(#tenantUseCase),
            ),
            returnValueForMissingStub: _FakeTenantUseCase_8(
              this,
              Invocation.getter(#tenantUseCase),
            ),
          )
          as _i9.TenantUseCase);

  @override
  _i10.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_9(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_9(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i10.DialogService);

  @override
  _i11.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_10(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_10(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i11.NavigationService);

  @override
  _i12.RxString get ticket =>
      (super.noSuchMethod(
            Invocation.getter(#ticket),
            returnValue: _FakeRxString_11(this, Invocation.getter(#ticket)),
            returnValueForMissingStub: _FakeRxString_11(
              this,
              Invocation.getter(#ticket),
            ),
          )
          as _i12.RxString);

  @override
  set ticket(_i12.RxString? _ticket) => super.noSuchMethod(
    Invocation.setter(#ticket, _ticket),
    returnValueForMissingStub: null,
  );

  @override
  _i12.RxBool get isBiometrics =>
      (super.noSuchMethod(
            Invocation.getter(#isBiometrics),
            returnValue: _FakeRxBool_12(this, Invocation.getter(#isBiometrics)),
            returnValueForMissingStub: _FakeRxBool_12(
              this,
              Invocation.getter(#isBiometrics),
            ),
          )
          as _i12.RxBool);

  @override
  set isBiometrics(_i12.RxBool? _isBiometrics) => super.noSuchMethod(
    Invocation.setter(#isBiometrics, _isBiometrics),
    returnValueForMissingStub: null,
  );

  @override
  _i12.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_13<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_13<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i12.InternalFinalCallback<void>);

  @override
  _i12.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_13<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_13<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i12.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<void> loginSso() =>
      (super.noSuchMethod(
            Invocation.method(#loginSso, []),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  _i14.Future<void> loginTraditional() =>
      (super.noSuchMethod(
            Invocation.method(#loginTraditional, []),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  void hideKeyboard() => super.noSuchMethod(
    Invocation.method(#hideKeyboard, []),
    returnValueForMissingStub: null,
  );

  @override
  bool validationUserInput({bool? isPassWordTextField}) =>
      (super.noSuchMethod(
            Invocation.method(#validationUserInput, [], {
              #isPassWordTextField: isPassWordTextField,
            }),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<void> onTenantOption({
    required _i15.SharedTenantModel? tenant,
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#onTenantOption, [], {
              #tenant: tenant,
              #userName: userName,
              #password: password,
            }),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  _i14.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i16.ErrorHandlingMode? mode = _i16.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  _i14.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i17.Disposer addListener(_i17.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i17.Disposer);

  @override
  void removeListener(_i18.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i18.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i17.Disposer addListenerId(Object? key, _i17.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i17.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
