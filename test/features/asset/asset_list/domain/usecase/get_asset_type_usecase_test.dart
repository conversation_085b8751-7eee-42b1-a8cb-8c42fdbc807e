import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_asset_type_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../../../mocks/storage_utils_mock.dart';
import 'get_asset_type_usecase_test.mocks.dart';

@GenerateMocks([AssetTypeRepository])
void main() {
  late GetAssetTypeUseCase useCase;
  late MockAssetTypeRepository mockRepository;
  late MockStorageUtils mockStorage;

  setUp(() {
    mockRepository = MockAssetTypeRepository();
    mockStorage = MockStorageUtils();
    useCase = GetAssetTypeUseCase(mockRepository);

    // 设置模拟实现
    StorageUtils.setMockImplementation(mockStorage);
  });

  tearDown(() {
    // 重置为真实实现
    StorageUtils.resetImplementation();
  });

  group('GetAssetTypeUseCase Tests', () {
    test('应当在选择了有效的资产类型ID时返回对应的资产类型', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: '类型 1'),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
        AssetTypeListModel(assetTypeId: 3, assetTypeName: '类型 3'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试
      final result = await useCase(GetAssetTypeParams(assetTypeId: 2));

      // 验证结果
      expect(result.assetTypeId, 2);
      expect(result.assetTypeName, '类型 2');
      verify(mockRepository.getAllAssetTypes()).called(1);

      // 验证存储调用
      expect(mockStorage.setCalls, contains(StorageUtils.keyRecordAssetTypeId));
    });

    test('在选择资产类型为0且有已存储的类型时，应使用存储的类型', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: '类型 1'),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
        AssetTypeListModel(assetTypeId: 3, assetTypeName: '类型 3'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 设置存储的值
      mockStorage.setupStorage({StorageUtils.keyRecordAssetTypeId: 3});

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试
      final result = await useCase(GetAssetTypeParams(assetTypeId: 0));

      // 验证结果
      expect(result.assetTypeId, 3);
      expect(result.assetTypeName, '类型 3');
      verify(mockRepository.getAllAssetTypes()).called(1);

      // 验证存储未被调用
      expect(mockStorage.setCalls, isEmpty);
    });

    test('在选择资产类型为0且没有存储类型时，应使用默认类型', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: '类型 1'),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
        AssetTypeListModel(assetTypeId: 3, assetTypeName: '类型 3'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 清空存储
      mockStorage.reset();

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试
      final result = await useCase(GetAssetTypeParams(assetTypeId: 0));

      // 验证结果
      expect(result.assetTypeId, 1); // 应使用第一个作为默认
      expect(result.assetTypeName, '类型 1');
      verify(mockRepository.getAllAssetTypes()).called(1);

      // 验证存储未被调用（因为用户没有明确选择）
      expect(mockStorage.setCalls, isEmpty);
    });

    test('当资产类型列表为空时应抛出业务异常', () async {
      // 准备测试数据
      final assetTypeResponse = AssetTypeResponse(assetTypeList: [], code: 1, msg: '');

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试并验证异常
      expect(
        () => useCase(GetAssetTypeParams(assetTypeId: 1)),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', '利用可能な資産タイプが存在しません。')),
      );
      verify(mockRepository.getAllAssetTypes()).called(1);
    });

    test('当资产类型列表为null时应抛出业务异常', () async {
      // 准备测试数据
      final assetTypeResponse = AssetTypeResponse(assetTypeList: null, code: 1, msg: '');

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试并验证异常
      expect(
        () => useCase(GetAssetTypeParams(assetTypeId: 1)),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', '利用可能な資産タイプが存在しません。')),
      );
      verify(mockRepository.getAllAssetTypes()).called(1);
    });

    test('当默认资产类型无效时应抛出业务异常', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: null, assetTypeName: '无效类型'),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试并验证异常
      expect(
        () => useCase(GetAssetTypeParams(assetTypeId: 0)),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', 'デフォルトの資産タイプが見つかりません。')),
      );
      verify(mockRepository.getAllAssetTypes()).called(1);
    });

    test('当存储的资产类型不再存在时，应使用默认资产类型', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: '类型 1'),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 设置一个不存在于列表中的存储值
      mockStorage.setupStorage({StorageUtils.keyRecordAssetTypeId: 5});

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试
      final result = await useCase(GetAssetTypeParams(assetTypeId: 0));

      // 验证结果
      expect(result.assetTypeId, 1); // 应使用默认资产类型
      expect(result.assetTypeName, '类型 1');
      verify(mockRepository.getAllAssetTypes()).called(1);

      // 验证存储已更新为默认值
      expect(mockStorage.setCalls, contains(StorageUtils.keyRecordAssetTypeId));
    });

    test('当资产类型名为null时应抛出业务异常', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: null),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试并验证异常
      expect(
        () => useCase(GetAssetTypeParams(assetTypeId: 1)),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', '資産タイプ名が無効です。')),
      );
      verify(mockRepository.getAllAssetTypes()).called(1);
    });

    test('当资产类型名为空字符串时应抛出业务异常', () async {
      // 准备测试数据
      final assetTypes = [
        AssetTypeListModel(assetTypeId: 1, assetTypeName: ''),
        AssetTypeListModel(assetTypeId: 2, assetTypeName: '类型 2'),
      ];
      final assetTypeResponse = AssetTypeResponse(assetTypeList: assetTypes, code: 1, msg: '');

      // 设置模拟行为
      when(mockRepository.getAllAssetTypes()).thenAnswer((_) async => assetTypeResponse);

      // 执行测试并验证异常
      expect(
        () => useCase(GetAssetTypeParams(assetTypeId: 1)),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', '資産タイプ名が無効です。')),
      );
      verify(mockRepository.getAllAssetTypes()).called(1);
    });
  });
}
