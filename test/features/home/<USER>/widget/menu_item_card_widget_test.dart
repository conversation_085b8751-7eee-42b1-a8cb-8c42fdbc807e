import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/menu_item_card_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateNiceMocks([MockSpec<MyHomeController>()])
import 'menu_item_card_widget_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// 创建一个模拟的 SvgPicture 组件
class MockSvgWidget extends StatelessWidget {
  const MockSvgWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(width: 16, height: 16);
  }
}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();
  late MockMyHomeController mockController;

  // 测试的固定值
  const String testTitle = '更新资产';
  const String testSubtitle = '一括更新処理';
  const String testIconPath = 'assets/icons/test_icon.svg';
  const String testActionIcon = 'star';
  const String testLabel = '新規';

  // 创建测试回调函数和标志
  bool wasTapped = false;
  void onTap() {
    wasTapped = true;
  }

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest({String? actionIcon, String? label, VoidCallback? onTap}) {
    return GetMaterialApp(
      home: Scaffold(
        body: MenuItemCardWidget(
          title: testTitle,
          subtitle: testSubtitle,
          iconPath: testIconPath,
          actionIcon: actionIcon,
          label: label,
          onTap: onTap,
        ),
      ),
    );
  }

  setUp(() {
    LogUtil.initialize();
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    // 重置测试标志
    wasTapped = false;

    // 初始化模拟控制器
    mockController = MockMyHomeController();

    // 关键：使用onStart
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 添加其他可能需要的方法模拟
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});

    Get.put<MyHomeController>(mockController);

    // 处理 SvgPicture.asset
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/path_provider'),
      (MethodCall methodCall) async {
        return '.';
      },
    );
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
  });

  group('MenuItemCardWidget UI 测试', () {
    testWidgets('应该正确渲染带有正确样式的容器', (WidgetTester tester) async {
      // 替换 SvgPicture.asset 以避免加载实际资源
      // 注意：这需要使用 mockito 或类似工具对静态方法进行模拟，
      // 这里我们改为检测 Container 而不是 SvgPicture

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证
      // 找到主容器并检查其装饰
      final mainContainer = tester.widget<Container>(
        find.descendant(of: find.byType(GestureDetector), matching: find.byType(Container)).first,
      );

      final BoxDecoration decoration = mainContainer.decoration as BoxDecoration;

      // 检查边框半径
      expect(decoration.borderRadius, BorderRadius.circular(12), reason: '主容器应该有12的边框半径');

      // 检查背景颜色
      expect(decoration.color, const Color.fromRGBO(255, 255, 255, 0.85), reason: '容器应具有正确的背景颜色');

      // 检查阴影
      expect(decoration.boxShadow!.length, 1, reason: '应该有一个阴影');
      expect(decoration.boxShadow![0].color, Colors.black12, reason: '阴影颜色应为black12');
      expect(decoration.boxShadow![0].blurRadius, 6, reason: '阴影模糊半径应为6');
    });

    testWidgets('应该显示标题和副标题', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证标题和副标题
      expect(find.text(testTitle), findsOneWidget, reason: '应该显示正确的标题');
      expect(find.text(testSubtitle), findsOneWidget, reason: '应该显示正确的副标题');
    });

    testWidgets('点击卡片应该触发onTap回调', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest(onTap: onTap));
      await tester.pump(const Duration(milliseconds: 50));

      // 点击卡片
      await tester.tap(find.byType(GestureDetector));
      await tester.pump();

      // 验证onTap被调用
      expect(wasTapped, true, reason: '点击卡片应该触发onTap回调');
    });

    testWidgets('如果提供actionIcon，应该存在图标部分', (WidgetTester tester) async {
      // 由于无法直接模拟 FontIcons.getIcon，我们检查 Row 中的元素数量
      // 执行
      await tester.pumpWidget(createWidgetUnderTest(actionIcon: testActionIcon));
      await tester.pump(const Duration(milliseconds: 50));

      // 找到第一行 - 包含图标和文本
      final firstRow = find.descendant(of: find.byType(Column), matching: find.byType(Row)).first;

      // 验证第一行存在
      expect(firstRow, findsOneWidget, reason: '应该存在包含图标的行');

      // 我们可以检查是否至少有一个 Icon 小部件，而不检查具体的图标
      final iconCount = tester
          .widgetList<Icon>(find.descendant(of: find.byType(Row).first, matching: find.byType(Icon)))
          .length;

      // 在有 actionIcon 的情况下，应该至少有一个图标（不包括 arrow_forward_ios）
      expect(iconCount >= 1, true, reason: '提供actionIcon时应该有图标');
    });

    testWidgets('如果提供label，应该显示标签文本', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest(label: testLabel));
      await tester.pump(const Duration(milliseconds: 50));

      // 验证标签文本存在
      expect(find.text(testLabel), findsOneWidget, reason: '应该显示标签文本');
    });

    testWidgets('应该显示前进箭头图标', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证箭头图标存在
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget, reason: '应该显示前进箭头图标');
    });

    testWidgets('应该有正确的内边距', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 找到Padding和Container
      final padding = tester.widget<Padding>(
        find.ancestor(of: find.byType(GestureDetector), matching: find.byType(Padding)),
      );

      final container = tester.widget<Container>(
        find.descendant(of: find.byType(GestureDetector), matching: find.byType(Container)),
      );

      // 验证padding值
      expect(
        padding.padding,
        const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        reason: '外部Padding应为水平16，垂直8',
      );
      expect(container.padding, const EdgeInsets.all(16), reason: '容器内边距应为全方向16');
    });

    testWidgets('Row应该有正确的子组件数量', (WidgetTester tester) async {
      // 执行 - 同时提供 actionIcon 和 label
      await tester.pumpWidget(createWidgetUnderTest(actionIcon: testActionIcon, label: testLabel));
      await tester.pump(const Duration(milliseconds: 50));

      // 验证标签文本存在
      expect(find.text(testLabel), findsOneWidget, reason: '应该显示标签文本');

      // 查找第一行 Row
      final firstRow = tester.widget<Row>(find.descendant(of: find.byType(Column), matching: find.byType(Row)).first);

      // 当同时提供 actionIcon 和 label 时，Row 中应该有更多子组件
      expect(firstRow.children.length > 3, true, reason: '当同时提供 actionIcon 和 label 时，Row 应该有更多子组件');
    });
  });
}
