# asset-force-mobile プロジェクト 貢献ガイド

**asset-force-mobile** プロジェクトの開発にご参加いただきありがとうございます。本プロジェクトは Git Flow 分岐モデルを採用しており、社内開発プロセス、コード規約、テスト要件、コミットフローなど一式の内部開発手順が整備されています。本ガイドは、社内の開発者がプロジェクトの開発、分岐管理、コードの提出およびテスト規約を迅速に把握できるよう支援することを目的としています。

---

## 目次

- [行動規範](#行動規範)
- [Git Flow 分岐モデル](#git-flow-分岐モデル)
- [貢献方法](#貢献方法)
    - [バグ報告](#バグ報告)
    - [新機能の提案](#新機能の提案)
    - [コードの提出](#コードの提出)
- [開発環境のセットアップ](#開発環境のセットアップ)
- [コードスタイルとコミット規約](#コードスタイルとコミット規約)
- [テスト要件](#テスト要件)
- [ドキュメントとサンプル](#ドキュメントとサンプル)
- [その他](#その他)
- [お問い合わせ](#お問い合わせ)

---

## 行動規範

- **協力と尊重**：チームメンバーとは礼儀正しく友好的な態度でコミュニケーションを行い、互いに助け合いながら向上を目指してください。
- **高品質の要求**：提出するコードは十分にテストされ、チームのコード規約に従っていることが必要です。安定性と保守性を確保してください。
- **オープンな議論**：疑問点やアーキテクチャ変更などが発生した場合は、まず内部の Issue やディスカッショングループで議論し、意見を求めた上で修正を行ってください。
- **機密保持**：本プロジェクトは社内プロジェクトです。内部の議論内容やコードを外部に漏洩しないようにしてください。

---

## Git Flow 分岐モデル

本プロジェクトは `Git Flow` 分岐管理モデルを採用しています。主なブランチは以下の通りです：

- **`main` ブランチ**
    - 常にリリース可能な安定したコードを保持し、十分にテスト・レビューされたコードのみがマージされます。

- **`develop` ブランチ**
    - 日常の開発のメインブランチです。すべての新機能や変更はまず `develop` ブランチに統合され、テストが通過した後に `main` ブランチへマージされリリースされます。

- **`feature` ブランチ**
    - 新機能や改善の開発に使用します。ブランチ名の形式は `feature/{developer_name}/ASCHK-{JIRA番号}` としてください。
    - `develop` ブランチから作成し、開発完了後は Pull Request を通じて `develop` ブランチにマージします。

- **`bugfix` ブランチ**
    - `develop` ブランチ上のバグ修正に使用します。ブランチ名の形式は `bugfix/{developer_name}/ASCHK-{JIRA番号}` としてください。
    - 修正完了後は `develop` ブランチにマージしてください。

- **`release` ブランチ**
    - リリース版の準備のために使用します。`develop` ブランチから作成し、最終調整や微修正を行い、リリース後は `main` ブランチにマージし、さらに `develop` ブランチと同期させます。

- **`hotfix` ブランチ**
    - `main` ブランチでリリース済みのコードに重大なバグが発生した場合、緊急修正として使用します。ブランチ名の形式は `hotfix/{developer_name}/ASCHK-{JIRA番号}` としてください。
    - 修正後は、それぞれ `main` ブランチと `develop` ブランチにマージし、バージョンの整合性を保ちます。

Pull Request を提出する際は、正しいブランチから新しいブランチを作成し、上記の命名規則に従ってください。

---

## 貢献方法

### バグ報告

1. **既存の問題を確認**  
   新たなバグを報告する前に、チーム内で既に同じ問題が報告されていないか確認してください。
2. **バグ報告の提出**
    - バグ報告には、問題の現象、再現手順、対象環境（Flutter のバージョン、プラットフォーム、依存ライブラリのバージョンなど）を詳細に記載してください。
    - エラーログやスクリーンショットがある場合は、併せて添付してください。

### 新機能の提案

1. **要件の検討**  
   新機能の提案を行う前に、内部ディスカッショングループで事前に議論し、要件の妥当性を確認してください。
2. **提案の提出**
    - 提案内容、背景、および期待される効果を詳細に記述してください。
    - 参考資料や実装アイデアがあれば、併せて記載してください。

### コードの提出

1. **Fork またはブランチの作成**
    - `develop` ブランチから新しい feature または bugfix ブランチを作成してください。ブランチ名は `feature/{developer_name}/ASCHK-{JIRA番号}` などの規則に従ってください。
2. **開発とテスト**
    - コードを書く際は、プロジェクトのディレクトリ構造やコード規約に従って実装してください。
    - 開発が完了したら、`flutter analyze`、`flutter test`、Widget テスト、統合テストを実行し、すべてのテストが通過していることを確認してください。
3. **Pull Request の提出**
    - コードを提出する際は、規定のコミットメッセージ形式に従ってください（下記「コードスタイルとコミット規約」を参照）。
    - PR を作成する際、関連する JIRA チケットと紐付け、変更内容およびテスト状況を詳細に記述し、レビューを待ちます。

---

## 開発環境のセットアップ

1. **Flutter SDK のインストール**  
   [Flutter 公式ドキュメント](https://flutter.dev/docs/get-started/install) を参照して Flutter をインストールしてください。
2. **環境変数の設定**  
   Flutter SDK の `bin` ディレクトリをシステム PATH に追加してください。
3. **必要なツールのインストール**  
   プロジェクトの要件に応じ、Android Studio（Android 開発用）、Xcode（iOS 開発用）などをインストールしてください。
4. **プロジェクトのクローン**
   ```bash
   git clone git@your-internal-git:asset-force-mobile.git
   cd asset-force-mobile
   flutter pub get
   ```
5. **プロジェクトの実行**  
   `flutter run` もしくは IDE を使用してプロジェクトを実行し、環境が正しく構築されていることを確認してください。

---

## コードスタイルとコミット規約

### コードスタイル

- Confluence のドキュメント（[リンク](https://smfl.atlassian.net/wiki/spaces/PROJASF/pages/40821632/assetforce+mobile+flutter)）を参照してください。
- [Effective Dart](https://dart.dev/guides/language/effective-dart) のガイドラインに従い、コードを読みやすく保守しやすく記述してください。
- `dart format .` を実行してコードフォーマットを統一してください。

### コミット規約

- コミットメッセージは [Conventional Commits](https://www.conventionalcommits.org/zh-hans/v1.0.0/) の形式を採用してください。例：
    - `feat: ログイン機能の追加`
    - `fix: ユーザー登録時のクラッシュ修正`
    - `chore: 依存ライブラリのアップデート`
- PR 提出時には、PR テンプレートに従い、変更内容およびテスト状況を詳細に記載してください。

---

## テスト要件

### 単体テスト

- Dart 単体テストを作成し、コアなビジネスロジックを網羅してください。`flutter test` を実行し、すべてのテストが通過することを確認してください。

### Widget テスト

- 主要な UI コンポーネントに対して Widget テストを作成し、ユーザーインターフェースの挙動やレイアウトが正しいことを確認してください。

### 統合テスト

- [integration_test](https://pub.dev/packages/integration_test) パッケージを使用して、エンドツーエンドのテストを実施し、全体のフローが正常に動作するかを確認してください。

### 静的解析

- `flutter analyze` を実行し、コードが規約に従っており、エラーや警告がないことを確認してください。

各コミット前に、上記のすべてのテストが通過していることを確認してください。

---

## ドキュメントとサンプル

- **ドキュメントの充実**  
  コード内に十分なコメントを記述し、README、Wiki、開発ドキュメントを最新の状態に保ってください。詳細は Confluence のドキュメント（[リンク](https://smfl.atlassian.net/wiki/spaces/PROJASF/pages/40820044/af+mobile)）を参照してください。

- **サンプルコード**  
  新機能や改善されたコンポーネントの使用例を示すサンプルコードまたはデモを提供し、他の開発者が容易に理解・利用できるようにしてください。

---

## その他

- **フィードバックとディスカッション**  
  社内の JIRA システムやディスカッショングループを通じて、問題や改善提案を積極的に共有してください。
- **著作権および機密保持**  
  本プロジェクトは社内プロジェクトです。すべてのコードおよびドキュメントは社内の機密保持契約に従って取り扱われ、提出されたコードは内部ライセンスに基づいて管理されます。
- **大規模な変更について**  
  大規模な改修やアーキテクチャ変更を行う場合は、事前にチーム内で十分に議論し、合意を得た上で PR を提出してください。

---

以上、**asset-force-mobile** プロジェクトへのご貢献に感謝いたします！

*最終更新日：2023-12-31*