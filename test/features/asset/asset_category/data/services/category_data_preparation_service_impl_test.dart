import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/services/category_data_preparation_service_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_data_preparation_service.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_assemble_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/models/asset_search_category_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late CategoryDataPreparationService categoryValidationService;
  late GetCategoryAssembleListUseCase getCategoryAssembleListUseCase;
  setUp(() {
    LogUtil.initialize();
    getCategoryAssembleListUseCase = GetCategoryAssembleListUseCase();
    categoryValidationService = CategoryDataPreparationServiceImpl(
      getCategoryAssembleListUseCase: getCategoryAssembleListUseCase,
    );
  });

  group('prepareCategoryList', () {
    test('prepareCategoryList case 1', () {
      categoryValidationService.prepareCategoryList(
        GetCategoryAllListParams(
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
            ),
          ],
          categoryList: [],
        ),
      );
    });

    test('prepareCategoryList case 2 go forward', () {
      categoryValidationService.prepareCategoryList(
        GetCategoryAllListParams(
          info: Info(titleValue: '-10000.00001'),
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
            ),
          ],
          categoryList: [
            CategoryData(
              titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              categoryInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
              categorySelectedData: CategorySelectedData(
                categoryItemId: 12493079,
                itemType: 'number',
                index: 0,
                itFinished: false,
              ),
            ),
          ],
        ),
      );
    });

    test('prepareCategoryList case 3 goBackLevelInfo', () {
      categoryValidationService.prepareCategoryList(
        GetCategoryAllListParams(
          info: Info(
            goBackLevelInfo: const CategoryModel(
              itemId: 12493079,
              itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              itemName: '数字',
              itemType: 'number',
            ),
          ),
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
            ),
          ],
          categoryList: [
            CategoryData(
              titleVal: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              categoryInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: 'itemName',
                itemType: 'itemType',
              ),
              categorySelectedData: CategorySelectedData(
                categoryItemId: 12493079,
                itemType: 'number',
                index: 0,
                itFinished: false,
              ),
            ),
            CategoryData(
              titleVal: '通貨_小数点桁数12',
              categoryInfo: const CategoryModel(
                itemId: 12493080,
                itemDisplayName: '通貨_小数点桁数12',
                itemName: '通貨',
                itemType: 'currency',
              ),
              categorySelectedData: CategorySelectedData(
                categoryItemId: 12493080,
                itemType: 'currency',
                index: 1,
                itFinished: false,
              ),
            ),
          ],
        ),
      );
    });

    test('prepareCategoryList case 4 assetSearchConditionList', () {
      categoryValidationService.prepareCategoryList(
        GetCategoryAllListParams(
          assetSearchConditionList: [
            AssetSearchCategoryUIModel(
              searchData: '',
              itemId: 12493079,
              itemName: '数字',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
            AssetSearchCategoryUIModel(
              searchData: '',
              itemId: 12493080,
              itemName: '通貨',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
            AssetSearchCategoryUIModel(
              searchData: '',
              itemId: 12493081,
              itemName: '計算',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
            AssetSearchCategoryUIModel(
              searchData: '',
              subItemId: 962060,
              subItemName: '数字666',
              itemId: 12493082,
              itemName: 'FXH_Master_All',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
          ],
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: '数字',
                itemType: 'number',
              ),
            ),
            Info(
              titleValue: '通貨_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493080,
                itemDisplayName: '通貨_小数点桁数12',
                itemName: '通貨',
                itemType: 'currency',
              ),
            ),
            Info(
              titleValue: '計算_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493081,
                itemDisplayName: '計算_小数点桁数12',
                itemName: '計算',
                itemType: 'calculate',
              ),
            ),
            Info(
              titleValue: 'FXH_Master_All(数字_小数点桁数12)',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493082,
                itemSubId: 962060,
                itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
                itemName: 'FXH_Master_All',
                itemSubName: '数字666',
                itemSubType: 'number',
                itemType: 'master',
              ),
            ),
          ],
          categoryList: [],
        ),
      );
    });

    test('prepareCategoryList case 5 assetSearchConditionList2', () {
      categoryValidationService.prepareCategoryList(
        GetCategoryAllListParams(
          assetSearchConditionList: [
            AssetSearchCategoryUIModel(
              searchData: '123',
              itemId: 12493079,
              itemName: '数字',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
            AssetSearchCategoryUIModel(
              searchData: '123',
              subItemId: 962060,
              subItemName: '数字666',
              itemId: 12493082,
              itemName: 'FXH_Master_All',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
            AssetSearchCategoryUIModel(
              searchData: '123',
              itemId: 12493081,
              itemName: '計算',
              searchLogic: 'AND',
              method: 'equBlank',
            ),
          ],
          assetTypeId: 111111855,
          searchId: '2',
          keyword: '',
          categoryOriginalList: [
            Info(
              titleValue: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493079,
                itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
                itemName: '数字',
                itemType: 'number',
              ),
            ),
            Info(
              titleValue: '通貨_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493080,
                itemDisplayName: '通貨_小数点桁数12',
                itemName: '通貨',
                itemType: 'currency',
              ),
            ),
            Info(
              titleValue: 'FXH_Master_All(数字_小数点桁数12)',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493082,
                itemSubId: 962060,
                itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
                itemName: 'FXH_Master_All',
                itemSubName: '数字666',
                itemSubType: 'number',
                itemType: 'master',
              ),
            ),
            Info(
              titleValue: '計算_小数点桁数12',
              goBackLevelInfo: const CategoryModel(
                itemId: 12493081,
                itemDisplayName: '計算_小数点桁数12',
                itemName: '計算',
                itemType: 'calculate',
              ),
            ),
          ],
          categoryList: [],
        ),
      );
    });
  });
  group('isClickCurrentLevel', () {
    test('isClickCurrentLevel case 1 afterInfo is not AssetSearchCategoryUIModel', () {
      final result = categoryValidationService.isClickCurrentLevel(
        const CategoryModel(
          itemId: 12493082,
          itemSubId: 962060,
          itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
          itemName: 'FXH_Master_All',
          itemSubName: '数字666',
          itemSubType: 'number',
          itemType: 'master',
        ),
        const CategoryModel(
          itemId: 12493082,
          itemSubId: 962060,
          itemDisplayName: 'FXH_Master_All(数字_小数点桁数12)',
          itemName: 'FXH_Master_All',
          itemSubName: '数字666',
          itemSubType: 'number',
          itemType: 'master',
        ),
      );
      expect(result, true);
    });
  });
}
