targets:
  $default:
    builders:
      get_auto_router|route_scanner:
        enabled: true
        generate_for:
          - lib/**/*.dart
      get_auto_router|route_table:
        enabled: true
        generate_for:
          - lib/**/*.dart

builders:
  route_scanner:
    import: "package:get_auto_router/builder.dart"
    builder_factories: ["annotationGenerator"]
    build_extensions: {".dart": [".t.json"]}
    auto_apply: dependents
    build_to: cache
    runs_before: ["get_auto_router|route_table"]
    applies_builders: ["source_gen|combining_builder"]

  route_table:
    import: "package:get_auto_router/builder.dart"
    builder_factories: ["routeTableGenerator"]
    build_extensions: {"$lib$": ["core/routes/auto_route.dart"]}
    auto_apply: dependents
    build_to: source
    required_inputs: [".t.json"]