// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/scan_setting/presentation/controllers/scan_setting_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/core/platform/method_channel.dart' as _i6;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i3;
import 'package:flutter/services.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeMethodCodec_0 extends _i1.SmartFake implements _i2.MethodCodec {
  _FakeMethodCodec_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBinaryMessenger_1 extends _i1.SmartFake
    implements _i2.BinaryMessenger {
  _FakeBinaryMessenger_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i3.IStorageUtils {
  MockIStorageUtils() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> initPersonalStorage({required String? cryptedName}) =>
      (super.noSuchMethod(
            Invocation.method(#initPersonalStorage, [], {
              #cryptedName: cryptedName,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  T? getValue<T>(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getValue, [key], {#personal: personal}),
          )
          as T?);

  @override
  _i4.Future<void> setValue<T>(
    String? key,
    T? value, {
    bool? personal = false,
    int? durationInSeconds,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setValue,
              [key, value],
              {#personal: personal, #durationInSeconds: durationInSeconds},
            ),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> removeValue(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#removeValue, [key], {#personal: personal}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  bool containsKey(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key], {#personal: personal}),
            returnValue: false,
          )
          as bool);

  @override
  _i4.Future<void> clearAll({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, [], {#personal: personal}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  List<String> getAllKeys({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, [], {#personal: personal}),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i4.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [PlatformChannel].
///
/// See the documentation for Mockito's code generation for more information.
class MockPlatformChannel extends _i1.Mock implements _i6.PlatformChannel {
  MockPlatformChannel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get name =>
      (super.noSuchMethod(
            Invocation.getter(#name),
            returnValue: _i5.dummyValue<String>(this, Invocation.getter(#name)),
          )
          as String);

  @override
  _i2.MethodCodec get codec =>
      (super.noSuchMethod(
            Invocation.getter(#codec),
            returnValue: _FakeMethodCodec_0(this, Invocation.getter(#codec)),
          )
          as _i2.MethodCodec);

  @override
  _i2.BinaryMessenger get binaryMessenger =>
      (super.noSuchMethod(
            Invocation.getter(#binaryMessenger),
            returnValue: _FakeBinaryMessenger_1(
              this,
              Invocation.getter(#binaryMessenger),
            ),
          )
          as _i2.BinaryMessenger);

  @override
  _i4.Future<void> syncStorage(Map<String, Object>? data) =>
      (super.noSuchMethod(
            Invocation.method(#syncStorage, [data]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendEventToIonic(Map<String, Object>? data) =>
      (super.noSuchMethod(
            Invocation.method(#sendEventToIonic, [data]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<T?> invokeMethod<T>(String? method, [dynamic arguments]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeMethod, [method, arguments]),
            returnValue: _i4.Future<T?>.value(),
          )
          as _i4.Future<T?>);

  @override
  _i4.Future<List<T>?> invokeListMethod<T>(
    String? method, [
    dynamic arguments,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeListMethod, [method, arguments]),
            returnValue: _i4.Future<List<T>?>.value(),
          )
          as _i4.Future<List<T>?>);

  @override
  _i4.Future<Map<K, V>?> invokeMapMethod<K, V>(
    String? method, [
    dynamic arguments,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeMapMethod, [method, arguments]),
            returnValue: _i4.Future<Map<K, V>?>.value(),
          )
          as _i4.Future<Map<K, V>?>);

  @override
  void setMethodCallHandler(
    _i4.Future<dynamic> Function(_i2.MethodCall)? handler,
  ) => super.noSuchMethod(
    Invocation.method(#setMethodCallHandler, [handler]),
    returnValueForMissingStub: null,
  );
}
