// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_reset_password/presentation/controllers/login_reset_password_controllers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i7;

import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i5;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/models/reset_password_result_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/repositories/reset_password_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/usecases/reset_password_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i8;
import 'package:flutter/cupertino.dart' as _i9;
import 'package:local_auth/local_auth.dart' as _i13;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i14;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResetPasswordRepository_0 extends _i1.SmartFake
    implements _i2.ResetPasswordRepository {
  _FakeResetPasswordRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResetPasswordResultModel_1 extends _i1.SmartFake
    implements _i3.ResetPasswordResultModel {
  _FakeResetPasswordResultModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePasswordVerificationRulesModel_2 extends _i1.SmartFake
    implements _i4.PasswordVerificationRulesModel {
  _FakePasswordVerificationRulesModel_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakePasswordVerificationResult_3 extends _i1.SmartFake
    implements _i4.PasswordVerificationResult {
  _FakePasswordVerificationResult_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeConfirmPasswordVerificationResult_4 extends _i1.SmartFake
    implements _i4.ConfirmPasswordVerificationResult {
  _FakeConfirmPasswordVerificationResult_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i5.DialogService {
  @override
  _i6.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i7.VoidCallback? onConfirm,
    _i7.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i8.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i7.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i9.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i8.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i10.NavigationService {
  @override
  _i6.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i6.Future<dynamic>.value(),
            returnValueForMissingStub: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i6.Future<dynamic>.value(),
            returnValueForMissingStub: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);

  @override
  _i6.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i6.Future<bool>.value(false),
            returnValueForMissingStub: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<dynamic> toAssetDetail(_i11.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i6.Future<dynamic>.value(),
            returnValueForMissingStub: _i6.Future<dynamic>.value(),
          )
          as _i6.Future<dynamic>);
}

/// A class which mocks [BiometricsSwitchUtil].
///
/// See the documentation for Mockito's code generation for more information.
class MockBiometricsSwitchUtil extends _i1.Mock
    implements _i12.BiometricsSwitchUtil {
  @override
  _i6.Future<String?> isAccountWorkingProperly({required String? password}) =>
      (super.noSuchMethod(
            Invocation.method(#isAccountWorkingProperly, [], {
              #password: password,
            }),
            returnValue: _i6.Future<String?>.value(),
            returnValueForMissingStub: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<void> closeOrOpenBiometrics({required bool? isOpen}) =>
      (super.noSuchMethod(
            Invocation.method(#closeOrOpenBiometrics, [], {#isOpen: isOpen}),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  bool getUserBiometricsOpenSwitch() =>
      (super.noSuchMethod(
            Invocation.method(#getUserBiometricsOpenSwitch, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i6.Future<bool> isBiometricsAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isBiometricsAvailable, []),
            returnValue: _i6.Future<bool>.value(false),
            returnValueForMissingStub: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<List<_i13.BiometricType>> getAvailableBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableBiometrics, []),
            returnValue: _i6.Future<List<_i13.BiometricType>>.value(
              <_i13.BiometricType>[],
            ),
            returnValueForMissingStub:
                _i6.Future<List<_i13.BiometricType>>.value(
                  <_i13.BiometricType>[],
                ),
          )
          as _i6.Future<List<_i13.BiometricType>>);

  @override
  _i6.Future<void> authenticateBiometricsSetting() =>
      (super.noSuchMethod(
            Invocation.method(#authenticateBiometricsSetting, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setBiometricSecretPassword({
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setBiometricSecretPassword, [], {
              #userName: userName,
              #password: password,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String> getBiometricSecretPassword() =>
      (super.noSuchMethod(
            Invocation.method(#getBiometricSecretPassword, []),
            returnValue: _i6.Future<String>.value(
              _i14.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
            returnValueForMissingStub: _i6.Future<String>.value(
              _i14.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
          )
          as _i6.Future<String>);
}

/// A class which mocks [ResetPasswordUsecase].
///
/// See the documentation for Mockito's code generation for more information.
class MockResetPasswordUsecase extends _i1.Mock
    implements _i4.ResetPasswordUsecase {
  @override
  _i2.ResetPasswordRepository get resetPasswordRepository =>
      (super.noSuchMethod(
            Invocation.getter(#resetPasswordRepository),
            returnValue: _FakeResetPasswordRepository_0(
              this,
              Invocation.getter(#resetPasswordRepository),
            ),
            returnValueForMissingStub: _FakeResetPasswordRepository_0(
              this,
              Invocation.getter(#resetPasswordRepository),
            ),
          )
          as _i2.ResetPasswordRepository);

  @override
  _i6.Future<_i3.ResetPasswordResultModel> call(
    _i4.ResetPasswordArgumentsModel? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i6.Future<_i3.ResetPasswordResultModel>.value(
              _FakeResetPasswordResultModel_1(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
            returnValueForMissingStub:
                _i6.Future<_i3.ResetPasswordResultModel>.value(
                  _FakeResetPasswordResultModel_1(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.ResetPasswordResultModel>);

  @override
  _i4.PasswordVerificationRulesModel initPasswordRules({
    required String? mustContain,
    required int? passwordLength,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initPasswordRules, [], {
              #mustContain: mustContain,
              #passwordLength: passwordLength,
            }),
            returnValue: _FakePasswordVerificationRulesModel_2(
              this,
              Invocation.method(#initPasswordRules, [], {
                #mustContain: mustContain,
                #passwordLength: passwordLength,
              }),
            ),
            returnValueForMissingStub: _FakePasswordVerificationRulesModel_2(
              this,
              Invocation.method(#initPasswordRules, [], {
                #mustContain: mustContain,
                #passwordLength: passwordLength,
              }),
            ),
          )
          as _i4.PasswordVerificationRulesModel);

  @override
  _i4.PasswordVerificationResult passwordLegitimacyCheck({
    required _i4.PasswordVerificationRulesModel? pvr,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#passwordLegitimacyCheck, [], {
              #pvr: pvr,
              #password: password,
            }),
            returnValue: _FakePasswordVerificationResult_3(
              this,
              Invocation.method(#passwordLegitimacyCheck, [], {
                #pvr: pvr,
                #password: password,
              }),
            ),
            returnValueForMissingStub: _FakePasswordVerificationResult_3(
              this,
              Invocation.method(#passwordLegitimacyCheck, [], {
                #pvr: pvr,
                #password: password,
              }),
            ),
          )
          as _i4.PasswordVerificationResult);

  @override
  _i4.ConfirmPasswordVerificationResult passwordConsistenceCheck({
    required String? confirmPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#passwordConsistenceCheck, [], {
              #confirmPassword: confirmPassword,
              #newPassword: newPassword,
            }),
            returnValue: _FakeConfirmPasswordVerificationResult_4(
              this,
              Invocation.method(#passwordConsistenceCheck, [], {
                #confirmPassword: confirmPassword,
                #newPassword: newPassword,
              }),
            ),
            returnValueForMissingStub: _FakeConfirmPasswordVerificationResult_4(
              this,
              Invocation.method(#passwordConsistenceCheck, [], {
                #confirmPassword: confirmPassword,
                #newPassword: newPassword,
              }),
            ),
          )
          as _i4.ConfirmPasswordVerificationResult);
}
