import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/controllers/scan_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/models/scan_setting_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/widgets/sound_settings_widget.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/widgets/vibration_setting_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'sound_settings_widget_test.mocks.dart';

// 生成 Mock 类
@GenerateNiceMocks([MockSpec<ScanSettingsController>()])
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  group('🧪 SoundSettingsWidget 单元测试', () {
    final mockInternalFinalCallback = MockInternalFinalCallback<void>();
    late MockScanSettingsController mockController;
    late ScanSettingUIModel testSettingsModel;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    // 设置 Mock Controller 的辅助方法
    void setupMockController() {
      // 创建测试设置数据
      testSettingsModel = ScanSettingUIModel(
        useSound: true,
        useVibration: true,
        soundValue: 75.0,
        vibrationValue: 50.0,
        scanMusicList: 1,
      );

      // Mock 响应式变量
      when(mockController.model).thenReturn(testSettingsModel.obs);

      // 关键：Mock GetX 生命周期方法
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onInit()).thenAnswer((_) async {});
      when(mockController.onClose()).thenAnswer((_) async {});
      when(mockController.onReady()).thenAnswer((_) async {});

      // Mock 业务方法
      when(mockController.soundEventChange(any)).thenAnswer((_) async {});
      when(mockController.vibrationEventChange(any)).thenAnswer((_) async {});
      when(mockController.musicChange(any)).thenAnswer((_) async {});
      when(mockController.soundValueChange(any)).thenAnswer((_) async {});
      when(mockController.vibrationValueChange(any)).thenAnswer((_) async {});
    }

    // 每个测试前的清理和重置
    setUp(() {
      // 重置 GetX 状态
      Get.reset();

      // 创建新的 Mock Controller
      mockController = MockScanSettingsController();

      // 设置 Mock Controller
      setupMockController();

      // 注入 Mock Controller
      Get.put<ScanSettingsController>(mockController);
    });

    // 每个测试后的清理
    tearDown(() {
      Get.reset();
    });

    // 创建测试 Widget 环境的辅助方法
    Widget createWidgetUnderTest() {
      return GetMaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(body: const SoundSettingsWidget()),
      );
    }

    // 创建不同测试场景的数据模型
    ScanSettingUIModel createTestModel({
      bool useSound = true,
      bool useVibration = true,
      double soundValue = 75.0,
      double vibrationValue = 50.0,
      int scanMusicList = 1,
    }) {
      return ScanSettingUIModel(
        useSound: useSound,
        useVibration: useVibration,
        soundValue: soundValue,
        vibrationValue: vibrationValue,
        scanMusicList: scanMusicList,
      );
    }

    // 更新 Mock Controller 数据的辅助方法
    void updateMockControllerData(ScanSettingUIModel newModel) {
      when(mockController.model).thenReturn(newModel.obs);
    }

    // ==========================================
    // 🚀 Phase 0: 测试基础设施建设
    // ==========================================

    group('📋 Phase 0: 测试基础设施建设', () {
      group('0.1 Mock Controller 设置验证', () {
        testWidgets('应该成功创建 MockScanSettingsController', (tester) async {
          // Assert
          expect(mockController, isNotNull, reason: 'Mock Controller 应该成功创建');
          expect(mockController, isA<ScanSettingsController>(), reason: 'Mock 应该是正确的类型');
        });

        testWidgets('应该正确设置 Mock Controller 的响应式数据', (tester) async {
          // Assert
          expect(mockController.model, isNotNull, reason: 'model 应该不为空');
          expect(mockController.model.value, isA<ScanSettingUIModel>(), reason: 'model.value 应该是正确类型');

          // 验证默认测试数据
          expect(mockController.model.value.useSound, true, reason: '默认 useSound 应该为 true');
          expect(mockController.model.value.soundValue, 75.0, reason: '默认 soundValue 应该为 75.0');
          expect(mockController.model.value.scanMusicList, 1, reason: '默认 scanMusicList 应该为 1');
        });

        testWidgets('应该正确 Mock Controller 的业务方法', (tester) async {
          // Act
          await mockController.musicChange(2);
          await mockController.soundValueChange(80.0);

          // Assert
          verify(mockController.musicChange(2)).called(1);
          verify(mockController.soundValueChange(80.0)).called(1);
        });

        testWidgets('应该正确处理 GetX 生命周期方法', (tester) async {
          // Act & Assert
          expect(() => mockController.onInit(), returnsNormally);
          expect(() => mockController.onClose(), returnsNormally);
          expect(() => mockController.onReady(), returnsNormally);
        });
      });

      group('0.2 测试数据模型验证', () {
        testWidgets('应该能够创建默认测试模型', (tester) async {
          // Act
          final model = createTestModel();

          // Assert
          expect(model.useSound, true);
          expect(model.useVibration, true);
          expect(model.soundValue, 75.0);
          expect(model.vibrationValue, 50.0);
          expect(model.scanMusicList, 1);
        });

        testWidgets('应该能够创建自定义测试模型', (tester) async {
          // Act
          final model = createTestModel(useSound: false, soundValue: 0.0, scanMusicList: 2);

          // Assert
          expect(model.useSound, false);
          expect(model.soundValue, 0.0);
          expect(model.scanMusicList, 2);
          // 其他参数使用默认值
          expect(model.useVibration, true);
          expect(model.vibrationValue, 50.0);
        });

        testWidgets('应该能够动态更新 Mock Controller 数据', (tester) async {
          // Arrange
          final newModel = createTestModel(useSound: false, soundValue: 0.0);

          // Act
          updateMockControllerData(newModel);

          // Assert
          expect(mockController.model.value.useSound, false);
          expect(mockController.model.value.soundValue, 0.0);
        });
      });

      group('0.3 GetX 依赖注入验证', () {
        testWidgets('应该成功注入 Mock Controller', (tester) async {
          // Assert
          expect(Get.isRegistered<ScanSettingsController>(), true, reason: 'Controller 应该已注册到 GetX');

          final injectedController = Get.find<ScanSettingsController>();
          expect(injectedController, same(mockController), reason: '注入的 Controller 应该是我们的 Mock');
        });

        testWidgets('setUp 和 tearDown 应该正确管理 GetX 状态', (tester) async {
          // Arrange - 验证初始状态
          expect(Get.isRegistered<ScanSettingsController>(), true);

          // Act - 手动调用 tearDown 逻辑（实际会自动调用）
          final controllerBefore = Get.find<ScanSettingsController>();
          Get.reset();

          // Assert - 验证清理后状态
          expect(Get.isRegistered<ScanSettingsController>(), false);

          // 重新设置（模拟下一个测试的 setUp）
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          expect(Get.isRegistered<ScanSettingsController>(), true);
        });
      });

      group('0.4 Widget 测试环境验证', () {
        testWidgets('应该能够创建测试 Widget 环境', (tester) async {
          // Act
          final widget = createWidgetUnderTest();

          // Assert
          expect(widget, isA<GetMaterialApp>(), reason: '应该返回 GetMaterialApp');
          expect(widget, isNotNull, reason: 'Widget 不应该为空');
        });

        testWidgets('应该能够渲染测试 Widget 而不报错', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());

          // Assert - 如果到这里没有异常，说明渲染成功
          expect(find.byType(GetMaterialApp), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);
        });

        testWidgets('应该包含 SoundSettingsWidget', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSettingsWidget), findsOneWidget, reason: '应该找到 SoundSettingsWidget');
        });

        testWidgets('应该正确应用 AppTheme', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final materialApp = tester.widget<GetMaterialApp>(find.byType(GetMaterialApp));
          expect(materialApp.theme, AppTheme.lightTheme, reason: '应该使用正确的主题');
        });
      });

      group('0.5 基础 Widget 构建验证', () {
        testWidgets('SoundSettingsWidget 应该能够成功构建', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSettingsWidget), findsOneWidget);

          // 验证没有构建错误
          expect(tester.takeException(), isNull, reason: '构建过程不应该有异常');
        });

        testWidgets('应该在 useSound = true 时渲染主要内容', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget, reason: 'useSound = true 时应该显示主容器');
        });

        testWidgets('应该在 useSound = false 时隐藏内容', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_settings_container')), findsNothing, reason: 'useSound = false 时应该隐藏主容器');
          expect(find.byType(SizedBox), findsOneWidget, reason: '应该显示 SizedBox.shrink()');
        });
      });

      group('0.6 响应式系统验证', () {
        testWidgets('应该能够在不同状态下正确构建 Widget', (tester) async {
          // Test 1: useSound = true 状态
          final model1 = createTestModel(useSound: true);
          when(mockController.model).thenReturn(model1.obs);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget, reason: 'useSound = true 时应该显示容器');

          // Test 2: useSound = false 状态 (在新的测试环境中)
          Get.reset();
          mockController = MockScanSettingsController();
          final model2 = createTestModel(useSound: false);
          when(mockController.model).thenReturn(model2.obs);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onInit()).thenAnswer((_) async {});
          when(mockController.onClose()).thenAnswer((_) async {});
          when(mockController.onReady()).thenAnswer((_) async {});
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_settings_container')), findsNothing, reason: 'useSound = false 时应该隐藏容器');
          expect(find.byType(SizedBox), findsOneWidget, reason: '应该显示 SizedBox.shrink()');
        });

        testWidgets('Mock Controller 数据模型应该能够正确反映不同的配置', (tester) async {
          // 验证不同配置下 Mock Controller 的行为
          final configs = [
            {'useSound': true, 'soundValue': 75.0, 'scanMusicList': 1},
            {'useSound': false, 'soundValue': 0.0, 'scanMusicList': 2},
            {'useSound': true, 'soundValue': 100.0, 'scanMusicList': 1},
          ];

          for (int i = 0; i < configs.length; i++) {
            final config = configs[i];
            final model = createTestModel(
              useSound: config['useSound'] as bool,
              soundValue: config['soundValue'] as double,
              scanMusicList: config['scanMusicList'] as int,
            );

            when(mockController.model).thenReturn(model.obs);

            // 验证 Mock Controller 状态
            expect(mockController.model.value.useSound, config['useSound']);
            expect(mockController.model.value.soundValue, config['soundValue']);
            expect(mockController.model.value.scanMusicList, config['scanMusicList']);
          }
        });
      });

      group('0.7 测试隔离和清理验证', () {
        testWidgets('测试之间应该正确隔离状态', (tester) async {
          // 这个测试验证每个测试都有干净的起始状态

          // Assert
          expect(Get.isRegistered<ScanSettingsController>(), true);
          expect(mockController.model.value.useSound, true);
          expect(mockController.model.value.soundValue, 75.0);

          // 验证这是一个新的 Mock 实例
          expect(mockController, isA<MockScanSettingsController>());
        });

        testWidgets('应该能够创建独立的测试数据', (tester) async {
          // Arrange - 创建特定的测试数据
          final customModel = createTestModel(useSound: false, soundValue: 25.0, scanMusicList: 2);
          updateMockControllerData(customModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(mockController.model.value.useSound, false);
          expect(mockController.model.value.soundValue, 25.0);
          expect(mockController.model.value.scanMusicList, 2);
        });
      });
    });

    // ==========================================
    // 🔄 Phase 1: 条件渲染核心逻辑测试
    // ==========================================

    group('🔄 Phase 1: 条件渲染核心逻辑测试', () {
      group('1.1 useSound = true 显示状态测试', () {
        testWidgets('应该显示完整的音效设置容器', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget, reason: 'useSound = true 时应该显示主容器');
          expect(find.byType(Container).first, findsOneWidget, reason: '应该找到主要的 Container 组件');
        });

        testWidgets('应该显示所有音效相关的 UI 元素', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有关键UI元素存在
          expect(find.byKey(const Key('sound_music_label')), findsOneWidget, reason: '应该显示音效选择标签');
          expect(find.byKey(const Key('sound_volume_label')), findsOneWidget, reason: '应该显示音量调节标签');
          expect(find.byKey(const Key('sound_music_dropdown')), findsOneWidget, reason: '应该显示音效选择下拉框');
          expect(find.byKey(const Key('sound_volume_slider')), findsOneWidget, reason: '应该显示音量滑块');
          expect(find.byKey(const Key('sound_volume_mute_icon')), findsOneWidget, reason: '应该显示静音图标');
          expect(find.byKey(const Key('sound_volume_up_icon')), findsOneWidget, reason: '应该显示音量增大图标');
          expect(find.byKey(const Key('sound_settings_divider')), findsOneWidget, reason: '应该显示分隔线');
        });

        testWidgets('应该正确嵌入 VibrationSettingWidget', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: '应该包含 VibrationSettingWidget');

          // 验证 VibrationSettingWidget 在正确位置（Container 内部）
          final containerFinder = find.byKey(const Key('sound_settings_container'));
          final vibrationWidgetFinder = find.byType(VibrationSettingWidget);
          expect(
            find.descendant(of: containerFinder, matching: vibrationWidgetFinder),
            findsOneWidget,
            reason: 'VibrationSettingWidget 应该在主容器内部',
          );
        });

        testWidgets('应该显示正确的文本内容', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('音の選択'), findsOneWidget, reason: '应该显示"音の選択"文本');
          expect(find.text('音量調節'), findsOneWidget, reason: '应该显示"音量調節"文本');
        });

        testWidgets('应该显示正确的布局结构', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证布局结构
          final containerFinder = find.byKey(const Key('sound_settings_container'));

          // 验证 Column 结构 (包含主容器的 Column 和 VibrationSettingWidget 内的 Column)
          expect(
            find.descendant(of: containerFinder, matching: find.byType(Column)),
            findsAtLeastNWidgets(1),
            reason: '主容器内应该有至少一个 Column 布局',
          );

          // 验证 Row 结构
          expect(
            find.descendant(of: containerFinder, matching: find.byType(Row)),
            findsAtLeastNWidgets(2),
            reason: '应该有多个 Row 布局',
          );
        });
      });

      group('1.2 useSound = false 隐藏状态测试', () {
        testWidgets('应该完全隐藏音效设置容器', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_settings_container')), findsNothing, reason: 'useSound = false 时不应该显示主容器');
        });

        testWidgets('应该渲染 SizedBox.shrink()', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SizedBox), findsOneWidget, reason: 'useSound = false 时应该显示 SizedBox.shrink()');

          // 验证 SizedBox 的尺寸为 0
          final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
          expect(sizedBox.width, 0.0, reason: 'SizedBox 宽度应该为 0');
          expect(sizedBox.height, 0.0, reason: 'SizedBox 高度应该为 0');
        });

        testWidgets('应该隐藏所有音效相关的 UI 元素', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有关键UI元素都不存在
          expect(find.byKey(const Key('sound_music_label')), findsNothing, reason: '不应该显示音效选择标签');
          expect(find.byKey(const Key('sound_volume_label')), findsNothing, reason: '不应该显示音量调节标签');
          expect(find.byKey(const Key('sound_music_dropdown')), findsNothing, reason: '不应该显示音效选择下拉框');
          expect(find.byKey(const Key('sound_volume_slider')), findsNothing, reason: '不应该显示音量滑块');
          expect(find.byKey(const Key('sound_volume_mute_icon')), findsNothing, reason: '不应该显示静音图标');
          expect(find.byKey(const Key('sound_volume_up_icon')), findsNothing, reason: '不应该显示音量增大图标');
          expect(find.byKey(const Key('sound_settings_divider')), findsNothing, reason: '不应该显示分隔线');
        });

        testWidgets('应该隐藏 VibrationSettingWidget', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(
            find.byType(VibrationSettingWidget),
            findsNothing,
            reason: 'useSound = false 时不应该显示 VibrationSettingWidget',
          );
        });

        testWidgets('应该隐藏所有文本内容', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('音の選択'), findsNothing, reason: '不应该显示"音の選択"文本');
          expect(find.text('音量調節'), findsNothing, reason: '不应该显示"音量調節"文本');
        });
      });

      group('1.3 条件渲染边界测试', () {
        testWidgets('应该正确处理 useSound 的不同初始值', (tester) async {
          // Test useSound = true 初始状态
          final model1 = createTestModel(useSound: true);
          when(mockController.model).thenReturn(model1.obs);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget);

          // 重新创建环境测试 useSound = false
          Get.reset();
          mockController = MockScanSettingsController();
          final model2 = createTestModel(useSound: false);
          when(mockController.model).thenReturn(model2.obs);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onInit()).thenAnswer((_) async {});
          when(mockController.onClose()).thenAnswer((_) async {});
          when(mockController.onReady()).thenAnswer((_) async {});
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_settings_container')), findsNothing);
        });

        testWidgets('条件渲染应该是完全二元的', (tester) async {
          // Test 1: useSound = true 时不应该有 SizedBox
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget);
          // 注意：当显示容器时，SizedBox.shrink() 应该不存在

          // Test 2: useSound = false 时不应该有 Container
          Get.reset();
          mockController = MockScanSettingsController();
          testSettingsModel.useSound = false;
          when(mockController.model).thenReturn(testSettingsModel.obs);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onInit()).thenAnswer((_) async {});
          when(mockController.onClose()).thenAnswer((_) async {});
          when(mockController.onReady()).thenAnswer((_) async {});
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          expect(find.byKey(const Key('sound_settings_container')), findsNothing);
          expect(find.byType(SizedBox), findsOneWidget);
        });

        testWidgets('应该正确处理极端数据情况下的条件渲染', (tester) async {
          // 测试在其他数据异常时，useSound 仍能正确控制渲染
          final extremeModel = createTestModel(
            useSound: true,
            soundValue: 50.0, // 使用有效的音量值
            scanMusicList: 1, // 使用有效的音效类型
            useVibration: false, // 关闭振动以简化测试
          );
          when(mockController.model).thenReturn(extremeModel.obs);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // useSound = true 时，容器应该显示
          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget, reason: 'useSound = true 时容器应该显示');
        });
      });

      group('1.4 组件树完整性验证', () {
        testWidgets('显示状态下应该有完整的组件层次结构', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证组件层次结构
          final soundSettingsWidget = find.byType(SoundSettingsWidget);
          final containerWidget = find.byKey(const Key('sound_settings_container'));
          final paddingWidget = find.byType(Padding);

          expect(soundSettingsWidget, findsOneWidget);
          expect(containerWidget, findsOneWidget, reason: '应该有主容器');
          expect(
            find.descendant(of: containerWidget, matching: paddingWidget),
            findsAtLeastNWidgets(1),
            reason: '主容器应该包含至少一个 Padding',
          );

          // 验证有多个 Obx (包括 SoundSettingsWidget 和 VibrationSettingWidget 的 Obx)
          expect(
            find.descendant(of: soundSettingsWidget, matching: find.byType(Obx)),
            findsAtLeastNWidgets(1),
            reason: 'SoundSettingsWidget 应该包含至少一个 Obx',
          );
        });

        testWidgets('隐藏状态下应该有简化的组件层次结构', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证简化的组件层次结构
          final soundSettingsWidget = find.byType(SoundSettingsWidget);
          final obxWidget = find.byType(Obx);
          final sizedBoxWidget = find.byType(SizedBox);

          expect(soundSettingsWidget, findsOneWidget);
          expect(
            find.descendant(of: soundSettingsWidget, matching: obxWidget),
            findsOneWidget,
            reason: 'SoundSettingsWidget 应该包含 Obx',
          );
          expect(
            find.descendant(of: obxWidget, matching: sizedBoxWidget),
            findsOneWidget,
            reason: 'Obx 应该包含 SizedBox.shrink()',
          );

          // 验证不应该存在复杂的子组件
          expect(find.byType(Container), findsNothing, reason: '隐藏状态不应该有 Container');
          expect(find.byType(Padding), findsNothing, reason: '隐藏状态不应该有 Padding');
          expect(find.byType(Column), findsNothing, reason: '隐藏状态不应该有 Column');
        });

        testWidgets('显示状态下所有子组件应该正确排列', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证主容器中包含所有预期的元素
          final containerFinder = find.byKey(const Key('sound_settings_container'));

          // 验证至少有一个 Column 布局
          expect(find.descendant(of: containerFinder, matching: find.byType(Column)), findsAtLeastNWidgets(1));

          // 音效选择行
          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_music_label'))),
            findsOneWidget,
          );

          // 音量调节行
          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_volume_label'))),
            findsOneWidget,
          );

          // 音量滑块行
          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_volume_slider'))),
            findsOneWidget,
          );

          // 分隔线
          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_settings_divider'))),
            findsOneWidget,
          );

          // VibrationSettingWidget
          expect(find.descendant(of: containerFinder, matching: find.byType(VibrationSettingWidget)), findsOneWidget);
        });
      });

      group('1.5 条件渲染性能验证', () {
        testWidgets('条件渲染不应该导致不必要的 Widget 创建', (tester) async {
          // Arrange
          testSettingsModel.useSound = false;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证隐藏状态下没有创建复杂的子组件
          expect(find.byType(DropdownButton<int>), findsNothing, reason: '隐藏状态不应该创建 DropdownButton');
          expect(find.byType(Slider), findsNothing, reason: '隐藏状态不应该创建 Slider');
          expect(find.byType(VibrationSettingWidget), findsNothing, reason: '隐藏状态不应该创建 VibrationSettingWidget');

          // 验证只有最少必要的组件
          expect(find.byType(SoundSettingsWidget), findsOneWidget);
          expect(find.byType(Obx), findsOneWidget);
          expect(find.byType(SizedBox), findsOneWidget);
        });

        testWidgets('显示状态应该创建所有必要的子组件', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证显示状态下所有组件都被创建
          expect(find.byType(SoundSettingsWidget), findsOneWidget);
          expect(find.byType(Obx), findsAtLeastNWidgets(1), reason: '应该有至少一个 Obx（可能有嵌套的 Obx）');
          expect(find.byType(Container), findsAtLeastNWidgets(1), reason: '应该有主容器');
          expect(find.byType(DropdownButton<int>), findsOneWidget, reason: '应该创建音效选择下拉框');
          expect(find.byKey(const Key('sound_volume_slider')), findsOneWidget, reason: '应该创建音量滑块（使用 Key 精确定位）');
          expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: '应该创建振动设置组件');
        });
      });
    });

    // ==========================================
    // 🎨 Phase 2: 静态 UI 组件测试
    // ==========================================

    group('🎨 Phase 2: 静态 UI 组件测试', () {
      group('2.1 文本组件测试', () {
        testWidgets('应该显示正确的音效选择标签', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('音の選択'), findsOneWidget, reason: '应该显示音效选择标签');

          // 验证文本通过Key也能找到
          final musicLabelWidget = tester.widget<Text>(find.byKey(const Key('sound_music_label')));
          expect(musicLabelWidget.data, '音の選択', reason: '音效选择标签内容应该正确');
        });

        testWidgets('应该显示正确的音量调节标签', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('音量調節'), findsOneWidget, reason: '应该显示音量调节标签');

          // 验证文本通过Key也能找到
          final volumeLabelWidget = tester.widget<Text>(find.byKey(const Key('sound_volume_label')));
          expect(volumeLabelWidget.data, '音量調節', reason: '音量调节标签内容应该正确');
        });

        testWidgets('文本组件应该具有正确的样式属性', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证文本样式
          final musicLabelWidget = tester.widget<Text>(find.byKey(const Key('sound_music_label')));
          final volumeLabelWidget = tester.widget<Text>(find.byKey(const Key('sound_volume_label')));

          // 验证文本不为空且符合预期
          expect(musicLabelWidget.data, isNotNull);
          expect(volumeLabelWidget.data, isNotNull);
          expect(musicLabelWidget.data, isNotEmpty);
          expect(volumeLabelWidget.data, isNotEmpty);
        });

        testWidgets('文本应该在正确的布局位置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证文本在容器内
          final containerFinder = find.byKey(const Key('sound_settings_container'));

          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_music_label'))),
            findsOneWidget,
            reason: '音效选择标签应该在主容器内',
          );

          expect(
            find.descendant(of: containerFinder, matching: find.byKey(const Key('sound_volume_label'))),
            findsOneWidget,
            reason: '音量调节标签应该在主容器内',
          );
        });
      });

      group('2.2 图标组件测试', () {
        testWidgets('应该显示静音图标', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_volume_mute_icon')), findsOneWidget, reason: '应该显示静音图标');

          final muteIconWidget = tester.widget<Icon>(find.byKey(const Key('sound_volume_mute_icon')));
          expect(muteIconWidget.icon, Icons.volume_mute, reason: '静音图标应该是 Icons.volume_mute');
        });

        testWidgets('应该显示音量增大图标', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_volume_up_icon')), findsOneWidget, reason: '应该显示音量增大图标');

          final volumeUpIconWidget = tester.widget<Icon>(find.byKey(const Key('sound_volume_up_icon')));
          expect(volumeUpIconWidget.icon, Icons.volume_up, reason: '音量增大图标应该是 Icons.volume_up');
        });

        testWidgets('图标应该具有正确的颜色', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final muteIconWidget = tester.widget<Icon>(find.byKey(const Key('sound_volume_mute_icon')));
          final volumeUpIconWidget = tester.widget<Icon>(find.byKey(const Key('sound_volume_up_icon')));

          expect(muteIconWidget.color, Colors.grey, reason: '静音图标颜色应该是灰色');
          expect(volumeUpIconWidget.color, Colors.grey, reason: '音量增大图标颜色应该是灰色');
        });

        testWidgets('图标应该在滑块两侧正确位置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证图标和滑块在同一Row中
          final containerFinder = find.byKey(const Key('sound_settings_container'));

          // 找到包含滑块的Row
          final sliderRow = find.ancestor(of: find.byKey(const Key('sound_volume_slider')), matching: find.byType(Row));
          expect(sliderRow, findsOneWidget);

          // 验证图标都在同一个Row中
          expect(
            find.descendant(of: sliderRow, matching: find.byKey(const Key('sound_volume_mute_icon'))),
            findsOneWidget,
          );

          expect(
            find.descendant(of: sliderRow, matching: find.byKey(const Key('sound_volume_up_icon'))),
            findsOneWidget,
          );
        });
      });

      group('2.3 下拉框组件测试', () {
        testWidgets('应该显示音效选择下拉框', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          testSettingsModel.scanMusicList = 1;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_music_dropdown')), findsOneWidget, reason: '应该显示音效选择下拉框');

          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));
          expect(dropdownWidget.value, 1, reason: '下拉框应该显示当前选中的值');
        });

        testWidgets('下拉框应该包含正确的选项', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          testSettingsModel.scanMusicList = 1;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));

          expect(dropdownWidget.items, isNotNull, reason: '下拉框应该有选项列表');
          expect(dropdownWidget.items!.length, 2, reason: '应该有2个音效选项');

          // 验证选项内容
          final firstItem = dropdownWidget.items![0];
          final secondItem = dropdownWidget.items![1];

          expect(firstItem.value, 1, reason: '第一个选项值应该是1');
          expect(secondItem.value, 2, reason: '第二个选项值应该是2');
        });

        testWidgets('下拉框应该具有正确的布局属性', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));

          expect(dropdownWidget.isExpanded, true, reason: '下拉框应该展开填满可用空间');
          expect(dropdownWidget.underline, isA<Container>(), reason: '下拉框应该有下划线设置');
        });

        testWidgets('下拉框应该在Expanded中正确布局', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证下拉框在Expanded中
          final expandedFinder = find.ancestor(
            of: find.byKey(const Key('sound_music_dropdown')),
            matching: find.byType(Expanded),
          );
          expect(expandedFinder, findsOneWidget, reason: '下拉框应该在Expanded组件中');

          final expandedWidget = tester.widget<Expanded>(expandedFinder);
          expect(expandedWidget.flex, 6, reason: 'Expanded的flex值应该是6');
        });
      });

      group('2.4 滑块组件测试', () {
        testWidgets('应该显示音量滑块', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          testSettingsModel.soundValue = 75.0;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_volume_slider')), findsOneWidget, reason: '应该显示音量滑块');

          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));
          expect(sliderWidget.value, 75.0, reason: '滑块应该显示当前音量值');
        });

        testWidgets('滑块应该具有正确的范围设置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));

          expect(sliderWidget.min, 0.0, reason: '滑块最小值应该是0.0');
          expect(sliderWidget.max, 100.0, reason: '滑块最大值应该是100.0');
        });

        testWidgets('滑块应该能反映不同的音量值', (tester) async {
          // Test 不同音量值
          final testValues = [0.0, 25.0, 50.0, 75.0, 100.0];

          for (final testValue in testValues) {
            // 为每个测试值创建新的数据模型和 Mock Controller
            Get.reset();
            mockController = MockScanSettingsController();

            final testModel = createTestModel(useSound: true, soundValue: testValue);
            when(mockController.model).thenReturn(testModel.obs);
            when(mockController.onStart).thenReturn(mockInternalFinalCallback);
            when(mockController.onInit()).thenAnswer((_) async {});
            when(mockController.onClose()).thenAnswer((_) async {});
            when(mockController.onReady()).thenAnswer((_) async {});
            Get.put<ScanSettingsController>(mockController);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));

            expect(sliderWidget.value, testValue, reason: '滑块应该显示音量值 $testValue');
          }
        });

        testWidgets('滑块应该在Expanded中正确布局', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证滑块在Expanded中
          final expandedFinder = find.ancestor(
            of: find.byKey(const Key('sound_volume_slider')),
            matching: find.byType(Expanded),
          );
          expect(expandedFinder, findsOneWidget, reason: '滑块应该在Expanded组件中');
        });
      });

      group('2.5 布局组件测试', () {
        testWidgets('主容器应该具有正确的装饰样式', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_container')));

          expect(containerWidget.decoration, isA<BoxDecoration>(), reason: '主容器应该有BoxDecoration装饰');

          final decoration = containerWidget.decoration as BoxDecoration;
          expect(decoration.borderRadius, isA<BorderRadius>(), reason: '装饰应该有圆角边框');
          expect(decoration.color, isNotNull, reason: '装饰应该有背景颜色');
        });

        testWidgets('主容器应该具有正确的边距设置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_container')));

          expect(containerWidget.margin, isA<EdgeInsets>(), reason: '主容器应该有边距设置');

          final margin = containerWidget.margin as EdgeInsets;
          expect(margin.top, 10.0, reason: '上边距应该是10.0');
          expect(margin.bottom, 10.0, reason: '下边距应该是10.0');
          expect(margin.left, 0.0, reason: '左边距应该是0.0');
          expect(margin.right, 0.0, reason: '右边距应该是0.0');
        });

        testWidgets('应该具有正确的Padding设置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 查找主要的Padding组件
          final containerFinder = find.byKey(const Key('sound_settings_container'));
          final paddingFinder = find.descendant(of: containerFinder, matching: find.byType(Padding));

          expect(paddingFinder, findsAtLeastNWidgets(1), reason: '应该有至少一个Padding组件');
        });

        testWidgets('Row组件应该具有正确的主轴对齐', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 查找Row组件
          final containerFinder = find.byKey(const Key('sound_settings_container'));
          final rowFinders = find.descendant(of: containerFinder, matching: find.byType(Row));

          expect(rowFinders, findsAtLeastNWidgets(1), reason: '应该有至少一个Row组件');

          // 验证第一个Row的对齐方式
          final firstRow = tester.widget<Row>(rowFinders.first);
          expect(firstRow.mainAxisAlignment, MainAxisAlignment.spaceBetween, reason: 'Row的主轴对齐应该是spaceBetween');
        });

        testWidgets('Flexible组件应该具有正确的flex比例', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 查找包含音效选择标签的Flexible
          final musicLabelFinder = find.byKey(const Key('sound_music_label'));
          final flexibleFinder = find.ancestor(of: musicLabelFinder, matching: find.byType(Flexible));

          expect(flexibleFinder, findsOneWidget, reason: '音效选择标签应该在Flexible中');

          final flexibleWidget = tester.widget<Flexible>(flexibleFinder);
          expect(flexibleWidget.flex, 4, reason: 'Flexible的flex值应该是4');
        });
      });

      group('2.6 分隔线组件测试', () {
        testWidgets('应该显示分隔线', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('sound_settings_divider')), findsOneWidget, reason: '应该显示分隔线');

          final dividerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_divider')));
          expect(dividerWidget, isNotNull, reason: '分隔线应该是Container组件');
        });

        testWidgets('分隔线应该具有正确的样式属性', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final dividerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_divider')));

          expect(dividerWidget.constraints?.maxHeight, 1.0, reason: '分隔线高度应该是1.0');
          expect(dividerWidget.color, Colors.grey, reason: '分隔线颜色应该是灰色');
        });

        testWidgets('分隔线应该具有正确的边距设置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final dividerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_divider')));

          expect(dividerWidget.margin, isA<EdgeInsets>(), reason: '分隔线应该有边距设置');

          final margin = dividerWidget.margin as EdgeInsets;
          expect(margin.top, 8.0, reason: '分隔线上边距应该是8.0');
        });

        testWidgets('分隔线应该在正确位置', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证分隔线在主容器内
          final containerFinder = find.byKey(const Key('sound_settings_container'));
          final dividerFinder = find.byKey(const Key('sound_settings_divider'));

          expect(
            find.descendant(of: containerFinder, matching: dividerFinder),
            findsOneWidget,
            reason: '分隔线应该在主容器内',
          );

          // 验证分隔线在VibrationSettingWidget之前
          final vibrationWidgetFinder = find.byType(VibrationSettingWidget);
          expect(
            find.descendant(of: containerFinder, matching: vibrationWidgetFinder),
            findsOneWidget,
            reason: 'VibrationSettingWidget应该在主容器内',
          );
        });
      });

      group('2.7 UI组件综合验证', () {
        testWidgets('所有UI组件应该在显示状态下协调工作', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          testSettingsModel.soundValue = 80.0;
          testSettingsModel.scanMusicList = 2;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有组件都存在并且数据一致
          // 文本组件
          expect(find.text('音の選択'), findsOneWidget);
          expect(find.text('音量調節'), findsOneWidget);

          // 图标组件
          expect(find.byKey(const Key('sound_volume_mute_icon')), findsOneWidget);
          expect(find.byKey(const Key('sound_volume_up_icon')), findsOneWidget);

          // 交互组件
          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));
          expect(dropdownWidget.value, 2);

          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));
          expect(sliderWidget.value, 80.0);

          // 布局组件
          expect(find.byKey(const Key('sound_settings_container')), findsOneWidget);
          expect(find.byKey(const Key('sound_settings_divider')), findsOneWidget);
        });

        testWidgets('UI组件样式应该保持一致性', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证样式一致性
          // 图标颜色一致性
          final muteIcon = tester.widget<Icon>(find.byKey(const Key('sound_volume_mute_icon')));
          final volumeUpIcon = tester.widget<Icon>(find.byKey(const Key('sound_volume_up_icon')));
          expect(muteIcon.color, volumeUpIcon.color, reason: '所有图标颜色应该一致');

          // 容器装饰一致性
          final containerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_container')));
          expect(containerWidget.decoration, isNotNull, reason: '容器应该有装饰');

          final dividerWidget = tester.widget<Container>(find.byKey(const Key('sound_settings_divider')));
          expect(dividerWidget.color, Colors.grey, reason: '分隔线颜色应该与图标颜色主题一致');
        });

        testWidgets('UI组件布局应该具有良好的层次结构', (tester) async {
          // Arrange
          testSettingsModel.useSound = true;
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证布局层次结构
          final containerFinder = find.byKey(const Key('sound_settings_container'));

          // 验证所有关键组件都在主容器内
          final keyComponents = [
            const Key('sound_music_label'),
            const Key('sound_volume_label'),
            const Key('sound_music_dropdown'),
            const Key('sound_volume_slider'),
            const Key('sound_volume_mute_icon'),
            const Key('sound_volume_up_icon'),
            const Key('sound_settings_divider'),
          ];

          for (final key in keyComponents) {
            expect(
              find.descendant(of: containerFinder, matching: find.byKey(key)),
              findsOneWidget,
              reason: 'Key $key 的组件应该在主容器内',
            );
          }
        });
      });
    });

    // ==========================================
    // 🎮 Phase 3: 交互组件功能测试
    // ==========================================

    group('🎮 Phase 3: 交互组件功能测试', () {
      group('3.1 DropdownButton 交互测试', () {
        testWidgets('应该能够点击并展开下拉框', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 确认下拉框存在
          final dropdownFinder = find.byKey(const Key('sound_music_dropdown'));
          expect(dropdownFinder, findsOneWidget);

          // 点击下拉框展开
          await tester.tap(dropdownFinder);
          await tester.pumpAndSettle();

          // 验证下拉选项出现
          expect(find.text('コンプリート'), findsWidgets);
          expect(find.text('ビンゴ'), findsWidgets);
        });

        testWidgets('应该能够选择不同的音效选项', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击下拉框
          await tester.tap(find.byKey(const Key('sound_music_dropdown')));
          await tester.pumpAndSettle();

          // 选择 "ビンゴ" 选项
          await tester.tap(find.text('ビンゴ').last);
          await tester.pumpAndSettle();

          // 验证 controller.musicChange 被调用
          verify(mockController.musicChange(2)).called(1);
        });

        testWidgets('应该能够选择 "コンプリート" 选项', (tester) async {
          // 设置初始值为2，这样可以测试选择1的情况
          Get.reset();
          mockController = MockScanSettingsController();
          final testModel = createTestModel(useSound: true, scanMusicList: 2);
          when(mockController.model).thenReturn(testModel.obs);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onInit()).thenAnswer((_) async {});
          when(mockController.onClose()).thenAnswer((_) async {});
          when(mockController.onReady()).thenAnswer((_) async {});
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 点击下拉框
          await tester.tap(find.byKey(const Key('sound_music_dropdown')));
          await tester.pumpAndSettle();

          // 选择 "コンプリート" 选项
          await tester.tap(find.text('コンプリート').last);
          await tester.pumpAndSettle();

          // 验证 controller.musicChange 被调用
          verify(mockController.musicChange(1)).called(1);
        });

        testWidgets('下拉框选择应该传递正确的参数', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 测试选择不同选项的参数传递
          final testCases = [
            {'text': 'コンプリート', 'value': 1},
            {'text': 'ビンゴ', 'value': 2},
          ];

          for (final testCase in testCases) {
            // 重置 mock 调用记录
            reset(mockController);
            setupMockController();

            await tester.tap(find.byKey(const Key('sound_music_dropdown')));
            await tester.pumpAndSettle();

            await tester.tap(find.text(testCase['text'] as String).last);
            await tester.pumpAndSettle();

            // 验证正确的值被传递
            verify(mockController.musicChange(testCase['value'] as int)).called(1);
          }
        });

        testWidgets('下拉框应该处理空值选择', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 获取下拉框widget进行直接测试
          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));

          // 验证下拉框的 onChanged 不为 null
          expect(dropdownWidget.onChanged, isNotNull);

          // 验证当前值符合预期
          expect(dropdownWidget.value, equals(testSettingsModel.scanMusicList));
        });
      });

      group('3.2 Slider 交互测试', () {
        testWidgets('应该能够拖拽滑块改变音量', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          expect(sliderFinder, findsOneWidget);

          // 获取滑块的初始位置和大小
          final sliderRect = tester.getRect(sliderFinder);

          // 计算拖拽距离到中间位置（约50%）
          final dragDistance = Offset(sliderRect.width * 0.3, 0);

          await tester.drag(sliderFinder, dragDistance);
          await tester.pumpAndSettle();

          // 验证 soundValueChange 被调用
          verify(mockController.soundValueChange(any)).called(greaterThan(0));
        });

        testWidgets('应该能够拖拽滑块到最小值', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          final sliderRect = tester.getRect(sliderFinder);

          // 拖拽到最左端（最小值），向左拖拽整个宽度
          final dragDistance = Offset(-sliderRect.width, 0);

          await tester.drag(sliderFinder, dragDistance);
          await tester.pumpAndSettle();

          // 验证 soundValueChange 被调用并获取参数
          final capturedArgs = verify(mockController.soundValueChange(captureAny)).captured;
          expect(capturedArgs, isNotEmpty, reason: '应该有方法调用');

          // 检查是否有接近0的值
          final hasLowValue = capturedArgs.any((value) => (value as double) < 10.0);
          expect(hasLowValue, isTrue, reason: '拖拽到最左端应该产生接近0的值');
        });

        testWidgets('应该能够拖拽滑块到最大值', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          final sliderRect = tester.getRect(sliderFinder);

          // 拖拽到最右端（最大值），向右拖拽整个宽度
          final dragDistance = Offset(sliderRect.width, 0);

          await tester.drag(sliderFinder, dragDistance);
          await tester.pumpAndSettle();

          // 验证 soundValueChange 被调用并获取参数
          final capturedArgs = verify(mockController.soundValueChange(captureAny)).captured;
          expect(capturedArgs, isNotEmpty, reason: '应该有方法调用');

          // 检查是否有接近100的值
          final hasHighValue = capturedArgs.any((value) => (value as double) > 90.0);
          expect(hasHighValue, isTrue, reason: '拖拽到最右端应该产生接近100的值');
        });

        testWidgets('滑块拖拽应该传递连续的数值', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          final sliderRect = tester.getRect(sliderFinder);

          // 从左到右慢慢拖拽
          final startPosition = Offset(sliderRect.left, sliderRect.center.dy);
          final endPosition = Offset(sliderRect.right, sliderRect.center.dy);

          await tester.dragFrom(startPosition, endPosition - startPosition);
          await tester.pumpAndSettle();

          // 验证 soundValueChange 被多次调用并获取参数
          final capturedArgs = verify(mockController.soundValueChange(captureAny)).captured;
          expect(capturedArgs.length, greaterThan(1), reason: '应该有多次方法调用');

          // 验证调用的值是合理范围
          for (final value in capturedArgs) {
            final doubleValue = value as double;
            expect(doubleValue, inInclusiveRange(0.0, 100.0), reason: '滑块值应该在有效范围内');
          }
        });

        testWidgets('滑块应该正确处理边界值', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));

          // 验证滑块的配置
          expect(sliderWidget.min, equals(0.0));
          expect(sliderWidget.max, equals(100.0));
          expect(sliderWidget.onChanged, isNotNull);
          expect(sliderWidget.value, inInclusiveRange(0.0, 100.0));
        });
      });

      group('3.3 用户操作序列测试', () {
        testWidgets('应该能够连续进行多次下拉框选择', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 进行多次下拉框选择
          for (int i = 0; i < 3; i++) {
            // 选择 "ビンゴ"
            await tester.tap(find.byKey(const Key('sound_music_dropdown')));
            await tester.pumpAndSettle();
            await tester.tap(find.text('ビンゴ').last);
            await tester.pumpAndSettle();

            // 选择 "コンプリート"
            await tester.tap(find.byKey(const Key('sound_music_dropdown')));
            await tester.pumpAndSettle();
            await tester.tap(find.text('コンプリート').last);
            await tester.pumpAndSettle();
          }

          // 验证方法被正确调用
          verify(mockController.musicChange(2)).called(3);
          verify(mockController.musicChange(1)).called(3);
        });

        testWidgets('应该能够在下拉框和滑块之间切换操作', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 操作下拉框
          await tester.tap(find.byKey(const Key('sound_music_dropdown')));
          await tester.pumpAndSettle();
          await tester.tap(find.text('ビンゴ').last);
          await tester.pumpAndSettle();

          // 操作滑块
          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          final sliderRect = tester.getRect(sliderFinder);
          final dragDistance = Offset(sliderRect.width * 0.3, 0);
          await tester.drag(sliderFinder, dragDistance);
          await tester.pumpAndSettle();

          // 再次操作下拉框
          await tester.tap(find.byKey(const Key('sound_music_dropdown')));
          await tester.pumpAndSettle();
          await tester.tap(find.text('コンプリート').last);
          await tester.pumpAndSettle();

          // 验证两个控件的方法都被调用
          verify(mockController.musicChange(any)).called(greaterThan(0));
          verify(mockController.soundValueChange(any)).called(greaterThan(0));
        });

        testWidgets('应该能够处理快速连续的滑块操作', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          final sliderRect = tester.getRect(sliderFinder);

          // 快速进行多次滑块拖拽
          final dragDistances = [0.1, 0.2, -0.3, 0.2, -0.1];

          for (final dragPercent in dragDistances) {
            final dragDistance = Offset(sliderRect.width * dragPercent, 0);
            await tester.drag(sliderFinder, dragDistance);
            await tester.pump(const Duration(milliseconds: 50));
          }

          await tester.pumpAndSettle();

          // 验证滑块方法被多次调用
          verify(mockController.soundValueChange(any)).called(greaterThan(0));
        });
      });

      group('3.4 交互组件状态验证', () {
        testWidgets('交互组件应该在正确状态下可用', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证下拉框可交互
          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));
          expect(dropdownWidget.onChanged, isNotNull, reason: '下拉框应该可交互');

          // 验证滑块可交互
          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));
          expect(sliderWidget.onChanged, isNotNull, reason: '滑块应该可交互');
        });

        testWidgets('交互组件应该反映当前数据状态', (tester) async {
          // 创建特定状态的测试数据
          Get.reset();
          mockController = MockScanSettingsController();
          final testModel = createTestModel(useSound: true, scanMusicList: 2, soundValue: 75.0);
          when(mockController.model).thenReturn(testModel.obs);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onInit()).thenAnswer((_) async {});
          when(mockController.onClose()).thenAnswer((_) async {});
          when(mockController.onReady()).thenAnswer((_) async {});
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证下拉框当前值
          final dropdownWidget = tester.widget<DropdownButton<int>>(find.byKey(const Key('sound_music_dropdown')));
          expect(dropdownWidget.value, equals(2));

          // 验证滑块当前值
          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('sound_volume_slider')));
          expect(sliderWidget.value, equals(75.0));
        });

        testWidgets('交互组件应该在 useSound=false 时不可见', (tester) async {
          // 创建 useSound=false 的测试数据
          Get.reset();
          mockController = MockScanSettingsController();
          final testModel = createTestModel(useSound: false);
          when(mockController.model).thenReturn(testModel.obs);
          when(mockController.onStart).thenReturn(mockInternalFinalCallback);
          when(mockController.onInit()).thenAnswer((_) async {});
          when(mockController.onClose()).thenAnswer((_) async {});
          when(mockController.onReady()).thenAnswer((_) async {});
          Get.put<ScanSettingsController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证交互组件都不可见
          expect(find.byKey(const Key('sound_music_dropdown')), findsNothing);
          expect(find.byKey(const Key('sound_volume_slider')), findsNothing);
        });

        testWidgets('交互组件应该具有正确的可访问性配置', (tester) async {
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证下拉框的可访问性
          final dropdownFinder = find.byKey(const Key('sound_music_dropdown'));
          expect(dropdownFinder, findsOneWidget);

          final dropdownWidget = tester.widget<DropdownButton<int>>(dropdownFinder);
          expect(dropdownWidget.items?.length, equals(2), reason: '下拉框应该有正确数量的选项');

          // 验证滑块的可访问性
          final sliderFinder = find.byKey(const Key('sound_volume_slider'));
          expect(sliderFinder, findsOneWidget);

          final sliderWidget = tester.widget<Slider>(sliderFinder);
          expect(sliderWidget.min, equals(0.0));
          expect(sliderWidget.max, equals(100.0));
          expect(sliderWidget.value, inInclusiveRange(0.0, 100.0));
        });
      });
    });
  });
}
