import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/action_scan_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_model_and_unread_hint_usecase.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_page_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/states/home_ui_state.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'home_controller_test.mocks.dart';

// 生成 Mock 类
@GenerateMocks([
  GetHomeModelAndUnreadHintUseCase,
  GetHomePageListUseCase,
  GetRoleListUseCase,
  TabsController,
  ActionScanService,
  NavigationService,
])
void main() {
  setUpAll(() {
    // 初始化 LogUtil 避免测试失败
    LogUtil.initialize();
  });

  group('MyHomeController Tests', () {
    // Phase 1: 基础设施测试
    group('Phase 1: 基础设施测试', () {
      late MyHomeController controller;
      late MockGetHomeModelAndUnreadHintUseCase mockGetHomeModelAndUnreadHintUseCase;
      late MockGetHomePageListUseCase mockGetHomePageListUseCase;
      late MockGetRoleListUseCase mockGetRoleListUseCase;
      late MockTabsController mockTabsController;
      late MockActionScanService mockActionScanService;
      late MockNavigationService mockNavigationService;

      setUp(() {
        // 创建所有必需的mock对象
        mockGetHomeModelAndUnreadHintUseCase = MockGetHomeModelAndUnreadHintUseCase();
        mockGetHomePageListUseCase = MockGetHomePageListUseCase();
        mockGetRoleListUseCase = MockGetRoleListUseCase();
        mockTabsController = MockTabsController();
        mockActionScanService = MockActionScanService();
        mockNavigationService = MockNavigationService();

        // 设置NavigationService的stub
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'), id: anyNamed('id')),
        ).thenAnswer((_) async => null);

        // 创建控制器实例
        controller = MyHomeController(
          getHomeModelAndUnreadHintUseCase: mockGetHomeModelAndUnreadHintUseCase,
          getHomePageListUseCase: mockGetHomePageListUseCase,
          getRoleListUseCase: mockGetRoleListUseCase,
          tabsController: mockTabsController,
          actionScanService: mockActionScanService,
          navigationService: mockNavigationService,
        );
      });

      group('构造函数和依赖注入验证', () {
        test('should inject all dependencies correctly', () {
          // Assert - 验证所有依赖项都正确注入
          expect(controller.getHomeModelAndUnreadHintUseCase, equals(mockGetHomeModelAndUnreadHintUseCase));
          expect(controller.getHomePageListUseCase, equals(mockGetHomePageListUseCase));
          expect(controller.getRoleListUseCase, equals(mockGetRoleListUseCase));
          expect(controller.tabsController, equals(mockTabsController));
          expect(controller.actionScanService, equals(mockActionScanService));
          expect(controller.navigationService, equals(mockNavigationService));
        });

        test('should initialize HomeUIState correctly', () {
          // Assert - 验证 HomeUIState 正确初始化
          expect(controller.state, isNotNull);
          expect(controller.state, isA<HomeUIState>());
        });

        test('should have correct initial state values', () {
          // Assert - 验证初始状态值
          expect(controller.state.showRedDot.value, false);
          expect(controller.state.homePageList.value, isEmpty);
          expect(controller.state.isNoPermission.value, false);
          expect(controller.state.isAllowTalk.value, false);
          expect(controller.state.homeModel.value, isNull);
          expect(controller.state.homeModelLoading.value, true);
          expect(controller.state.homePageListLoading.value, true);
          expect(controller.state.unreadCount.value, 0);
          expect(controller.state.unreadCountText.value, '');
          expect(controller.state.latestReceiveDate.value, '');
        });
      });

      group('resetHomePageData 方法测试', () {
        test('should reset all state values to defaults', () {
          // Arrange - 设置一些非默认值
          controller.state.showRedDot.value = true;
          controller.state.homePageList.add('test item');
          controller.state.isNoPermission.value = true;
          controller.state.isAllowTalk.value = true;
          controller.state.homeModel.value = HomeModel(
            talkBadgeNum: 5,
            saveTemporaryActionCount: 2,
            saveTemporaryProcessCount: 3,
            unApproveProcessCount: 1,
            saveTemporaryActionDisplayCount: '2',
            saveTemporaryProcessDisplayCount: '3',
            unApproveProcessDisplayCount: '1',
            saveTemporaryActionLatestDate: '2024-01-01',
            saveTemporaryProcessLatestDate: '2024-01-02',
            unApproveProcessLatestDate: '2024-01-03',
            assetActionList: [],
            workflowList: [],
          );
          controller.state.homeModelLoading.value = false;
          controller.state.homePageListLoading.value = false;

          // Act - 调用重置方法
          controller.resetHomePageData();

          // Assert - 验证所有状态都被重置
          expect(controller.state.homePageList.value, isEmpty);
          expect(controller.state.homeModel.value, isNull);
          expect(controller.state.isAllowTalk.value, false);
          expect(controller.state.homeModelLoading.value, true);
          expect(controller.state.homePageListLoading.value, true);
          expect(controller.state.showRedDot.value, false);
        });

        test('should clear homePageList properly', () {
          // Arrange - 添加一些测试数据
          controller.state.homePageList.addAll(['item1', 'item2', 'item3']);
          expect(controller.state.homePageList.length, 3);

          // Act
          controller.resetHomePageData();

          // Assert
          expect(controller.state.homePageList.value, isEmpty);
          expect(controller.state.homePageList.length, 0);
        });

        test('should set loading states to true', () {
          // Arrange - 设置加载状态为false
          controller.state.homeModelLoading.value = false;
          controller.state.homePageListLoading.value = false;

          // Act
          controller.resetHomePageData();

          // Assert - 验证加载状态被设置为true
          expect(controller.state.homeModelLoading.value, true);
          expect(controller.state.homePageListLoading.value, true);
        });

        test('should be callable multiple times safely', () {
          // Arrange - 设置一些状态
          controller.state.showRedDot.value = true;
          controller.state.homePageList.add('test');

          // Act - 多次调用
          controller.resetHomePageData();
          controller.resetHomePageData();
          controller.resetHomePageData();

          // Assert - 状态仍然正确
          expect(controller.state.showRedDot.value, false);
          expect(controller.state.homePageList.value, isEmpty);
          expect(controller.state.homeModelLoading.value, true);
          expect(controller.state.homePageListLoading.value, true);
        });
      });

      group('pushToNative 静态方法测试', () {
        test('should be callable without throwing exception', () {
          // Act & Assert - 验证静态方法可以调用且不抛出异常
          expect(() => MyHomeController.pushToNative(), returnsNormally);
        });

        test('should return void', () {
          // Act
          final result = MyHomeController.pushToNative();

          // Assert - 验证返回值为void
          expect(result, isNull);
        });

        test('should be accessible without controller instance', () {
          // Act & Assert - 验证可以通过类名直接调用
          expect(() => MyHomeController.pushToNative(), returnsNormally);
        });

        test('should not affect controller state when called', () {
          // Arrange - 设置一些状态
          controller.state.showRedDot.value = true;
          controller.state.homePageList.add('test item');

          // Act - 调用静态方法
          MyHomeController.pushToNative();

          // Assert - 验证状态没有改变
          expect(controller.state.showRedDot.value, true);
          expect(controller.state.homePageList.length, 1);
          expect(controller.state.homePageList.first, 'test item');
        });
      });

      // Phase 3: 导航方法测试
      group('Phase 3: 导航方法测试', () {
        late MyHomeController controller;
        late MockGetHomeModelAndUnreadHintUseCase mockGetHomeModelAndUnreadHintUseCase;
        late MockGetHomePageListUseCase mockGetHomePageListUseCase;
        late MockGetRoleListUseCase mockGetRoleListUseCase;
        late MockTabsController mockTabsController;
        late MockActionScanService mockActionScanService;
        late MockNavigationService mockNavigationService;

        setUp(() {
          // 创建所有必需的mock对象
          mockGetHomeModelAndUnreadHintUseCase = MockGetHomeModelAndUnreadHintUseCase();
          mockGetHomePageListUseCase = MockGetHomePageListUseCase();
          mockGetRoleListUseCase = MockGetRoleListUseCase();
          mockTabsController = MockTabsController();
          mockActionScanService = MockActionScanService();
          mockNavigationService = MockNavigationService();

          // 设置NavigationService的stub
          when(
            mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'), id: anyNamed('id')),
          ).thenAnswer((_) async => null);

          // 设置TabsController的stub
          when(mockTabsController.handleTabTap(any)).thenReturn(null);

          // 创建控制器实例
          controller = MyHomeController(
            getHomeModelAndUnreadHintUseCase: mockGetHomeModelAndUnreadHintUseCase,
            getHomePageListUseCase: mockGetHomePageListUseCase,
            getRoleListUseCase: mockGetRoleListUseCase,
            tabsController: mockTabsController,
            actionScanService: mockActionScanService,
            navigationService: mockNavigationService,
          );
        });

        group('基础导航方法测试', () {
          test('goMyPage should navigate to me page', () {
            // Act
            controller.goMyPage();

            // Assert
            verify(mockNavigationService.navigateTo(AutoRoutes.me)).called(1);
          });

          test('toMessage should clear red dot and navigate to message page', () {
            // Arrange - 设置红点状态
            controller.state.showRedDot.value = true;

            // Act
            controller.toMessage();

            // Assert
            expect(controller.state.showRedDot.value, false);
            verify(mockNavigationService.navigateTo(AutoRoutes.meMessagePage)).called(1);
          });

          test('toMessage should navigate even when red dot is already false', () {
            // Arrange - 红点已经是false
            controller.state.showRedDot.value = false;

            // Act
            controller.toMessage();

            // Assert
            expect(controller.state.showRedDot.value, false);
            verify(mockNavigationService.navigateTo(AutoRoutes.meMessagePage)).called(1);
          });

          test('toMessage should not affect other state values', () {
            // Arrange
            controller.state.homePageList.add('test item');
            controller.state.isAllowTalk.value = true;

            // Act
            controller.toMessage();

            // Assert - 验证其他状态不受影响
            expect(controller.state.homePageList.length, 1);
            expect(controller.state.isAllowTalk.value, true);
          });
        });

        group('复杂导航方法测试', () {
          test('onUnApproveProcessClick should handle tab tap and navigate with correct arguments', () {
            // Act
            controller.onUnApproveProcessClick();

            // Assert
            verify(mockTabsController.handleTabTap(SharedNavBarEnum.workflow)).called(1);
            verify(
              mockNavigationService.navigateTo(
                AutoRoutes.appTabWorkflowTabs,
                id: SharedNavBarEnum.workflow.navigatorId,
                arguments: {'fromHomePage': true, 'initialIndex': 2},
              ),
            ).called(1);
          });

          test('onSaveTemporaryProcessClick should handle tab tap and navigate with correct arguments', () {
            // Act
            controller.onSaveTemporaryProcessClick();

            // Assert
            verify(mockTabsController.handleTabTap(SharedNavBarEnum.workflow)).called(1);
            verify(
              mockNavigationService.navigateTo(
                AutoRoutes.appTabWorkflowTabs,
                id: SharedNavBarEnum.workflow.navigatorId,
                arguments: {'fromHomePage': true, 'initialIndex': 1},
              ),
            ).called(1);
          });

          test('onSaveTemporaryActionClick should handle tab tap and navigate with correct arguments', () {
            // Act
            controller.onSaveTemporaryActionClick();

            // Assert
            verify(mockTabsController.handleTabTap(SharedNavBarEnum.actionList)).called(1);
            verify(
              mockNavigationService.navigateTo(
                AutoRoutes.actionProgressList,
                id: SharedNavBarEnum.actionList.navigatorId,
                arguments: {'fromHomePage': true},
              ),
            ).called(1);
          });

          test('navigation methods should not affect controller state', () {
            // Arrange - 设置一些状态
            controller.state.showRedDot.value = true;
            controller.state.homePageList.add('test item');

            // Act - 调用所有导航方法（除了toMessage，它会改变showRedDot）
            controller.goMyPage();
            controller.onUnApproveProcessClick();
            controller.onSaveTemporaryProcessClick();
            controller.onSaveTemporaryActionClick();

            // Assert - 验证状态不受影响（除了showRedDot）
            expect(controller.state.showRedDot.value, true);
            expect(controller.state.homePageList.length, 1);
            expect(controller.state.homePageList.first, 'test item');
          });

          test('multiple calls to same navigation method should work correctly', () {
            // Act - 多次调用同一个导航方法
            controller.onUnApproveProcessClick();
            controller.onUnApproveProcessClick();

            // Assert - 验证每次调用都正常执行
            verify(mockTabsController.handleTabTap(SharedNavBarEnum.workflow)).called(2);
            verify(
              mockNavigationService.navigateTo(
                AutoRoutes.appTabWorkflowTabs,
                id: SharedNavBarEnum.workflow.navigatorId,
                arguments: {'fromHomePage': true, 'initialIndex': 2},
              ),
            ).called(2);
          });
        });
      });

      // Phase 4: 数据加载方法测试
      group('Phase 4: 数据加载方法测试', () {
        late MyHomeController controller;
        late MockGetHomeModelAndUnreadHintUseCase mockGetHomeModelAndUnreadHintUseCase;
        late MockGetHomePageListUseCase mockGetHomePageListUseCase;
        late MockGetRoleListUseCase mockGetRoleListUseCase;
        late MockTabsController mockTabsController;
        late MockActionScanService mockActionScanService;
        late MockNavigationService mockNavigationService;

        setUp(() {
          // 创建所有必需的mock对象
          mockGetHomeModelAndUnreadHintUseCase = MockGetHomeModelAndUnreadHintUseCase();
          mockGetHomePageListUseCase = MockGetHomePageListUseCase();
          mockGetRoleListUseCase = MockGetRoleListUseCase();
          mockTabsController = MockTabsController();
          mockActionScanService = MockActionScanService();
          mockNavigationService = MockNavigationService();

          // 设置基础stub
          when(
            mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'), id: anyNamed('id')),
          ).thenAnswer((_) async => null);
          when(mockTabsController.handleTabTap(any)).thenReturn(null);
          when(
            mockTabsController.updateUnreadHints(workflow: anyNamed('workflow'), actionTask: anyNamed('actionTask')),
          ).thenReturn(null);

          // 创建控制器实例
          controller = MyHomeController(
            getHomeModelAndUnreadHintUseCase: mockGetHomeModelAndUnreadHintUseCase,
            getHomePageListUseCase: mockGetHomePageListUseCase,
            getRoleListUseCase: mockGetRoleListUseCase,
            tabsController: mockTabsController,
            actionScanService: mockActionScanService,
            navigationService: mockNavigationService,
          );
        });

        group('getHomePageList 方法测试', () {
          test('should set loading true, call usecase, update list and set loading false (success)', () async {
            // Arrange
            final testList = ['item1', 'item2', 'item3'];
            when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => testList);
            controller.state.homePageList.value = [];
            controller.state.homePageListLoading.value = false;

            // Act
            await controller.getHomePageList();

            // Assert
            expect(controller.state.homePageListLoading.value, false);
            expect(controller.state.homePageList.value, testList);
            verify(mockGetHomePageListUseCase.call(null)).called(1);
          });

          test('should clear list and set loading false when usecase throws exception', () async {
            // Arrange
            controller.state.homePageList.value = ['existing1', 'existing2'];
            controller.state.homePageListLoading.value = false;
            when(mockGetHomePageListUseCase.call(null)).thenThrow(Exception('Network error'));

            // Act & Assert - 现在异常会被rethrow，所以需要捕获异常
            try {
              await controller.getHomePageList();
            } catch (e) {
              // 异常被捕获，这是预期的行为
              expect(e, isA<Exception>());
            }

            // Assert
            expect(controller.state.homePageListLoading.value, false);
            expect(controller.state.homePageList.value, isEmpty);
            verify(mockGetHomePageListUseCase.call(null)).called(1);
          });

          test('should handle empty list from usecase correctly', () async {
            // Arrange
            when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => <String>[]);
            controller.state.homePageList.value = ['existing'];

            // Act
            await controller.getHomePageList();

            // Assert
            expect(controller.state.homePageListLoading.value, false);
            expect(controller.state.homePageList.value, isEmpty);
            verify(mockGetHomePageListUseCase.call(null)).called(1);
          });

          test('should not affect other state values during loading', () async {
            // Arrange
            final testList = ['newItem'];
            when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => testList);
            controller.state.showRedDot.value = true;
            controller.state.isAllowTalk.value = true;
            controller.state.homeModel.value = HomeModel(
              talkBadgeNum: 0,
              saveTemporaryActionCount: 0,
              saveTemporaryProcessCount: 0,
              unApproveProcessCount: 0,
              saveTemporaryActionDisplayCount: '0',
              saveTemporaryProcessDisplayCount: '0',
              unApproveProcessDisplayCount: '0',
              saveTemporaryActionLatestDate: '',
              saveTemporaryProcessLatestDate: '',
              unApproveProcessLatestDate: '',
              assetActionList: [],
              workflowList: [],
            );

            // Act
            await controller.getHomePageList();

            // Assert - 验证其他状态不受影响
            expect(controller.state.showRedDot.value, true);
            expect(controller.state.isAllowTalk.value, true);
            expect(controller.state.homeModel.value, isNotNull);
          });

          test('should always set loading to false even if usecase throws', () async {
            // Arrange
            when(mockGetHomePageListUseCase.call(null)).thenThrow(Exception('Test error'));
            controller.state.homePageListLoading.value = false;

            // Act & Assert - 现在异常会被rethrow，所以需要捕获异常
            try {
              await controller.getHomePageList();
            } catch (e) {
              // 异常被捕获，这是预期的行为
              expect(e, isA<Exception>());
            }

            // Assert
            expect(controller.state.homePageListLoading.value, false);
          });
        });

        group('getHomePage 方法测试', () {
          test('should load home page data successfully and update all states', () async {
            // Arrange
            final mockHomeModel = HomeModel(
              talkBadgeNum: 5,
              saveTemporaryActionCount: 2,
              saveTemporaryProcessCount: 1,
              unApproveProcessCount: 3,
              saveTemporaryActionDisplayCount: '2',
              saveTemporaryProcessDisplayCount: '1',
              unApproveProcessDisplayCount: '3',
              saveTemporaryActionLatestDate: '2024-01-01',
              saveTemporaryProcessLatestDate: '2024-01-02',
              unApproveProcessLatestDate: '2024-01-03',
              assetActionList: [],
              workflowList: [],
            );
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: mockHomeModel,
              talkAuthority: true,
              showWorkflowUnreadHintSubject: true,
              showActionTaskUnreadHintSubject: true,
              showHomeMessageUnreadHintSubject: true,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

            // Act
            await controller.getHomePage();

            // Assert
            expect(controller.state.homeModelLoading.value, false);
            expect(controller.state.isAllowTalk.value, true);
            expect(controller.state.showRedDot.value, true);
            expect(controller.state.homeModel.value, mockHomeModel);
            verify(mockGetHomeModelAndUnreadHintUseCase.call(null)).called(1);
            verify(mockTabsController.updateUnreadHints(workflow: true, actionTask: true)).called(1);
          });

          test('should handle false boolean values correctly', () async {
            // Arrange
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: null,
              talkAuthority: false,
              showWorkflowUnreadHintSubject: false,
              showActionTaskUnreadHintSubject: false,
              showHomeMessageUnreadHintSubject: false,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

            // Act
            await controller.getHomePage();

            // Assert
            expect(controller.state.homeModelLoading.value, false);
            expect(controller.state.isAllowTalk.value, false);
            expect(controller.state.showRedDot.value, false);
            expect(controller.state.homeModel.value, null);
            verify(mockTabsController.updateUnreadHints(workflow: false, actionTask: false)).called(1);
          });

          test('should set loading false and not update states when usecase throws', () async {
            // Arrange
            controller.state.homeModelLoading.value = true;
            controller.state.isAllowTalk.value = true;
            controller.state.showRedDot.value = true;
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenThrow(Exception('API error'));

            // Act & Assert - 现在异常会被rethrow，所以需要捕获异常
            try {
              await controller.getHomePage();
            } catch (e) {
              // 异常被捕获，这是预期的行为
              expect(e, isA<Exception>());
            }

            // Assert
            expect(controller.state.homeModelLoading.value, false);
            // 状态应该保持原值，因为发生了异常
            expect(controller.state.isAllowTalk.value, true);
            expect(controller.state.showRedDot.value, true);
            verify(mockGetHomeModelAndUnreadHintUseCase.call(null)).called(1);
            // 异常时不应该调用updateUnreadHints
            verifyNever(
              mockTabsController.updateUnreadHints(workflow: anyNamed('workflow'), actionTask: anyNamed('actionTask')),
            );
          });

          test('should not affect other state values during loading', () async {
            // Arrange
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: HomeModel(
                talkBadgeNum: 1,
                saveTemporaryActionCount: 0,
                saveTemporaryProcessCount: 0,
                unApproveProcessCount: 0,
                saveTemporaryActionDisplayCount: '0',
                saveTemporaryProcessDisplayCount: '0',
                unApproveProcessDisplayCount: '0',
                saveTemporaryActionLatestDate: '',
                saveTemporaryProcessLatestDate: '',
                unApproveProcessLatestDate: '',
                assetActionList: [],
                workflowList: [],
              ),
              talkAuthority: true,
              showWorkflowUnreadHintSubject: false,
              showActionTaskUnreadHintSubject: false,
              showHomeMessageUnreadHintSubject: false,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);
            controller.state.homePageList.add('test item');
            controller.state.isNoPermission.value = true;

            // Act
            await controller.getHomePage();

            // Assert - 验证不相关的状态不受影响
            expect(controller.state.homePageList.length, 1);
            expect(controller.state.homePageList.first, 'test item');
            expect(controller.state.isNoPermission.value, true);
          });

          test('should always set homeModelLoading to false even when exception occurs', () async {
            // Arrange
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenThrow(Exception('Network error'));
            controller.state.homeModelLoading.value = true;

            // Act & Assert - 现在异常会被rethrow，所以需要捕获异常
            try {
              await controller.getHomePage();
            } catch (e) {
              // 异常被捕获，这是预期的行为
              expect(e, isA<Exception>());
            }

            // Assert
            expect(controller.state.homeModelLoading.value, false);
          });

          test('should handle null homeModel correctly', () async {
            // Arrange
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: null,
              talkAuthority: true,
              showWorkflowUnreadHintSubject: true,
              showActionTaskUnreadHintSubject: true,
              showHomeMessageUnreadHintSubject: true,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

            // Act
            await controller.getHomePage();

            // Assert
            expect(controller.state.homeModel.value, null);
            expect(controller.state.isAllowTalk.value, true);
            expect(controller.state.showRedDot.value, true);
          });
        });
      });

      // Phase 5: 刷新相关方法测试
      group('Phase 5: 刷新相关方法测试', () {
        late MyHomeController controller;
        late MockGetHomeModelAndUnreadHintUseCase mockGetHomeModelAndUnreadHintUseCase;
        late MockGetHomePageListUseCase mockGetHomePageListUseCase;
        late MockGetRoleListUseCase mockGetRoleListUseCase;
        late MockTabsController mockTabsController;
        late MockActionScanService mockActionScanService;
        late MockNavigationService mockNavigationService;

        setUp(() {
          // 创建所有必需的mock对象
          mockGetHomeModelAndUnreadHintUseCase = MockGetHomeModelAndUnreadHintUseCase();
          mockGetHomePageListUseCase = MockGetHomePageListUseCase();
          mockGetRoleListUseCase = MockGetRoleListUseCase();
          mockTabsController = MockTabsController();
          mockActionScanService = MockActionScanService();
          mockNavigationService = MockNavigationService();

          // 设置基础stub
          when(
            mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'), id: anyNamed('id')),
          ).thenAnswer((_) async => null);
          when(mockTabsController.handleTabTap(any)).thenReturn(null);
          when(
            mockTabsController.updateUnreadHints(workflow: anyNamed('workflow'), actionTask: anyNamed('actionTask')),
          ).thenReturn(null);

          // 创建控制器实例
          controller = MyHomeController(
            getHomeModelAndUnreadHintUseCase: mockGetHomeModelAndUnreadHintUseCase,
            getHomePageListUseCase: mockGetHomePageListUseCase,
            getRoleListUseCase: mockGetRoleListUseCase,
            tabsController: mockTabsController,
            actionScanService: mockActionScanService,
            navigationService: mockNavigationService,
          );
        });

        group('doRefresh 方法测试', () {
          test('should set both loading states to true and call loadData', () async {
            // Arrange - 设置loadData所需的mocks
            when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1', 'role2']);
            when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => ['item1']);
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: HomeModel(
                talkBadgeNum: 1,
                saveTemporaryActionCount: 0,
                saveTemporaryProcessCount: 0,
                unApproveProcessCount: 0,
                saveTemporaryActionDisplayCount: '0',
                saveTemporaryProcessDisplayCount: '0',
                unApproveProcessDisplayCount: '0',
                saveTemporaryActionLatestDate: '',
                saveTemporaryProcessLatestDate: '',
                unApproveProcessLatestDate: '',
                assetActionList: [],
                workflowList: [],
              ),
              talkAuthority: true,
              showWorkflowUnreadHintSubject: false,
              showActionTaskUnreadHintSubject: false,
              showHomeMessageUnreadHintSubject: false,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

            // 初始状态设为false
            controller.state.homeModelLoading.value = false;
            controller.state.homePageListLoading.value = false;

            // Act
            await controller.doRefresh();

            // Assert - 验证loading状态被设置且loadData被调用
            // loadData完成后，loading应该为false（因为loadData内部会设置）
            expect(controller.state.homeModelLoading.value, false);
            expect(controller.state.homePageListLoading.value, false);

            // 验证所有相关usecase都被调用（通过loadData）
            verify(mockGetRoleListUseCase.call(null)).called(1);
            verify(mockGetHomePageListUseCase.call(null)).called(1);
            verify(mockGetHomeModelAndUnreadHintUseCase.call(null)).called(1);
          });

          test('should handle when loadData fails during refresh', () async {
            // Arrange - 设置getRoleListUseCase失败
            when(mockGetRoleListUseCase.call(null)).thenThrow(Exception('Network error'));

            // Act
            await controller.doRefresh();

            // Assert - 验证即使loadData失败，doRefresh也不会抛异常
            // loadData失败时会设置isNoPermission为true
            expect(controller.state.isNoPermission.value, true);
            verify(mockGetRoleListUseCase.call(null)).called(1);
          });

          test('should reset state before calling loadData', () async {
            // Arrange - 设置loadData成功
            when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1']);
            when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => ['item1']);
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: null,
              talkAuthority: false,
              showWorkflowUnreadHintSubject: false,
              showActionTaskUnreadHintSubject: false,
              showHomeMessageUnreadHintSubject: false,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

            // 设置一些初始状态
            controller.state.homePageList.add('existing');
            controller.state.homeModel.value = HomeModel(
              talkBadgeNum: 99,
              saveTemporaryActionCount: 5,
              saveTemporaryProcessCount: 3,
              unApproveProcessCount: 2,
              saveTemporaryActionDisplayCount: '5',
              saveTemporaryProcessDisplayCount: '3',
              unApproveProcessDisplayCount: '2',
              saveTemporaryActionLatestDate: '2024-01-01',
              saveTemporaryProcessLatestDate: '2024-01-02',
              unApproveProcessLatestDate: '2024-01-03',
              assetActionList: [],
              workflowList: [],
            );

            // Act
            await controller.doRefresh();

            // Assert - 验证状态被重置（通过loadData中的resetHomePageData）
            expect(controller.state.homePageList.value, ['item1']); // 新加载的数据
            expect(controller.state.homeModel.value, null); // 被重置后重新设置
          });

          test('should handle empty role list during refresh', () async {
            // Arrange - 设置空角色列表
            when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => <String>[]);

            // Act
            await controller.doRefresh();

            // Assert - 验证无权限状态被设置
            expect(controller.state.isNoPermission.value, true);
            verify(mockGetRoleListUseCase.call(null)).called(1);
            // 空角色列表时不应该调用其他usecase
            verifyNever(mockGetHomePageListUseCase.call(null));
            verifyNever(mockGetHomeModelAndUnreadHintUseCase.call(null));
          });

          test('should not affect unrelated state during refresh', () async {
            // Arrange
            when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1']);
            when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => ['item1']);
            final mockParams = GetHomeModelAndUnreadHintParams(
              homeModel: null,
              talkAuthority: true,
              showWorkflowUnreadHintSubject: true,
              showActionTaskUnreadHintSubject: true,
              showHomeMessageUnreadHintSubject: true,
            );
            when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

            // 设置一些不相关的状态
            controller.state.showRedDot.value = false; // 这会被loadData更新
            controller.state.isAllowTalk.value = false; // 这会被loadData更新

            // Act
            await controller.doRefresh();

            // Assert - 验证相关状态被更新（因为loadData会更新它们）
            expect(controller.state.showRedDot.value, true);
            expect(controller.state.isAllowTalk.value, true);
          });
        });

        // Phase 6: 最复杂的业务方法测试
        group('Phase 6: 最复杂的业务方法测试', () {
          late MyHomeController controller;
          late MockGetHomeModelAndUnreadHintUseCase mockGetHomeModelAndUnreadHintUseCase;
          late MockGetHomePageListUseCase mockGetHomePageListUseCase;
          late MockGetRoleListUseCase mockGetRoleListUseCase;
          late MockTabsController mockTabsController;
          late MockActionScanService mockActionScanService;
          late MockNavigationService mockNavigationService;

          setUp(() {
            // 创建所有必需的mock对象
            mockGetHomeModelAndUnreadHintUseCase = MockGetHomeModelAndUnreadHintUseCase();
            mockGetHomePageListUseCase = MockGetHomePageListUseCase();
            mockGetRoleListUseCase = MockGetRoleListUseCase();
            mockTabsController = MockTabsController();
            mockActionScanService = MockActionScanService();
            mockNavigationService = MockNavigationService();

            // 设置基础stub
            when(
              mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'), id: anyNamed('id')),
            ).thenAnswer((_) async => null);
            when(mockTabsController.handleTabTap(any)).thenReturn(null);
            when(
              mockTabsController.updateUnreadHints(workflow: anyNamed('workflow'), actionTask: anyNamed('actionTask')),
            ).thenReturn(null);

            // 创建控制器实例
            controller = MyHomeController(
              getHomeModelAndUnreadHintUseCase: mockGetHomeModelAndUnreadHintUseCase,
              getHomePageListUseCase: mockGetHomePageListUseCase,
              getRoleListUseCase: mockGetRoleListUseCase,
              tabsController: mockTabsController,
              actionScanService: mockActionScanService,
              navigationService: mockNavigationService,
            );
          });

          group('loadData 方法测试', () {
            test('should successfully load all data when user has permissions', () async {
              // Arrange - 设置成功的场景
              when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1', 'role2']);
              when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => ['item1', 'item2']);
              final mockParams = GetHomeModelAndUnreadHintParams(
                homeModel: HomeModel(
                  talkBadgeNum: 3,
                  saveTemporaryActionCount: 1,
                  saveTemporaryProcessCount: 2,
                  unApproveProcessCount: 1,
                  saveTemporaryActionDisplayCount: '1',
                  saveTemporaryProcessDisplayCount: '2',
                  unApproveProcessDisplayCount: '1',
                  saveTemporaryActionLatestDate: '2024-01-01',
                  saveTemporaryProcessLatestDate: '2024-01-02',
                  unApproveProcessLatestDate: '2024-01-03',
                  assetActionList: [],
                  workflowList: [],
                ),
                talkAuthority: true,
                showWorkflowUnreadHintSubject: true,
                showActionTaskUnreadHintSubject: true,
                showHomeMessageUnreadHintSubject: true,
              );
              when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

              // 设置一些初始状态
              controller.state.homePageList.add('old item');
              controller.state.isNoPermission.value = true;

              // Act
              await controller.loadData();

              // Assert - 验证完整的成功流程
              expect(controller.state.isNoPermission.value, false);
              expect(controller.state.homePageList.value, ['item1', 'item2']);
              expect(controller.state.homeModel.value, isNotNull);
              expect(controller.state.isAllowTalk.value, true);
              expect(controller.state.showRedDot.value, true);
              expect(controller.state.homeModelLoading.value, false);
              expect(controller.state.homePageListLoading.value, false);

              // 验证所有usecase都被调用
              verify(mockGetRoleListUseCase.call(null)).called(1);
              verify(mockGetHomePageListUseCase.call(null)).called(1);
              verify(mockGetHomeModelAndUnreadHintUseCase.call(null)).called(1);
              verify(mockTabsController.updateUnreadHints(workflow: true, actionTask: true)).called(1);
            });

            test('should set isNoPermission true when role list is empty', () async {
              // Arrange - 空角色列表
              when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => <String>[]);

              // 设置初始状态
              controller.state.isNoPermission.value = false;
              controller.state.homePageList.add('existing item');

              // Act
              await controller.loadData();

              // Assert - 验证无权限处理
              expect(controller.state.isNoPermission.value, true);
              expect(controller.state.homePageList.value, isEmpty); // 被resetHomePageData清空
              expect(controller.state.homeModel.value, null);
              expect(controller.state.homeModelLoading.value, true); // 重置后的状态
              expect(controller.state.homePageListLoading.value, true);

              // 验证只调用了角色检查，没有调用其他usecase
              verify(mockGetRoleListUseCase.call(null)).called(1);
              verifyNever(mockGetHomePageListUseCase.call(null));
              verifyNever(mockGetHomeModelAndUnreadHintUseCase.call(null));
            });

            test('should handle getRoleListUseCase failure and set isNoPermission true', () async {
              // Arrange - 角色检查失败
              when(mockGetRoleListUseCase.call(null)).thenThrow(Exception('Role check failed'));

              // 设置初始状态
              controller.state.isNoPermission.value = false;

              // Act
              await controller.loadData();

              // Assert - 验证异常处理
              expect(controller.state.isNoPermission.value, true);
              verify(mockGetRoleListUseCase.call(null)).called(1);
              verifyNever(mockGetHomePageListUseCase.call(null));
              verifyNever(mockGetHomeModelAndUnreadHintUseCase.call(null));
            });

            test('should handle Future.wait failure when one of parallel operations fails', () async {
              // Arrange - 角色检查成功，但并行操作之一失败
              when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1']);
              when(mockGetHomePageListUseCase.call(null)).thenThrow(Exception('Network error'));
              // 需要为第二个并行操作也提供stub，因为Future.wait会启动两个操作
              final mockHomeModel = HomeModel(
                talkBadgeNum: 5,
                saveTemporaryActionCount: 2,
                saveTemporaryProcessCount: 1,
                unApproveProcessCount: 3,
                saveTemporaryActionDisplayCount: '2',
                saveTemporaryProcessDisplayCount: '1',
                unApproveProcessDisplayCount: '3',
                saveTemporaryActionLatestDate: '2024-01-01',
                saveTemporaryProcessLatestDate: '2024-01-02',
                unApproveProcessLatestDate: '2024-01-03',
                assetActionList: [],
                workflowList: [],
              );
              final mockParams = GetHomeModelAndUnreadHintParams(
                homeModel: mockHomeModel,
                talkAuthority: true,
                showWorkflowUnreadHintSubject: true,
                showActionTaskUnreadHintSubject: true,
                showHomeMessageUnreadHintSubject: true,
              );
              when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenAnswer((_) async => mockParams);

              // Act
              await controller.loadData();

              // Assert - 验证并行操作失败的处理
              expect(controller.state.isNoPermission.value, true);
              verify(mockGetRoleListUseCase.call(null)).called(1);
              verify(mockGetHomePageListUseCase.call(null)).called(1);
              // Future.wait失败时，两个操作都会启动，但整体会失败
            });

            test('should handle Future.wait failure when both parallel operations fail', () async {
              // Arrange - 角色检查成功，但两个并行操作都失败
              when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1']);
              when(mockGetHomePageListUseCase.call(null)).thenThrow(Exception('Network error 1'));
              when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenThrow(Exception('Network error 2'));

              // Act
              await controller.loadData();

              // Assert - 验证都失败时的处理
              expect(controller.state.isNoPermission.value, true);
              verify(mockGetRoleListUseCase.call(null)).called(1);
              verify(mockGetHomePageListUseCase.call(null)).called(1);
            });

            test('should reset state at the beginning regardless of outcome', () async {
              // Arrange - 设置一个会失败的场景
              when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => <String>[]);

              // 设置一些初始状态
              controller.state.homePageList.addAll(['item1', 'item2', 'item3']);
              controller.state.homeModel.value = HomeModel(
                talkBadgeNum: 99,
                saveTemporaryActionCount: 5,
                saveTemporaryProcessCount: 3,
                unApproveProcessCount: 2,
                saveTemporaryActionDisplayCount: '5',
                saveTemporaryProcessDisplayCount: '3',
                unApproveProcessDisplayCount: '2',
                saveTemporaryActionLatestDate: '2024-01-01',
                saveTemporaryProcessLatestDate: '2024-01-02',
                unApproveProcessLatestDate: '2024-01-03',
                assetActionList: [],
                workflowList: [],
              );
              controller.state.isAllowTalk.value = true;
              controller.state.showRedDot.value = true;
              controller.state.homeModelLoading.value = false;
              controller.state.homePageListLoading.value = false;

              // Act
              await controller.loadData();

              // Assert - 验证状态被重置（通过resetHomePageData）
              expect(controller.state.homePageList.value, isEmpty);
              expect(controller.state.homeModel.value, null);
              expect(controller.state.isAllowTalk.value, false);
              expect(controller.state.showRedDot.value, false);
              expect(controller.state.homeModelLoading.value, true);
              expect(controller.state.homePageListLoading.value, true);
              expect(controller.state.isNoPermission.value, true); // 因为角色列表为空
            });

            test('should handle partial success in Future.wait correctly', () async {
              // Arrange - 角色检查成功，一个并行操作成功，一个失败
              when(mockGetRoleListUseCase.call(null)).thenAnswer((_) async => ['role1']);
              when(mockGetHomePageListUseCase.call(null)).thenAnswer((_) async => ['success_item']);
              when(mockGetHomeModelAndUnreadHintUseCase.call(null)).thenThrow(Exception('Home model failed'));

              // Act
              await controller.loadData();

              // Assert - Future.wait遇到任何异常都会失败
              expect(controller.state.isNoPermission.value, true);
              verify(mockGetRoleListUseCase.call(null)).called(1);
              verify(mockGetHomePageListUseCase.call(null)).called(1);
              verify(mockGetHomeModelAndUnreadHintUseCase.call(null)).called(1);
            });
          });

          group('onToNewActionClick 方法测试', () {
            test('should call actionScanService.scanStartNextStep with correct parameters', () async {
              // Arrange
              final mockSharedAssetAction = SharedAssetAction(
                assetActionId: 123,
                assetActionName: 'Test Action',
                assetTypeId: 456,
                amountType: 'INVENTORY',
                processId: null,
                assetDisplayList: [],
              );
              when(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).thenAnswer((_) async => []);

              // Act
              controller.onToNewActionClick(mockSharedAssetAction);

              // Assert - 验证调用参数
              final captured = verify(
                mockActionScanService.scanStartNextStep(clickNextStepData: captureAnyNamed('clickNextStepData')),
              ).captured;

              expect(captured.length, 1);
              final clickNextStepData = captured[0] as ClickNextStepDataModel;
              expect(clickNextStepData.data, mockSharedAssetAction);
              expect(clickNextStepData.actionLabel, '');
              expect(clickNextStepData.tempActionListType, 2);
              expect(clickNextStepData.isNewAction, true);
              expect(clickNextStepData.isFromHome, true);
              expect(clickNextStepData.isFromTakeMiddleButtonPage, false);
              expect(clickNextStepData.searchKey, '');
              expect(clickNextStepData.isNeedJump, true);
            });

            test('should handle different SharedAssetAction objects correctly', () async {
              // Arrange
              final mockAction1 = SharedAssetAction(
                assetActionId: 111,
                assetActionName: 'Action 1',
                assetTypeId: 222,
                amountType: 'NORMAL',
                processId: 333,
                assetDisplayList: [],
              );
              final mockAction2 = SharedAssetAction(
                assetActionId: 444,
                assetActionName: 'Action 2',
                assetTypeId: 555,
                amountType: 'INVENTORY',
                processId: null,
                assetDisplayList: [],
              );
              when(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).thenAnswer((_) async => []);

              // Act - 调用两次不同的action
              controller.onToNewActionClick(mockAction1);
              controller.onToNewActionClick(mockAction2);

              // Assert - 验证两次调用都正确
              verify(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).called(2);
            });

            test('should not affect controller state when called', () async {
              // Arrange
              final mockAction = SharedAssetAction(
                assetActionId: 999,
                assetActionName: 'Test Action',
                assetTypeId: 888,
                amountType: 'INVENTORY',
                processId: null,
                assetDisplayList: [],
              );
              when(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).thenAnswer((_) async => []);

              // 设置一些状态
              controller.state.showRedDot.value = true;
              controller.state.homePageList.add('test item');
              controller.state.isAllowTalk.value = true;

              // Act
              controller.onToNewActionClick(mockAction);

              // Assert - 验证状态不受影响
              expect(controller.state.showRedDot.value, true);
              expect(controller.state.homePageList.length, 1);
              expect(controller.state.isAllowTalk.value, true);
            });

            test('should handle actionScanService returning different results', () async {
              // Arrange
              final mockAction = SharedAssetAction(
                assetActionId: 777,
                assetActionName: 'Test Action',
                assetTypeId: 666,
                amountType: 'INVENTORY',
                processId: null,
                assetDisplayList: [],
              );

              // 测试返回不同结果
              when(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).thenAnswer((_) async => null); // 可能返回null

              // Act & Assert - 不应该抛异常
              expect(() => controller.onToNewActionClick(mockAction), returnsNormally);
              verify(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).called(1);
            });

            test('should handle actionScanService throwing exception', () async {
              // Arrange
              final mockAction = SharedAssetAction(
                assetActionId: 555,
                assetActionName: 'Test Action',
                assetTypeId: 444,
                amountType: 'INVENTORY',
                processId: null,
                assetDisplayList: [],
              );
              when(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).thenThrow(Exception('Scan service error'));

              // Act & Assert - 验证方法会抛出异常（因为onToNewActionClick没有try-catch）
              expect(() => controller.onToNewActionClick(mockAction), throwsException);
              verify(
                mockActionScanService.scanStartNextStep(clickNextStepData: anyNamed('clickNextStepData')),
              ).called(1);
            });
          });
        });
      });
    });
  });
}
