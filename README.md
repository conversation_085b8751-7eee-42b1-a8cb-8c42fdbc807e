# asset_force_mobile_v2

This is the Flutter version of **asset-force-mobile**.
これは、**asset-force-mobile**のFlutterバージョンです。

## Getting Started

Commit all the changes in your commit window unless you believe something should not be committed. 
If so, please update the .gitignore file accordingly.
コミットウィンドウに表示されているすべての変更をコミットしてください。
ただし、コミットすべきでないと思うものがあれば、.gitignore ファイルを適切に更新してください。

## Testing

### Running Tests

#### Run all tests / 全てのテストを実行
```bash
flutter test
```

#### Run specific test file / 特定のテストファイルを実行
```bash
flutter test test/features/asset/asset_list/presentation/controllers/asset_list_controller_test.dart
```

#### Run tests with verbose output / 詳細出力でテストを実行
```bash
flutter test --verbose
```

#### Run tests with compact reporter / 簡潔な形式でテストを実行
```bash
flutter test --reporter=compact
```

#### Stop on first failure / 最初の失敗で停止
```bash
flutter test --fail-fast
```

#### Run tests in watch mode / ファイル変更を監視してテストを実行
```bash
flutter test --watch
```

### Test Coverage / テストカバレッジ

#### Generate coverage data / カバレッジデータを生成
```bash
flutter test --coverage
```

#### Generate HTML coverage report / HTMLカバレッジレポートを生成
```bash
genhtml coverage/lcov.info -o coverage/html
```

#### Open coverage report in browser / ブラウザでカバレッジレポートを開く
```bash
# macOS
open coverage/html/index.html

# Windows
start coverage/html/index.html

# Linux
xdg-open coverage/html/index.html
```

#### One-line command for coverage and report / カバレッジ生成から表示まで一括実行
```bash
flutter test --coverage && genhtml coverage/lcov.info -o coverage/html && open coverage/html/index.html
```

### Useful Test Commands / 便利なテストコマンド

#### Run tests for specific directory / 特定のディレクトリのテストを実行
```bash
flutter test test/features/asset/
```

#### Run tests with name filter / 名前でフィルタしてテストを実行
```bash
flutter test --name "should update assetType"
```

#### Generate JSON test results / JSON形式のテスト結果を生成
```bash
flutter test --reporter=json > test_results.json
```

### Test File Structure / テストファイル構造

Tests are organized under the `test/` directory following the same structure as the `lib/` directory:

```
test/
├── features/
│   ├── asset/
│   │   ├── asset_list/
│   │   └── asset_type/
│   ├── home/
│   ├── search/
│   └── shared/
└── core/
```

### Test Coverage Guidelines / テストカバレッジガイドライン

- Controllers: Focus on business logic testing
- Use Cases: Test all success and error scenarios  
- Repositories: Mock external dependencies
- Models: Test serialization/deserialization

コントローラー: ビジネスロジックのテストに重点を置く
ユースケース: 成功シナリオとエラーシナリオを全てテスト
リポジトリ: 外部依存関係をモック化
モデル: シリアライゼーション/デシリアライゼーションをテスト
