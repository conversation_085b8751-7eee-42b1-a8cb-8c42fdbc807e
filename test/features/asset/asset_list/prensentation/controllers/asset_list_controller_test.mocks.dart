// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_list/prensentation/controllers/asset_list_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i12;
import 'dart:ui' as _i17;

import 'package:asset_force_mobile_v2/core/services/dialog_service.dart'
    as _i16;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i14;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i15;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_asset_type_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_list_ui_model.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/controllers/asset_type_controller.dart'
    as _i20;
import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart'
    as _i21;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_functional_processing_repository.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/search_conditions_repository.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i18;
import 'package:flutter/cupertino.dart' as _i9;
import 'package:flutter/gestures.dart' as _i10;
import 'package:flutter/rendering.dart' as _i19;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i22;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAssetTypeRepository_0 extends _i1.SmartFake
    implements _i2.AssetTypeRepository {
  _FakeAssetTypeRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetAssetTypeResult_1 extends _i1.SmartFake
    implements _i3.GetAssetTypeResult {
  _FakeGetAssetTypeResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSearchConditionsRepository_2 extends _i1.SmartFake
    implements _i4.SearchConditionsRepository {
  _FakeSearchConditionsRepository_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetSearchIdResult_3 extends _i1.SmartFake
    implements _i5.GetSearchIdResult {
  _FakeGetSearchIdResult_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetRepository_4 extends _i1.SmartFake
    implements _i6.AssetRepository {
  _FakeAssetRepository_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetFunctionalProcessingHelperRepository_5 extends _i1.SmartFake
    implements _i7.AssetFunctionalProcessingHelperRepository {
  _FakeAssetFunctionalProcessingHelperRepository_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeLoadDataResult_6 extends _i1.SmartFake
    implements _i8.LoadDataResult {
  _FakeLoadDataResult_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeScrollPosition_7 extends _i1.SmartFake
    implements _i9.ScrollPosition {
  _FakeScrollPosition_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeScrollPhysics_8 extends _i1.SmartFake implements _i9.ScrollPhysics {
  _FakeScrollPhysics_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeScrollContext_9 extends _i1.SmartFake implements _i9.ScrollContext {
  _FakeScrollContext_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeValueNotifier_10<T> extends _i1.SmartFake
    implements _i9.ValueNotifier<T> {
  _FakeValueNotifier_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeScrollHoldController_11 extends _i1.SmartFake
    implements _i9.ScrollHoldController {
  _FakeScrollHoldController_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDrag_12 extends _i1.SmartFake implements _i10.Drag {
  _FakeDrag_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeScrollMetrics_13 extends _i1.SmartFake implements _i9.ScrollMetrics {
  _FakeScrollMetrics_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetTypeListModel_14 extends _i1.SmartFake
    implements _i11.AssetTypeListModel {
  _FakeAssetTypeListModel_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetAssetTypeUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAssetTypeUseCase extends _i1.Mock
    implements _i3.GetAssetTypeUseCase {
  MockGetAssetTypeUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AssetTypeRepository get assetTypeRepository =>
      (super.noSuchMethod(
            Invocation.getter(#assetTypeRepository),
            returnValue: _FakeAssetTypeRepository_0(
              this,
              Invocation.getter(#assetTypeRepository),
            ),
          )
          as _i2.AssetTypeRepository);

  @override
  _i12.Future<_i3.GetAssetTypeResult> call(_i3.GetAssetTypeParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i12.Future<_i3.GetAssetTypeResult>.value(
              _FakeGetAssetTypeResult_1(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i12.Future<_i3.GetAssetTypeResult>);
}

/// A class which mocks [GetSearchIdUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetSearchIdUseCase extends _i1.Mock
    implements _i5.GetSearchIdUseCase {
  MockGetSearchIdUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.SearchConditionsRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeSearchConditionsRepository_2(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i4.SearchConditionsRepository);

  @override
  _i12.Future<_i5.GetSearchIdResult> call(_i5.GetAssetTypeIdParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i12.Future<_i5.GetSearchIdResult>.value(
              _FakeGetSearchIdResult_3(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i12.Future<_i5.GetSearchIdResult>);

  @override
  _i12.Future<_i5.GetSearchIdResult> swapSearchIdResultArrPlaces({
    required dynamic assetTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#swapSearchIdResultArrPlaces, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i12.Future<_i5.GetSearchIdResult>.value(
              _FakeGetSearchIdResult_3(
                this,
                Invocation.method(#swapSearchIdResultArrPlaces, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i12.Future<_i5.GetSearchIdResult>);
}

/// A class which mocks [LoadDataUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoadDataUseCase extends _i1.Mock implements _i13.LoadDataUseCase {
  MockLoadDataUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.AssetRepository get assetRepository =>
      (super.noSuchMethod(
            Invocation.getter(#assetRepository),
            returnValue: _FakeAssetRepository_4(
              this,
              Invocation.getter(#assetRepository),
            ),
          )
          as _i6.AssetRepository);

  @override
  _i2.AssetTypeRepository get assetTypeRepository =>
      (super.noSuchMethod(
            Invocation.getter(#assetTypeRepository),
            returnValue: _FakeAssetTypeRepository_0(
              this,
              Invocation.getter(#assetTypeRepository),
            ),
          )
          as _i2.AssetTypeRepository);

  @override
  _i7.AssetFunctionalProcessingHelperRepository get assetHelper =>
      (super.noSuchMethod(
            Invocation.getter(#assetHelper),
            returnValue: _FakeAssetFunctionalProcessingHelperRepository_5(
              this,
              Invocation.getter(#assetHelper),
            ),
          )
          as _i7.AssetFunctionalProcessingHelperRepository);

  @override
  _i12.Future<_i8.LoadDataResult> call(_i13.LoadDataParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i12.Future<_i8.LoadDataResult>.value(
              _FakeLoadDataResult_6(this, Invocation.method(#call, [params])),
            ),
          )
          as _i12.Future<_i8.LoadDataResult>);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i14.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i12.Future<dynamic> navigateTo(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);

  @override
  _i12.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);

  @override
  _i12.Future<bool> navigateUntil(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i12.Future<bool>.value(false),
          )
          as _i12.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Future<dynamic> toAssetDetail(_i15.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i12.Future<dynamic>.value(),
          )
          as _i12.Future<dynamic>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i16.DialogService {
  MockDialogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i12.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i17.VoidCallback? onConfirm,
    _i17.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i18.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i17.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i9.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i18.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [ScrollController].
///
/// See the documentation for Mockito's code generation for more information.
class MockScrollController extends _i1.Mock implements _i9.ScrollController {
  MockScrollController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get keepScrollOffset =>
      (super.noSuchMethod(
            Invocation.getter(#keepScrollOffset),
            returnValue: false,
          )
          as bool);

  @override
  double get initialScrollOffset =>
      (super.noSuchMethod(
            Invocation.getter(#initialScrollOffset),
            returnValue: 0.0,
          )
          as double);

  @override
  Iterable<_i9.ScrollPosition> get positions =>
      (super.noSuchMethod(
            Invocation.getter(#positions),
            returnValue: <_i9.ScrollPosition>[],
          )
          as Iterable<_i9.ScrollPosition>);

  @override
  bool get hasClients =>
      (super.noSuchMethod(Invocation.getter(#hasClients), returnValue: false)
          as bool);

  @override
  _i9.ScrollPosition get position =>
      (super.noSuchMethod(
            Invocation.getter(#position),
            returnValue: _FakeScrollPosition_7(
              this,
              Invocation.getter(#position),
            ),
          )
          as _i9.ScrollPosition);

  @override
  double get offset =>
      (super.noSuchMethod(Invocation.getter(#offset), returnValue: 0.0)
          as double);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i12.Future<void> animateTo(
    double? offset, {
    required Duration? duration,
    required _i9.Curve? curve,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #animateTo,
              [offset],
              {#duration: duration, #curve: curve},
            ),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void jumpTo(double? value) => super.noSuchMethod(
    Invocation.method(#jumpTo, [value]),
    returnValueForMissingStub: null,
  );

  @override
  void attach(_i9.ScrollPosition? position) => super.noSuchMethod(
    Invocation.method(#attach, [position]),
    returnValueForMissingStub: null,
  );

  @override
  void detach(_i9.ScrollPosition? position) => super.noSuchMethod(
    Invocation.method(#detach, [position]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i9.ScrollPosition createScrollPosition(
    _i9.ScrollPhysics? physics,
    _i9.ScrollContext? context,
    _i9.ScrollPosition? oldPosition,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createScrollPosition, [
              physics,
              context,
              oldPosition,
            ]),
            returnValue: _FakeScrollPosition_7(
              this,
              Invocation.method(#createScrollPosition, [
                physics,
                context,
                oldPosition,
              ]),
            ),
          )
          as _i9.ScrollPosition);

  @override
  void debugFillDescription(List<String>? description) => super.noSuchMethod(
    Invocation.method(#debugFillDescription, [description]),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i17.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i17.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [ScrollPosition].
///
/// See the documentation for Mockito's code generation for more information.
class MockScrollPosition extends _i1.Mock implements _i9.ScrollPosition {
  MockScrollPosition() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i9.ScrollPhysics get physics =>
      (super.noSuchMethod(
            Invocation.getter(#physics),
            returnValue: _FakeScrollPhysics_8(
              this,
              Invocation.getter(#physics),
            ),
          )
          as _i9.ScrollPhysics);

  @override
  _i9.ScrollContext get context =>
      (super.noSuchMethod(
            Invocation.getter(#context),
            returnValue: _FakeScrollContext_9(
              this,
              Invocation.getter(#context),
            ),
          )
          as _i9.ScrollContext);

  @override
  bool get keepScrollOffset =>
      (super.noSuchMethod(
            Invocation.getter(#keepScrollOffset),
            returnValue: false,
          )
          as bool);

  @override
  _i9.ValueNotifier<bool> get isScrollingNotifier =>
      (super.noSuchMethod(
            Invocation.getter(#isScrollingNotifier),
            returnValue: _FakeValueNotifier_10<bool>(
              this,
              Invocation.getter(#isScrollingNotifier),
            ),
          )
          as _i9.ValueNotifier<bool>);

  @override
  double get minScrollExtent =>
      (super.noSuchMethod(Invocation.getter(#minScrollExtent), returnValue: 0.0)
          as double);

  @override
  double get maxScrollExtent =>
      (super.noSuchMethod(Invocation.getter(#maxScrollExtent), returnValue: 0.0)
          as double);

  @override
  bool get hasContentDimensions =>
      (super.noSuchMethod(
            Invocation.getter(#hasContentDimensions),
            returnValue: false,
          )
          as bool);

  @override
  double get pixels =>
      (super.noSuchMethod(Invocation.getter(#pixels), returnValue: 0.0)
          as double);

  @override
  bool get hasPixels =>
      (super.noSuchMethod(Invocation.getter(#hasPixels), returnValue: false)
          as bool);

  @override
  double get viewportDimension =>
      (super.noSuchMethod(
            Invocation.getter(#viewportDimension),
            returnValue: 0.0,
          )
          as double);

  @override
  bool get hasViewportDimension =>
      (super.noSuchMethod(
            Invocation.getter(#hasViewportDimension),
            returnValue: false,
          )
          as bool);

  @override
  bool get haveDimensions =>
      (super.noSuchMethod(
            Invocation.getter(#haveDimensions),
            returnValue: false,
          )
          as bool);

  @override
  bool get shouldIgnorePointer =>
      (super.noSuchMethod(
            Invocation.getter(#shouldIgnorePointer),
            returnValue: false,
          )
          as bool);

  @override
  double get devicePixelRatio =>
      (super.noSuchMethod(
            Invocation.getter(#devicePixelRatio),
            returnValue: 0.0,
          )
          as double);

  @override
  bool get allowImplicitScrolling =>
      (super.noSuchMethod(
            Invocation.getter(#allowImplicitScrolling),
            returnValue: false,
          )
          as bool);

  @override
  _i19.ScrollDirection get userScrollDirection =>
      (super.noSuchMethod(
            Invocation.getter(#userScrollDirection),
            returnValue: _i19.ScrollDirection.idle,
          )
          as _i19.ScrollDirection);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i9.AxisDirection get axisDirection =>
      (super.noSuchMethod(
            Invocation.getter(#axisDirection),
            returnValue: _i9.AxisDirection.up,
          )
          as _i9.AxisDirection);

  @override
  _i9.Axis get axis =>
      (super.noSuchMethod(
            Invocation.getter(#axis),
            returnValue: _i9.Axis.horizontal,
          )
          as _i9.Axis);

  @override
  bool get outOfRange =>
      (super.noSuchMethod(Invocation.getter(#outOfRange), returnValue: false)
          as bool);

  @override
  bool get atEdge =>
      (super.noSuchMethod(Invocation.getter(#atEdge), returnValue: false)
          as bool);

  @override
  double get extentBefore =>
      (super.noSuchMethod(Invocation.getter(#extentBefore), returnValue: 0.0)
          as double);

  @override
  double get extentInside =>
      (super.noSuchMethod(Invocation.getter(#extentInside), returnValue: 0.0)
          as double);

  @override
  double get extentAfter =>
      (super.noSuchMethod(Invocation.getter(#extentAfter), returnValue: 0.0)
          as double);

  @override
  double get extentTotal =>
      (super.noSuchMethod(Invocation.getter(#extentTotal), returnValue: 0.0)
          as double);

  @override
  void absorb(_i9.ScrollPosition? other) => super.noSuchMethod(
    Invocation.method(#absorb, [other]),
    returnValueForMissingStub: null,
  );

  @override
  double setPixels(double? newPixels) =>
      (super.noSuchMethod(
            Invocation.method(#setPixels, [newPixels]),
            returnValue: 0.0,
          )
          as double);

  @override
  void correctPixels(double? value) => super.noSuchMethod(
    Invocation.method(#correctPixels, [value]),
    returnValueForMissingStub: null,
  );

  @override
  void correctBy(double? correction) => super.noSuchMethod(
    Invocation.method(#correctBy, [correction]),
    returnValueForMissingStub: null,
  );

  @override
  void forcePixels(double? value) => super.noSuchMethod(
    Invocation.method(#forcePixels, [value]),
    returnValueForMissingStub: null,
  );

  @override
  void saveScrollOffset() => super.noSuchMethod(
    Invocation.method(#saveScrollOffset, []),
    returnValueForMissingStub: null,
  );

  @override
  void restoreScrollOffset() => super.noSuchMethod(
    Invocation.method(#restoreScrollOffset, []),
    returnValueForMissingStub: null,
  );

  @override
  void restoreOffset(double? offset, {bool? initialRestore = false}) =>
      super.noSuchMethod(
        Invocation.method(
          #restoreOffset,
          [offset],
          {#initialRestore: initialRestore},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void saveOffset() => super.noSuchMethod(
    Invocation.method(#saveOffset, []),
    returnValueForMissingStub: null,
  );

  @override
  double applyBoundaryConditions(double? value) =>
      (super.noSuchMethod(
            Invocation.method(#applyBoundaryConditions, [value]),
            returnValue: 0.0,
          )
          as double);

  @override
  bool applyViewportDimension(double? viewportDimension) =>
      (super.noSuchMethod(
            Invocation.method(#applyViewportDimension, [viewportDimension]),
            returnValue: false,
          )
          as bool);

  @override
  bool applyContentDimensions(
    double? minScrollExtent,
    double? maxScrollExtent,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#applyContentDimensions, [
              minScrollExtent,
              maxScrollExtent,
            ]),
            returnValue: false,
          )
          as bool);

  @override
  bool correctForNewDimensions(
    _i9.ScrollMetrics? oldPosition,
    _i9.ScrollMetrics? newPosition,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#correctForNewDimensions, [
              oldPosition,
              newPosition,
            ]),
            returnValue: false,
          )
          as bool);

  @override
  void applyNewDimensions() => super.noSuchMethod(
    Invocation.method(#applyNewDimensions, []),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Future<void> ensureVisible(
    _i9.RenderObject? object, {
    double? alignment = 0.0,
    Duration? duration = Duration.zero,
    _i9.Curve? curve = _i9.Curves.ease,
    _i9.ScrollPositionAlignmentPolicy? alignmentPolicy =
        _i9.ScrollPositionAlignmentPolicy.explicit,
    _i9.RenderObject? targetRenderObject,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #ensureVisible,
              [object],
              {
                #alignment: alignment,
                #duration: duration,
                #curve: curve,
                #alignmentPolicy: alignmentPolicy,
                #targetRenderObject: targetRenderObject,
              },
            ),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  _i12.Future<void> animateTo(
    double? to, {
    required Duration? duration,
    required _i9.Curve? curve,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #animateTo,
              [to],
              {#duration: duration, #curve: curve},
            ),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void jumpTo(double? value) => super.noSuchMethod(
    Invocation.method(#jumpTo, [value]),
    returnValueForMissingStub: null,
  );

  @override
  void pointerScroll(double? delta) => super.noSuchMethod(
    Invocation.method(#pointerScroll, [delta]),
    returnValueForMissingStub: null,
  );

  @override
  _i12.Future<void> moveTo(
    double? to, {
    Duration? duration,
    _i9.Curve? curve,
    bool? clamp = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #moveTo,
              [to],
              {#duration: duration, #curve: curve, #clamp: clamp},
            ),
            returnValue: _i12.Future<void>.value(),
            returnValueForMissingStub: _i12.Future<void>.value(),
          )
          as _i12.Future<void>);

  @override
  void jumpToWithoutSettling(double? value) => super.noSuchMethod(
    Invocation.method(#jumpToWithoutSettling, [value]),
    returnValueForMissingStub: null,
  );

  @override
  _i9.ScrollHoldController hold(_i17.VoidCallback? holdCancelCallback) =>
      (super.noSuchMethod(
            Invocation.method(#hold, [holdCancelCallback]),
            returnValue: _FakeScrollHoldController_11(
              this,
              Invocation.method(#hold, [holdCancelCallback]),
            ),
          )
          as _i9.ScrollHoldController);

  @override
  _i10.Drag drag(
    _i9.DragStartDetails? details,
    _i17.VoidCallback? dragCancelCallback,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#drag, [details, dragCancelCallback]),
            returnValue: _FakeDrag_12(
              this,
              Invocation.method(#drag, [details, dragCancelCallback]),
            ),
          )
          as _i10.Drag);

  @override
  void beginActivity(_i9.ScrollActivity? newActivity) => super.noSuchMethod(
    Invocation.method(#beginActivity, [newActivity]),
    returnValueForMissingStub: null,
  );

  @override
  void didStartScroll() => super.noSuchMethod(
    Invocation.method(#didStartScroll, []),
    returnValueForMissingStub: null,
  );

  @override
  void didUpdateScrollPositionBy(double? delta) => super.noSuchMethod(
    Invocation.method(#didUpdateScrollPositionBy, [delta]),
    returnValueForMissingStub: null,
  );

  @override
  void didEndScroll() => super.noSuchMethod(
    Invocation.method(#didEndScroll, []),
    returnValueForMissingStub: null,
  );

  @override
  void didOverscrollBy(double? value) => super.noSuchMethod(
    Invocation.method(#didOverscrollBy, [value]),
    returnValueForMissingStub: null,
  );

  @override
  void didUpdateScrollDirection(_i19.ScrollDirection? direction) =>
      super.noSuchMethod(
        Invocation.method(#didUpdateScrollDirection, [direction]),
        returnValueForMissingStub: null,
      );

  @override
  void didUpdateScrollMetrics() => super.noSuchMethod(
    Invocation.method(#didUpdateScrollMetrics, []),
    returnValueForMissingStub: null,
  );

  @override
  bool recommendDeferredLoading(_i9.BuildContext? context) =>
      (super.noSuchMethod(
            Invocation.method(#recommendDeferredLoading, [context]),
            returnValue: false,
          )
          as bool);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );

  @override
  void debugFillDescription(List<String>? description) => super.noSuchMethod(
    Invocation.method(#debugFillDescription, [description]),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i17.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i17.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  _i9.ScrollMetrics copyWith({
    double? minScrollExtent,
    double? maxScrollExtent,
    double? pixels,
    double? viewportDimension,
    _i9.AxisDirection? axisDirection,
    double? devicePixelRatio,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#copyWith, [], {
              #minScrollExtent: minScrollExtent,
              #maxScrollExtent: maxScrollExtent,
              #pixels: pixels,
              #viewportDimension: viewportDimension,
              #axisDirection: axisDirection,
              #devicePixelRatio: devicePixelRatio,
            }),
            returnValue: _FakeScrollMetrics_13(
              this,
              Invocation.method(#copyWith, [], {
                #minScrollExtent: minScrollExtent,
                #maxScrollExtent: maxScrollExtent,
                #pixels: pixels,
                #viewportDimension: viewportDimension,
                #axisDirection: axisDirection,
                #devicePixelRatio: devicePixelRatio,
              }),
            ),
          )
          as _i9.ScrollMetrics);
}

/// A class which mocks [AssetTypeResultModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetTypeResultModel extends _i1.Mock
    implements _i20.AssetTypeResultModel {
  MockAssetTypeResultModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i11.AssetTypeListModel get assetTypeInfo =>
      (super.noSuchMethod(
            Invocation.getter(#assetTypeInfo),
            returnValue: _FakeAssetTypeListModel_14(
              this,
              Invocation.getter(#assetTypeInfo),
            ),
          )
          as _i11.AssetTypeListModel);
}

/// A class which mocks [AssetTypeListModel].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetTypeListModel extends _i1.Mock
    implements _i11.AssetTypeListModel {
  MockAssetTypeListModel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set assetTypeId(int? _assetTypeId) => super.noSuchMethod(
    Invocation.setter(#assetTypeId, _assetTypeId),
    returnValueForMissingStub: null,
  );

  @override
  set tenantId(String? _tenantId) => super.noSuchMethod(
    Invocation.setter(#tenantId, _tenantId),
    returnValueForMissingStub: null,
  );

  @override
  set assetTypeName(String? _assetTypeName) => super.noSuchMethod(
    Invocation.setter(#assetTypeName, _assetTypeName),
    returnValueForMissingStub: null,
  );

  @override
  set itemFlg(String? _itemFlg) => super.noSuchMethod(
    Invocation.setter(#itemFlg, _itemFlg),
    returnValueForMissingStub: null,
  );

  @override
  set layoutFlg(String? _layoutFlg) => super.noSuchMethod(
    Invocation.setter(#layoutFlg, _layoutFlg),
    returnValueForMissingStub: null,
  );

  @override
  set layoutTempSaveFlg(String? _layoutTempSaveFlg) => super.noSuchMethod(
    Invocation.setter(#layoutTempSaveFlg, _layoutTempSaveFlg),
    returnValueForMissingStub: null,
  );

  @override
  set quantityFlg(String? _quantityFlg) => super.noSuchMethod(
    Invocation.setter(#quantityFlg, _quantityFlg),
    returnValueForMissingStub: null,
  );

  @override
  set createdById(String? _createdById) => super.noSuchMethod(
    Invocation.setter(#createdById, _createdById),
    returnValueForMissingStub: null,
  );

  @override
  set createdDate(String? _createdDate) => super.noSuchMethod(
    Invocation.setter(#createdDate, _createdDate),
    returnValueForMissingStub: null,
  );

  @override
  set modifiedById(String? _modifiedById) => super.noSuchMethod(
    Invocation.setter(#modifiedById, _modifiedById),
    returnValueForMissingStub: null,
  );

  @override
  set modifiedByName(String? _modifiedByName) => super.noSuchMethod(
    Invocation.setter(#modifiedByName, _modifiedByName),
    returnValueForMissingStub: null,
  );

  @override
  set modifiedDate(String? _modifiedDate) => super.noSuchMethod(
    Invocation.setter(#modifiedDate, _modifiedDate),
    returnValueForMissingStub: null,
  );

  @override
  set displayModifiedDate(String? _displayModifiedDate) => super.noSuchMethod(
    Invocation.setter(#displayModifiedDate, _displayModifiedDate),
    returnValueForMissingStub: null,
  );

  @override
  set groupIds(String? _groupIds) => super.noSuchMethod(
    Invocation.setter(#groupIds, _groupIds),
    returnValueForMissingStub: null,
  );

  @override
  set alertCount(String? _alertCount) => super.noSuchMethod(
    Invocation.setter(#alertCount, _alertCount),
    returnValueForMissingStub: null,
  );

  @override
  set displayFlg(String? _displayFlg) => super.noSuchMethod(
    Invocation.setter(#displayFlg, _displayFlg),
    returnValueForMissingStub: null,
  );

  @override
  set groupPermissionCheckLog(String? _groupPermissionCheckLog) =>
      super.noSuchMethod(
        Invocation.setter(#groupPermissionCheckLog, _groupPermissionCheckLog),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> toJson() =>
      (super.noSuchMethod(
            Invocation.method(#toJson, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);
}

/// A class which mocks [SearchConditionResult].
///
/// See the documentation for Mockito's code generation for more information.
class MockSearchConditionResult extends _i1.Mock
    implements _i21.SearchConditionResult {
  MockSearchConditionResult() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get searchId =>
      (super.noSuchMethod(Invocation.getter(#searchId), returnValue: 0) as int);

  @override
  String get searchKey =>
      (super.noSuchMethod(
            Invocation.getter(#searchKey),
            returnValue: _i22.dummyValue<String>(
              this,
              Invocation.getter(#searchKey),
            ),
          )
          as String);
}
