// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_list/domain/usecase/get_search_id_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_conditions_result.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/search_conditions_repository.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SearchConditionsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSearchConditionsRepository extends _i1.Mock
    implements _i2.SearchConditionsRepository {
  MockSearchConditionsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.SearchConditionsResult?> getSearchConditions(
    int? assetTypeId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getSearchConditions, [assetTypeId]),
            returnValue: _i3.Future<_i4.SearchConditionsResult?>.value(),
          )
          as _i3.Future<_i4.SearchConditionsResult?>);
}
