import 'dart:async';
import 'dart:math';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/usecases/reset_password_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant_common/controller/user_tenant_common_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/password_policy_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'user_tenant_common_controller_test.mocks.dart';

// Mock annotations
@GenerateNiceMocks([MockSpec<TenantUseCase>(), MockSpec<DialogService>(), MockSpec<NavigationService>()])
/// 测试用的具体实现类，因为UserTenantCommonController是抽象类
class TestUserTenantCommonController extends UserTenantCommonController {
  bool handleExceptionCalled = false;
  dynamic lastException;
  bool showLoadingCalled = false;
  bool hideLoadingCalled = false;

  TestUserTenantCommonController({
    required super.tenantUseCase,
    required super.navigationService,
    required super.dialogService,
  });

  @override
  Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    ErrorHandlingMode mode = ErrorHandlingMode.dialog,
  ]) async {
    // 在测试中，我们只记录异常调用，不执行实际的异常处理逻辑
    handleExceptionCalled = true;
    lastException = exception;
    // 不调用super.handleException以避免UI相关的操作
  }

  @override
  Future<void> showLoading() async {
    // 在测试中，我们只记录调用，不执行实际的UI操作
    showLoadingCalled = true;
    // 不调用super.showLoading以避免UI相关的操作
  }

  @override
  void hideLoading() {
    // 在测试中，我们只记录调用，不执行实际的UI操作
    hideLoadingCalled = true;
    // 不调用super.hideLoading以避免UI相关的操作
  }

  /// 重置测试状态
  void resetState() {
    handleExceptionCalled = false;
    lastException = null;
    showLoadingCalled = false;
    hideLoadingCalled = false;
  }
}

void main() {
  group('UserTenantCommonController', () {
    late TestUserTenantCommonController controller;
    late MockTenantUseCase mockTenantUseCase;
    late MockDialogService mockDialogService;
    late MockNavigationService mockNavigationService;

    setUp(() {
      // 重置GetX状态
      Get.reset();

      // 初始化GetX测试模式，并设置基本导航
      Get.testMode = true;
      Get.config(enableLog: false);

      // 初始化LogUtil（防止LateInitializationError）
      LogUtil.initialize();

      // 创建Mock对象
      mockTenantUseCase = MockTenantUseCase();
      mockDialogService = MockDialogService();
      mockNavigationService = MockNavigationService();

      // 创建控制器实例
      controller = TestUserTenantCommonController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
      );

      // 初始化控制器
      controller.onInit();

      // 重置测试状态
      controller.handleExceptionCalled = false;
      controller.lastException = null;
      controller.showLoadingCalled = false;
      controller.hideLoadingCalled = false;

      // 重置所有Mock对象状态，防止测试间干扰
      reset(mockTenantUseCase);
      reset(mockDialogService);
      reset(mockNavigationService);
    });

    tearDown(() {
      // 清理GetX状态
      Get.reset();
    });

    group('Phase 0: 基础设施和构造测试', () {
      group('构造函数和依赖注入验证', () {
        test('应该正确创建实例 - 提供所有必需参数', () {
          // Arrange & Act
          final testController = TestUserTenantCommonController(
            tenantUseCase: mockTenantUseCase,
            navigationService: mockNavigationService,
            dialogService: mockDialogService,
          );

          // Assert
          expect(testController, isA<UserTenantCommonController>());
          expect(testController, isA<TestUserTenantCommonController>());
          expect(testController.tenantUseCase, equals(mockTenantUseCase));
          expect(testController.navigationService, equals(mockNavigationService));
          expect(testController.dialogService, equals(mockDialogService));
        });

        test('应该正确注入TenantUseCase依赖', () {
          // Assert
          expect(controller.tenantUseCase, isNotNull);
          expect(controller.tenantUseCase, isA<TenantUseCase>());
          expect(controller.tenantUseCase, equals(mockTenantUseCase));
        });

        test('应该正确注入DialogService依赖', () {
          // Assert
          expect(controller.dialogService, isNotNull);
          expect(controller.dialogService, isA<DialogService>());
          expect(controller.dialogService, equals(mockDialogService));
        });

        test('应该正确注入NavigationService依赖', () {
          // Assert
          expect(controller.navigationService, isNotNull);
          expect(controller.navigationService, isA<NavigationService>());
          expect(controller.navigationService, equals(mockNavigationService));
        });

        test('UserTenantCommonController是抽象类不能直接实例化', () {
          // Assert - 验证继承关系
          expect(controller, isA<UserTenantCommonController>());

          // 确保我们的测试实现是有效的
          expect(controller.runtimeType.toString(), equals('TestUserTenantCommonController'));

          // 验证TestUserTenantCommonController继承了UserTenantCommonController的所有属性
          expect(controller.tenantUseCase, isNotNull);
          expect(controller.dialogService, isNotNull);
          expect(controller.navigationService, isNotNull);
        });
      });

      group('初始状态验证', () {
        test('ticket应该初始化为空字符串', () {
          // Assert
          expect(controller.ticket, isA<RxString>());
          expect(controller.ticket.value, equals(''));
          expect(controller.ticket.value.isEmpty, isTrue);
        });

        test('isBiometrics应该初始化为false', () {
          // Assert
          expect(controller.isBiometrics, isA<RxBool>());
          expect(controller.isBiometrics.value, equals(false));
          expect(controller.isBiometrics.value, isFalse);
        });

        test('响应式状态应该正确配置为观察模式', () {
          // Arrange
          bool ticketChanged = false;
          bool biometricsChanged = false;

          // Act - 设置监听器
          controller.ticket.listen((value) => ticketChanged = true);
          controller.isBiometrics.listen((value) => biometricsChanged = true);

          // 改变值
          controller.ticket.value = 'test_ticket';
          controller.isBiometrics.value = true;

          // Assert
          expect(ticketChanged, isTrue);
          expect(biometricsChanged, isTrue);
          expect(controller.ticket.value, equals('test_ticket'));
          expect(controller.isBiometrics.value, isTrue);
        });

        test('应该继承BaseController的所有功能', () {
          // Assert - 检查继承的方法是否可用
          expect(controller.parameter, isA<Map<String, String?>>());

          // 验证方法存在（不调用，只验证方法签名）
          expect(controller.showLoading, isA<Function>());
          expect(controller.hideLoading, isA<Function>());
          expect(controller.handleException, isA<Function>());

          // 验证控制器是GetxController的实例
          expect(controller, isA<GetxController>());
        });

        test('控制器状态应该是初始化状态', () {
          // Assert - 验证没有被disposed
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证响应式变量可以正常更新
          controller.ticket.value = 'test';
          controller.isBiometrics.value = true;
          expect(controller.ticket.value, equals('test'));
          expect(controller.isBiometrics.value, isTrue);
        });
      });

      group('Mock对象验证', () {
        test('MockTenantUseCase应该正确创建', () {
          // Assert
          expect(mockTenantUseCase, isA<MockTenantUseCase>());
          expect(mockTenantUseCase, isA<TenantUseCase>());

          // 验证类型正确，无需实际调用mock
          verifyZeroInteractions(mockTenantUseCase);
        });

        test('MockDialogService应该正确创建', () {
          // Assert
          expect(mockDialogService, isA<MockDialogService>());
          expect(mockDialogService, isA<DialogService>());

          // 验证类型正确，无需实际调用mock
          verifyZeroInteractions(mockDialogService);
        });

        test('MockNavigationService应该正确创建', () {
          // Assert
          expect(mockNavigationService, isA<MockNavigationService>());
          expect(mockNavigationService, isA<NavigationService>());

          // 验证类型正确，无需实际调用mock
          verifyZeroInteractions(mockNavigationService);
        });

        test('应该能够设置TenantUseCase mock行为', () {
          // Arrange - 创建一个临时的 mock 实例来测试
          final tempMock = MockTenantUseCase();
          final expectedResult = GetTokenResultModel(code: 0, msg: 'success');

          // Act & Assert - 验证mock设置成功
          expect(() => when(tempMock.call(any)).thenAnswer((_) async => expectedResult), returnsNormally);

          // 验证mock可以被配置，不会影响全局的 mockTenantUseCase
          verifyZeroInteractions(mockTenantUseCase);
        });

        test('应该能够创建独立的DialogService mock实例', () {
          // Arrange & Act
          final freshMockDialogService = MockDialogService();

          // Assert - 验证Mock可以被正确创建
          expect(freshMockDialogService, isA<DialogService>());
          expect(freshMockDialogService, isA<MockDialogService>());
          expect(freshMockDialogService, isNotNull);

          // 验证与controller中的实例不同
          expect(freshMockDialogService, isNot(equals(mockDialogService)));
        });

        test('应该能够创建独立的NavigationService mock实例', () {
          // Arrange & Act
          final freshMockNavigationService = MockNavigationService();

          // Assert - 验证Mock创建和类型
          expect(freshMockNavigationService, isA<NavigationService>());
          expect(freshMockNavigationService, isA<MockNavigationService>());
          expect(freshMockNavigationService, isNotNull);

          // 验证与controller中的实例不同
          expect(freshMockNavigationService, isNot(equals(mockNavigationService)));
        });
      });

      group('类型和接口验证', () {
        test('应该正确继承BaseController', () {
          // Assert
          expect(controller, isA<GetxController>());

          // 验证继承链 - 通过实例验证继承关系
          expect(controller, isA<UserTenantCommonController>());
        });

        test('应该具有正确的泛型类型约束', () {
          // Assert - 验证响应式变量类型
          expect(controller.ticket, isA<RxString>());
          expect(controller.isBiometrics, isA<RxBool>());

          // 验证依赖类型
          expect(controller.tenantUseCase, isA<TenantUseCase>());
          expect(controller.dialogService, isA<DialogService>());
          expect(controller.navigationService, isA<NavigationService>());
        });

        test('应该支持多态性', () {
          // Arrange
          UserTenantCommonController polymorphicController = controller;

          // Assert
          expect(polymorphicController, isA<UserTenantCommonController>());
          expect(polymorphicController.tenantUseCase, equals(mockTenantUseCase));
          expect(polymorphicController.dialogService, equals(mockDialogService));
          expect(polymorphicController.navigationService, equals(mockNavigationService));
        });
      });

      group('内存和资源管理验证', () {
        test('控制器应该正确管理GetX生命周期', () {
          // Assert - 验证可以正常访问响应式变量
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证响应式变量可以正常工作
          final originalTicket = controller.ticket.value;
          final originalBiometrics = controller.isBiometrics.value;

          controller.ticket.value = 'lifecycle_test';
          controller.isBiometrics.value = !originalBiometrics;

          expect(controller.ticket.value, equals('lifecycle_test'));
          expect(controller.isBiometrics.value, equals(!originalBiometrics));
        });

        test('Mock对象应该不产生内存泄漏', () {
          // Arrange - 创建多个mock实例
          final mocks = List.generate(10, (_) => MockTenantUseCase());

          // Assert - 验证所有mock都正常创建
          for (var mock in mocks) {
            expect(mock, isA<TenantUseCase>());
            verifyZeroInteractions(mock);
          }

          // 验证内存使用合理（创建大量mock不会导致问题）
          expect(mocks.length, equals(10));
          expect(mocks.every((mock) => mock != null), isTrue);

          // 验证每个mock都是独立的实例
          for (int i = 0; i < mocks.length - 1; i++) {
            expect(mocks[i], isNot(equals(mocks[i + 1])));
          }
        });

        test('控制器实例应该正确清理资源', () {
          // Arrange
          final testController = TestUserTenantCommonController(
            tenantUseCase: MockTenantUseCase(),
            navigationService: MockNavigationService(),
            dialogService: MockDialogService(),
          );
          testController.onInit();

          // Act
          testController.onClose();

          // Assert - 验证依赖注入的对象依然可访问
          expect(testController.tenantUseCase, isNotNull);
          expect(testController.dialogService, isNotNull);
          expect(testController.navigationService, isNotNull);

          // 验证控制器依然是正确的类型
          expect(testController, isA<UserTenantCommonController>());
          expect(testController, isA<TestUserTenantCommonController>());
        });
      });
    });

    group('Phase 1: 参数验证和前置条件测试', () {
      group('输入参数验证', () {
        test('tenant.tenantId为null时应该处理SystemException', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: null, // null tenantId
            zoneId: 'valid_zone_id',
            tenantName: 'Test Tenant',
          );

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证TenantUseCase.call没有被调用（因为提前返回）
          verifyNever(mockTenantUseCase.call(any));

          // 验证没有进行导航（因为异常处理）
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });

        test('tenant.zoneId为null时应该处理SystemException', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: null, // null zoneId
            tenantName: 'Test Tenant',
          );

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证TenantUseCase.call没有被调用
          verifyNever(mockTenantUseCase.call(any));

          // 验证没有进行导航
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });

        test('tenant.tenantId和zoneId都为null时应该处理SystemException', () async {
          // Arrange
          final tenant = SharedTenantModel(tenantId: null, zoneId: null, tenantName: 'Test Tenant');

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证TenantUseCase.call没有被调用
          verifyNever(mockTenantUseCase.call(any));

          // 验证没有进行导航
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });

        test('userName为空字符串时应该正常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: 'valid_zone_id',
            tenantName: 'Test Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act & Assert - 验证不会因为空用户名而崩溃
          expect(() async {
            await controller.onTenantOption(
              tenant: tenant,
              userName: '', // 空字符串
              password: 'password123',
            );
          }, returnsNormally);
        });

        test('password为空字符串时应该正常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: 'valid_zone_id',
            tenantName: 'Test Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act & Assert - 验证不会因为空密码而崩溃
          expect(() async {
            await controller.onTenantOption(
              tenant: tenant,
              userName: '<EMAIL>',
              password: '', // 空字符串
            );
          }, returnsNormally);
        });

        test('userName和password都为空字符串时应该正常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: 'valid_zone_id',
            tenantName: 'Test Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act & Assert - 验证不会因为空参数而崩溃
          expect(() async {
            await controller.onTenantOption(
              tenant: tenant,
              userName: '', // 空字符串
              password: '', // 空字符串
            );
          }, returnsNormally);
        });

        test('tenant.tenantName为null时不应该影响核心流程', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: 'valid_zone_id',
            tenantName: null, // tenantName为null
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act & Assert - 验证null tenantName不影响流程
          expect(() async {
            await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');
          }, returnsNormally);
        });

        test('极长的userName应该正常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: 'valid_zone_id',
            tenantName: 'Test Tenant',
          );

          final longUserName = 'a' * 1000; // 1000个字符的用户名

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act & Assert - 验证极长用户名不导致问题
          expect(() async {
            await controller.onTenantOption(tenant: tenant, userName: longUserName, password: 'password123');
          }, returnsNormally);
        });

        test('极长的password应该正常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'valid_tenant_id',
            zoneId: 'valid_zone_id',
            tenantName: 'Test Tenant',
          );

          final longPassword = 'b' * 1000; // 1000个字符的密码

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act & Assert - 验证极长密码不导致问题
          expect(() async {
            await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: longPassword);
          }, returnsNormally);
        });
      });

      group('前置状态验证', () {
        test('ticket状态应该正确设置和获取', () {
          // Arrange & Act
          controller.ticket.value = 'test_ticket_12345';

          // Assert - 验证ticket状态正确设置
          expect(controller.ticket.value, equals('test_ticket_12345'));
          expect(controller.ticket.value.isNotEmpty, isTrue);
        });

        test('isBiometrics状态应该正确设置和获取', () {
          // Arrange & Act
          controller.isBiometrics.value = true;

          // Assert - 验证isBiometrics状态正确设置
          expect(controller.isBiometrics.value, isTrue);

          // 再次测试false状态
          controller.isBiometrics.value = false;
          expect(controller.isBiometrics.value, isFalse);
        });

        test('响应式状态应该正确触发观察者', () {
          // Arrange
          bool ticketChanged = false;
          bool biometricsChanged = false;

          // 设置监听器
          controller.ticket.listen((value) => ticketChanged = true);
          controller.isBiometrics.listen((value) => biometricsChanged = true);

          // Act
          controller.ticket.value = 'new_ticket';
          controller.isBiometrics.value = true;

          // Assert - 验证观察者被正确触发
          expect(ticketChanged, isTrue);
          expect(biometricsChanged, isTrue);
        });

        test('空ticket状态应该正确处理', () {
          // Arrange & Act
          controller.ticket.value = ''; // 空ticket

          // Assert - 验证空ticket正确设置
          expect(controller.ticket.value, equals(''));
          expect(controller.ticket.value.isEmpty, isTrue);
        });

        test('状态初始化后应该可以正常修改', () {
          // Arrange - 获取初始状态
          final initialTicket = controller.ticket.value;
          final initialBiometrics = controller.isBiometrics.value;

          // Act - 修改状态
          controller.ticket.value = 'modified_ticket';
          controller.isBiometrics.value = !initialBiometrics;

          // Assert - 验证状态成功修改
          expect(controller.ticket.value, isNot(equals(initialTicket)));
          expect(controller.isBiometrics.value, isNot(equals(initialBiometrics)));
          expect(controller.ticket.value, equals('modified_ticket'));
        });

        test('多次状态修改应该保持最新值', () {
          // Act - 多次修改
          controller.ticket.value = 'first_ticket';
          controller.ticket.value = 'second_ticket';
          controller.ticket.value = 'final_ticket';

          controller.isBiometrics.value = true;
          controller.isBiometrics.value = false;
          controller.isBiometrics.value = true;

          // Assert - 验证保持最新值
          expect(controller.ticket.value, equals('final_ticket'));
          expect(controller.isBiometrics.value, isTrue);
        });
      });
    });

    group('Phase 2: 核心业务逻辑测试', () {
      group('成功登录流程', () {
        test('正常登录成功时应该导航到AppTab', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'success_tenant_id',
            zoneId: 'success_zone_id',
            tenantName: 'Success Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 0, // 正常登录成功
              msg: 'success',
            ),
          );
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});
          when(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          ).thenAnswer((_) async {});

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'success_password',
          );

          // Assert - 验证成功流程
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
          expect(controller.handleExceptionCalled, isFalse);

          // 验证导航到AppTab
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
          verifyNever(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          );
        });

        test('正常登录时应该正确传递tenant参数给UseCase', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'param_tenant_id',
            zoneId: 'param_zone_id',
            tenantName: 'Param Tenant',
          );

          controller.ticket.value = 'test_ticket_value';
          controller.isBiometrics.value = true;

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'param_password');

          // Assert - 验证UseCase调用参数
          final capturedCall = verify(mockTenantUseCase.call(captureAny)).captured[0] as SelectTenantModel;
          expect(capturedCall.zoneId, equals('param_zone_id'));
          expect(capturedCall.dealTenant?.tenantId, equals('param_tenant_id'));
          expect(capturedCall.dealTenant?.ticket, equals('test_ticket_value'));
          expect(capturedCall.dealTenant?.isBiometrics, isTrue);
          expect(capturedCall.dealDeeplink, isNull);
          expect(capturedCall.optTenant, isNull);
        });

        test('成功登录流程不应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'no_exception_tenant_id',
            zoneId: 'no_exception_zone_id',
            tenantName: 'No Exception Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'no_exception_password',
          );

          // Assert - 验证没有异常处理
          expect(controller.handleExceptionCalled, isFalse);
          expect(controller.lastException, isNull);
        });
      });

      group('重置密码流程', () {
        test('密码过期时应该显示重置密码对话框', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'reset_tenant_id',
            zoneId: 'reset_zone_id',
            tenantName: 'Reset Tenant',
          );

          final mockPolicy = PasswordPolicyModel(
            policyId: 1,
            tenantId: 'reset_tenant_id',
            expireTime: 90,
            mustContain: 'ABC123',
            enableFlg: '1',
            length: 8,
            repeatForbidCount: 3,
            lockEnableFlg: '1',
            lockTimes: 5,
            lockPeriod: 30,
            strength: 'medium',
            tenantName: 'Reset Tenant',
          );
          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 7158, // 重置密码状态码
              msg: 'password_expired',
              policy: mockPolicy,
            ),
          );

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'expired_password');

          // Assert - 验证显示重置密码对话框
          verify(
            mockDialogService.show(
              title: 'パスワードの有効期限が切れています',
              content: '新しいパスワードを設定してください。',
              cancelText: 'キャンセル',
              onConfirm: anyNamed('onConfirm'),
            ),
          ).called(1);

          // 验证没有导航到AppTab
          verifyNever(mockNavigationService.navigateOffAll(any));
        });

        test('重置密码时policy为null应该抛出SystemException', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'policy_null_tenant_id',
            zoneId: 'policy_null_zone_id',
            tenantName: 'Policy Null Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 7158,
              msg: 'password_expired',
              policy: null, // policy为null
            ),
          );

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'policy_null_password',
          );

          // Assert - 验证抛出SystemException
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());
        });

        test('重置密码对话框确认应该导航到重置密码页面', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'confirm_tenant_id',
            zoneId: 'confirm_zone_id',
            tenantName: 'Confirm Tenant',
          );

          final mockPolicy = PasswordPolicyModel(
            policyId: 2,
            tenantId: 'confirm_tenant_id',
            expireTime: 60,
            mustContain: 'ABCD1234',
            enableFlg: '1',
            length: 10,
            repeatForbidCount: 5,
            lockEnableFlg: '1',
            lockTimes: 3,
            lockPeriod: 60,
            strength: 'strong',
            tenantName: 'Confirm Tenant',
          );
          // 设置mock行为

          clearInteractions(mockDialogService);
          when(
            mockTenantUseCase.call(any),
          ).thenAnswer((_) async => GetTokenResultModel(code: 7158, msg: 'password_expired', policy: mockPolicy));

          VoidCallback? capturedOnConfirm;
          when(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          ).thenAnswer((invocation) {
            capturedOnConfirm = invocation.namedArguments[const Symbol('onConfirm')];
            return Future.value();
          });

          controller.isBiometrics.value = true;

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'confirm_password',
          );

          // 模拟用户点击确认按钮
          expect(capturedOnConfirm, isNotNull);
          capturedOnConfirm!();

          // Assert - 验证导航到重置密码页面
          verify(
            mockNavigationService.navigateTo(AutoRoutes.loginResetPassword, arguments: anyNamed('arguments')),
          ).called(1);
        });
      });

      group('双重认证流程', () {
        test('SMS双重认证（code=3）应该导航到OTP页面', () async {
          // Arrange
          final tenant = SharedTenantModel(tenantId: 'sms_tenant_id', zoneId: 'sms_zone_id', tenantName: 'SMS Tenant');

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 3, // SMS双重认证
              msg: 'sms_required',
              user: SharedUserModel(userId: 12345, nationCode: '81', tel: '09012345678'),
            ),
          );

          controller.ticket.value = 'sms_ticket';

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'sms_password');

          // Assert - 验证导航到OTP页面
          verify(mockNavigationService.navigateTo(AutoRoutes.loginOpt, arguments: anyNamed('arguments'))).called(1);
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          );
        });

        test('新SMS双重认证（code=4）应该导航到OTP页面', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'new_sms_tenant_id',
            zoneId: 'new_sms_zone_id',
            tenantName: 'New SMS Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 4, // 新SMS双重认证
              msg: 'new_sms_required',
              user: SharedUserModel(userId: 67890, nationCode: '81', tel: '08087654321'),
            ),
          );

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'new_sms_password',
          );

          // Assert - 验证导航到OTP页面
          verify(mockNavigationService.navigateTo(AutoRoutes.loginOpt, arguments: anyNamed('arguments'))).called(1);
        });

        test('Email双重认证（code=6）应该导航到OTP页面', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'email_tenant_id',
            zoneId: 'email_zone_id',
            tenantName: 'Email Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 6, // Email双重认证
              msg: 'email_required',
              user: SharedUserModel(userId: 11111, nationCode: '81', tel: '09011111111'),
            ),
          );

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'email_password');

          // Assert - 验证导航到OTP页面
          verify(mockNavigationService.navigateTo(AutoRoutes.loginOpt, arguments: anyNamed('arguments'))).called(1);
        });

        test('双重认证时应该正确设置OptTenantModel参数', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'opt_param_tenant_id',
            zoneId: 'opt_param_zone_id',
            tenantName: 'OPT Param Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 3,
              msg: 'sms_required',
              user: SharedUserModel(userId: 99999, nationCode: '86', tel: '13800138000'),
            ),
          );

          controller.ticket.value = 'opt_ticket_value';

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'opt_param_password',
          );

          // Assert - 验证导航参数
          final capturedCall =
              verify(
                    mockNavigationService.navigateTo(AutoRoutes.loginOpt, arguments: captureAnyNamed('arguments')),
                  ).captured[0]
                  as SelectTenantModel;

          expect(capturedCall.zoneId, equals('opt_param_zone_id'));
          expect(capturedCall.dealTenant, isNull);
          expect(capturedCall.dealDeeplink, isNull);

          final optTenant = capturedCall.optTenant!;
          expect(optTenant.nationCode, equals('86'));
          expect(optTenant.tel, equals('13800138000'));
          expect(optTenant.ticket, equals('opt_ticket_value'));
          expect(optTenant.reCode, equals(3));
          expect(optTenant.userId, equals(99999));
          expect(optTenant.userName, equals('<EMAIL>'));
          expect(optTenant.password, equals('opt_param_password'));
          expect(optTenant.tenantId, equals('opt_param_tenant_id'));
          expect(optTenant.email, equals('<EMAIL>'));
        });
      });

      group('业务流程控制', () {
        test('Loading状态应该正确管理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'loading_tenant_id',
            zoneId: 'loading_zone_id',
            tenantName: 'Loading Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'loading_password',
          );

          // Assert - 验证Loading状态管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });

        test('异常情况下也应该正确隐藏Loading', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'exception_loading_tenant_id',
            zoneId: 'exception_loading_zone_id',
            tenantName: 'Exception Loading Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(Exception('UseCase异常'));

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'exception_loading_password',
          );

          // Assert - 验证异常情况下Loading也被隐藏
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
          expect(controller.handleExceptionCalled, isTrue);
        });

        test('业务流程应该按正确顺序执行', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'sequence_tenant_id',
            zoneId: 'sequence_zone_id',
            tenantName: 'Sequence Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'sequence_password',
          );

          // Assert - 验证执行顺序
          verifyInOrder([
            // 首先显示Loading
            // 然后调用UseCase (我们无法直接验证顺序，但可以验证都被调用了)
            mockTenantUseCase.call(any),
            // 最后导航
            mockNavigationService.navigateOffAll(AutoRoutes.appTab),
          ]);
        });

        test('未知状态码应该不执行任何导航操作', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'unknown_code_tenant_id',
            zoneId: 'unknown_code_zone_id',
            tenantName: 'Unknown Code Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: 9999, // 未知状态码
              msg: 'unknown_status',
            ),
          );

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'unknown_code_password',
          );

          // Assert - 验证没有进行任何导航操作
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
          verifyNever(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          );
        });
      });
    });

    group('Phase 3: 异常处理和错误场景测试', () {
      group('网络和服务异常处理', () {
        test('网络连接异常应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'network_error_tenant_id',
            zoneId: 'network_error_zone_id',
            tenantName: 'Network Error Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(Exception('Network connection failed'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用 (所有异常都被转换成SystemException)
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证没有进行导航
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });

        test('HTTP超时异常应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'timeout_tenant_id',
            zoneId: 'timeout_zone_id',
            tenantName: 'Timeout Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(TimeoutException('Request timeout', const Duration(seconds: 30)));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用 (所有异常都被转换成SystemException)
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });

        test('服务器内部错误应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'server_error_tenant_id',
            zoneId: 'server_error_zone_id',
            tenantName: 'Server Error Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(Exception('Internal server error 500'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用 (所有异常都被转换成SystemException)
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());
        });

        test('未授权异常应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'unauthorized_tenant_id',
            zoneId: 'unauthorized_zone_id',
            tenantName: 'Unauthorized Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(Exception('Unauthorized access 401'));

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'wrong_password',
          );

          // Assert - 验证异常处理被调用 (所有异常都被转换成SystemException)
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());
        });
      });

      group('数据验证和格式错误处理', () {
        test('无效JSON响应应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'invalid_json_tenant_id',
            zoneId: 'invalid_json_zone_id',
            tenantName: 'Invalid JSON Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(FormatException('Invalid JSON format'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用 (所有异常都被转换成SystemException)
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());
        });

        test('数据字段不完整的响应应该正确处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'incomplete_data_tenant_id',
            zoneId: 'incomplete_data_zone_id',
            tenantName: 'Incomplete Data Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenAnswer(
            (_) async => GetTokenResultModel(
              code: -1, // 异常的code值
              msg: 'response_with_incomplete_data',
              user: null, // 缺失user字段
              policy: null, // 缺失policy字段
            ),
          );

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证不会崩溃，应该能正常处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证TenantUseCase被调用
          verify(mockTenantUseCase.call(any)).called(1);

          // 验证未知code值不会进行导航
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });

        test('空响应应该触发异常处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'null_response_tenant_id',
            zoneId: 'null_response_zone_id',
            tenantName: 'Null Response Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(Exception('Null response received'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理被调用 (所有异常都被转换成SystemException)
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());
        });
      });

      group('边界条件和状态管理测试', () {
        test('并发请求应该正确处理', () async {
          // Arrange
          final tenant1 = SharedTenantModel(
            tenantId: 'concurrent_tenant_1',
            zoneId: 'concurrent_zone_1',
            tenantName: 'Concurrent Tenant 1',
          );

          final tenant2 = SharedTenantModel(
            tenantId: 'concurrent_tenant_2',
            zoneId: 'concurrent_zone_2',
            tenantName: 'Concurrent Tenant 2',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 100));
            return GetTokenResultModel(code: 0, msg: 'success');
          });
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 并发调用
          final future1 = controller.onTenantOption(
            tenant: tenant1,
            userName: '<EMAIL>',
            password: 'password123',
          );

          final future2 = controller.onTenantOption(
            tenant: tenant2,
            userName: '<EMAIL>',
            password: 'password123',
          );

          await Future.wait([future1, future2]);

          // Assert - 验证两个请求都被处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证UseCase被调用了多次
          verify(mockTenantUseCase.call(any)).called(greaterThan(1));
        });

        test('快速连续调用应该正确处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'rapid_calls_tenant_id',
            zoneId: 'rapid_calls_zone_id',
            tenantName: 'Rapid Calls Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 快速连续调用
          for (int i = 0; i < 5; i++) {
            await controller.onTenantOption(tenant: tenant, userName: 'rapid$<EMAIL>', password: 'password123');
          }

          // Assert - 验证所有调用都被处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证UseCase被调用了5次
          verify(mockTenantUseCase.call(any)).called(5);
        });

        test('异常后的恢复调用应该正常工作', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'recovery_tenant_id',
            zoneId: 'recovery_zone_id',
            tenantName: 'Recovery Tenant',
          );

          // 设置mock行为
          // Act & Assert - 第一次调用抛出异常
          when(mockTenantUseCase.call(any)).thenThrow(Exception('First call error'));

          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          expect(controller.handleExceptionCalled, isTrue);

          // 重置测试状态
          controller.handleExceptionCalled = false;
          controller.lastException = null;

          // 第二次调用成功
          when(
            mockTenantUseCase.call(any),
          ).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'recovery_success'));
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // 验证第二次调用成功
          expect(controller.handleExceptionCalled, isFalse);
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });
      });

      group('内存和资源管理测试', () {
        test('大量异常不应该导致内存泄漏', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'memory_test_tenant_id',
            zoneId: 'memory_test_zone_id',
            tenantName: 'Memory Test Tenant',
          );

          // 设置mock行为

          when(mockTenantUseCase.call(any)).thenThrow(Exception('Repeated exception'));

          // Act - 多次异常调用
          for (int i = 0; i < 10; i++) {
            await controller.onTenantOption(tenant: tenant, userName: 'memory$<EMAIL>', password: 'password123');

            // 重置异常状态
            controller.handleExceptionCalled = false;
            controller.lastException = null;
          }

          // Assert - 验证控制器仍然可以正常工作
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证UseCase被调用了10次
          verify(mockTenantUseCase.call(any)).called(10);
        });

        test('控制器dispose后不应该处理新请求', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'disposed_tenant_id',
            zoneId: 'disposed_zone_id',
            tenantName: 'Disposed Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async => GetTokenResultModel(code: 0, msg: 'success'));

          // Act - dispose控制器
          controller.onClose();

          // Assert - 验证dispose后状态
          expect(
            () => controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123'),
            returnsNormally,
          ); // 不应该崩溃，但可能不会执行
        });
      });
    });

    group('Phase 4: 服务交互和集成测试', () {
      group('服务调用顺序和依赖关系', () {
        test('成功登录流程中的完整服务调用链', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'integration_tenant_id',
            zoneId: 'integration_zone_id',
            tenantName: 'Integration Tenant',
          );

          final List<String> callOrder = [];

          // 设置mock行为 - 跟踪调用顺序
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            callOrder.add('tenantUseCase.call');
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {
            callOrder.add('navigationService.navigateOffAll');
          });

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证服务调用顺序
          expect(callOrder, ['tenantUseCase.call', 'navigationService.navigateOffAll']);

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证TenantUseCase调用参数
          final capturedArgs = verify(mockTenantUseCase.call(captureAny)).captured;
          expect(capturedArgs, hasLength(1));
          final selectTenantModel = capturedArgs[0] as SelectTenantModel;
          expect(selectTenantModel.zoneId, equals('integration_zone_id'));
          expect(selectTenantModel.dealTenant?.tenantId, equals('integration_tenant_id'));

          // 验证NavigationService调用
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('重置密码流程中的服务协调', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'reset_tenant_id',
            zoneId: 'reset_zone_id',
            tenantName: 'Reset Tenant',
          );

          final mockPolicy = PasswordPolicyModel(length: 8, mustContain: 'upper,lower,number');

          final List<String> callOrder = [];

          // 设置mock行为 - 跟踪调用顺序
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            callOrder.add('tenantUseCase.call');
            return GetTokenResultModel(code: 7158, msg: 'reset_password', policy: mockPolicy);
          });

          when(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          ).thenAnswer((invocation) async {
            callOrder.add('dialogService.show');
            final onConfirm = invocation.namedArguments[Symbol('onConfirm')] as Function;
            onConfirm();
          });

          when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async {
            callOrder.add('navigationService.navigateTo');
          });

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'oldpassword123');

          // Assert - 验证服务调用顺序
          expect(callOrder, ['tenantUseCase.call', 'dialogService.show', 'navigationService.navigateTo']);

          // 验证DialogService调用参数
          verify(
            mockDialogService.show(
              title: 'パスワードの有効期限が切れています',
              content: '新しいパスワードを設定してください。',
              cancelText: 'キャンセル',
              onConfirm: anyNamed('onConfirm'),
            ),
          ).called(1);

          // 验证NavigationService调用参数
          final capturedArgs = verify(
            mockNavigationService.navigateTo(AutoRoutes.loginResetPassword, arguments: captureAnyNamed('arguments')),
          ).captured;
          expect(capturedArgs, hasLength(1));
          final rpModel = capturedArgs[0] as ResetPasswordArgumentsModel;
          expect(rpModel.tenantId, equals('reset_tenant_id'));
          expect(rpModel.zoneId, equals('reset_zone_id'));
          expect(rpModel.oldPassword, equals('oldpassword123'));
          expect(rpModel.userName, equals('<EMAIL>'));
          expect(rpModel.policy, equals(mockPolicy));
        });

        test('双重认证流程中的服务交互', () async {
          // Arrange
          final tenant = SharedTenantModel(tenantId: 'otp_tenant_id', zoneId: 'otp_zone_id', tenantName: 'OTP Tenant');

          final mockUser = SharedUserModel(userId: 12345, nationCode: '+81', tel: '09012345678');

          final List<String> callOrder = [];

          // 设置mock行为 - 跟踪调用顺序
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            callOrder.add('tenantUseCase.call');
            return GetTokenResultModel(code: 4, msg: 'otp_required', user: mockUser);
          });

          when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async {
            callOrder.add('navigationService.navigateTo');
          });

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证服务调用顺序
          expect(callOrder, ['tenantUseCase.call', 'navigationService.navigateTo']);

          // 验证NavigationService调用参数
          final capturedArgs = verify(
            mockNavigationService.navigateTo(AutoRoutes.loginOpt, arguments: captureAnyNamed('arguments')),
          ).captured;
          expect(capturedArgs, hasLength(1));
          final selectTenantModel = capturedArgs[0] as SelectTenantModel;
          expect(selectTenantModel.zoneId, equals('otp_zone_id'));
          expect(selectTenantModel.optTenant?.tenantId, equals('otp_tenant_id'));
          expect(selectTenantModel.optTenant?.userName, equals('<EMAIL>'));
          expect(selectTenantModel.optTenant?.password, equals('password123'));
          expect(selectTenantModel.optTenant?.userId, equals(12345));
        });

        test('异常处理与服务清理的协调', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'exception_tenant_id',
            zoneId: 'exception_zone_id',
            tenantName: 'Exception Tenant',
          );

          final List<String> callOrder = [];

          // 设置mock行为 - 跟踪调用顺序
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            callOrder.add('tenantUseCase.call');
            throw Exception('Service failure');
          });

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证服务调用顺序
          expect(callOrder, ['tenantUseCase.call']);

          // 验证异常处理被调用
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证没有进行导航
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });
      });

      group('多服务协同工作场景', () {
        test('TenantUseCase与Loading状态的同步', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'sync_tenant_id',
            zoneId: 'sync_zone_id',
            tenantName: 'Sync Tenant',
          );

          bool useCaseCallStarted = false;
          bool loadingShownBeforeUseCase = false;

          // 设置mock行为 - 验证Loading在UseCase调用前显示
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            useCaseCallStarted = true;
            loadingShownBeforeUseCase = controller.showLoadingCalled;
            await Future.delayed(const Duration(milliseconds: 100));
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证Loading在UseCase调用前显示
          expect(useCaseCallStarted, isTrue);
          expect(loadingShownBeforeUseCase, isTrue);

          // 验证Loading在完成后隐藏
          expect(controller.hideLoadingCalled, isTrue);

          // 验证服务调用成功
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('DialogService与NavigationService的交互', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'dialog_tenant_id',
            zoneId: 'dialog_zone_id',
            tenantName: 'Dialog Tenant',
          );

          final mockPolicy = PasswordPolicyModel(length: 8, mustContain: 'upper,lower,number');

          bool dialogShown = false;
          bool navigationCalledAfterDialog = false;

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 7158, msg: 'reset_password', policy: mockPolicy);
          });

          when(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          ).thenAnswer((invocation) async {
            dialogShown = true;
            final onConfirm = invocation.namedArguments[Symbol('onConfirm')] as Function;
            onConfirm();
          });

          when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async {
            navigationCalledAfterDialog = dialogShown;
          });

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证DialogService在NavigationService之前调用
          expect(dialogShown, isTrue);
          expect(navigationCalledAfterDialog, isTrue);

          // 验证服务调用次数
          verify(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          ).called(1);

          verify(
            mockNavigationService.navigateTo(AutoRoutes.loginResetPassword, arguments: anyNamed('arguments')),
          ).called(1);
        });

        test('多个服务调用的事务性处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'transaction_tenant_id',
            zoneId: 'transaction_zone_id',
            tenantName: 'Transaction Tenant',
          );

          // 设置mock行为 - NavigationService失败的情况
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenThrow(Exception('Navigation failed'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证即使NavigationService失败，也不会影响其他服务的状态
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证TenantUseCase仍然被调用
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });
      });

      group('服务状态一致性', () {
        test('异常后的服务状态恢复', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'recovery_tenant_id',
            zoneId: 'recovery_zone_id',
            tenantName: 'Recovery Tenant',
          );

          // 第一次调用失败
          when(mockTenantUseCase.call(any)).thenThrow(Exception('First call failed'));

          // Act - 第一次调用
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证第一次调用的状态
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 重置controller状态
          controller.resetState();

          // 设置第二次调用成功
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success');
          });
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 第二次调用
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证第二次调用成功
          expect(controller.handleExceptionCalled, isFalse);
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('并发调用的状态管理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'concurrent_tenant_id',
            zoneId: 'concurrent_zone_id',
            tenantName: 'Concurrent Tenant',
          );

          int useCaseCallCount = 0;

          // 设置mock行为 - 模拟慢速调用
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            useCaseCallCount++;
            await Future.delayed(const Duration(milliseconds: 200));
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 同时发起多个调用
          final futures = [
            controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123'),
            controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123'),
          ];

          await Future.wait(futures);

          // Assert - 验证并发调用的处理
          expect(useCaseCallCount, equals(2));

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证NavigationService被调用
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(2);
        });
      });

      group('集成边界条件', () {
        test('服务调用失败后的回滚机制', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'rollback_tenant_id',
            zoneId: 'rollback_zone_id',
            tenantName: 'Rollback Tenant',
          );

          // 设置mock行为 - 在中间步骤失败
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenThrow(Exception('Navigation failed'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证回滚机制
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态在异常后正确清理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证TenantUseCase已被调用
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('多个服务同时失败的处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'multi_fail_tenant_id',
            zoneId: 'multi_fail_zone_id',
            tenantName: 'Multi Fail Tenant',
          );

          // 设置mock行为 - 所有服务都失败
          when(mockTenantUseCase.call(any)).thenThrow(Exception('UseCase failed'));

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证异常处理
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证没有进行导航
          verifyNever(mockNavigationService.navigateOffAll(any));
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });

        test('服务依赖链中断的处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'chain_break_tenant_id',
            zoneId: 'chain_break_zone_id',
            tenantName: 'Chain Break Tenant',
          );

          // 设置mock行为 - 重置密码流程中policy为null
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 7158, msg: 'reset_password', policy: null);
          });

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123');

          // Assert - 验证依赖链中断后的处理
          expect(controller.handleExceptionCalled, isTrue);
          expect(controller.lastException, isA<SystemException>());

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证没有调用DialogService和NavigationService
          verifyNever(
            mockDialogService.show(
              title: anyNamed('title'),
              content: anyNamed('content'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          );
          verifyNever(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')));
        });
      });
    });

    group('Phase 5: 并发和边界条件测试', () {
      group('高并发场景测试', () {
        test('大量并发请求的处理能力', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'stress_tenant_id',
            zoneId: 'stress_zone_id',
            tenantName: 'Stress Test Tenant',
          );

          int callCount = 0;
          final List<String> executionOrder = [];
          final Completer<void> allCallsStarted = Completer<void>();
          int startedCalls = 0;

          // 设置mock行为 - 模拟慢速网络响应
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            final callId = ++callCount;
            executionOrder.add('call_${callId}_start');
            startedCalls++;

            if (startedCalls >= 50) {
              allCallsStarted.complete();
            }

            // 模拟网络延迟
            await Future.delayed(const Duration(milliseconds: 50));
            executionOrder.add('call_${callId}_end');
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 发起50个并发请求
          final futures = List.generate(
            50,
            (index) => controller.onTenantOption(
              tenant: tenant,
              userName: 'stress$<EMAIL>',
              password: 'password123',
            ),
          );

          // 等待所有请求开始
          await allCallsStarted.future;

          // 等待所有请求完成
          await Future.wait(futures);

          // Assert - 验证并发处理
          expect(callCount, equals(50));
          expect(executionOrder.length, equals(100)); // 50个start + 50个end

          // 验证Loading状态正确管理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证所有请求都被处理
          verify(mockTenantUseCase.call(any)).called(50);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(50);
        });

        test('并发请求中的异常隔离', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'isolation_tenant_id',
            zoneId: 'isolation_zone_id',
            tenantName: 'Isolation Test Tenant',
          );

          int callCount = 0;

          // 设置mock行为 - 奇数调用成功，偶数调用失败
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            final currentCall = ++callCount;
            if (currentCall % 2 == 0) {
              throw Exception('Even call failed: $currentCall');
            }
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 发起20个并发请求
          final futures = List.generate(
            20,
            (index) => controller.onTenantOption(
              tenant: tenant,
              userName: 'isolation$<EMAIL>',
              password: 'password123',
            ),
          );

          await Future.wait(futures);

          // Assert - 验证异常隔离
          expect(callCount, equals(20));

          // 验证成功的请求正常处理
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(10);

          // 验证所有请求都被调用
          verify(mockTenantUseCase.call(any)).called(20);
        });

        test('高频率连续调用的稳定性', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'frequency_tenant_id',
            zoneId: 'frequency_zone_id',
            tenantName: 'High Frequency Test Tenant',
          );

          final List<Duration> responseTimes = [];

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 10));
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 高频率连续调用
          for (int i = 0; i < 100; i++) {
            final stopwatch = Stopwatch()..start();

            await controller.onTenantOption(
              tenant: tenant,
              userName: 'frequency$<EMAIL>',
              password: 'password123',
            );

            stopwatch.stop();
            responseTimes.add(stopwatch.elapsed);

            // 重置控制器状态以准备下一次调用
            controller.resetState();
          }

          // Assert - 验证稳定性
          expect(responseTimes.length, equals(100));

          // 计算平均响应时间
          final avgResponseTime =
              responseTimes.map((d) => d.inMilliseconds).reduce((a, b) => a + b) / responseTimes.length;

          // 验证响应时间保持稳定（应该小于50ms）
          expect(avgResponseTime, lessThan(50));

          // 验证没有显著的性能衰减
          final firstHalf = responseTimes.take(50).map((d) => d.inMilliseconds).reduce((a, b) => a + b) / 50;
          final secondHalf = responseTimes.skip(50).map((d) => d.inMilliseconds).reduce((a, b) => a + b) / 50;

          // 后半部分的平均时间不应该显著增加（允许20%的增长）
          expect(secondHalf, lessThan(firstHalf * 1.2));
        });

        test('并发状态竞争条件处理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'race_tenant_id',
            zoneId: 'race_zone_id',
            tenantName: 'Race Condition Test Tenant',
          );

          final List<String> stateChanges = [];
          bool isProcessing = false;

          // 设置mock行为 - 模拟状态竞争
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            if (isProcessing) {
              stateChanges.add('concurrent_access_detected');
            }
            isProcessing = true;
            stateChanges.add('processing_started');

            await Future.delayed(const Duration(milliseconds: 30));

            stateChanges.add('processing_ended');
            isProcessing = false;

            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 快速连续发起请求以触发竞争条件
          final futures = [
            controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123'),
            controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123'),
            controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'password123'),
          ];

          await Future.wait(futures);

          // Assert - 验证竞争条件处理
          expect(stateChanges.isNotEmpty, isTrue);

          // 验证所有请求都被处理
          verify(mockTenantUseCase.call(any)).called(3);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(3);
        });
      });

      group('内存管理和资源优化测试', () {
        test('大量对象创建和销毁的内存稳定性', () async {
          // Arrange
          final List<SharedTenantModel> tenants = List.generate(
            1000,
            (index) => SharedTenantModel(
              tenantId: 'memory_tenant_$index',
              zoneId: 'memory_zone_$index',
              tenantName: 'Memory Test Tenant $index',
            ),
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 大量对象操作
          for (int i = 0; i < 1000; i++) {
            await controller.onTenantOption(
              tenant: tenants[i],
              userName: 'memory$<EMAIL>',
              password: 'password$i',
            );

            // 定期重置状态
            if (i % 100 == 0) {
              controller.resetState();
            }
          }

          // Assert - 验证内存稳定性
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证控制器仍然正常工作
          await controller.onTenantOption(tenant: tenants[0], userName: '<EMAIL>', password: 'finalpassword');

          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });

        test('长时间运行的内存泄漏检测', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'leak_test_tenant_id',
            zoneId: 'leak_test_zone_id',
            tenantName: 'Memory Leak Test Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 模拟长时间运行
          for (int cycle = 0; cycle < 50; cycle++) {
            // 每个周期执行多次操作
            for (int i = 0; i < 20; i++) {
              await controller.onTenantOption(
                tenant: tenant,
                userName: 'leak$cycle$<EMAIL>',
                password: 'password$cycle$i',
              );
              controller.resetState();
            }

            // 验证控制器状态仍然健康
            expect(() => controller.ticket.value = 'test_$cycle', returnsNormally);
            expect(() => controller.isBiometrics.value = cycle % 2 == 0, returnsNormally);
            expect(controller.ticket.value, equals('test_$cycle'));
            expect(controller.isBiometrics.value, equals(cycle % 2 == 0));
          }

          // Assert - 验证没有内存泄漏
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证最终状态正确
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'finaltest');

          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });

        test('异常情况下的资源清理', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'cleanup_tenant_id',
            zoneId: 'cleanup_zone_id',
            tenantName: 'Resource Cleanup Test Tenant',
          );

          int exceptionCount = 0;

          // 设置mock行为 - 随机抛出异常
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            exceptionCount++;
            if (exceptionCount % 3 == 0) {
              throw Exception('Resource cleanup test exception $exceptionCount');
            }
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 多次调用，期间会发生异常
          for (int i = 0; i < 100; i++) {
            await controller.onTenantOption(tenant: tenant, userName: 'cleanup$<EMAIL>', password: 'password$i');

            // 重置状态
            controller.resetState();
          }

          // Assert - 验证资源正确清理
          expect(exceptionCount, equals(100));

          // 验证控制器状态正常
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证控制器仍然可以正常工作
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success');
          });

          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'recovery123');

          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });
      });

      group('极限条件和边界值测试', () {
        test('超大数据量处理', () async {
          // Arrange
          final hugeString = 'x' * 100000; // 100KB字符串
          final tenant = SharedTenantModel(
            tenantId: 'huge_data_tenant_${hugeString.hashCode}',
            zoneId: 'huge_data_zone_${hugeString.hashCode}',
            tenantName: 'Huge Data Test Tenant $hugeString',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'success_with_huge_data');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '${hugeString.substring(0, 1000)}@example.com', // 限制用户名长度
            password: hugeString.substring(0, 1000), // 限制密码长度
          );

          // Assert - 验证大数据量处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证调用成功
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('极限边界值测试', () async {
          // Arrange
          const maxInt = 9223372036854775807; // Dart int最大值
          final tenant = SharedTenantModel(
            tenantId: maxInt.toString(),
            zoneId: maxInt.toString(),
            tenantName: 'Boundary Test Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'boundary_success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // 设置极限值状态
          controller.ticket.value = maxInt.toString();
          controller.isBiometrics.value = true;

          // Act
          await controller.onTenantOption(tenant: tenant, userName: '<EMAIL>', password: 'boundary123');

          // Assert - 验证边界值处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证状态保持正确
          expect(controller.ticket.value, equals(maxInt.toString()));
          expect(controller.isBiometrics.value, isTrue);

          // 验证调用成功
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('空值和特殊字符处理', () async {
          // Arrange
          final specialChars = '!@#\$%^&*()_+-=[]{}|;:\",./<>?~`\n\t\r\\';
          final tenant = SharedTenantModel(
            tenantId: 'special_tenant_id',
            zoneId: 'special_zone_id',
            tenantName: 'Special Chars Test: $specialChars',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'special_chars_success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // 设置特殊字符状态
          controller.ticket.value = specialChars;

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '${specialChars}<EMAIL>',
            password: '${specialChars}password123',
          );

          // Assert - 验证特殊字符处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证状态正确保存特殊字符
          expect(controller.ticket.value, equals(specialChars));

          // 验证调用成功
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });

        test('Unicode和国际化字符处理', () async {
          // Arrange
          const unicodeChars = '你好世界🌍🚀💡😊🎉中文Ññáéíóú日本語한국어العربية';
          final tenant = SharedTenantModel(
            tenantId: 'unicode_tenant_id',
            zoneId: 'unicode_zone_id',
            tenantName: 'Unicode Test: $unicodeChars',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'unicode_success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // 设置Unicode字符状态
          controller.ticket.value = unicodeChars;

          // Act
          await controller.onTenantOption(
            tenant: tenant,
            userName: '${unicodeChars}@example.com',
            password: '${unicodeChars}123',
          );

          // Assert - 验证Unicode字符处理
          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);

          // 验证Unicode字符正确保存
          expect(controller.ticket.value, equals(unicodeChars));

          // 验证调用成功
          verify(mockTenantUseCase.call(any)).called(1);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(1);
        });
      });

      group('性能和压力测试', () {
        test('响应时间性能测试', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'performance_tenant_id',
            zoneId: 'performance_zone_id',
            tenantName: 'Performance Test Tenant',
          );

          final List<Duration> responseTimes = [];

          // 设置mock行为 - 模拟真实网络延迟
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 100));
            return GetTokenResultModel(code: 0, msg: 'performance_success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 10));
          });

          // Act - 测量响应时间
          for (int i = 0; i < 20; i++) {
            final stopwatch = Stopwatch()..start();

            await controller.onTenantOption(
              tenant: tenant,
              userName: 'performance$<EMAIL>',
              password: 'performance123',
            );

            stopwatch.stop();
            responseTimes.add(stopwatch.elapsed);
            controller.resetState();
          }

          // Assert - 验证性能指标
          expect(responseTimes.length, equals(20));

          // 计算统计数据
          final responseTimesMs = responseTimes.map((d) => d.inMilliseconds).toList();
          final avgTime = responseTimesMs.reduce((a, b) => a + b) / responseTimesMs.length;
          final maxTime = responseTimesMs.reduce((a, b) => a > b ? a : b);
          final minTime = responseTimesMs.reduce((a, b) => a < b ? a : b);

          // 验证性能要求
          expect(avgTime, lessThan(150)); // 平均响应时间应该小于150ms
          expect(maxTime, lessThan(200)); // 最大响应时间应该小于200ms
          expect(minTime, greaterThan(50)); // 最小响应时间应该大于50ms（考虑mock延迟）

          // 验证响应时间稳定性（标准差不应该太大）
          final variance =
              responseTimesMs.map((t) => (t - avgTime) * (t - avgTime)).reduce((a, b) => a + b) /
              responseTimesMs.length;
          final stdDev = sqrt(variance);
          expect(stdDev, lessThan(30)); // 标准差应该小于30ms
        });

        test('内存使用压力测试', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'stress_memory_tenant_id',
            zoneId: 'stress_memory_zone_id',
            tenantName: 'Memory Stress Test Tenant',
          );

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            return GetTokenResultModel(code: 0, msg: 'memory_stress_success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 内存压力测试
          final List<Future<void>> allFutures = [];

          // 创建大量并发任务
          for (int batch = 0; batch < 10; batch++) {
            final batchFutures = List.generate(
              50,
              (index) => controller.onTenantOption(
                tenant: tenant,
                userName: 'stress${batch}_$<EMAIL>',
                password: 'stress_password_$batch$index',
              ),
            );
            allFutures.addAll(batchFutures);

            // 每批次之间短暂等待
            await Future.delayed(const Duration(milliseconds: 10));
          }

          // 等待所有任务完成
          await Future.wait(allFutures);

          // Assert - 验证压力测试结果
          expect(allFutures.length, equals(500));

          // 验证控制器仍然正常工作
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证所有调用都被处理
          verify(mockTenantUseCase.call(any)).called(500);
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(500);
        });

        test('长期稳定性测试', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'stability_tenant_id',
            zoneId: 'stability_zone_id',
            tenantName: 'Long Term Stability Test Tenant',
          );

          int totalCalls = 0;
          final List<int> batchCounts = [];

          // 设置mock行为
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            totalCalls++;
            await Future.delayed(const Duration(milliseconds: 5));
            return GetTokenResultModel(code: 0, msg: 'stability_success');
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 长期稳定性测试
          for (int round = 0; round < 10; round++) {
            int roundCalls = 0;

            // 每轮执行100次调用
            for (int i = 0; i < 100; i++) {
              await controller.onTenantOption(
                tenant: tenant,
                userName: 'stability${round}_$<EMAIL>',
                password: 'stability123',
              );
              roundCalls++;
              controller.resetState();
            }

            batchCounts.add(roundCalls);

            // 验证每轮后的状态
            expect(() => controller.ticket.value, returnsNormally);
            expect(() => controller.isBiometrics.value, returnsNormally);
          }

          // Assert - 验证长期稳定性
          expect(totalCalls, equals(1000));
          expect(batchCounts.length, equals(10));
          expect(batchCounts.every((count) => count == 100), isTrue);

          // 验证最终状态正常
          await controller.onTenantOption(
            tenant: tenant,
            userName: '<EMAIL>',
            password: 'final_stability123',
          );

          expect(controller.showLoadingCalled, isTrue);
          expect(controller.hideLoadingCalled, isTrue);
        });

        test('异常恢复压力测试', () async {
          // Arrange
          final tenant = SharedTenantModel(
            tenantId: 'recovery_stress_tenant_id',
            zoneId: 'recovery_stress_zone_id',
            tenantName: 'Recovery Stress Test Tenant',
          );

          int callCount = 0;
          int successCount = 0;
          int exceptionCount = 0;

          // 设置mock行为 - 70%成功率
          when(mockTenantUseCase.call(any)).thenAnswer((_) async {
            callCount++;
            if (callCount % 10 < 7) {
              successCount++;
              return GetTokenResultModel(code: 0, msg: 'recovery_success');
            } else {
              exceptionCount++;
              throw Exception('Recovery stress test exception $callCount');
            }
          });

          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});

          // Act - 异常恢复压力测试
          for (int i = 0; i < 200; i++) {
            await controller.onTenantOption(
              tenant: tenant,
              userName: 'recovery_stress$<EMAIL>',
              password: 'recovery_stress123',
            );
            controller.resetState();
          }

          // Assert - 验证异常恢复能力
          expect(callCount, equals(200));
          expect(successCount, greaterThan(120)); // 至少60%成功
          expect(exceptionCount, greaterThan(50)); // 至少25%异常

          // 验证控制器在大量异常后仍然正常
          expect(() => controller.ticket.value, returnsNormally);
          expect(() => controller.isBiometrics.value, returnsNormally);

          // 验证NavigationService只在成功时被调用
          verify(mockNavigationService.navigateOffAll(AutoRoutes.appTab)).called(successCount);
        });
      });
    });
  });
}
