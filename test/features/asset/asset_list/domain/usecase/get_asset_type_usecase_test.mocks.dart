// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_list/domain/usecase/get_asset_type_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAssetTypeResponse_0 extends _i1.SmartFake
    implements _i2.AssetTypeResponse {
  _FakeAssetTypeResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AssetTypeRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetTypeRepository extends _i1.Mock
    implements _i3.AssetTypeRepository {
  MockAssetTypeRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.AssetTypeResponse> getAllAssetTypes({
    String? assetTypeId = '',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllAssetTypes, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i4.Future<_i2.AssetTypeResponse>.value(
              _FakeAssetTypeResponse_0(
                this,
                Invocation.method(#getAllAssetTypes, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.AssetTypeResponse>);

  @override
  _i4.Future<int> checkAssetAuthority(int? assetTypeId, int? assetId) =>
      (super.noSuchMethod(
            Invocation.method(#checkAssetAuthority, [assetTypeId, assetId]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);
}
