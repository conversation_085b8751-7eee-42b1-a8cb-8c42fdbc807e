// test/asset/asset_type/presentation/pages/asset_type_list_page_test.dart

import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/controllers/asset_type_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/pages/asset_type_list_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/states/asset_type_list_ui_state.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// モッククラスのインポート
@GenerateNiceMocks([MockSpec<AssetTypeController>()])
import 'asset_type_list_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// モックデータ生成用のヘルパーメソッド
AssetTypeListModel createAssetType({required String assetTypeName, required int assetTypeId}) {
  return AssetTypeListModel(assetTypeName: assetTypeName, assetTypeId: assetTypeId);
}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockAssetTypeController mockController;
  late AssetTypeListUIState mockState;

  // テスト用の Widget 環境を構築するヘルパーメソッド
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: AssetTypeListPage());
  }

  setUp(() {
    // モックコントローラの初期化
    mockController = MockAssetTypeController();
    mockState = AssetTypeListUIState();

    // mockController.state が mockState を返すように設定
    when(mockController.state).thenReturn(mockState);

    // 必要なメソッドのスタブを設定
    when(mockController.cancelSelection()).thenReturn(null);
    when(mockController.updateSearch(any)).thenReturn(null);
    when(mockController.selectAssetType(any)).thenAnswer((_) async {});

    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // GetX の依存関係にモックコントローラを登録
    Get.put<AssetTypeController>(mockController);
  });

  tearDown(() {
    // GetX の依存関係をリセット
    Get.reset();
    reset(mockController);
  });

  group('AssetTypeListPage Widget Tests', () {
    // UI 要素のテスト
    group('UI ELEMENT TESTS', () {
      testWidgets('Displays AppBar with correct title and back button', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('資産種類選択'), findsOneWidget);
        expect(find.byIcon(Icons.chevron_left), findsOneWidget);
        expect(find.byType(TextField), findsOneWidget);
      });

      testWidgets('Displays BottomNavigationBar with cancel button', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('キャンセル'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('Displays "データがありません" when filteredAssetTypes is empty and isEmpty is true', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = true;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('データがありません'), findsOneWidget);
        expect(find.byType(ListView), findsNothing);
        expect(find.text('全件表示されました'), findsNothing);
      });

      testWidgets('Displays ListView with asset types when filteredAssetTypes is not empty', (tester) async {
        // Arrange
        final assetTypes = [
          createAssetType(assetTypeName: '資産種類A', assetTypeId: 1),
          createAssetType(assetTypeName: '資産種類B', assetTypeId: 2),
        ];
        mockState.filteredAssetTypes.assignAll(assetTypes);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(find.text('資産種類A'), findsOneWidget);
        expect(find.text('資産種類B'), findsOneWidget);
        expect(find.text('全件表示されました'), findsNothing);
      });

      testWidgets('Displays "全件表示されました" when isAllDataDisplayed is true', (tester) async {
        // Arrange
        final assetTypes = [
          createAssetType(assetTypeName: '資産種類A', assetTypeId: 1),
          createAssetType(assetTypeName: '資産種類B', assetTypeId: 2),
        ];
        mockState.filteredAssetTypes.assignAll(assetTypes);
        mockState.isAllDataDisplayed.value = true;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('全件表示されました'), findsOneWidget);
      });
    });

    // ユーザーインタラクションのテスト
    group('INTERACTION TESTS', () {
      testWidgets('Tapping back button calls cancelSelection', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap back button
        await tester.tap(find.byIcon(Icons.chevron_left));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.cancelSelection()).called(1);
      });

      testWidgets('Tapping cancel button calls cancelSelection', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap cancel button
        await tester.tap(find.text('キャンセル'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.cancelSelection()).called(1);
      });

      testWidgets('Typing in search field calls updateSearch', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Enter text in search field
        await tester.enterText(find.byType(TextField), '資産');
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.updateSearch('資産')).called(1);
      });

      testWidgets('Tapping on asset type item calls selectAssetType', (tester) async {
        // Arrange
        final assetTypes = [
          createAssetType(assetTypeName: '資産種類A', assetTypeId: 1),
          createAssetType(assetTypeName: '資産種類B', assetTypeId: 2),
        ];
        mockState.filteredAssetTypes.assignAll(assetTypes);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Tap on '資産種類A'
        await tester.tap(find.text('資産種類A'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockController.selectAssetType(assetTypes[0])).called(1);
      });
    });

    // 条件付きレンダリングのテスト
    group('CONDITIONAL RENDERING TESTS', () {
      testWidgets('Displays "データがありません" when filteredAssetTypes is empty and isEmpty is true', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = true;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('データがありません'), findsOneWidget);
        expect(find.byType(ListView), findsNothing);
        expect(find.text('全件表示されました'), findsNothing);
      });

      testWidgets('Displays ListView with asset types and "全件表示されました" when isAllDataDisplayed is true', (tester) async {
        // Arrange
        final assetTypes = [
          createAssetType(assetTypeName: '資産種類A', assetTypeId: 1),
          createAssetType(assetTypeName: '資産種類B', assetTypeId: 2),
        ];
        mockState.filteredAssetTypes.assignAll(assetTypes);
        mockState.isAllDataDisplayed.value = true;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(find.text('資産種類A'), findsOneWidget);
        expect(find.text('資産種類B'), findsOneWidget);
        expect(find.text('全件表示されました'), findsOneWidget);
      });
    });

    // デバッグ用のテスト（オプション）
    group('DEBUGGING TESTS', () {
      testWidgets('Prints widget tree for debugging', (tester) async {
        // Arrange
        mockState.filteredAssetTypes.assignAll([]);
        mockState.isAllDataDisplayed.value = false;
        mockState.isEmpty.value = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Print Widget tree
        debugDumpApp();

        // Assert
        expect(find.byType(AssetTypeListPage), findsOneWidget);
      });
    });
  });
}
