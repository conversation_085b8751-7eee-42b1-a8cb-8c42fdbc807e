import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/controllers/tenant_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';

// 自动生成 Mock 类
@GenerateNiceMocks([MockSpec<TenantUseCase>(), MockSpec<NavigationService>(), MockSpec<DialogService>()])
import 'tenant_controller_test.mocks.dart';

// 测试专用的 TenantController，避免影响原逻辑
class TestTenantController extends TenantController {
  TestTenantController({
    required super.tenantUseCase,
    required super.navigationService,
    required super.dialogService,
    this.testArguments,
  });

  final dynamic testArguments;
  bool exceptionHandled = false;
  bool getBackCalled = false;
  String lastUserNameUsed = '';
  String lastPasswordUsed = '';
  SharedTenantModel? lastTenantUsed;

  // 用于测试的字段来追踪私有变量的值
  String testPassword = '';
  String testUserName = '';

  @override
  void onInit() {
    // 不调用 super.onInit() 而是手动调用初始化逻辑
    _initializeDataWithTestArguments();
  }

  // 使用测试参数而不是 Get.arguments
  void _initializeDataWithTestArguments() async {
    final arguments = testArguments;

    if (arguments == null) {
      await handleException(SystemException());
      getBackCalled = true; // 模拟 Get.back() 调用
      return;
    }
    if (arguments is TenantParams) {
      final params = arguments;
      final List<SharedTenantModel> tenantList = params.tenants;

      tenantsList.value = tenantList;
      ticket.value = params.ticket;
      isBiometrics.value = params.isBiometrics;
      testPassword = params.password;
      testUserName = params.userName;
    } else {
      await handleException(SystemException());
      getBackCalled = true; // 模拟 Get.back() 调用
      return;
    }
  }

  @override
  Future<void> showLoading() async {
    // 在测试中跳过Loading UI显示
  }

  @override
  void hideLoading() {
    // 在测试中跳过Loading UI隐藏
  }

  @override
  Future<void> handleException(dynamic exception, [StackTrace? stackTrace, ErrorHandlingMode? errorHandlingMode]) {
    exceptionHandled = true;
    return Future<void>.value();
  }

  @override
  Future<void> onTenantOption({
    required SharedTenantModel tenant,
    required String userName,
    required String password,
  }) async {
    // 记录调用参数用于测试验证
    lastTenantUsed = tenant;
    lastUserNameUsed = userName;
    lastPasswordUsed = password;
  }

  // 重写 onTenantClick 以使用测试字段而不是私有字段
  @override
  onTenantClick(SharedTenantModel tenant) async {
    await onTenantOption(tenant: tenant, userName: testUserName, password: testPassword);
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late TestTenantController controller;
  late MockTenantUseCase mockTenantUseCase;
  late MockNavigationService mockNavigationService;
  late MockDialogService mockDialogService;

  // 测试数据
  final testTenantList = [
    SharedTenantModel(tenantId: 'tenant1', tenantName: 'Test Tenant 1', zoneId: 'zone1'),
    SharedTenantModel(tenantId: 'tenant2', tenantName: 'Test Tenant 2', zoneId: 'zone2'),
  ];

  final testTenantParams = TenantParams(
    tenants: testTenantList,
    isBiometrics: true,
    ticket: 'test-ticket',
    password: 'test-password',
    userName: 'test-user',
  );

  setUp(() {
    mockTenantUseCase = MockTenantUseCase();
    mockNavigationService = MockNavigationService();
    mockDialogService = MockDialogService();

    Get.testMode = true;
  });

  tearDown(() {
    Get.reset();
  });

  /// ----------------------------
  /// TenantController Tests
  /// ----------------------------

  /// 初始状态测试
  group('初始状态测试', () {
    test('初始状态应为空', () {
      // 初始状态应为空
      controller = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      expect(controller.tenantsList.isEmpty, true);
      expect(controller.tenantTitle.value, 'テナント選択');
      expect(controller.ticket.value, '');
      expect(controller.isBiometrics.value, false);
    });
  });

  /// 数据初始化测试
  group('数据初始化测试 (onInit)', () {
    test('正常情况下应成功初始化数据', () async {
      // 正常情况下应成功初始化数据
      // Arrange
      controller = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        testArguments: testTenantParams,
        dialogService: mockDialogService,
      );

      // Act
      controller.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成

      // Assert
      expect(controller.tenantsList.length, 2);
      expect(controller.tenantsList.first.tenantId, 'tenant1');
      expect(controller.ticket.value, 'test-ticket');
      expect(controller.isBiometrics.value, true);
      expect(controller.exceptionHandled, false);
    });

    test('当 arguments 为 null 时应处理异常', () async {
      // 当 arguments 为 null 时应处理异常
      // Arrange
      controller = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act
      controller.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成

      // Assert
      expect(controller.exceptionHandled, true);
      expect(controller.tenantsList.isEmpty, true);
    });

    test('当 arguments 不是 TenantParams 类型时应处理异常', () async {
      // 当 arguments 不是 TenantParams 类型时应处理异常
      // Arrange
      controller = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: 'invalid-type',
      );

      // Act
      controller.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成

      // Assert
      expect(controller.exceptionHandled, true);
      expect(controller.tenantsList.isEmpty, true);
    });

    test('应正确设置所有参数值', () async {
      // 应正确设置所有参数值
      // Arrange
      controller = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testTenantParams,
      );

      // Act
      controller.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成

      // Assert
      expect(controller.tenantsList, testTenantList);
      expect(controller.ticket.value, testTenantParams.ticket);
      expect(controller.isBiometrics.value, testTenantParams.isBiometrics);
      expect(controller.testPassword, testTenantParams.password);
      expect(controller.testUserName, testTenantParams.userName);
    });
  });

  /// 租户选择测试
  group('租户选择测试 (onTenantClick)', () {
    late TestTenantController controllerWithData;

    setUp(() async {
      // 先初始化数据
      controllerWithData = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: testTenantParams,
      );
      controllerWithData.onInit();
      await Future.delayed(Duration.zero); // 等待异步操作完成
    });

    test('应使用正确的参数调用 onTenantOption', () async {
      // 应使用正确的参数调用 onTenantOption
      // Arrange
      final testTenant = testTenantList.first;

      // Act
      await controllerWithData.onTenantClick(testTenant);

      // Assert
      expect(controllerWithData.lastTenantUsed, testTenant);
      expect(controllerWithData.lastUserNameUsed, 'test-user');
      expect(controllerWithData.lastPasswordUsed, 'test-password');
    });

    test('应能处理空租户对象', () async {
      // 应能处理空租户对象
      // Arrange
      final emptyTenant = SharedTenantModel();

      // Act
      await controllerWithData.onTenantClick(emptyTenant);

      // Assert
      expect(controllerWithData.lastTenantUsed, emptyTenant);
      expect(controllerWithData.lastUserNameUsed, 'test-user');
      expect(controllerWithData.lastPasswordUsed, 'test-password');
    });
  });

  /// 异常处理测试
  group('异常处理测试', () {
    test('handleException 应正确设置异常标志', () async {
      // handleException 应正确设置异常标志
      // Arrange
      controller = TestTenantController(
        tenantUseCase: mockTenantUseCase,
        navigationService: mockNavigationService,
        dialogService: mockDialogService,
        testArguments: null,
      );

      // Act
      await controller.handleException(SystemException());

      // Assert
      expect(controller.exceptionHandled, true);
    });
  });
}
