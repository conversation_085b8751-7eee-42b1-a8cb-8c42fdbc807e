import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/home_item_placeholder_widget.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/placeholder_animated_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateNiceMocks([MockSpec<MyHomeController>()])
import 'home_item_placeholder_widget_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockMyHomeController mockController;

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: Scaffold(body: HomeItemPlaceholderWidget()));
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    // 初始化模拟控制器
    mockController = MockMyHomeController();

    // 关键：使用onStart
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 添加其他可能需要的方法模拟
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});

    Get.put<MyHomeController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
  });

  group('HomeItemPlaceholderWidget UI 测试', () {
    testWidgets('应该正确渲染带有正确样式的容器', (WidgetTester tester) async {
      // 执行 - 使用 pump 而不是 pumpAndSettle，因为 ShimmerEffect 可能有无限动画
      await tester.pumpWidget(createWidgetUnderTest());

      // 只 pump 几次来更新 UI，但不等待所有动画完成
      await tester.pump(const Duration(milliseconds: 50));

      // 验证
      // 使用更精确的查找方式找到主容器
      final mainContainer = tester.widget<Container>(
        find.descendant(of: find.byType(HomeItemPlaceholderWidget), matching: find.byType(Container)).first,
      );

      final BoxDecoration decoration = mainContainer.decoration as BoxDecoration;

      // 检查边框半径
      expect(decoration.borderRadius, BorderRadius.circular(12), reason: '主容器应该有12的边框半径');

      // 检查背景颜色
      expect(decoration.color, const Color.fromRGBO(255, 255, 255, 0.85), reason: '容器应具有正确的背景颜色');

      // 检查阴影
      expect(decoration.boxShadow!.length, 1, reason: '应该有一个阴影');
      expect(decoration.boxShadow![0].color, Colors.black12, reason: '阴影颜色应为black12');
      expect(decoration.boxShadow![0].blurRadius, 6, reason: '阴影模糊半径应为6');
    });

    testWidgets('应该包含三个ShimmerEffect组件', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证
      final shimmerEffects = find.byType(ShimmerEffect);
      expect(shimmerEffects, findsNWidgets(3), reason: '应该有三个ShimmerEffect组件');
    });

    testWidgets('第一个ShimmerEffect组件应该包含有宽度的Container', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 找到第一个ShimmerEffect内的Container
      final firstShimmerEffect = find.byType(ShimmerEffect).first;
      final containerInShimmer = find.descendant(of: firstShimmerEffect, matching: find.byType(Container));

      // 验证Container存在且是否能找到它
      expect(containerInShimmer, findsOneWidget, reason: '第一个ShimmerEffect内应该有一个Container');

      // 这里不再直接访问width属性，而是检查Container是否正确渲染
      expect(tester.getSize(containerInShimmer).width > 0, true, reason: '第一个ShimmerEffect内的Container应该有宽度');
    });

    testWidgets('所有ShimmerEffect内的Container应该有正确的高度', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 检查每个ShimmerEffect内的Container
      for (int i = 0; i < 3; i++) {
        final containerInShimmer = find.descendant(
          of: find.byType(ShimmerEffect).at(i),
          matching: find.byType(Container),
        );

        // 验证Container的高度
        expect(tester.getSize(containerInShimmer).height, 10, reason: '第${i + 1}个ShimmerEffect内的Container高度应为10');
      }
    });

    testWidgets('所有ShimmerEffect内的Container应具有正确的背景颜色', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 找到所有ShimmerEffect内的Container
      for (int i = 0; i < 3; i++) {
        final container = tester.widget<Container>(
          find.descendant(of: find.byType(ShimmerEffect).at(i), matching: find.byType(Container)),
        );

        // 检查Container的装饰
        final decoration = container.decoration as BoxDecoration;
        expect(
          decoration.color,
          const Color.fromRGBO(169, 169, 169, 0.4),
          reason: '第${i + 1}个ShimmerEffect内的Container背景颜色应为RGBA(169,169,169,0.4)',
        );
      }
    });

    testWidgets('Column应具有正确的对齐方式', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 找到Column - 使用更精确的查找方式
      final column = tester.widget<Column>(
        find.descendant(of: find.byType(HomeItemPlaceholderWidget), matching: find.byType(Column)).first,
      );

      // 验证Column的属性
      expect(column.mainAxisAlignment, MainAxisAlignment.center, reason: 'Column的主轴对齐应为center');
      expect(column.crossAxisAlignment, CrossAxisAlignment.start, reason: 'Column的交叉轴对齐应为start');
    });

    testWidgets('主容器应具有正确的内边距', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 注意：这里是错误发生的地方，使用更精确的查找方法
      // 直接查找 HomeItemPlaceholderWidget 的第一个后代 Padding
      final padding = tester.widget<Padding>(
        find.descendant(of: find.byType(HomeItemPlaceholderWidget), matching: find.byType(Padding)).first,
      );

      // 查找 Padding 内的第一个 Container
      final container = tester.widget<Container>(
        find.descendant(of: find.byType(Padding).first, matching: find.byType(Container)).first,
      );

      // 验证padding值
      expect(
        padding.padding,
        const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        reason: '外部Padding应为水平16，垂直8',
      );
      expect(container.padding, const EdgeInsets.all(12), reason: '容器内边距应为全方向12');
    });

    testWidgets('组件应该能够在不同尺寸的屏幕上正确渲染', (WidgetTester tester) async {
      // 执行 - 在测试器的视图模式下测试
      addTearDown(() => tester.view.reset());

      // 设置屏幕尺寸
      tester.view.physicalSize = const Size(320, 480);
      tester.platformDispatcher.textScaleFactorTestValue = 1.0;

      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证 - 如果渲染没有错误，测试就通过
      expect(find.byType(HomeItemPlaceholderWidget), findsOneWidget);
    });
  });
}
