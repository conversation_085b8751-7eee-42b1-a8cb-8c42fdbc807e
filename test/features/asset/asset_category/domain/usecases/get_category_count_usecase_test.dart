// test/get_category_count_usecase_test.dart

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_list_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/repositories/category_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_count_usecase.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 通过 @GenerateMocks 注解生成 MockCategoryRepository
@GenerateMocks([CategoryRepository])
import 'get_category_count_usecase_test.mocks.dart';

void main() {
  late GetCategoryCountUseCase useCase;
  late MockCategoryRepository mockRepository;

  setUp(() {
    LogUtil.initialize();
    mockRepository = MockCategoryRepository();
    useCase = GetCategoryCountUseCase(mockRepository);
  });

  final testParams = GetCategoryCountParams(params: {'key': 'value'}, searchId: 'search123', keyword: 'test');

  final testCategoryItem = CategoryItemValueModel(
    categoryItemValue: 'Category1',
    count: 10,
    homeImageMobileDisplayFlg: true,
    // 其他字段根据需要添加
  );

  final testResult = GetCategoryCountResult(items: [testCategoryItem]);

  group('GetCategoryCountUseCase', () {
    test('should return GetCategoryCountResult when repository succeeds', () async {
      // Arrange
      when(
        mockRepository.getCategoryCount(
          params: anyNamed('params'),
          searchId: anyNamed('searchId'),
          keyword: anyNamed('keyword'),
        ),
      ).thenAnswer((_) async => CategoryItemValueListResponse(categoryItemValueList: [testCategoryItem]));

      // Act
      final result = await useCase.call(testParams);

      // Assert
      expect(result.items, isA<List<CategoryItemValueModel>>());
      expect(result.items, equals(testResult.items));
      verify(
        mockRepository.getCategoryCount(
          params: testParams.params,
          searchId: testParams.searchId,
          keyword: testParams.keyword,
        ),
      ).called(1);
    });

    test('should throw BusinessException when repository throws BusinessException', () async {
      // Arrange
      when(
        mockRepository.getCategoryCount(
          params: anyNamed('params'),
          searchId: anyNamed('searchId'),
          keyword: anyNamed('keyword'),
        ),
      ).thenThrow(BusinessException('Original business exception'));

      // Act & Assert
      expect(
        () => useCase.call(testParams),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', 'カテゴリ数の取得に失敗しました')),
      );

      verify(
        mockRepository.getCategoryCount(
          params: testParams.params,
          searchId: testParams.searchId,
          keyword: testParams.keyword,
        ),
      ).called(1);
    });

    test('should rethrow SystemException when repository throws SystemException', () async {
      // Arrange
      final systemException = SystemException(technicalMessage: 'System failure');
      when(
        mockRepository.getCategoryCount(
          params: anyNamed('params'),
          searchId: anyNamed('searchId'),
          keyword: anyNamed('keyword'),
        ),
      ).thenThrow(systemException);

      // Act & Assert
      expect(() => useCase.call(testParams), throwsA(systemException));

      verify(
        mockRepository.getCategoryCount(
          params: testParams.params,
          searchId: testParams.searchId,
          keyword: testParams.keyword,
        ),
      ).called(1);
    });
  });
}

// 假设 CategoryCountResponse 是 CategoryRepository.getCategoryCount 返回的类型
class CategoryCountResponse {
  final List<CategoryItemValueModel> categoryItemValueList;

  CategoryCountResponse({required this.categoryItemValueList});
}
