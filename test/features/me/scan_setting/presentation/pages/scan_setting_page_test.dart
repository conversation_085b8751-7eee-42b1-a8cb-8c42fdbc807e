import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/controllers/scan_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/models/scan_setting_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/pages/scan_setting_page.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/widgets/sound_settings_widget.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/widgets/sound_switch_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'scan_setting_page_test.mocks.dart';

// 生成 Mock 类
@GenerateNiceMocks([MockSpec<ScanSettingsController>()])
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  group('🧪 ScanSettingsPage Widget 测试', () {
    final mockInternalFinalCallback = MockInternalFinalCallback<void>();
    late MockScanSettingsController mockController;
    late ScanSettingUIModel testSettingsModel;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    // 创建测试 Widget 环境的辅助方法
    Widget createWidgetUnderTest() {
      return GetMaterialApp(theme: AppTheme.lightTheme, home: const ScanSettingsPage());
    }

    // 设置 Mock Controller 的辅助方法
    void setupMockController() {
      // 创建测试设置数据
      testSettingsModel = ScanSettingUIModel(
        useSound: true,
        useVibration: true,
        soundValue: 75.0,
        vibrationValue: 50.0,
        scanMusicList: 1,
      );

      // Mock 响应式变量
      when(mockController.model).thenReturn(testSettingsModel.obs);

      // 关键：Mock GetX 生命周期方法
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onInit()).thenAnswer((_) async {});
      when(mockController.onClose()).thenAnswer((_) async {});
      when(mockController.onReady()).thenAnswer((_) async {});

      // Mock 业务方法
      when(mockController.soundEventChange(any)).thenAnswer((_) async {});
      when(mockController.vibrationEventChange(any)).thenAnswer((_) async {});
      when(mockController.musicChange(any)).thenAnswer((_) async {});
      when(mockController.soundValueChange(any)).thenAnswer((_) async {});
      when(mockController.vibrationValueChange(any)).thenAnswer((_) async {});
    }

    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      Get.testMode = true; // 关键：启用 GetX 测试模式
      Get.reset(); // 确保干净的测试环境

      // 创建 Mock 实例
      mockController = MockScanSettingsController();

      // 配置 Mock Controller
      setupMockController();

      // 注册 Mock Controller
      Get.put<ScanSettingsController>(mockController);
    });

    tearDown(() {
      // 清理资源
      reset(mockController);
      clearInteractions(mockController);
      Get.reset();
    });

    // ========================================
    // Phase 0: 基础设施验证
    // ========================================
    group('🏗️ Phase 0: 测试基础设施验证', () {
      group('0.1 Mock 依赖创建验证', () {
        test('应该能够创建 MockScanSettingsController', () {
          // Arrange & Act
          final controller = MockScanSettingsController();

          // Assert
          expect(controller, isNotNull);
          expect(controller, isA<ScanSettingsController>());
        });

        test('测试设置数据应该正确初始化', () {
          // Arrange & Act
          final model = ScanSettingUIModel(
            useSound: true,
            useVibration: false,
            soundValue: 75.0,
            vibrationValue: 50.0,
            scanMusicList: 1,
          );

          // Assert
          expect(model.useSound, true);
          expect(model.useVibration, false);
          expect(model.soundValue, 75.0);
          expect(model.vibrationValue, 50.0);
          expect(model.scanMusicList, 1);
        });

        test('Mock Controller 的响应式变量应该正确工作', () {
          // Arrange & Act - setupMockController 已经在 setUp 中调用

          // Assert
          expect(mockController.model, isNotNull);
          expect(mockController.model.value, isA<ScanSettingUIModel>());
          expect(mockController.model.value.useSound, true);
          expect(mockController.model.value.soundValue, 75.0);
        });

        test('GetX 依赖注入应该正常工作', () {
          // Arrange & Act
          final injectedController = Get.find<ScanSettingsController>();

          // Assert
          expect(injectedController, isNotNull);
          expect(injectedController, equals(mockController));
        });

        test('应该能够对 Mock 方法进行 verify', () {
          // Arrange & Act
          mockController.soundEventChange(true);

          // Assert
          verify(mockController.soundEventChange(true)).called(1);
        });
      });

      group('0.2 Widget 构建测试', () {
        testWidgets('应该能够成功构建 ScanSettingsPage Widget', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(ScanSettingsPage), findsOneWidget);
        });

        testWidgets('应该能够构建 Scaffold', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(Scaffold), findsOneWidget);
        });

        testWidgets('应该能够构建 AppBar', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(AppBar), findsOneWidget);
        });

        testWidgets('应该能够构建子组件 Widgets', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSwitchWidget), findsOneWidget);
          expect(find.byType(SoundSettingsWidget), findsOneWidget);
        });

        testWidgets('Controller 依赖注入验证 - 正常状态下应该有 Controller', (tester) async {
          // Arrange - 确保有正确的 controller
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证正常构建成功
          expect(find.byType(ScanSettingsPage), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);
          expect(find.byType(AppBar), findsOneWidget);

          // 验证 Controller 被正确注入
          final injectedController = Get.find<ScanSettingsController>();
          expect(injectedController, isNotNull);
          expect(injectedController, equals(mockController));
        });

        testWidgets('重复构建不应该造成内存泄漏', (tester) async {
          // Act - 多次构建和销毁
          for (int i = 0; i < 3; i++) {
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();
            await tester.pumpWidget(Container()); // 清空
            await tester.pumpAndSettle();
          }

          // 最后一次正常构建
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(ScanSettingsPage), findsOneWidget);
        });
      });

      group('0.3 核心元素定位测试', () {
        testWidgets('页面主要 Key 应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('scan_setting_page')), findsOneWidget, reason: 'scan_setting_page Key 应该能被找到');
        });

        testWidgets('AppBar Key 应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(
            find.byKey(const Key('scan_setting_appbar')),
            findsOneWidget,
            reason: 'scan_setting_appbar Key 应该能被找到',
          );
        });

        testWidgets('标题 Key 应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('scan_setting_title')), findsOneWidget, reason: 'scan_setting_title Key 应该能被找到');
        });

        testWidgets('返回按钮 Key 应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(
            find.byKey(const Key('scan_setting_back_button')),
            findsOneWidget,
            reason: 'scan_setting_back_button Key 应该能被找到',
          );
        });

        testWidgets('页面 Body Key 应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('scan_setting_body')), findsOneWidget, reason: 'scan_setting_body Key 应该能被找到');
        });

        testWidgets('内容 Column Key 应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(
            find.byKey(const Key('scan_setting_content_column')),
            findsOneWidget,
            reason: 'scan_setting_content_column Key 应该能被找到',
          );
        });

        testWidgets('所有核心 Key 元素应该都能定位到', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有关键 Key
          final coreKeys = [
            'scan_setting_page',
            'scan_setting_appbar',
            'scan_setting_title',
            'scan_setting_back_button',
            'scan_setting_body',
            'scan_setting_content_column',
          ];

          for (final key in coreKeys) {
            expect(find.byKey(Key(key)), findsOneWidget, reason: 'Key "$key" 应该能被找到');
          }
        });
      });
    });

    // ========================================
    // Phase 1: UI 渲染和静态内容验证
    // ========================================
    group('🎨 Phase 1: UI 渲染和静态内容验证', () {
      group('1.1 AppBar 内容验证', () {
        testWidgets('应该显示正确的标题文本', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('スキャン音の設定'), findsOneWidget, reason: 'AppBar 应该显示正确的标题文本');
        });

        testWidgets('应该显示返回按钮', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byIcon(Icons.chevron_left), findsOneWidget, reason: '应该有一个左箭头返回按钮');
        });

        testWidgets('AppBar 应该有正确的背景色', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final appBar = tester.widget<AppBar>(find.byType(AppBar));
          expect(appBar.backgroundColor, const Color.fromARGB(0x20, 0x20, 0x20, 0x20), reason: 'AppBar 背景色应该正确');
        });

        testWidgets('返回按钮应该有正确的颜色', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final iconButton = tester.widget<IconButton>(find.byKey(const Key('scan_setting_back_button')));
          expect(iconButton.color, Colors.white, reason: '返回按钮颜色应该是白色');
        });

        testWidgets('AppBar 应该禁用自动返回按钮', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final appBar = tester.widget<AppBar>(find.byType(AppBar));
          expect(appBar.automaticallyImplyLeading, false, reason: 'AppBar 应该禁用自动返回按钮，使用自定义返回按钮');
        });
      });

      group('1.2 页面布局验证', () {
        testWidgets('Body 应该有正确的 Padding', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final padding = tester.widget<Padding>(find.byKey(const Key('scan_setting_body')));
          expect(padding.padding, const EdgeInsets.all(16.0), reason: 'Body 应该有 16.0 的全方向 padding');
        });

        testWidgets('主内容应该是 Column 布局', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('scan_setting_content_column')), findsOneWidget, reason: '主内容应该使用 Column 布局');

          final column = tester.widget<Column>(find.byKey(const Key('scan_setting_content_column')));
          expect(column.children.length, 2, reason: 'Column 应该包含 2 个子组件');
        });

        testWidgets('布局层次结构应该正确', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证层次结构

          // Scaffold 包含 AppBar 和 Body
          final scaffold = find.byType(Scaffold);
          expect(scaffold, findsOneWidget);

          // Body 是 Padding
          final body = find.descendant(of: scaffold, matching: find.byKey(const Key('scan_setting_body')));
          expect(body, findsOneWidget);

          // Padding 包含 Column
          final column = find.descendant(of: body, matching: find.byKey(const Key('scan_setting_content_column')));
          expect(column, findsOneWidget);
        });

        testWidgets('页面应该可以滚动（如果内容溢出）', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 虽然当前内容不需要滚动，但验证布局结构支持滚动
          final column = tester.widget<Column>(find.byKey(const Key('scan_setting_content_column')));

          // Column 默认不滚动，但内容应该适合屏幕
          expect(column.mainAxisSize, MainAxisSize.max, reason: '布局应该使用最大可用空间');
        });
      });

      group('1.3 子组件存在性验证', () {
        testWidgets('应该包含 SoundSwitchWidget', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSwitchWidget), findsOneWidget, reason: '页面应该包含一个 SoundSwitchWidget');
        });

        testWidgets('应该包含 SoundSettingsWidget', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(SoundSettingsWidget), findsOneWidget, reason: '页面应该包含一个 SoundSettingsWidget');
        });

        testWidgets('子组件应该按正确顺序排列', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final column = tester.widget<Column>(find.byKey(const Key('scan_setting_content_column')));
          final children = column.children;

          expect(children.length, 2, reason: '应该有 2 个子组件');
          expect(children[0], isA<SoundSwitchWidget>(), reason: '第一个组件应该是 SoundSwitchWidget');
          expect(children[1], isA<SoundSettingsWidget>(), reason: '第二个组件应该是 SoundSettingsWidget');
        });

        testWidgets('所有子组件应该正确渲染', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有子组件都能找到
          expect(find.byType(SoundSwitchWidget), findsOneWidget);
          expect(find.byType(SoundSettingsWidget), findsOneWidget);

          // 验证 Column 中只有这两个直接子组件
          final column = tester.widget<Column>(find.byKey(const Key('scan_setting_content_column')));
          expect(column.children.length, 2, reason: '应该只有 2 个直接子组件');

          // 验证子组件类型
          expect(column.children[0], isA<SoundSwitchWidget>());
          expect(column.children[1], isA<SoundSettingsWidget>());
        });
      });

      group('1.4 主题和样式验证', () {
        testWidgets('应该使用正确的主题', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final materialApp = tester.widget<GetMaterialApp>(find.byType(GetMaterialApp));
          expect(materialApp.theme, isNotNull, reason: '应该应用主题');
          expect(materialApp.theme, AppTheme.lightTheme, reason: '应该使用 AppTheme.lightTheme');
        });

        testWidgets('AppBar 样式应该正确应用', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final appBar = tester.widget<AppBar>(find.byType(AppBar));

          // 验证关键样式属性
          expect(appBar.backgroundColor, isNotNull, reason: 'AppBar 应该有背景色');
          expect(appBar.automaticallyImplyLeading, false, reason: '应该禁用自动返回按钮');
        });

        testWidgets('标题文本样式应该正确', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final titleText = tester.widget<Text>(find.byKey(const Key('scan_setting_title')));
          expect(titleText.data, 'スキャン音の設定', reason: '标题文本内容应该正确');
        });

        testWidgets('图标样式应该正确', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final iconButton = tester.widget<IconButton>(find.byKey(const Key('scan_setting_back_button')));
          final icon = iconButton.icon as Icon;

          expect(icon.icon, Icons.chevron_left, reason: '应该使用正确的图标');
          expect(iconButton.color, Colors.white, reason: '图标颜色应该是白色');
        });
      });

      group('1.5 文本内容验证', () {
        testWidgets('应该显示正确的页面标题', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('スキャン音の設定'), findsOneWidget, reason: '应该显示日文页面标题');
        });

        testWidgets('标题文本应该在正确位置', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final titleFinder = find.text('スキャン音の設定');
          final appBarFinder = find.byType(AppBar);

          // 验证标题在 AppBar 内
          final titleInAppBar = find.descendant(of: appBarFinder, matching: titleFinder);
          expect(titleInAppBar, findsOneWidget, reason: '标题应该在 AppBar 内');
        });

        testWidgets('不应该有多余的文本内容', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 在主页面级别不应该有其他文本（子组件的文本不计算在内）
          final allTexts = tester.widgetList<Text>(find.byType(Text));
          final pageTexts = allTexts.where((text) {
            // 只统计页面级别的文本，不包括子组件内的文本
            final textWidget = find.byWidget(text);
            final scaffoldFinder = find.byType(Scaffold);

            try {
              find.descendant(of: scaffoldFinder, matching: textWidget);
              return true;
            } catch (e) {
              return false;
            }
          }).toList();

          // 目前主页面应该只有标题文本
          expect(find.text('スキャン音の設定'), findsOneWidget, reason: '页面级别应该只有标题文本');
        });

        testWidgets('文本应该支持国际化', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final titleText = find.text('スキャン音の設定');
          expect(titleText, findsOneWidget, reason: '应该正确显示日文文本');

          // 验证字符编码正确
          final textWidget = tester.widget<Text>(find.byKey(const Key('scan_setting_title')));
          expect(textWidget.data, contains('スキャン'), reason: '应该正确显示日文字符');
          expect(textWidget.data, contains('音'), reason: '应该正确显示汉字字符');
          expect(textWidget.data, contains('設定'), reason: '应该正确显示日文汉字');
        });
      });

      // ========================================
      // Phase 2: 用户交互测试
      // ========================================
      group('🎯 Phase 2: 用户交互测试', () {
        group('2.1 返回按钮交互测试', () {
          testWidgets('返回按钮应该可以点击', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证按钮存在且可点击
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            expect(backButton, findsOneWidget, reason: '返回按钮应该存在');

            // 验证按钮可点击（不会抛出异常）
            await tester.tap(backButton);
            await tester.pumpAndSettle();
          });

          testWidgets('返回按钮点击应该触发 Get.back', (tester) async {
            // Arrange
            bool getBackCalled = false;

            // Mock Get.back 方法
            Get.testMode = true;

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Act - 点击返回按钮
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            await tester.tap(backButton);
            await tester.pumpAndSettle();

            // Assert - 验证 Get.back 被调用
            // 注意：在测试环境中，Get.back 不会实际执行，但不会抛出异常
            expect(backButton, findsOneWidget, reason: '按钮点击后仍然存在');
          });

          testWidgets('返回按钮应该有正确的点击区域', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证点击区域
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            final buttonWidget = tester.widget<IconButton>(backButton);

            // 验证按钮不为空且可交互
            expect(buttonWidget.onPressed, isNotNull, reason: '返回按钮应该有 onPressed 处理');
          });

          testWidgets('返回按钮的图标应该可见', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final icon = find.byIcon(Icons.chevron_left);
            expect(icon, findsOneWidget, reason: '返回按钮图标应该可见');

            // 验证图标在按钮内
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            final iconInButton = find.descendant(of: backButton, matching: icon);
            expect(iconInButton, findsOneWidget, reason: '图标应该在返回按钮内');
          });

          testWidgets('返回按钮在不同设备尺寸下应该正常工作', (tester) async {
            // Arrange - 设置不同的设备尺寸
            await tester.binding.setSurfaceSize(const Size(360, 640)); // 小屏幕

            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            expect(backButton, findsOneWidget);

            // 验证按钮可点击
            await tester.tap(backButton);
            await tester.pumpAndSettle();

            // 恢复默认尺寸
            await tester.binding.setSurfaceSize(null);
          });
        });

        group('2.2 导航交互测试', () {
          testWidgets('页面应该正确处理系统返回', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证页面存在，可以处理返回操作
            expect(find.byType(ScanSettingsPage), findsOneWidget);

            // 验证页面有 Scaffold，可以处理系统返回
            expect(find.byType(Scaffold), findsOneWidget);

            // 在测试环境中，我们不模拟实际的系统返回，只验证页面结构稳定性
            expect(find.byKey(const Key('scan_setting_back_button')), findsOneWidget, reason: '页面应该有自定义返回按钮处理返回');
          });

          testWidgets('AppBar 应该正确集成导航', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final appBar = tester.widget<AppBar>(find.byType(AppBar));
            expect(appBar.automaticallyImplyLeading, false, reason: '应该禁用自动返回按钮，使用自定义返回按钮');

            // 验证自定义返回按钮存在
            expect(find.byKey(const Key('scan_setting_back_button')), findsOneWidget);
          });

          testWidgets('导航状态应该正确维护', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证页面状态
            expect(find.byType(Scaffold), findsOneWidget);
            expect(find.byType(AppBar), findsOneWidget);

            // 点击返回按钮后，页面结构应该保持稳定
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            await tester.tap(backButton);
            await tester.pumpAndSettle();

            // 在测试环境中，页面应该仍然存在
            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });
        });

        group('2.3 子组件交互能力测试', () {
          testWidgets('SoundSwitchWidget 应该可以交互', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final soundSwitchWidget = find.byType(SoundSwitchWidget);
            expect(soundSwitchWidget, findsOneWidget, reason: 'SoundSwitchWidget 应该存在');

            // 验证子组件可以接收点击（不测试具体逻辑，只测试交互能力）
            await tester.tap(soundSwitchWidget);
            await tester.pumpAndSettle();

            // 子组件应该仍然存在
            expect(soundSwitchWidget, findsOneWidget);
          });

          testWidgets('SoundSettingsWidget 应该可以交互', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final soundSettingsWidget = find.byType(SoundSettingsWidget);
            expect(soundSettingsWidget, findsOneWidget, reason: 'SoundSettingsWidget 应该存在');

            // 验证子组件可以接收点击（不测试具体逻辑，只测试交互能力）
            await tester.tap(soundSettingsWidget);
            await tester.pumpAndSettle();

            // 子组件应该仍然存在
            expect(soundSettingsWidget, findsOneWidget);
          });

          testWidgets('子组件与页面应该正确协作', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证子组件与主页面的集成
            final soundSwitchWidget = find.byType(SoundSwitchWidget);
            final soundSettingsWidget = find.byType(SoundSettingsWidget);
            final scanSettingsPage = find.byType(ScanSettingsPage);

            // 验证层次结构
            expect(find.descendant(of: scanSettingsPage, matching: soundSwitchWidget), findsOneWidget);
            expect(find.descendant(of: scanSettingsPage, matching: soundSettingsWidget), findsOneWidget);

            // 验证交互不会影响页面结构
            await tester.tap(soundSwitchWidget);
            await tester.tap(soundSettingsWidget);
            await tester.pumpAndSettle();

            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('子组件应该能接收来自 Controller 的数据', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证数据流
            expect(find.byType(SoundSwitchWidget), findsOneWidget);
            expect(find.byType(SoundSettingsWidget), findsOneWidget);

            // 验证 Controller 的 model 数据可用
            expect(mockController.model, isNotNull);
            expect(mockController.model.value, isA<ScanSettingUIModel>());

            // 验证子组件可以访问数据（通过 GetView 或其他方式）
            final injectedController = Get.find<ScanSettingsController>();
            expect(injectedController, equals(mockController));
          });
        });

        group('2.4 手势交互测试', () {
          testWidgets('页面应该支持基本点击手势', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 测试不同位置的点击

            // 点击 AppBar
            await tester.tap(find.byType(AppBar));
            await tester.pumpAndSettle();

            // 点击 Body
            await tester.tap(find.byKey(const Key('scan_setting_body')));
            await tester.pumpAndSettle();

            // 页面应该保持稳定
            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('页面应该支持滚动手势（如果需要）', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 测试滚动
            final scrollableArea = find.byKey(const Key('scan_setting_content_column'));

            // 尝试滚动（即使内容不需要滚动，也应该不报错）
            await tester.drag(scrollableArea, const Offset(0, -100));
            await tester.pumpAndSettle();

            // 页面应该保持稳定
            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('页面应该正确处理多点触控', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 快速多次点击
            final backButton = find.byKey(const Key('scan_setting_back_button'));

            for (int i = 0; i < 3; i++) {
              await tester.tap(backButton);
              await tester.pump(const Duration(milliseconds: 50));
            }
            await tester.pumpAndSettle();

            // 页面应该保持稳定
            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('页面应该在不同屏幕密度下正常工作', (tester) async {
            // Arrange - 模拟不同的屏幕密度
            await tester.binding.setSurfaceSize(const Size(720, 1280)); // 高密度

            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            await tester.tap(backButton);
            await tester.pumpAndSettle();

            expect(find.byType(ScanSettingsPage), findsOneWidget);

            // 恢复
            await tester.binding.setSurfaceSize(null);
          });
        });

        group('2.5 交互反馈测试', () {
          testWidgets('返回按钮应该提供正确的视觉反馈', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            final buttonWidget = tester.widget<IconButton>(backButton);

            // 验证按钮有 onPressed 处理
            expect(buttonWidget.onPressed, isNotNull);

            // 验证按钮可以接收焦点和点击
            await tester.tap(backButton);
            await tester.pump(const Duration(milliseconds: 100)); // 短暂 pump 检查即时反馈

            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('交互应该不会造成内存泄漏', (tester) async {
            // Act - 重复交互
            for (int i = 0; i < 5; i++) {
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 各种交互
              await tester.tap(find.byKey(const Key('scan_setting_back_button')));
              await tester.tap(find.byType(SoundSwitchWidget));
              await tester.drag(find.byKey(const Key('scan_setting_content_column')), const Offset(0, -50));
              await tester.pumpAndSettle();

              // 清空页面
              await tester.pumpWidget(Container());
              await tester.pumpAndSettle();
            }

            // 最后一次正常构建
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert
            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('快速连续交互应该稳定处理', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 快速连续操作
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            final soundSwitch = find.byType(SoundSwitchWidget);

            // 快速交替点击
            for (int i = 0; i < 10; i++) {
              await tester.tap(i % 2 == 0 ? backButton : soundSwitch);
              await tester.pump(const Duration(milliseconds: 10));
            }
            await tester.pumpAndSettle();

            // 页面应该保持稳定
            expect(find.byType(ScanSettingsPage), findsOneWidget);
            expect(find.byKey(const Key('scan_setting_back_button')), findsOneWidget);
            expect(find.byType(SoundSwitchWidget), findsOneWidget);
          });

          testWidgets('错误的交互输入应该被正确处理', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 测试边界情况

            // 点击不存在的区域（不应该报错）
            await tester.tapAt(const Offset(-100, -100));
            await tester.pumpAndSettle();

            // 在页面外滑动（不应该报错）
            await tester.dragFrom(const Offset(-50, 100), const Offset(50, 100));
            await tester.pumpAndSettle();

            // 页面应该保持稳定
            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });

          testWidgets('无障碍性交互应该正常工作', (tester) async {
            // Act
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 验证无障碍性
            final backButton = find.byKey(const Key('scan_setting_back_button'));
            final buttonWidget = tester.widget<IconButton>(backButton);

            // IconButton 默认支持无障碍性
            expect(buttonWidget, isA<IconButton>());

            // 验证可以通过语义操作
            await tester.tap(backButton);
            await tester.pumpAndSettle();

            expect(find.byType(ScanSettingsPage), findsOneWidget);
          });
        });

        // ========================================
        // Phase 3: 子组件深度集成测试
        // ========================================
        group('🧩 Phase 3: 子组件深度集成测试', () {
          group('3.1 SoundSwitchWidget 深度测试', () {
            testWidgets('音声开关应该显示正确的文本', (tester) async {
              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              expect(find.text('音 ON/OFF'), findsOneWidget, reason: 'SoundSwitchWidget 应该显示音声开关文本');
            });

            testWidgets('音声开关应该反映 Controller 的状态', (tester) async {
              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final soundSwitch = tester.widget<Switch>(find.byType(Switch).first);
              expect(
                soundSwitch.value,
                mockController.model.value.useSound,
                reason: '开关状态应该与 Controller 中的 useSound 一致',
              );
            });

            testWidgets('点击音声开关应该调用 soundEventChange', (tester) async {
              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 点击开关
              await tester.tap(find.byType(Switch).first);
              await tester.pumpAndSettle();

              // Assert
              verify(mockController.soundEventChange(any)).called(1);
            });

            testWidgets('音声开关应该有正确的布局结构', (tester) async {
              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 验证布局结构
              final soundSwitchWidget = find.byType(SoundSwitchWidget);
              expect(soundSwitchWidget, findsOneWidget);

              // 验证 Container 和 Row 结构
              final container = find.descendant(of: soundSwitchWidget, matching: find.byType(Container));
              expect(container, findsOneWidget);

              final row = find.descendant(of: soundSwitchWidget, matching: find.byType(Row));
              expect(row, findsOneWidget);
            });

            testWidgets('音声开关状态变化应该立即反映', (tester) async {
              // Arrange - 设置初始状态
              testSettingsModel.useSound = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert 初始状态
              var soundSwitch = tester.widget<Switch>(find.byType(Switch).first);
              expect(soundSwitch.value, false);

              // 改变状态
              testSettingsModel.useSound = true;
              reset(mockController);
              setupMockController();
              when(mockController.model).thenReturn(testSettingsModel.obs);
              Get.reset();
              Get.put<ScanSettingsController>(mockController);
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert 新状态
              soundSwitch = tester.widget<Switch>(find.byType(Switch).first);
              expect(soundSwitch.value, true);
            });
          });

          group('3.2 SoundSettingsWidget 深度测试', () {
            testWidgets('当音声开关关闭时应该隐藏设置内容', (tester) async {
              // Arrange - 设置音声开关为关闭
              testSettingsModel.useSound = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 应该找不到音效设置的内容
              expect(find.text('音の選択'), findsNothing, reason: '音声关闭时不应该显示音效选择');
              expect(find.text('音量調節'), findsNothing, reason: '音声关闭时不应该显示音量调节');
              expect(find.byType(DropdownButton<int>), findsNothing, reason: '音声关闭时不应该显示下拉框');
              expect(find.byType(Slider), findsNothing, reason: '音声关闭时不应该显示滑块');
            });

            testWidgets('当音声开关开启时应该显示设置内容', (tester) async {
              // Arrange - 设置音声开关为开启
              testSettingsModel.useSound = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 应该显示音效设置的内容
              expect(find.text('音の選択'), findsOneWidget, reason: '音声开启时应该显示音效选择');
              expect(find.text('音量調節'), findsOneWidget, reason: '音声开启时应该显示音量调节');
              expect(find.byType(DropdownButton<int>), findsOneWidget, reason: '音声开启时应该显示下拉框');
            });

            testWidgets('音效选择下拉框应该显示正确的选项', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 点击下拉框
              await tester.tap(find.byType(DropdownButton<int>));
              await tester.pumpAndSettle();

              // Assert - 验证下拉框选项（展开时会有重复项，所以至少有一个）
              expect(find.text('コンプリート'), findsWidgets, reason: '应该有コンプリート选项');
              expect(find.text('ビンゴ'), findsWidgets, reason: '应该有ビンゴ选项');
              expect(find.text('コンプリート'), findsAtLeastNWidgets(1), reason: '至少应该找到一个コンプリート选项');
              expect(find.text('ビンゴ'), findsAtLeastNWidgets(1), reason: '至少应该找到一个ビンゴ选项');
            });

            testWidgets('音效选择应该反映 Controller 的状态', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.scanMusicList = 2; // ビンゴ
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final dropdown = tester.widget<DropdownButton<int>>(find.byType(DropdownButton<int>));
              expect(dropdown.value, 2, reason: '下拉框值应该与 Controller 中的 scanMusicList 一致');
            });

            testWidgets('音量滑块应该反映 Controller 的状态', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.soundValue = 75.0;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final slider = tester.widget<Slider>(find.byType(Slider).first);
              expect(slider.value, 75.0, reason: '滑块值应该与 Controller 中的 soundValue 一致');
              expect(slider.min, 0.0, reason: '滑块最小值应该是 0.0');
              expect(slider.max, 100.0, reason: '滑块最大值应该是 100.0');
            });

            testWidgets('改变音效选择应该调用 musicChange', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 点击下拉框
              await tester.tap(find.byType(DropdownButton<int>));
              await tester.pumpAndSettle();

              // 选择 ビンゴ 选项
              await tester.tap(find.text('ビンゴ').last);
              await tester.pumpAndSettle();

              // Assert
              verify(mockController.musicChange(2)).called(1);
            });

            testWidgets('音量滑块应该显示正确的图标', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              expect(find.byIcon(Icons.volume_mute), findsOneWidget, reason: '应该有静音图标');
              expect(find.byIcon(Icons.volume_up), findsOneWidget, reason: '应该有音量增大图标');
            });
          });

          group('3.3 VibrationSettingWidget 深度测试', () {
            testWidgets('当音声开关关闭时振动设置应该隐藏', (tester) async {
              // Arrange
              testSettingsModel.useSound = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              expect(find.text('バイブレーション'), findsNothing, reason: '音声关闭时不应该显示振动设置');
              expect(find.text('振動調節'), findsNothing, reason: '音声关闭时不应该显示振动调节');
            });

            testWidgets('当音声开关开启时振动设置应该显示', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              expect(find.text('バイブレーション'), findsOneWidget, reason: '音声开启时应该显示振动设置');
              expect(find.text('振動調節'), findsOneWidget, reason: '音声开启时应该显示振动调节');
            });

            testWidgets('振动开关应该反映 Controller 的状态', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final vibrationSwitch = tester.widget<Switch>(find.byType(Switch).at(1)); // 第二个开关是振动开关
              expect(vibrationSwitch.value, false, reason: '振动开关状态应该与 Controller 中的 useVibration 一致');
            });

            testWidgets('当振动开关关闭时振动滑块应该隐藏', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 第一个滑块是音量，第二个应该是振动（但应该隐藏）
              final sliders = find.byType(Slider);
              expect(sliders, findsOneWidget, reason: '只应该有音量滑块，振动滑块应该隐藏');
            });

            testWidgets('当振动开关开启时振动滑块应该显示', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final sliders = find.byType(Slider);
              expect(sliders, findsNWidgets(2), reason: '应该有音量和振动两个滑块');
            });

            testWidgets('振动滑块应该反映 Controller 的状态', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              testSettingsModel.vibrationValue = 80.0;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final vibrationSlider = tester.widget<Slider>(find.byType(Slider).at(1)); // 第二个滑块是振动
              expect(vibrationSlider.value, 80.0, reason: '振动滑块值应该与 Controller 中的 vibrationValue 一致');
              expect(vibrationSlider.divisions, 4, reason: '振动滑块应该有 4 个分段');
            });

            testWidgets('点击振动开关应该调用 vibrationEventChange', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 点击振动开关
              await tester.tap(find.byType(Switch).at(1));
              await tester.pumpAndSettle();

              // Assert
              verify(mockController.vibrationEventChange(any)).called(1);
            });

            testWidgets('振动滑块应该显示正确的图标', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              expect(find.byIcon(Icons.phone_android), findsOneWidget, reason: '应该有手机图标');
              expect(find.byIcon(Icons.vibration), findsOneWidget, reason: '应该有振动图标');
            });
          });

          group('3.4 子组件数据绑定测试', () {
            testWidgets('Controller 数据变化应该立即反映到所有子组件', (tester) async {
              // Arrange - 初始状态
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              testSettingsModel.soundValue = 50.0;
              testSettingsModel.vibrationValue = 60.0;
              testSettingsModel.scanMusicList = 1;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 验证初始状态
              var soundSwitch = tester.widget<Switch>(find.byType(Switch).first);
              var vibrationSwitch = tester.widget<Switch>(find.byType(Switch).at(1));
              var soundSlider = tester.widget<Slider>(find.byType(Slider).first);
              var vibrationSlider = tester.widget<Slider>(find.byType(Slider).at(1));
              var dropdown = tester.widget<DropdownButton<int>>(find.byType(DropdownButton<int>));

              expect(soundSwitch.value, true);
              expect(vibrationSwitch.value, true);
              expect(soundSlider.value, 50.0);
              expect(vibrationSlider.value, 60.0);
              expect(dropdown.value, 1);

              // Act - 改变数据（创建新的设置模型）
              final updatedSettingsModel = ScanSettingUIModel(
                useSound: false, // 关闭音声，这应该隐藏大部分内容
                useVibration: false,
                soundValue: 50.0,
                vibrationValue: 60.0,
                scanMusicList: 1,
              );

              reset(mockController);
              when(mockController.model).thenReturn(updatedSettingsModel.obs);
              // Mock 生命周期方法
              when(mockController.onStart).thenReturn(mockInternalFinalCallback);
              when(mockController.onInit()).thenAnswer((_) async {});
              when(mockController.onClose()).thenAnswer((_) async {});
              when(mockController.onReady()).thenAnswer((_) async {});
              // Mock 业务方法
              when(mockController.soundEventChange(any)).thenAnswer((_) async {});
              when(mockController.vibrationEventChange(any)).thenAnswer((_) async {});
              when(mockController.musicChange(any)).thenAnswer((_) async {});
              when(mockController.soundValueChange(any)).thenAnswer((_) async {});
              when(mockController.vibrationValueChange(any)).thenAnswer((_) async {});

              Get.reset();
              Get.put<ScanSettingsController>(mockController);
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 验证变化后的状态
              soundSwitch = tester.widget<Switch>(find.byType(Switch).first);
              expect(soundSwitch.value, false, reason: '音声开关应该变为关闭状态');

              // 其他组件应该隐藏
              expect(find.text('音の選択'), findsNothing);
              expect(find.text('バイブレーション'), findsNothing);
            });

            testWidgets('子组件应该能正确处理边界值数据', (tester) async {
              // Arrange - 设置边界值
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              testSettingsModel.soundValue = 0.0; // 最小值
              testSettingsModel.vibrationValue = 100.0; // 最大值
              testSettingsModel.scanMusicList = 2;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              final soundSlider = tester.widget<Slider>(find.byType(Slider).first);
              final vibrationSlider = tester.widget<Slider>(find.byType(Slider).at(1));
              final dropdown = tester.widget<DropdownButton<int>>(find.byType(DropdownButton<int>));

              expect(soundSlider.value, 0.0, reason: '应该正确处理最小音量值');
              expect(vibrationSlider.value, 100.0, reason: '应该正确处理最大振动值');
              expect(dropdown.value, 2, reason: '应该正确处理音效选择值');
            });
          });

          group('3.5 子组件交互联动测试', () {
            testWidgets('音声开关关闭应该隐藏所有相关设置', (tester) async {
              // Arrange - 先测试音声关闭状态下的隐藏行为
              testSettingsModel.useSound = false;
              testSettingsModel.useVibration = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 音声关闭时所有相关设置都应该隐藏
              expect(find.text('音の選択'), findsNothing, reason: '音声关闭后音效选择应该隐藏');
              expect(find.text('バイブレーション'), findsNothing, reason: '音声关闭后振动设置应该隐藏');
              expect(find.byType(Slider), findsNothing, reason: '音声关闭后所有滑块都应该隐藏');
              expect(find.byType(DropdownButton<int>), findsNothing, reason: '音声关闭后下拉框应该隐藏');

              // 验证只有音声开关仍然可见
              expect(find.text('音 ON/OFF'), findsOneWidget, reason: '音声开关文本应该始终可见');
              expect(find.byType(Switch), findsOneWidget, reason: '应该只有音声开关可见');
            });

            testWidgets('振动开关关闭应该只隐藏振动滑块', (tester) async {
              // Arrange - 音声开启，振动关闭
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = false;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 验证振动关闭时的状态
              expect(find.byType(Slider), findsOneWidget, reason: '振动关闭后应该只有音量滑块');
              expect(find.text('音の選択'), findsOneWidget, reason: '音效选择应该仍然可见');
              expect(find.text('バイブレーション'), findsOneWidget, reason: '振动开关文本应该仍然可见');
              expect(find.text('振動調節'), findsOneWidget, reason: '振动调节文本应该可见');

              // 验证有两个开关（音声和振动）
              expect(find.byType(Switch), findsNWidgets(2), reason: '应该有音声和振动两个开关');

              // 验证振动开关状态
              final vibrationSwitch = tester.widget<Switch>(find.byType(Switch).at(1));
              expect(vibrationSwitch.value, false, reason: '振动开关应该是关闭状态');
            });

            testWidgets('所有子组件应该能同时正常工作', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 依次测试所有交互元素

              // 1. 测试音声开关
              await tester.tap(find.byType(Switch).first);
              await tester.pump(const Duration(milliseconds: 100));
              verify(mockController.soundEventChange(any)).called(1);

              // 2. 测试音效选择
              await tester.tap(find.byType(DropdownButton<int>));
              await tester.pumpAndSettle();
              await tester.tap(find.text('ビンゴ').last);
              await tester.pumpAndSettle();
              verify(mockController.musicChange(2)).called(1);

              // 3. 测试振动开关
              await tester.tap(find.byType(Switch).at(1));
              await tester.pump(const Duration(milliseconds: 100));
              verify(mockController.vibrationEventChange(any)).called(1);

              // Assert - 所有组件应该仍然存在且稳定
              expect(find.byType(SoundSwitchWidget), findsOneWidget);
              expect(find.byType(SoundSettingsWidget), findsOneWidget);
            });
          });
        });

        // ========================================
        // Phase 4: 高级功能和稳定性测试
        // ========================================
        group('🚀 Phase 4: 高级功能和稳定性测试', () {
          group('4.1 性能测试', () {
            testWidgets('页面构建性能应该在合理范围内', (tester) async {
              // Arrange
              final stopwatch = Stopwatch()..start();

              // Act - 多次构建页面测试性能
              for (int i = 0; i < 5; i++) {
                await tester.pumpWidget(createWidgetUnderTest());
                await tester.pumpAndSettle();
                await tester.pumpWidget(Container()); // 清理
                await tester.pumpAndSettle();
              }

              stopwatch.stop();

              // Assert - 性能应该在合理范围内（每次构建 < 1秒）
              final averageTime = stopwatch.elapsedMilliseconds / 5;
              expect(averageTime, lessThan(1000), reason: '页面平均构建时间应该少于1秒，实际: ${averageTime}ms');

              print('📊 页面平均构建时间: ${averageTime.toStringAsFixed(2)}ms');
            });

            testWidgets('响应式数据变化性能测试', (tester) async {
              // Arrange
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              final stopwatch = Stopwatch()..start();

              // Act - 快速连续的数据变化
              for (int i = 0; i < 20; i++) {
                testSettingsModel.useSound = i % 2 == 0;
                testSettingsModel.useVibration = i % 3 == 0;
                testSettingsModel.soundValue = (i * 5.0) % 100;
                testSettingsModel.vibrationValue = (i * 3.0) % 100;

                reset(mockController);
                setupMockController();
                when(mockController.model).thenReturn(testSettingsModel.obs);
                Get.reset();
                Get.put<ScanSettingsController>(mockController);

                await tester.pumpWidget(createWidgetUnderTest());
                await tester.pump(); // 只 pump 一次，不等待动画
              }

              stopwatch.stop();

              // Assert
              final averageUpdateTime = stopwatch.elapsedMilliseconds / 20;
              expect(averageUpdateTime, lessThan(100), reason: '数据更新平均时间应该少于100ms，实际: ${averageUpdateTime}ms');

              print('📊 数据更新平均时间: ${averageUpdateTime.toStringAsFixed(2)}ms');

              // 验证最终状态正确
              expect(find.byType(ScanSettingsPage), findsOneWidget);
            });

            testWidgets('大量交互操作性能测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              final stopwatch = Stopwatch()..start();

              // Act - 大量快速交互操作
              for (int i = 0; i < 50; i++) {
                // 交替点击不同的交互元素
                switch (i % 4) {
                  case 0:
                    await tester.tap(find.byType(Switch).first);
                    break;
                  case 1:
                    if (find.byType(Switch).evaluate().length > 1) {
                      await tester.tap(find.byType(Switch).at(1));
                    }
                    break;
                  case 2:
                    if (find.byType(DropdownButton<int>).evaluate().isNotEmpty) {
                      await tester.tap(find.byType(DropdownButton<int>));
                      await tester.pump(const Duration(milliseconds: 10));
                      // 快速关闭下拉框
                      await tester.tapAt(const Offset(0, 0));
                    }
                    break;
                  case 3:
                    // 滑块拖拽操作
                    if (find.byType(Slider).evaluate().isNotEmpty) {
                      await tester.drag(find.byType(Slider).first, const Offset(50, 0));
                    }
                    break;
                }
                await tester.pump(const Duration(milliseconds: 5)); // 最小延迟
              }

              stopwatch.stop();

              // Assert
              final averageInteractionTime = stopwatch.elapsedMilliseconds / 50;
              expect(averageInteractionTime, lessThan(50), reason: '交互操作平均时间应该少于50ms，实际: ${averageInteractionTime}ms');

              print('📊 交互操作平均时间: ${averageInteractionTime.toStringAsFixed(2)}ms');

              // 页面应该保持稳定
              expect(find.byType(ScanSettingsPage), findsOneWidget);
            });

            testWidgets('内存使用稳定性测试', (tester) async {
              // Act - 重复创建和销毁页面
              for (int i = 0; i < 10; i++) {
                await tester.pumpWidget(createWidgetUnderTest());
                await tester.pumpAndSettle();

                // 执行一些交互
                await tester.tap(find.byType(Switch).first);
                await tester.pump();

                // 销毁页面
                await tester.pumpWidget(Container());
                await tester.pumpAndSettle();

                // 清理 GetX
                Get.reset();
                setupMockController();
                Get.put<ScanSettingsController>(mockController);
              }

              // 最后一次正常构建
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 页面应该仍然正常工作
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.byType(Switch), findsWidgets);

              print('✅ 内存使用稳定性测试完成 - 10次创建/销毁循环');
            });
          });

          group('4.2 压力测试', () {
            testWidgets('快速连续开关切换压力测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 极快速的开关切换
              for (int i = 0; i < 100; i++) {
                await tester.tap(find.byType(Switch).first);
                await tester.pump(const Duration(milliseconds: 1));

                if (i % 10 == 0) {
                  // 每10次检查页面稳定性
                  expect(find.byType(ScanSettingsPage), findsOneWidget, reason: '第${i}次操作后页面应该保持稳定');
                }
              }

              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.byType(Switch), findsWidgets);
              verify(mockController.soundEventChange(any)).called(100);

              print('✅ 快速开关切换压力测试完成 - 100次连续操作');
            });

            testWidgets('滑块连续拖拽压力测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 连续快速拖拽滑块
              final slider = find.byType(Slider).first;
              for (int i = 0; i < 50; i++) {
                final direction = i % 2 == 0 ? const Offset(20, 0) : const Offset(-20, 0);
                await tester.drag(slider, direction);
                await tester.pump(const Duration(milliseconds: 2));
              }

              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.byType(Slider), findsWidgets);

              // 验证滑块操作回调被调用
              verify(mockController.soundValueChange(any)).called(greaterThan(10));

              print('✅ 滑块拖拽压力测试完成 - 50次连续拖拽');
            });

            testWidgets('混合操作压力测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 混合各种操作
              for (int i = 0; i < 30; i++) {
                // 随机选择操作类型
                switch (i % 6) {
                  case 0:
                    await tester.tap(find.byType(Switch).first);
                    break;
                  case 1:
                    if (find.byType(Switch).evaluate().length > 1) {
                      await tester.tap(find.byType(Switch).at(1));
                    }
                    break;
                  case 2:
                    if (find.byType(DropdownButton<int>).evaluate().isNotEmpty) {
                      await tester.tap(find.byType(DropdownButton<int>));
                      await tester.pump(const Duration(milliseconds: 5));
                      await tester.tapAt(const Offset(0, 0)); // 关闭下拉框
                    }
                    break;
                  case 3:
                    if (find.byType(Slider).evaluate().isNotEmpty) {
                      await tester.drag(find.byType(Slider).first, const Offset(30, 0));
                    }
                    break;
                  case 4:
                    // 点击AppBar
                    await tester.tap(find.byType(AppBar));
                    break;
                  case 5:
                    // 点击返回按钮
                    await tester.tap(find.byKey(const Key('scan_setting_back_button')));
                    break;
                }
                await tester.pump(const Duration(milliseconds: 3));

                // 每5次操作检查一次稳定性
                if (i % 5 == 0) {
                  expect(find.byType(ScanSettingsPage), findsOneWidget, reason: '混合操作第${i}次后页面应该保持稳定');
                }
              }

              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              print('✅ 混合操作压力测试完成 - 30次随机操作组合');
            });

            testWidgets('长时间连续操作稳定性测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              int operationCount = 0;
              final stopwatch = Stopwatch()..start();

              // Act - 持续操作直到达到时间或操作数限制
              while (stopwatch.elapsedMilliseconds < 2000 && operationCount < 200) {
                await tester.tap(find.byType(Switch).first);
                await tester.pump(const Duration(milliseconds: 10));
                operationCount++;

                // 每20次操作验证稳定性
                if (operationCount % 20 == 0) {
                  expect(find.byType(ScanSettingsPage), findsOneWidget, reason: '长时间操作第${operationCount}次后页面应该保持稳定');
                }
              }

              stopwatch.stop();
              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(operationCount, greaterThan(50), reason: '应该执行了足够多的操作');

              print('✅ 长时间操作稳定性测试完成 - ${operationCount}次操作，耗时${stopwatch.elapsedMilliseconds}ms');
            });
          });

          group('4.3 异常处理测试', () {
            testWidgets('Controller 异常状态处理', (tester) async {
              // Arrange - 模拟 Controller 方法异常，但不是 model getter 异常
              reset(mockController);
              setupMockController();
              when(mockController.soundEventChange(any)).thenThrow(Exception('Mock Controller Error'));
              when(mockController.vibrationEventChange(any)).thenThrow(Exception('Mock Controller Error'));
              when(mockController.musicChange(any)).thenThrow(Exception('Mock Controller Error'));

              // Act - 正常构建页面
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 页面能正常构建
              expect(find.byType(ScanSettingsPage), findsOneWidget);

              // 验证异常方法被正确设置（通过异常的存在来验证）
              expect(() => mockController.soundEventChange(true), throwsA(isA<Exception>()));
              expect(() => mockController.vibrationEventChange(true), throwsA(isA<Exception>()));
              expect(() => mockController.musicChange(1), throwsA(isA<Exception>()));

              print('✅ Controller 异常状态测试完成');
            });

            testWidgets('数据模型异常处理', (tester) async {
              // Arrange - 创建边界有效但接近极限的数据模型
              final boundaryModel = ScanSettingUIModel(
                useSound: true,
                useVibration: true,
                soundValue: 0.0, // 最小有效值
                vibrationValue: 100.0, // 最大有效值
                scanMusicList: 1, // 有效选项
              );

              reset(mockController);
              setupMockController();
              when(mockController.model).thenReturn(boundaryModel.obs);
              Get.reset();
              Get.put<ScanSettingsController>(mockController);

              // Act - 页面应该能正常构建
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 页面应该能够处理边界数据
              expect(find.byType(ScanSettingsPage), findsOneWidget);

              // 验证滑块显示边界值
              if (find.byType(Slider).evaluate().isNotEmpty) {
                final soundSlider = tester.widget<Slider>(find.byType(Slider).first);
                expect(soundSlider.value, 0.0, reason: '音量滑块应该显示最小值');

                if (find.byType(Slider).evaluate().length > 1) {
                  final vibrationSlider = tester.widget<Slider>(find.byType(Slider).at(1));
                  expect(vibrationSlider.value, 100.0, reason: '振动滑块应该显示最大值');
                }
              }

              print('✅ 数据模型异常处理测试完成');
            });

            testWidgets('GetX 依赖注入失败处理', (tester) async {
              // 注意：这个测试验证依赖注入的重要性，而不是测试异常捕获
              // 我们通过对比有依赖和无依赖的情况来验证

              // 首先测试正常情况
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();
              expect(find.byType(ScanSettingsPage), findsOneWidget);

              // 然后清理并验证没有依赖时确实会有问题
              Get.reset();
              // 不重新注入依赖，这样就模拟了依赖注入失败的情况

              // 验证依赖确实被清空了
              expect(() => Get.find<ScanSettingsController>(), throwsA(isA<String>()));

              print('✅ GetX 依赖注入失败处理测试完成');
            });

            testWidgets('网络或存储异常模拟', (tester) async {
              // Arrange - 正常构建页面
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 模拟异步操作异常（在操作之前设置）
              when(mockController.soundEventChange(any)).thenThrow(Exception('Storage Error'));
              when(mockController.vibrationEventChange(any)).thenThrow(Exception('Network Error'));
              when(mockController.musicChange(any)).thenThrow(Exception('Update Error'));

              // Act & Assert - 验证异常确实被设置
              expect(() => mockController.soundEventChange(true), throwsA(isA<Exception>()));
              expect(() => mockController.vibrationEventChange(true), throwsA(isA<Exception>()));
              expect(() => mockController.musicChange(1), throwsA(isA<Exception>()));

              // 页面结构应该保持稳定
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.byType(Switch), findsWidgets);

              print('✅ 网络存储异常模拟测试完成');
            });

            testWidgets('UI 异常恢复能力测试', (tester) async {
              // Arrange - 测试UI在异常条件下的恢复能力
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 模拟数据状态变化（而不是触发异常）

              // 1. 测试从异常数据恢复到正常数据
              final errorStateModel = ScanSettingUIModel(
                useSound: false,
                useVibration: false,
                soundValue: 0.0,
                vibrationValue: 0.0,
                scanMusicList: 1,
              );

              reset(mockController);
              setupMockController();
              when(mockController.model).thenReturn(errorStateModel.obs);
              Get.reset();
              Get.put<ScanSettingsController>(mockController);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // 验证错误状态下页面仍然稳定
              expect(find.byType(ScanSettingsPage), findsOneWidget);

              // 2. 恢复到正常状态
              reset(mockController);
              setupMockController();
              when(mockController.model).thenReturn(testSettingsModel.obs);
              Get.reset();
              Get.put<ScanSettingsController>(mockController);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 应该能够完全恢复
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.byType(Switch), findsWidgets);

              // 正常交互应该工作
              await tester.tap(find.byType(Switch).first);
              await tester.pump();
              verify(mockController.soundEventChange(any)).called(1);

              print('✅ UI 异常恢复能力测试完成');
            });
          });

          group('4.4 边界条件测试', () {
            testWidgets('滑块极限值测试', (tester) async {
              // Arrange - 设置极限值
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              testSettingsModel.soundValue = 0.0; // 最小值
              testSettingsModel.vibrationValue = 100.0; // 最大值
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              if (find.byType(Slider).evaluate().isNotEmpty) {
                final soundSlider = tester.widget<Slider>(find.byType(Slider).first);
                expect(soundSlider.value, 0.0, reason: '音量滑块应该显示最小值');
                expect(soundSlider.min, 0.0, reason: '最小值应该是0');
                expect(soundSlider.max, 100.0, reason: '最大值应该是100');

                if (find.byType(Slider).evaluate().length > 1) {
                  final vibrationSlider = tester.widget<Slider>(find.byType(Slider).at(1));
                  expect(vibrationSlider.value, 100.0, reason: '振动滑块应该显示最大值');
                }
              }

              print('✅ 滑块极限值测试完成');
            });

            testWidgets('下拉框边界选项测试', (tester) async {
              // Arrange - 测试边界选项值
              testSettingsModel.useSound = true;
              testSettingsModel.scanMusicList = 1; // 最小有效选项
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 测试选项1
              if (find.byType(DropdownButton<int>).evaluate().isNotEmpty) {
                final dropdown = tester.widget<DropdownButton<int>>(find.byType(DropdownButton<int>));
                expect(dropdown.value, 1, reason: '下拉框应该显示选项1');
              }

              // 切换到选项2 - 需要重新创建数据模型，而不是修改现有的
              final newSettingsModel = ScanSettingUIModel(
                useSound: true,
                useVibration: testSettingsModel.useVibration,
                soundValue: testSettingsModel.soundValue,
                vibrationValue: testSettingsModel.vibrationValue,
                scanMusicList: 2, // 最大有效选项
              );

              reset(mockController);
              setupMockController();
              when(mockController.model).thenReturn(newSettingsModel.obs);
              Get.reset();
              Get.put<ScanSettingsController>(mockController);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              if (find.byType(DropdownButton<int>).evaluate().isNotEmpty) {
                final dropdown = tester.widget<DropdownButton<int>>(find.byType(DropdownButton<int>));
                expect(dropdown.value, 2, reason: '下拉框应该显示选项2');
              }

              print('✅ 下拉框边界选项测试完成');
            });

            testWidgets('布尔值边界状态测试', (tester) async {
              // Arrange & Act - 测试所有布尔值组合
              final combinations = [
                [true, true], // 都开启
                [true, false], // 只开启音声
                [false, true], // 只开启振动（应该被音声控制）
                [false, false], // 都关闭
              ];

              for (int i = 0; i < combinations.length; i++) {
                // 创建新的数据模型而不是修改现有的
                final newSettingsModel = ScanSettingUIModel(
                  useSound: combinations[i][0],
                  useVibration: combinations[i][1],
                  soundValue: 50.0,
                  vibrationValue: 50.0,
                  scanMusicList: 1,
                );

                reset(mockController);
                setupMockController();
                when(mockController.model).thenReturn(newSettingsModel.obs);
                Get.reset();
                Get.put<ScanSettingsController>(mockController);

                await tester.pumpWidget(createWidgetUnderTest());
                await tester.pumpAndSettle();

                // Assert
                expect(find.byType(ScanSettingsPage), findsOneWidget, reason: '布尔组合 ${i + 1} 应该正常显示');

                final switches = find.byType(Switch);
                expect(switches, findsWidgets, reason: '至少应该有音声开关');

                if (switches.evaluate().isNotEmpty) {
                  final soundSwitch = tester.widget<Switch>(switches.first);
                  expect(soundSwitch.value, combinations[i][0], reason: '音声开关状态应该正确 - 组合${i + 1}');
                }
              }

              print('✅ 布尔值边界状态测试完成 - 测试了4种组合');
            });

            testWidgets('空数据和初始状态处理测试', (tester) async {
              // Arrange - 重置数据到初始状态
              testSettingsModel.useSound = false;
              testSettingsModel.useVibration = false;
              testSettingsModel.soundValue = 0.0;
              testSettingsModel.vibrationValue = 0.0;
              testSettingsModel.scanMusicList = 1;

              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert - 应该能够处理"空"状态
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.text('音 ON/OFF'), findsOneWidget);

              // 音声关闭时，其他内容应该隐藏
              expect(find.text('音の選択'), findsNothing);
              expect(find.text('バイブレーション'), findsNothing);

              print('✅ 空数据处理测试完成');
            });

            testWidgets('数据类型边界测试', (tester) async {
              // Arrange - 测试浮点数精度边界
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              testSettingsModel.soundValue = 0.1; // 最小精度
              testSettingsModel.vibrationValue = 99.9; // 最大精度
              when(mockController.model).thenReturn(testSettingsModel.obs);

              // Act
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              if (find.byType(Slider).evaluate().isNotEmpty) {
                final soundSlider = tester.widget<Slider>(find.byType(Slider).first);
                expect(soundSlider.value, closeTo(0.1, 0.01), reason: '应该能处理小数精度');

                if (find.byType(Slider).evaluate().length > 1) {
                  final vibrationSlider = tester.widget<Slider>(find.byType(Slider).at(1));
                  expect(vibrationSlider.value, closeTo(99.9, 0.01), reason: '应该能处理接近最大值的小数');
                }
              }

              print('✅ 数据类型边界测试完成');
            });
          });

          group('4.5 并发和竞态条件测试', () {
            testWidgets('快速状态切换竞态测试', (tester) async {
              // Arrange
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 快速连续改变多个状态
              for (int i = 0; i < 20; i++) {
                // 快速切换音声开关
                await tester.tap(find.byType(Switch).first);
                await tester.pump(const Duration(microseconds: 500));

                // 如果有振动开关，也快速切换
                if (find.byType(Switch).evaluate().length > 1) {
                  await tester.tap(find.byType(Switch).at(1));
                  await tester.pump(const Duration(microseconds: 500));
                }
              }

              await tester.pumpAndSettle();

              // Assert - 页面应该保持稳定
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              expect(find.byType(Switch), findsWidgets);

              print('✅ 快速状态切换竞态测试完成 - 40次快速切换');
            });

            testWidgets('并发数据更新测试', (tester) async {
              // Arrange
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 模拟并发数据更新
              for (int i = 0; i < 10; i++) {
                // 同时更新多个值
                testSettingsModel.useSound = i % 2 == 0;
                testSettingsModel.useVibration = i % 3 == 0;
                testSettingsModel.soundValue = (i * 10.0) % 100;
                testSettingsModel.vibrationValue = (i * 7.0) % 100;
                testSettingsModel.scanMusicList = (i % 2) + 1;

                // 立即更新 UI，不等待
                reset(mockController);
                setupMockController();
                when(mockController.model).thenReturn(testSettingsModel.obs);
                Get.reset();
                Get.put<ScanSettingsController>(mockController);

                await tester.pumpWidget(createWidgetUnderTest());
                await tester.pump(const Duration(milliseconds: 1));
              }

              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              print('✅ 并发数据更新测试完成 - 10次快速数据更新');
            });

            testWidgets('多组件同时交互测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 顺序执行操作而不是真正的并发，避免测试框架冲突

              // 操作1: 连续点击开关
              for (int i = 0; i < 5; i++) {
                await tester.tap(find.byType(Switch).first);
                await tester.pump(const Duration(milliseconds: 10));
              }

              // 操作2: 连续拖拽滑块
              if (find.byType(Slider).evaluate().isNotEmpty) {
                for (int i = 0; i < 3; i++) {
                  await tester.drag(find.byType(Slider).first, const Offset(10, 0));
                  await tester.pump(const Duration(milliseconds: 10));
                }
              }

              // 操作3: 点击返回按钮
              for (int i = 0; i < 2; i++) {
                await tester.tap(find.byKey(const Key('scan_setting_back_button')));
                await tester.pump(const Duration(milliseconds: 15));
              }

              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              print('✅ 多组件同时交互测试完成');
            });

            testWidgets('状态一致性验证测试', (tester) async {
              // Arrange
              testSettingsModel.useSound = true;
              testSettingsModel.useVibration = true;
              testSettingsModel.soundValue = 50.0;
              testSettingsModel.vibrationValue = 75.0;
              testSettingsModel.scanMusicList = 1;
              when(mockController.model).thenReturn(testSettingsModel.obs);

              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Act - 进行一系列复杂操作后验证状态一致性
              for (int i = 0; i < 5; i++) {
                // 操作音声开关
                await tester.tap(find.byType(Switch).first);
                await tester.pump();

                // 操作振动开关
                if (find.byType(Switch).evaluate().length > 1) {
                  await tester.tap(find.byType(Switch).at(1));
                  await tester.pump();
                }

                // 操作下拉框
                if (find.byType(DropdownButton<int>).evaluate().isNotEmpty) {
                  await tester.tap(find.byType(DropdownButton<int>));
                  await tester.pump();
                  await tester.tapAt(const Offset(0, 0)); // 关闭
                  await tester.pump();
                }
              }

              await tester.pumpAndSettle();

              // Assert - 验证最终状态的一致性
              expect(find.byType(ScanSettingsPage), findsOneWidget);

              // 验证所有组件仍然可以正常访问
              expect(find.byType(Switch), findsWidgets);
              expect(find.text('音 ON/OFF'), findsOneWidget);

              // 验证 Controller 调用
              verify(mockController.soundEventChange(any)).called(5);

              print('✅ 状态一致性验证测试完成');
            });

            testWidgets('资源竞争处理测试', (tester) async {
              // Arrange & Act - 模拟资源竞争
              final operationCount = 15;

              for (int i = 0; i < operationCount; i++) {
                // 快速创建和销毁页面，模拟资源竞争
                await tester.pumpWidget(createWidgetUnderTest());
                await tester.pump(); // 不等待完成

                // 立即进行交互
                if (find.byType(Switch).evaluate().isNotEmpty) {
                  await tester.tap(find.byType(Switch).first);
                  await tester.pump(const Duration(microseconds: 100));
                }

                // 立即销毁
                await tester.pumpWidget(Container());
                await tester.pump(const Duration(microseconds: 100));

                // 重新设置
                Get.reset();
                setupMockController();
                Get.put<ScanSettingsController>(mockController);
              }

              // 最终验证
              await tester.pumpWidget(createWidgetUnderTest());
              await tester.pumpAndSettle();

              // Assert
              expect(find.byType(ScanSettingsPage), findsOneWidget);
              print('✅ 资源竞争处理测试完成 - ${operationCount}次快速创建/销毁');
            });
          });
        });
      });
    });
  });
}
