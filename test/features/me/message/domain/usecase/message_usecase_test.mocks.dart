// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/message/domain/usecase/message_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/features/me/message/data/models/notification_detail_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/me/message/data/models/notification_model.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/me/message/domain/repositories/message_repository.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_usecase.dart'
    as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeNotificationDetailModel_0 extends _i1.SmartFake
    implements _i2.NotificationDetailModel {
  _FakeNotificationDetailModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MessageRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockMessageRepository extends _i1.Mock implements _i3.MessageRepository {
  MockMessageRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<int> getNotificationCount() =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationCount, []),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<List<_i5.NotificationModel>> getNotificationList({
    required _i6.NotificationListQuery? query,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationList, [], {#query: query}),
            returnValue: _i4.Future<List<_i5.NotificationModel>>.value(
              <_i5.NotificationModel>[],
            ),
          )
          as _i4.Future<List<_i5.NotificationModel>>);

  @override
  _i4.Future<_i2.NotificationDetailModel> getNotificationDetail({
    required int? notificationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationDetail, [], {
              #notificationId: notificationId,
            }),
            returnValue: _i4.Future<_i2.NotificationDetailModel>.value(
              _FakeNotificationDetailModel_0(
                this,
                Invocation.method(#getNotificationDetail, [], {
                  #notificationId: notificationId,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.NotificationDetailModel>);

  @override
  _i4.Future<void> setNotificationRead(int? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#setNotificationRead, [notificationId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
