// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_list/domain/usecase/load_data_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i14;

import 'package:asset_force_mobile_v2/core/network/base_response.dart' as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_history_records_count_response.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting_response.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/customize_asset_list_model_response.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/master_layout_setting_response.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart'
    as _i15;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart'
    as _i19;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_by_id_response.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_by_keyword_response.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_layout_setting_response.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_relation_list_response.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart'
    as _i18;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_functional_processing_repository.dart'
    as _i17;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart'
    as _i16;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i20;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCustomizeAssetListModelResponse_0 extends _i1.SmartFake
    implements _i2.CustomizeAssetListModelResponse {
  _FakeCustomizeAssetListModelResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeAssetMobileResponse_1 extends _i1.SmartFake
    implements _i3.AssetMobileResponse {
  _FakeAssetMobileResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMasterLayoutSettingResponse_2 extends _i1.SmartFake
    implements _i4.MasterLayoutSettingResponse {
  _FakeMasterLayoutSettingResponse_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetRelationListResponse_3 extends _i1.SmartFake
    implements _i5.AssetRelationListResponse {
  _FakeAssetRelationListResponse_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetByIdResponse_4 extends _i1.SmartFake
    implements _i6.AssetByIdResponse {
  _FakeAssetByIdResponse_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetItemResponse_5 extends _i1.SmartFake
    implements _i7.AssetItemResponse {
  _FakeAssetItemResponse_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBaseResponse_6 extends _i1.SmartFake implements _i8.BaseResponse {
  _FakeBaseResponse_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetLayoutSettingResponse_7 extends _i1.SmartFake
    implements _i9.AssetLayoutSettingResponse {
  _FakeAssetLayoutSettingResponse_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetHistoryRecordsCountResponse_8 extends _i1.SmartFake
    implements _i10.AssetHistoryRecordsCountResponse {
  _FakeAssetHistoryRecordsCountResponse_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeAssetByKeywordResponse_9 extends _i1.SmartFake
    implements _i11.AssetByKeywordResponse {
  _FakeAssetByKeywordResponse_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAssetTypeResponse_10 extends _i1.SmartFake
    implements _i12.AssetTypeResponse {
  _FakeAssetTypeResponse_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AssetRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetRepository extends _i1.Mock implements _i13.AssetRepository {
  MockAssetRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Future<_i2.CustomizeAssetListModelResponse> getCustomizeSearchForMobile({
    required _i15.CustomizeSearchForMobileRequestQuery? searchParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getCustomizeSearchForMobile, [], {
              #searchParams: searchParams,
            }),
            returnValue: _i14.Future<_i2.CustomizeAssetListModelResponse>.value(
              _FakeCustomizeAssetListModelResponse_0(
                this,
                Invocation.method(#getCustomizeSearchForMobile, [], {
                  #searchParams: searchParams,
                }),
              ),
            ),
          )
          as _i14.Future<_i2.CustomizeAssetListModelResponse>);

  @override
  _i14.Future<_i3.AssetMobileResponse> getAssetMobileSettings({
    required int? assetTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetMobileSettings, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i14.Future<_i3.AssetMobileResponse>.value(
              _FakeAssetMobileResponse_1(
                this,
                Invocation.method(#getAssetMobileSettings, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i14.Future<_i3.AssetMobileResponse>);

  @override
  _i14.Future<_i4.MasterLayoutSettingResponse> getAllMasterLayoutSetting({
    required int? typeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllMasterLayoutSetting, [], {
              #typeId: typeId,
            }),
            returnValue: _i14.Future<_i4.MasterLayoutSettingResponse>.value(
              _FakeMasterLayoutSettingResponse_2(
                this,
                Invocation.method(#getAllMasterLayoutSetting, [], {
                  #typeId: typeId,
                }),
              ),
            ),
          )
          as _i14.Future<_i4.MasterLayoutSettingResponse>);

  @override
  _i14.Future<_i5.AssetRelationListResponse> getAssetRelationList({
    required int? assetId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetRelationList, [], {#assetId: assetId}),
            returnValue: _i14.Future<_i5.AssetRelationListResponse>.value(
              _FakeAssetRelationListResponse_3(
                this,
                Invocation.method(#getAssetRelationList, [], {
                  #assetId: assetId,
                }),
              ),
            ),
          )
          as _i14.Future<_i5.AssetRelationListResponse>);

  @override
  _i14.Future<_i6.AssetByIdResponse> getAssetById({required int? assetId}) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetById, [], {#assetId: assetId}),
            returnValue: _i14.Future<_i6.AssetByIdResponse>.value(
              _FakeAssetByIdResponse_4(
                this,
                Invocation.method(#getAssetById, [], {#assetId: assetId}),
              ),
            ),
          )
          as _i14.Future<_i6.AssetByIdResponse>);

  @override
  _i14.Future<bool> getAssetsPrintAble({required String? assetTypeIdsStr}) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetsPrintAble, [], {
              #assetTypeIdsStr: assetTypeIdsStr,
            }),
            returnValue: _i14.Future<bool>.value(false),
          )
          as _i14.Future<bool>);

  @override
  _i14.Future<_i7.AssetItemResponse> getAssetItemType({
    required String? assetTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetItemType, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i14.Future<_i7.AssetItemResponse>.value(
              _FakeAssetItemResponse_5(
                this,
                Invocation.method(#getAssetItemType, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i14.Future<_i7.AssetItemResponse>);

  @override
  _i14.Future<_i8.BaseResponse> saveAsset({
    required String? assetId,
    required String? assetTypeId,
    required String? assetText,
    String? modifiedDate,
    String? barcode,
    String? relationAssetIdList,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#saveAsset, [], {
              #assetId: assetId,
              #assetTypeId: assetTypeId,
              #assetText: assetText,
              #modifiedDate: modifiedDate,
              #barcode: barcode,
              #relationAssetIdList: relationAssetIdList,
            }),
            returnValue: _i14.Future<_i8.BaseResponse>.value(
              _FakeBaseResponse_6(
                this,
                Invocation.method(#saveAsset, [], {
                  #assetId: assetId,
                  #assetTypeId: assetTypeId,
                  #assetText: assetText,
                  #modifiedDate: modifiedDate,
                  #barcode: barcode,
                  #relationAssetIdList: relationAssetIdList,
                }),
              ),
            ),
          )
          as _i14.Future<_i8.BaseResponse>);

  @override
  _i14.Future<_i8.BaseResponse> insertAsset(Map<String, dynamic>? assetData) =>
      (super.noSuchMethod(
            Invocation.method(#insertAsset, [assetData]),
            returnValue: _i14.Future<_i8.BaseResponse>.value(
              _FakeBaseResponse_6(
                this,
                Invocation.method(#insertAsset, [assetData]),
              ),
            ),
          )
          as _i14.Future<_i8.BaseResponse>);

  @override
  _i14.Future<_i9.AssetLayoutSettingResponse> getLayoutSetting({
    String? typeId = '0',
    String? classification = '5',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getLayoutSetting, [], {
              #typeId: typeId,
              #classification: classification,
            }),
            returnValue: _i14.Future<_i9.AssetLayoutSettingResponse>.value(
              _FakeAssetLayoutSettingResponse_7(
                this,
                Invocation.method(#getLayoutSetting, [], {
                  #typeId: typeId,
                  #classification: classification,
                }),
              ),
            ),
          )
          as _i14.Future<_i9.AssetLayoutSettingResponse>);

  @override
  _i14.Future<_i10.AssetHistoryRecordsCountResponse>
  getAssetHistoryRecordsCount({
    required int? assetId,
    required String? appurtenancesInformationTypeId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAssetHistoryRecordsCount, [], {
              #assetId: assetId,
              #appurtenancesInformationTypeId: appurtenancesInformationTypeId,
            }),
            returnValue:
                _i14.Future<_i10.AssetHistoryRecordsCountResponse>.value(
                  _FakeAssetHistoryRecordsCountResponse_8(
                    this,
                    Invocation.method(#getAssetHistoryRecordsCount, [], {
                      #assetId: assetId,
                      #appurtenancesInformationTypeId:
                          appurtenancesInformationTypeId,
                    }),
                  ),
                ),
          )
          as _i14.Future<_i10.AssetHistoryRecordsCountResponse>);

  @override
  _i14.Future<_i11.AssetByKeywordResponse> findAssetByKeyword({
    required int? assetTypeid,
    required String? keyword,
    required int? currentPage,
    required int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#findAssetByKeyword, [], {
              #assetTypeid: assetTypeid,
              #keyword: keyword,
              #currentPage: currentPage,
              #pageSize: pageSize,
            }),
            returnValue: _i14.Future<_i11.AssetByKeywordResponse>.value(
              _FakeAssetByKeywordResponse_9(
                this,
                Invocation.method(#findAssetByKeyword, [], {
                  #assetTypeid: assetTypeid,
                  #keyword: keyword,
                  #currentPage: currentPage,
                  #pageSize: pageSize,
                }),
              ),
            ),
          )
          as _i14.Future<_i11.AssetByKeywordResponse>);
}

/// A class which mocks [AssetTypeRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetTypeRepository extends _i1.Mock
    implements _i16.AssetTypeRepository {
  MockAssetTypeRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Future<_i12.AssetTypeResponse> getAllAssetTypes({
    String? assetTypeId = '',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllAssetTypes, [], {
              #assetTypeId: assetTypeId,
            }),
            returnValue: _i14.Future<_i12.AssetTypeResponse>.value(
              _FakeAssetTypeResponse_10(
                this,
                Invocation.method(#getAllAssetTypes, [], {
                  #assetTypeId: assetTypeId,
                }),
              ),
            ),
          )
          as _i14.Future<_i12.AssetTypeResponse>);

  @override
  _i14.Future<int> checkAssetAuthority(int? assetTypeId, int? assetId) =>
      (super.noSuchMethod(
            Invocation.method(#checkAssetAuthority, [assetTypeId, assetId]),
            returnValue: _i14.Future<int>.value(0),
          )
          as _i14.Future<int>);
}

/// A class which mocks [AssetFunctionalProcessingHelperRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetFunctionalProcessingHelperRepository extends _i1.Mock
    implements _i17.AssetFunctionalProcessingHelperRepository {
  MockAssetFunctionalProcessingHelperRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String? assembledCheckboxTypeValue({
    required _i18.SharedLayoutSetting? layoutSetting,
    required dynamic value,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#assembledCheckboxTypeValue, [], {
              #layoutSetting: layoutSetting,
              #value: value,
            }),
          )
          as String?);

  @override
  String? assembledNumberTypeValue({
    required _i18.SharedLayoutSetting? layoutSetting,
    required dynamic value,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#assembledNumberTypeValue, [], {
              #layoutSetting: layoutSetting,
              #value: value,
            }),
          )
          as String?);

  @override
  String? assembledDateTimeTypeValue({
    required _i18.SharedLayoutSetting? layoutSetting,
    required dynamic value,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#assembledDateTimeTypeValue, [], {
              #layoutSetting: layoutSetting,
              #value: value,
            }),
          )
          as String?);

  @override
  String? assembledCurrencyTypeValue({
    required _i18.SharedLayoutSetting? layoutSetting,
    required dynamic value,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#assembledCurrencyTypeValue, [], {
              #layoutSetting: layoutSetting,
              #value: value,
            }),
          )
          as String?);

  @override
  String? assembledAppurInfoSummaryTypeValue({
    required _i18.SharedLayoutSetting? layoutSetting,
    required dynamic value,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#assembledAppurInfoSummaryTypeValue, [], {
              #layoutSetting: layoutSetting,
              #value: value,
            }),
          )
          as String?);

  @override
  String? assembledCalculateTypeValue({
    required _i18.SharedLayoutSetting? layoutSetting,
    required dynamic value,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#assembledCalculateTypeValue, [], {
              #layoutSetting: layoutSetting,
              #value: value,
            }),
          )
          as String?);

  @override
  _i14.Future<String?> getHomeImageFun({
    required List<_i18.SharedLayoutSetting>? assetItemTypeSettingList,
    required _i19.Asset? assetItem,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getHomeImageFun, [], {
              #assetItemTypeSettingList: assetItemTypeSettingList,
              #assetItem: assetItem,
            }),
            returnValue: _i14.Future<String?>.value(),
          )
          as _i14.Future<String?>);

  @override
  String itemValueToString({required dynamic value}) =>
      (super.noSuchMethod(
            Invocation.method(#itemValueToString, [], {#value: value}),
            returnValue: _i20.dummyValue<String>(
              this,
              Invocation.method(#itemValueToString, [], {#value: value}),
            ),
          )
          as String);
}
