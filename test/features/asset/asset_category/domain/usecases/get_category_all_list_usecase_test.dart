// test/features/asset/asset_category/domain/usecases/get_category_all_list_usecase_test.dart

// 导入被测试的 UseCase 和相关模型
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_count_processing_service.dart';
// 导入服务接口
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_data_preparation_service.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/services/category_validation_service.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/models/asset_search_category_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 导入生成的模拟类
import 'get_category_all_list_usecase_test.mocks.dart';

// 使用 Mockito 注解生成模拟类
@GenerateMocks([CategoryDataPreparationService, CategoryCountProcessingService, CategoryValidationService])
void main() {
  // 定义变量
  late GetCategoryAllListUseCase useCase;
  late MockCategoryDataPreparationService mockDataPreparationService;
  late MockCategoryCountProcessingService mockCountProcessingService;
  late MockCategoryValidationService mockValidationService;

  setUp(() {
    // 初始化 LogUtil（假设有一个初始化方法）
    LogUtil.initialize();

    // 实例化模拟服务
    mockDataPreparationService = MockCategoryDataPreparationService();
    mockCountProcessingService = MockCategoryCountProcessingService();
    mockValidationService = MockCategoryValidationService();

    // 实例化 UseCase 并注入模拟服务
    useCase = GetCategoryAllListUseCase(
      dataPreparationService: mockDataPreparationService,
      countProcessingService: mockCountProcessingService,
      validationService: mockValidationService,
    );
  });

  group('GetCategoryAllListUseCase', () {
    test('should load category list correctly when not completed and not empty', () async {
      // Arrange
      final testParams = GetCategoryAllListParams(
        info: Info(
          titleValue: 'Category 1',
          goBackLevelInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
        ),
        assetSearchConditionList: [
          AssetSearchCategoryUIModel(searchData: 'SearchData1', itemId: 1, itemName: 'ItemName1', searchLogic: 'AND'),
        ],
        assetTypeId: 1,
        searchId: 'search_123',
        keyword: 'keyword',
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
      );

      final preparedCategoryList = [
        CategoryData(
          titleVal: 'Category 1',
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 1,
            categorySubItemId: 101,
            categoryItemValue: 'Value1',
            itemType: 'type1',
            index: 0,
            itFinished: false,
          ),
        ),
      ];

      final categoryCountList = [
        CategoryItemValueModel(categoryItemValue: 'Value1', count: 10, homeImageMobileDisplayFlg: true),
      ];

      // 配置模拟服务的行为
      when(mockDataPreparationService.prepareCategoryList(testParams)).thenAnswer((_) async => preparedCategoryList);

      when(mockValidationService.checkCompletionStatus(preparedCategoryList)).thenReturn(false);

      when(
        mockCountProcessingService.fetchCategoryCount(testParams, preparedCategoryList),
      ).thenAnswer((_) async => categoryCountList);

      when(mockValidationService.isEmptyCategory(categoryCountList)).thenReturn(false);

      // Act
      final result = await useCase.call(testParams);

      // Assert
      expect(result.categoryList, preparedCategoryList);
      expect(result.categoryCountList, categoryCountList);
      expect(result.isCompleted, false);
      expect(result.isEmpty, false);
    });

    test('should return completed result when validation indicates completion', () async {
      // Arrange
      final testParams = GetCategoryAllListParams(
        info: Info(
          titleValue: 'Category 1',
          goBackLevelInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
        ),
        assetSearchConditionList: [
          AssetSearchCategoryUIModel(searchData: 'SearchData1', itemId: 1, itemName: 'ItemName1', searchLogic: 'AND'),
        ],
        assetTypeId: 1,
        searchId: 'search_123',
        keyword: 'keyword',
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
      );

      final preparedCategoryList = [
        CategoryData(
          titleVal: 'Category 1',
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 1,
            categorySubItemId: 101,
            categoryItemValue: 'Value1',
            itemType: 'type1',
            index: 0,
            itFinished: false,
          ),
        ),
      ];

      // 配置模拟服务的行为
      when(mockDataPreparationService.prepareCategoryList(testParams)).thenAnswer((_) async => preparedCategoryList);

      when(mockValidationService.checkCompletionStatus(preparedCategoryList)).thenReturn(true);

      // Act
      final result = await useCase.call(testParams);

      // Assert
      expect(result.categoryList, preparedCategoryList);
      expect(result.categoryCountList, isEmpty);
      expect(result.isCompleted, true);
      expect(result.isEmpty, false);
    });

    test('should return empty result when category count list is empty', () async {
      // Arrange
      final testParams = GetCategoryAllListParams(
        info: Info(
          titleValue: 'Category 1',
          goBackLevelInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
        ),
        assetSearchConditionList: [
          AssetSearchCategoryUIModel(searchData: 'SearchData1', itemId: 1, itemName: 'ItemName1', searchLogic: 'AND'),
        ],
        assetTypeId: 1,
        searchId: 'search_123',
        keyword: 'keyword',
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
      );

      final preparedCategoryList = [
        CategoryData(
          titleVal: 'Category 1',
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 1,
            categorySubItemId: 101,
            categoryItemValue: 'Value1',
            itemType: 'type1',
            index: 0,
            itFinished: false,
          ),
        ),
      ];

      final categoryCountList = <CategoryItemValueModel>[];

      // 配置模拟服务的行为
      when(mockDataPreparationService.prepareCategoryList(testParams)).thenAnswer((_) async => preparedCategoryList);

      when(mockValidationService.checkCompletionStatus(preparedCategoryList)).thenReturn(false);

      when(
        mockCountProcessingService.fetchCategoryCount(testParams, preparedCategoryList),
      ).thenAnswer((_) async => categoryCountList);

      when(mockValidationService.isEmptyCategory(categoryCountList)).thenReturn(true);

      // Act
      final result = await useCase.call(testParams);

      // Assert
      expect(result.categoryList, preparedCategoryList);
      expect(result.categoryCountList, isEmpty);
      expect(result.isCompleted, false);
      expect(result.isEmpty, true);
    });

    test('should throw BusinessException when any exception occurs', () async {
      // Arrange
      final testParams = GetCategoryAllListParams(
        info: Info(
          titleValue: 'Category 1',
          goBackLevelInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
        ),
        assetSearchConditionList: [
          AssetSearchCategoryUIModel(searchData: 'SearchData1', itemId: 1, itemName: 'ItemName1', searchLogic: 'AND'),
        ],
        assetTypeId: 1,
        searchId: 'search_123',
        keyword: 'keyword',
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
      );

      // 配置模拟服务的行为，抛出异常
      when(mockDataPreparationService.prepareCategoryList(testParams)).thenThrow(BusinessException('Unexpected error'));

      // Act & Assert
      await expectLater(
        useCase.call(testParams),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', 'カテゴリの取得に失敗しました。もう一度お試しください。')),
      );
    });

    test('should throw SystemException when any exception occurs', () async {
      // Arrange
      final testParams = GetCategoryAllListParams(
        info: Info(
          titleValue: 'Category 1',
          goBackLevelInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
        ),
        assetSearchConditionList: [
          AssetSearchCategoryUIModel(searchData: 'SearchData1', itemId: 1, itemName: 'ItemName1', searchLogic: 'AND'),
        ],
        assetTypeId: 1,
        searchId: 'search_123',
        keyword: 'keyword',
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
      );

      // 配置模拟服务的行为，抛出异常
      when(
        mockDataPreparationService.prepareCategoryList(testParams),
      ).thenThrow(SystemException(technicalMessage: 'Unexpected error'));

      // Act & Assert
      await expectLater(useCase.call(testParams), throwsA(isA<SystemException>()));
    });
  });

  test('should return true when isClickCurrentLevel service returns true', () {
    // Arrange
    final beforeInfo = const CategoryModel(
      itemId: 1,
      itemDisplayName: 'Display Name 1',
      itemName: 'Name1',
      itemType: 'type1',
      optionObj: {'key1': 'value1'},
      itemSubName: 'SubName1',
      itemSubId: 101,
      itemSubType: 'subType1',
      subOptionObj: SubOptionObjModel(),
      value: 'Value1',
      selectedCategoryValue: 'SelectedValue1',
      subOption: 'SubOption1',
      itemSubDisplayName: 'SubDisplayName1',
    );
    final afterInfo = {
      // 根据实际情况填充 afterInfo 的内容
      'someKey': 'someValue',
    };

    // 配置模拟服务的行为
    when(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).thenReturn(true);

    // Act
    final result = useCase.isClickCurrentLevel(beforeInfo, afterInfo);

    // Assert
    expect(result, true);
    verify(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).called(1);
    verifyNoMoreInteractions(mockDataPreparationService);
  });

  test('should return false when isClickCurrentLevel service returns false', () {
    // Arrange
    final beforeInfo = const CategoryModel(
      itemId: 2,
      itemDisplayName: 'Display Name 2',
      itemName: 'Name2',
      itemType: 'type2',
      optionObj: {'key2': 'value2'},
      itemSubName: 'SubName2',
      itemSubId: 202,
      itemSubType: 'subType2',
      subOptionObj: SubOptionObjModel(),
      value: 'Value2',
      selectedCategoryValue: 'SelectedValue2',
      subOption: 'SubOption2',
      itemSubDisplayName: 'SubDisplayName2',
    );
    final afterInfo = {
      // 根据实际情况填充 afterInfo 的内容
      'anotherKey': 'anotherValue',
    };

    // 配置模拟服务的行为
    when(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).thenReturn(false);

    // Act
    final result = useCase.isClickCurrentLevel(beforeInfo, afterInfo);

    // Assert
    expect(result, false);
    verify(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).called(1);
    verifyNoMoreInteractions(mockDataPreparationService);
  });

  test('should handle null beforeInfo correctly', () {
    // Arrange
    final beforeInfo = null;
    final afterInfo = {
      // 根据实际情况填充 afterInfo 的内容
      'key': 'value',
    };

    // 配置模拟服务的行为
    when(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).thenReturn(false);

    // Act
    final result = useCase.isClickCurrentLevel(beforeInfo, afterInfo);

    // Assert
    expect(result, false);
    verify(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).called(1);
    verifyNoMoreInteractions(mockDataPreparationService);
  });

  test('should handle null afterInfo correctly', () {
    // Arrange
    final beforeInfo = const CategoryModel(
      itemId: 3,
      itemDisplayName: 'Display Name 3',
      itemName: 'Name3',
      itemType: 'type3',
      optionObj: {'key3': 'value3'},
      itemSubName: 'SubName3',
      itemSubId: 303,
      itemSubType: 'subType3',
      subOptionObj: SubOptionObjModel(),
      value: 'Value3',
      selectedCategoryValue: 'SelectedValue3',
      subOption: 'SubOption3',
      itemSubDisplayName: 'SubDisplayName3',
    );
    final afterInfo = null;

    // 配置模拟服务的行为
    when(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).thenReturn(true);

    // Act
    final result = useCase.isClickCurrentLevel(beforeInfo, afterInfo);

    // Assert
    expect(result, true);
    verify(mockDataPreparationService.isClickCurrentLevel(beforeInfo, afterInfo)).called(1);
    verifyNoMoreInteractions(mockDataPreparationService);
  });
}
