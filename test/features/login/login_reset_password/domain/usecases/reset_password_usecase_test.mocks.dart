// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/login/login_reset_password/domain/usecases/reset_password_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/models/reset_password_result_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/repositories/reset_password_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResetPasswordResultModel_0 extends _i1.SmartFake
    implements _i2.ResetPasswordResultModel {
  _FakeResetPasswordResultModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ResetPasswordRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockResetPasswordRepository extends _i1.Mock
    implements _i3.ResetPasswordRepository {
  MockResetPasswordRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.ResetPasswordResultModel> setUpdatePassword({
    required String? userName,
    required String? oldPassword,
    required String? newPassword,
    required String? tenantId,
    required String? zoneId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setUpdatePassword, [], {
              #userName: userName,
              #oldPassword: oldPassword,
              #newPassword: newPassword,
              #tenantId: tenantId,
              #zoneId: zoneId,
            }),
            returnValue: _i4.Future<_i2.ResetPasswordResultModel>.value(
              _FakeResetPasswordResultModel_0(
                this,
                Invocation.method(#setUpdatePassword, [], {
                  #userName: userName,
                  #oldPassword: oldPassword,
                  #newPassword: newPassword,
                  #tenantId: tenantId,
                  #zoneId: zoneId,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.ResetPasswordResultModel>);
}
