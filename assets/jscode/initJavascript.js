class MethodTypeEnum {
  static DELETE = 'DELETE';
  static GET = 'GET';
  static HEAD = 'HEAD';
  static JSONP = 'JSONP';
  static OPTIONS = 'OPTIONS';
  static POST = 'POST';
  static PUT = 'PUT';
  static PATCH = 'PATCH';
}

class HttpUtilsService {
  constructor() {
    this.loadingCount = 0;
    this.backgroundApiRequestCount = 0;
    this.cache = new Map();
    this.isNetErr = false;
    this.isHasUserGroup = true;
    this.timeInteval = 1500;
    this.timer = null;

    // Callback for API count changes
    this.apiCountChangeCallback = null;
  }

  async fetchApi(url, options) {
    try {
      const response = await Promise.race([
        fetch(url, options),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Request timed out')), 10000)),
      ]);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json(); // assuming a JSON response
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  async appNetWorkResponse(fullApiPath, headers, methodType, requestBody, isAuth, resultReuse = true) {
    let erqHeaders = headers === undefined ? {} : { headers };
    if (isAuth) {
      erqHeaders['observe'] = 'response';
    }
    const options = {
      method: methodType,
      headers: headers,
      body: JSON.stringify(requestBody),
    };

    if (methodType === MethodTypeEnum.GET) {
      delete options.body; // GET requests should not have a body
    }

    if (resultReuse && methodType === MethodTypeEnum.GET) {
      const now = Date.now();
      if (!this.cache.has(fullApiPath) || now - this.cache.get(fullApiPath).time > 3000) {
        const result = this.fetchApi(fullApiPath, options);
        this.cache.set(fullApiPath, { responsePromise: result, time: now });
        return result;
      }
      return this.cache.get(fullApiPath).responsePromise;
    } else if (resultReuse && methodType === MethodTypeEnum.POST) {
      const cachedResult = this.cache.get(fullApiPath);
      return cachedResult && cachedResult.responsePromise
        ? cachedResult.responsePromise
        : this.fetchApi(fullApiPath, options);
    } else {
      return this.fetchApi(fullApiPath, options); // Directly fetch for other methods or when not reusing
    }
  }

  async post(url, requestBody, needLoading = true, delay = true) {
    const { fullApiPath, isAuth } = await this.getFullApiPath(url);
    const request = {
      method: MethodTypeEnum.POST,
      fullApiPath,
      requestBody,
    };
    let headers = await this.getHeader2(MethodTypeEnum.POST);
    if (needLoading) {
      this.addLoadingQueue(delay);
    }
    return this.appNetWorkResponse(fullApiPath, headers, request.method, request.requestBody, isAuth)
      .then((responseData) => {
        console.log('Success', responseData);
        if (isAuth && responseData.status === 200) {
          return responseData;
        } else {
          throw responseData;
        }
      })
      .catch((error) => {
        console.error('Error', error);
        console.log('An error occurred: ', error.message);
      })
      .finally(() => {
        if (needLoading) {
          this.removeLoadingQueue(delay);
        }
        this.removeApiCount();
      });
  }
  async get(url, noHeader = false, needLoading = true, delay = true, resultReuse = true) {
    const { fullApiPath, isAuth } = await this.getFullApiPath(url);
    let headers = {};

    if (!noHeader) {
      headers = await this.getHeader(MethodTypeEnum.GET);
    }

    if (needLoading) {
      this.addLoadingQueue(delay);
    }
    this.addApiCount();

    return this.appNetWorkResponse(fullApiPath, headers, MethodTypeEnum.GET, undefined, isAuth, resultReuse)
      .then((responseData) => {
        console.log('Success', responseData);
        if (isAuth && responseData.status === 200) {
          return responseData;
        } else {
          throw responseData;
        }
      })
      .catch((error) => {
        console.error('Error', error);
        console.log('An error occurred: ', error.message);
      })
      .finally(() => {
        if (needLoading) {
          this.removeLoadingQueue(delay);
        }
        this.removeApiCount();
      });
  }

  async getFullApiPath(url) {
    if (url.startsWith('http')) {
      return { fullApiPath: url, isAuth: false };
    }

    const { hostUrl, isAuth } = await this.getHost(url);
    let apiUrl = url;
    if (hostUrl.lastIndexOf('/') === hostUrl.length - 1) {
      hostUrl = hostUrl.slice(0, -1);
    }
    if (apiUrl.indexOf('/') !== 0) {
      apiUrl = '/' + apiUrl;
    }
    let fullApiPath = hostUrl + apiUrl;

    return {
      fullApiPath,
      isAuth,
    };
  }

  async getHost(url) {
    const getAuthHostUrls = [
      'auth/login',
      'auth/logout',
      '/auth/sso/exchangeTicket',
      'common/mobile-deployment/last?',
      'common/mobile-deployment',
    ];
    const getNotificationUrls = ['notification/getNotificationById'];
    var filterResult = getAuthHostUrls.filter((p) => {
      return url.startsWith(p);
    });
    var isAuth = false;
    var hostUrl = null;
    let idx = url.indexOf('?');
    let compareUrl = idx < 0 ? url : url.substring(0, idx);
    var notificationFilter = getNotificationUrls.filter((p) => {
      return compareUrl == p;
    });
    if (notificationFilter && notificationFilter.length > 0) {
      hostUrl = apiGWHost;
      isAuth = false;
    } else if (filterResult && filterResult.length > 0) {
      hostUrl = apiAuthHost;
      isAuth = true;
    } else {
      hostUrl = apiHost;
    }
    return {
      hostUrl,
      isAuth,
    };
  }

  async getHeader(method) {
    const contentType = method === 'post' ? 'application/x-www-form-urlencoded' : 'application/json';
    const token = tokenLocal;
    const userId = userIdLocal;
    const tenantId = tenantIdLocal;
    console.log('💡 =====> getHeader', {
      method: method,
      'Content-Type': contentType,
      device: 'iphone',
      pageId: 'index',
      userId,
      tenantId,
      token,
    });
    return {
      'Content-Type': contentType,
      device: 'iphone',
      pageId: 'index',
      userId,
      tenantId,
      token,
    };
  }

  async getHeader2(method) {
    const token = tokenLocal;
    const userId = userIdLocal;
    const tenantId = tenantIdLocal;
    return {
      device: 'iphone',
      pageId: 'index',
      userId,
      tenantId,
      token,
    };
  }

  async handleSuccess(responseData, isAuth) {
    // Handle the response success logic here
    console.log('Success', responseData);
    if (isAuth && responseData.status === 200) {
      return responseData;
    } else {
      throw responseData; // Or handle authentication errors
    }
  }

  async handleError(error) {
    // Handle error logic here
    console.error('Error', error);
    console.log('An error occurred: ', error.message);
  }

  // Add Loading Queue
  addLoadingQueue(delay) {
    this.loadingCount++;
    console.log('Loading count increased:', this.loadingCount);
    // Implement loading UI indication as needed
  }

  removeLoadingQueue(delay) {
    this.loadingCount--;
    console.log('Loading count decreased:', this.loadingCount);
    // Implement removing loading UI indication as needed
  }

  addApiCount() {
    this.backgroundApiRequestCount++;
    this.callbackApiCount();
  }

  removeApiCount() {
    this.backgroundApiRequestCount = Math.max(this.backgroundApiRequestCount - 1, 0);
    this.callbackApiCount();
  }

  callbackApiCount() {
    if (this.apiCountChangeCallback) {
      this.apiCountChangeCallback(this.backgroundApiRequestCount);
    }
  }

  setApiRequestCountChangeCallback(callback) {
    this.apiCountChangeCallback = callback;
  }
}
