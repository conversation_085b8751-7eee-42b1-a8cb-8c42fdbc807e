function getFormatDateReturnDate(date) {
  const formattedDateTimeStr = getFormatDate(date);
  return new Date(formattedDateTimeStr);
}

function getFormatDate(date) {
  const dateObject = validateDateInput(date);
  if (dateObject === undefined || dateObject === null) {
    return date;
  }
  return formatAndReturn(dateObject, 'yyyy/MM/dd');
}

function validateDateInput(date) {
  let dateObject;
  if (typeof date === 'string') {
    dateObject = parseDateString(date);
  } else {
    dateObject = date;
  }
  if (isValid(dateObject)) {
    return dateObject;
  }
  return undefined;
}

function isValid(date) {
  return date instanceof Date && !isNaN(date.getTime());
}

function parseDateString(dateString) {
  // 处理 / 和 空格 格式问题
  if (dateString.includes('/')) {
    dateString = dateString.replace(/\\/g, '-');
  }
  if (dateString.includes(' ')) {
    dateString = dateString.replace(' ', 'T');
  }

  const parsedDate = new Date(dateString);

  if (!isNaN(parsedDate.getTime())) {
    return parsedDate;
  }
  return undefined;
}

function formatAndReturn(date, formatStr) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  // 根据传入的 formatStr 格式化日期
  return formatStr
    .replace('yyyy', year)
    .replace('MM', month)
    .replace('dd', day)
    .replace('HH', hours)
    .replace('mm', minutes);
}

function dayHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  date.setDate(date.getDate() + quantity);

  // 根据原始输入格式决定返回格式
  // 如果原始输入包含时间信息，返回完整的日期时间格式
  // 否则只返回日期格式
//  if (typeof dateStr === 'string' && dateStr.includes(' ')) {
//    // 原始输入包含时间，返回 yyyy/MM/dd HH:mm 格式
//    return formatAndReturn(date, 'yyyy/MM/dd HH:mm');
//  } else {
    // 原始输入只有日期，返回 yyyy/MM/dd 格式
    return formatAndReturn(date, 'yyyy/MM/dd');
//  }
}
function monthHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  let day = date.getDate();
  date.setMonth(date.getMonth() + quantity);
  let day2 = date.getDate();
  if (day > day2) {
    date = new Date(date.getFullYear(), date.getMonth(), 0);
  }

  // 根据原始输入格式决定返回格式
//  if (typeof dateStr === 'string' && dateStr.includes(' ')) {
//    // 原始输入包含时间，返回 yyyy/MM/dd HH:mm 格式
//    return formatAndReturn(date, 'yyyy/MM/dd HH:mm');
//  } else {
    // 原始输入只有日期，返回 yyyy/MM/dd 格式
    return formatAndReturn(date, 'yyyy/MM/dd');
//  }
}
function yearHandle(dateStr, quantity) {
  let date;
  if (dateStr instanceof Date) {
    date = dateStr;
  } else {
    date = getFormatDateReturnDate(dateStr);
  }
  date.setFullYear(date.getFullYear() + quantity);

  // 根据原始输入格式决定返回格式
//  if (typeof dateStr === 'string' && dateStr.includes(' ')) {
//    // 原始输入包含时间，返回 yyyy/MM/dd HH:mm 格式
//    return formatAndReturn(date, 'yyyy/MM/dd HH:mm');
//  } else {
    // 原始输入只有日期，返回 yyyy/MM/dd 格式
    return formatAndReturn(date, 'yyyy/MM/dd');
//  }
}

function now() {
  return new Date();
}

function dayBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }
  console.log('date1', date1);

  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }
  console.log('date2', date2);

  let interval = date2.getTime() - date1.getTime();

  let days = Math.floor(interval / (24 * 3600 * 1000));
  return days;
}
function monthBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
  }
  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (30 * 24 * 3600 * 1000));
  return days;
}

function yearBetween(dateStr, dateStr2) {
  let date1;
  if (dateStr instanceof Date) {
    date1 = dateStr;
  } else {
    date1 = getFormatDateReturnDate(dateStr);
    console.log('date1', date1);
  }
  let date2;
  if (dateStr2 instanceof Date) {
    date2 = dateStr2;
  } else {
    date2 = getFormatDateReturnDate(dateStr2);
    console.log('date1', date2);
  }
  let interval = date2.getTime() - date1.getTime();
  let days = Math.floor(interval / (365 * 24 * 3600 * 1000));
  return days;
}
