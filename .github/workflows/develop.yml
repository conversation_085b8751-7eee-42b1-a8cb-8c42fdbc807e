name: Build iOS with Manual Signing (Ad Hoc)

on:
  # PR 阶段：当有指向 develop 分支的 PR 被创建或更新时触发
  pull_request:
    branches: [ develop ]
  # 合并后：当有代码 push 到 develop 分支时触发构建任务
  push:
    branches: [ develop ]
  # 手动触发工作流（用于测试）
  workflow_dispatch:
    inputs:
      skip_tests:
        description: '跳过测试步骤'
        required: false
        default: false
        type: boolean
      build_type:
        description: '构建类型'
        required: false
        default: 'debug'
        type: choice
        options:
        - debug
        - release

jobs:
  pr-check:
    if: github.event_name == 'pull_request'
    runs-on: macos-15
    outputs:
      skip_format: ${{ steps.check_commit.outputs.skip_format }}
    steps:
      - name: Check commit message (PR title & body) for [skip format]
        id: check_commit
        uses: actions/github-script@v6
        with:
          script: |
            const prTitle = context.payload.pull_request.title || "";
            const prBody = context.payload.pull_request.body || "";
            const combined = prTitle + "\n" + prBody;
            console.log(`PR title & body: ${combined}`);
            if (combined.includes('[skip format]')) {
              core.setOutput('skip_format', 'true');
            } else {
              core.setOutput('skip_format', 'false');
            }

      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Flutter
        if: steps.check_commit.outputs.skip_format == 'false'
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.32.5"
          cache: true

      - name: Check Flutter and Dart versions
        if: steps.check_commit.outputs.skip_format == 'false'
        run: |
          flutter --version
          dart --version

      - name: Run dart format and push changes (if any)
        if: steps.check_commit.outputs.skip_format == 'false'
        run: |
          echo "=== Running dart format ==="
          dart format --line-length 120 .
          if [[ `git status --porcelain` ]]; then
            echo "Formatting changes detected, committing..."
            git config user.name "github-actions[bot]"
            git config user.email "github-actions[bot]@users.noreply.github.com"
            git add .
            git commit -m "chore: format code with flutter format [skip format]"
            # 注意：直接 push 到 PR 分支需要确保有相应权限
            git push origin HEAD:${{ github.head_ref }}
          else
            echo "No formatting changes detected."
          fi

      # 运行 Flutter 静态分析检查
      - name: Run Flutter static analysis
        run: |
          echo "=== Running Flutter static analysis ==="
          # 如果静态分析有错误则退出
          flutter analyze || { echo "Flutter static analysis failed"; exit 1; }

      - name: Run Flutter tests (PR)
        id: flutter_test
        shell: bash
        run: |
          echo "=== Running Flutter tests (develop) ==="
          flutter test --machine > test_results.json
          if PASSED_TESTS=$(grep -c '"result":"success"' test_results.json); then
            PASSED_TESTS=$PASSED_TESTS
          else
            PASSED_TESTS=0
          fi
          if FAILED_OUTPUT=$(grep -c '"result":"error"' test_results.json); then
            FAILED_TESTS=$FAILED_OUTPUT
          else
            FAILED_TESTS=0
          fi
          TOTAL_TESTS=$((PASSED_TESTS + FAILED_TESTS))
          echo "Test Summary: Total=$TOTAL_TESTS, Passed=$PASSED_TESTS, Failed=$FAILED_TESTS"
          echo "TOTAL_TESTS=$TOTAL_TESTS" >> $GITHUB_ENV
          echo "PASSED_TESTS=$PASSED_TESTS" >> $GITHUB_ENV
          echo "FAILED_TESTS=$FAILED_TESTS" >> $GITHUB_ENV
          if [ "$FAILED_TESTS" -eq "0" ]; then
            echo "TEST_STATUS=✅" >> $GITHUB_ENV
          else
            echo "TEST_STATUS=❌" >> $GITHUB_ENV
            exit 1
          fi

  #      # 如果测试失败，则发送 Slack 通知
  #      - name: Notify PR test failure to Slack
  #        if: failure()
  #        uses: slackapi/slack-github-action@v1.25.0
  #        env:
  #          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
  #          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
  #        with:
  #          payload: |
  #            {
  #              "blocks": [
  #                {
  #                  "type": "header",
  #                  "text": {
  #                    "type": "plain_text",
  #                    "text": "❌ Flutter PR Test Failed"
  #                  }
  #                },
  #                {
  #                  "type": "section",
  #                  "text": {
  #                    "type": "mrkdwn",
  #                    "text": "PR for branch *${{ github.head_ref }}* has failed tests. Please check the results."
  #                  }
  #                },
  #                {
  #                  "type": "section",
  #                  "fields": [
  #                    {
  #                      "type": "mrkdwn",
  #                      "text": "*Total Tests:*\n${{ env.TOTAL_TESTS }}"
  #                    },
  #                    {
  #                      "type": "mrkdwn",
  #                      "text": "*Passed:*\n${{ env.PASSED_TESTS }}"
  #                    },
  #                    {
  #                      "type": "mrkdwn",
  #                      "text": "*Failed:*\n${{ env.FAILED_TESTS }}"
  #                    }
  #                  ]
  #                },
  #                {
  #                  "type": "section",
  #                  "text": {
  #                    "type": "mrkdwn",
  #                    "text": "*Action URL:*\n${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
  #                  }
  #                }
  #              ]
  #            }

  build-ios:
    if: github.event_name == 'push'
    runs-on: macos-15
    env:
      FLUTTER_XCODE_VERSION: 16.2

    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.27.3"
          cache: true

      - name: Select Xcode Version
        run: sudo xcode-select -s /Applications/Xcode_${FLUTTER_XCODE_VERSION}.app

      - name: Check Xcode Version
        run: xcodebuild -version

      - name: Install Flutter dependencies
        run: flutter pub get

      - name: Cache Git LFS files
        id: cache-lfs
        uses: actions/cache@v3
        with:
          path: |
            .git/lfs
            ios/Aiocr-Pod/opencv2.framework
            ios/Aiocr-Pod/TensorFlowLiteSelectTfOps.framework
          key: ${{ runner.os }}-lfs-${{ hashFiles('**/.gitattributes') }}
          restore-keys: |
            ${{ runner.os }}-lfs-

      - name: Pull Git LFS files
        if: steps.cache-lfs.outputs.cache-hit != 'true'
        run: git lfs pull

      - name: Cache CocoaPods
        uses: actions/cache@v3
        with:
          path: |
            ios/Pods
            ~/.cocoapods
          key: ${{ runner.os }}-pods-${{ hashFiles('**/Podfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-pods-

      - name: Install CocoaPods dependencies
        run: |
          cd ios
          pod install --repo-update
          cd ..

      - name: Clean and remove old Pods
        run: |
          flutter clean
          rm -rf ios/Pods ios/Podfile.lock ios/Runner.xcworkspace
          rm -rf ~/Library/Developer/Xcode/DerivedData

      - name: Run Flutter tests (post-merge)
        id: flutter_test
        shell: bash
        run: |
          echo "=== Running Flutter tests (develop) ==="
          flutter test --machine > test_results.json
          if PASSED_TESTS=$(grep -c '"result":"success"' test_results.json); then
            PASSED_TESTS=$PASSED_TESTS
          else
            PASSED_TESTS=0
          fi
          if FAILED_OUTPUT=$(grep -c '"result":"error"' test_results.json); then
            FAILED_TESTS=$FAILED_OUTPUT
          else
            FAILED_TESTS=0
          fi
          TOTAL_TESTS=$((PASSED_TESTS + FAILED_TESTS))
          echo "Test Summary: Total=$TOTAL_TESTS, Passed=$PASSED_TESTS, Failed=$FAILED_TESTS"
          echo "TOTAL_TESTS=$TOTAL_TESTS" >> $GITHUB_ENV
          echo "PASSED_TESTS=$PASSED_TESTS" >> $GITHUB_ENV
          echo "FAILED_TESTS=$FAILED_TESTS" >> $GITHUB_ENV
          if [ "$FAILED_TESTS" -eq "0" ]; then
            echo "TEST_STATUS=✅" >> $GITHUB_ENV
          else
            echo "TEST_STATUS=❌" >> $GITHUB_ENV
            exit 1
          fi

      #      # 如果测试失败，则发送 Slack 通知，并中断后续构建步骤
      #      - name: Notify post-merge test failure to Slack
      #        if: failure()
      #        uses: slackapi/slack-github-action@v1.25.0
      #        env:
      #          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      #          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
      #        with:
      #          payload: |
      #            {
      #              "blocks": [
      #                {
      #                  "type": "header",
      #                  "text": {
      #                    "type": "plain_text",
      #                    "text": "❌ Flutter Test Failed (develop)"
      #                  }
      #                },
      #                {
      #                  "type": "section",
      #                  "text": {
      #                    "type": "mrkdwn",
      #                    "text": "Tests on the develop branch have failed after merge. Please review the results."
      #                  }
      #                },
      #                {
      #                  "type": "section",
      #                  "fields": [
      #                    {
      #                      "type": "mrkdwn",
      #                      "text": "*Total Tests:*\n${{ env.TOTAL_TESTS }}"
      #                    },
      #                    {
      #                      "type": "mrkdwn",
      #                      "text": "*Passed:*\n${{ env.PASSED_TESTS }}"
      #                    },
      #                    {
      #                      "type": "mrkdwn",
      #                      "text": "*Failed:*\n${{ env.FAILED_TESTS }}"
      #                    }
      #                  ]
      #                },
      #                {
      #                  "type": "section",
      #                  "text": {
      #                    "type": "mrkdwn",
      #                    "text": "*Action URL:*\n${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
      #                  }
      #                }
      #              ]
      #            }

      # 以下步骤只有在测试全部通过时才会继续执行

      - name: Configure Manual Signing
        env:
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_PROVISIONING_PROFILE: ${{ secrets.APPLE_PROVISIONING_PROFILE }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          echo "=== Configuring Manual Signing ==="
          security create-keychain -p "${KEYCHAIN_PASSWORD}" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "${KEYCHAIN_PASSWORD}" build.keychain
          security set-keychain-settings -t 3600 build.keychain
          security list-keychains -s build.keychain

          echo "$APPLE_CERTIFICATE" | base64 --decode > certificate.p12
          security import certificate.p12 \
            -P "$APPLE_CERTIFICATE_PASSWORD" \
            -k build.keychain \
            -T /usr/bin/codesign \
            -T /usr/bin/security \
            -T /usr/bin/xcodebuild
          security set-key-partition-list -S apple-tool:,apple: -s -k "${KEYCHAIN_PASSWORD}" build.keychain

          echo "$APPLE_PROVISIONING_PROFILE" | base64 --decode > profile.mobileprovision
          security cms -D -i profile.mobileprovision > temp.plist
          UUID=$(/usr/libexec/PlistBuddy -c "Print UUID" temp.plist)
          PROFILE_NAME=$(/usr/libexec/PlistBuddy -c "Print Name" temp.plist)
          echo "Provisioning Profile Name: $PROFILE_NAME"
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles/"$UUID.mobileprovision"

          echo "=== Available Code Signing Identities ==="
          security find-identity -p codesigning -v build.keychain
          echo "=== Provisioning Profiles ==="
          ls -la ~/Library/MobileDevice/Provisioning\ Profiles/

      - name: Build iOS IPA
        run: |
          echo "=== Building iOS IPA ==="
          flutter build ipa \
            --release \
            --export-options-plist=export_options.plist \
            --dart-define=CI=true \
            --flavor assetdev \
            --no-tree-shake-icons || exit 1

      - name: Rename IPA
        run: |
          mv build/ios/ipa/*.ipa build/ios/ipa/assetforce_dev.ipa

      - name: Upload IPA artifact
        uses: actions/upload-artifact@v4
        with:
          name: ios-ad-hoc
          path: build/ios/ipa/assetforce_dev.ipa

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: 'ap-northeast-1'

      - name: Sync IPA to S3
        run: |
          aws s3 cp build/ios/ipa/assetforce_dev.ipa s3://assetforce-app/GitHub/assetforce_dev.ipa

#      TODO 成功失败都要通知Slack
#      - name: Notify S3 upload failure to Slack
#        if: failure()
#        uses: slackapi/slack-github-action@v1.25.0
#        env:
#          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
#          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
#        with:
#          payload: |
#            {
#              "blocks": [
#                {
#                  "type": "header",
#                  "text": {
#                    "type": "plain_text",
#                    "text": "❌ iOS Build Failed"
#                  }
#                },
#                {
#                  "type": "section",
#                  "text": {
#                    "type": "mrkdwn",
#                    "text": "<!subteam^${{ secrets.AF_MOBILE_DEVELOPERS }}> iOS Build failed. Please check the logs."
#                  }
#                },
#                {
#                  "type": "section",
#                  "fields":
