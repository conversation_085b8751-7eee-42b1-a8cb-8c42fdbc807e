import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/pages/home_page.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/states/home_ui_state.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/menu_section_widget.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/task_section_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateNiceMocks([MockSpec<MyHomeController>()])
import 'home_page_test.mocks.dart';

// 创建自定义状态类以避免模拟嵌套属性
class TestHomeUIState extends HomeUIState {
  @override
  final RxBool showRedDot = false.obs;
}

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();
  late MockMyHomeController mockController;
  late TestHomeUIState testState;

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: MyHomePage());
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    // 创建真实的测试状态
    testState = TestHomeUIState();

    // 初始化模拟控制器
    mockController = MockMyHomeController();

    // 关键：使用onStart
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    // 设置状态
    when(mockController.state).thenReturn(testState);

    // 添加其他可能需要的方法模拟
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});
    when(mockController.loadData()).thenAnswer((_) async => null);
    when(mockController.doRefresh()).thenAnswer((_) async => null);
    when(mockController.goMyPage()).thenReturn(null);
    when(mockController.toMessage()).thenReturn(null);

    Get.put<MyHomeController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
  });

  group('MyHomePage UI 测试', () {
    testWidgets('应该显示正确的AppBar元素', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());

      // 使用pump而不是pumpAndSettle，避免等待无限动画
      await tester.pump(const Duration(milliseconds: 50));

      // 验证AppBar元素
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byIcon(Icons.account_circle), findsOneWidget);
      expect(find.byIcon(Icons.notifications), findsOneWidget);

      // 验证logo
      final svgPicture = find.byType(SvgPicture);
      expect(svgPicture, findsOneWidget);
    });

    testWidgets('应该包含任务和菜单部分', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证包含任务和菜单部分
      expect(find.byType(TaskSectionWidget), findsOneWidget);
      expect(find.byType(MenuSectionWidget), findsOneWidget);
    });

    testWidgets('应该包含下拉刷新组件', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证包含下拉刷新组件
      expect(find.byType(RefreshIndicator), findsOneWidget);

      // 验证可滚动组件
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('当showRedDot为true时，应显示通知红点', (WidgetTester tester) async {
      // 设置状态
      testState.showRedDot.value = true;

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证显示红点 - 使用更精确的查找
      final redDotFinder = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).color == Colors.red &&
            (widget.decoration as BoxDecoration).shape == BoxShape.circle,
      );

      expect(redDotFinder, findsOneWidget);
    });

    testWidgets('当showRedDot为false时，不应显示通知红点', (WidgetTester tester) async {
      // 设置状态
      testState.showRedDot.value = false;

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证不显示红点
      final redDotFinder = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).shape == BoxShape.circle &&
            (widget.decoration as BoxDecoration).color == Colors.red,
      );

      expect(redDotFinder, findsNothing);
    });

    testWidgets('点击个人资料图标应调用goMyPage方法', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 点击个人资料图标
      await tester.tap(find.byIcon(Icons.account_circle));
      await tester.pump();

      // 验证调用了相应方法
      verify(mockController.goMyPage()).called(1);
    });

    testWidgets('点击通知图标应调用toMessage方法', (WidgetTester tester) async {
      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 点击通知图标
      await tester.tap(find.byIcon(Icons.notifications));
      await tester.pump();

      // 验证调用了相应方法
      verify(mockController.toMessage()).called(1);
    });

    testWidgets('下拉刷新应调用doRefresh方法', (WidgetTester tester) async {
      // 构建测试 widget
      await tester.pumpWidget(createWidgetUnderTest());
      // 允许addPostFrameCallback执行
      await tester.pump();

      // 查找RefreshIndicator组件
      final refreshIndicatorFinder = find.byType(RefreshIndicator);
      expect(refreshIndicatorFinder, findsOneWidget);

      // 模拟下拉刷新手势
      final gesture = await tester.startGesture(tester.getCenter(refreshIndicatorFinder));
      await gesture.moveBy(const Offset(0, 300)); // 向下拖动足够距离
      await gesture.up();

      // 使用固定时间 pump 等待刷新动画开始和完成
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(seconds: 1));

      // 验证doRefresh方法被调用一次
      verify(mockController.doRefresh()).called(1);
    });

    testWidgets('组件应该能够在不同尺寸的屏幕上正确渲染', (WidgetTester tester) async {
      // 执行 - 使用较新的API
      final binding = tester.binding;
      addTearDown(binding.platformDispatcher.clearAllTestValues);

      tester.view.physicalSize = const Size(320, 480);
      tester.view.devicePixelRatio = 1.0;

      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证 - 如果渲染没有错误，测试就通过
      expect(find.byType(MyHomePage), findsOneWidget);
    });
  });
}
