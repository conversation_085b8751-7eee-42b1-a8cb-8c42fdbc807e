import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/models/reset_password_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/repositories/reset_password_repository.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/usecases/reset_password_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/password_policy_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'reset_password_usecase_test.mocks.dart';

@GenerateMocks([ResetPasswordRepository])
void main() {
  late ResetPasswordUsecase useCase;
  late MockResetPasswordRepository mockRepository;

  setUp(() {
    mockRepository = MockResetPasswordRepository();
    useCase = ResetPasswordUsecase(resetPasswordRepository: mockRepository);
  });

  group('ResetPasswordUsecase Tests', () {
    group('基础测试', () {
      test('should successfully call repository when parameters are valid', () async {
        // 参数有效时应成功调用repository
        // Arrange
        final params = ResetPasswordArgumentsModel(
          isBiometrics: false,
          zoneId: 'zone123',
          oldPassword: 'oldPassword123',
          newPassword: 'newPassword123',
          userName: '<EMAIL>',
          tenantId: 'tenant123',
          tenantName: 'Test Tenant',
          policy: PasswordPolicyModel(mustContain: 'upper,lower,number', length: 8),
        );

        final expectedResult = ResetPasswordResultModel(code: 0, msg: 'Success');
        expectedResult.updatePwdResult = 'Password updated successfully';

        when(
          mockRepository.setUpdatePassword(
            userName: anyNamed('userName'),
            oldPassword: anyNamed('oldPassword'),
            newPassword: anyNamed('newPassword'),
            tenantId: anyNamed('tenantId'),
            zoneId: anyNamed('zoneId'),
          ),
        ).thenAnswer((_) async => expectedResult);

        // Act
        final result = await useCase(params);

        // Assert
        expect(result, isA<ResetPasswordResultModel>());
        expect(result.code, 0);
        expect(result.msg, 'Success');
        expect(result.updatePwdResult, 'Password updated successfully');

        verify(
          mockRepository.setUpdatePassword(
            userName: params.userName,
            oldPassword: params.oldPassword,
            newPassword: params.newPassword!,
            tenantId: params.tenantId,
            zoneId: params.zoneId,
          ),
        ).called(1);
      });
    });

    group('call() 方法测试', () {
      test('should throw SystemException when newPassword is null', () async {
        // newPassword为null时应抛出SystemException
        // Arrange
        final params = ResetPasswordArgumentsModel(
          isBiometrics: false,
          zoneId: 'zone123',
          oldPassword: 'oldPassword123',
          newPassword: null, // null值
          userName: '<EMAIL>',
          tenantId: 'tenant123',
          tenantName: 'Test Tenant',
          policy: PasswordPolicyModel(mustContain: 'upper,lower,number', length: 8),
        );

        // Act & Assert
        expect(() => useCase(params), throwsA(isA<SystemException>()));

        // 验证repository没有被调用
        verifyNever(
          mockRepository.setUpdatePassword(
            userName: anyNamed('userName'),
            oldPassword: anyNamed('oldPassword'),
            newPassword: anyNamed('newPassword'),
            tenantId: anyNamed('tenantId'),
            zoneId: anyNamed('zoneId'),
          ),
        );
      });

      test('should throw SystemException when newPassword is empty string', () async {
        // newPassword为空字符串时应抛出SystemException
        // Arrange
        final params = ResetPasswordArgumentsModel(
          isBiometrics: false,
          zoneId: 'zone123',
          oldPassword: 'oldPassword123',
          newPassword: '', // 空字符串
          userName: '<EMAIL>',
          tenantId: 'tenant123',
          tenantName: 'Test Tenant',
          policy: PasswordPolicyModel(mustContain: 'upper,lower,number', length: 8),
        );

        // Act & Assert
        expect(() => useCase(params), throwsA(isA<SystemException>()));

        // 验证repository没有被调用
        verifyNever(
          mockRepository.setUpdatePassword(
            userName: anyNamed('userName'),
            oldPassword: anyNamed('oldPassword'),
            newPassword: anyNamed('newPassword'),
            tenantId: anyNamed('tenantId'),
            zoneId: anyNamed('zoneId'),
          ),
        );
      });

      test('should throw SystemException when repository throws exception', () async {
        // repository抛出异常时应抛出SystemException
        // Arrange
        final params = ResetPasswordArgumentsModel(
          isBiometrics: false,
          zoneId: 'zone123',
          oldPassword: 'oldPassword123',
          newPassword: 'newPassword123',
          userName: '<EMAIL>',
          tenantId: 'tenant123',
          tenantName: 'Test Tenant',
          policy: PasswordPolicyModel(mustContain: 'upper,lower,number', length: 8),
        );

        when(
          mockRepository.setUpdatePassword(
            userName: anyNamed('userName'),
            oldPassword: anyNamed('oldPassword'),
            newPassword: anyNamed('newPassword'),
            tenantId: anyNamed('tenantId'),
            zoneId: anyNamed('zoneId'),
          ),
        ).thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => useCase(params),
          throwsA(isA<Exception>().having((e) => e.toString(), 'message', contains('Repository error'))),
        );

        verify(
          mockRepository.setUpdatePassword(
            userName: params.userName,
            oldPassword: params.oldPassword,
            newPassword: params.newPassword!,
            tenantId: params.tenantId,
            zoneId: params.zoneId,
          ),
        ).called(1);
      });
    });

    group('initPasswordRules() 方法测试', () {
      test('should generate basic rules with upper, lower, and number', () {
        // 基本规则生成：包含大写字母、小写字母、数字
        // Act
        final result = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Assert
        expect(result, isA<PasswordVerificationRulesModel>());
        expect(result.regexText, '^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])\{8,\}');
        expect(result.mustContainArray, containsAll(['英大文字を1文字以上含む(A-Z)', '英小文字を1文字以上含む(a-z)', '数字を1文字以上含む(0-9)']));
        expect(result.signFlg, false);
        expect(result.passwordLength, 8);
      });

      test('should generate rules with special characters', () {
        // 包含特殊字符：包含符号规则
        // Act
        final result = useCase.initPasswordRules(mustContain: 'upper,lower,number,sign', passwordLength: 10);

        // Assert
        expect(result, isA<PasswordVerificationRulesModel>());
        expect(result.regexText, '^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])\{10,\}');
        expect(result.mustContainArray, containsAll(['英大文字を1文字以上含む(A-Z)', '英小文字を1文字以上含む(a-z)', '数字を1文字以上含む(0-9)']));
        expect(result.mustContainArray.any((item) => item.contains('记号を1文字以上含む')), isTrue);
        expect(result.signFlg, true);
        expect(result.passwordLength, 10);
      });

      test('should generate rules without special characters', () {
        // 不包含特殊字符：不包含符号规则
        // Act
        final result = useCase.initPasswordRules(mustContain: 'upper,lower', passwordLength: 6);

        // Assert
        expect(result, isA<PasswordVerificationRulesModel>());
        expect(result.regexText, '^(?=.*[A-Z])(?=.*[a-z])\{6,\}');
        expect(result.mustContainArray, containsAll(['英大文字を1文字以上含む(A-Z)', '英小文字を1文字以上含む(a-z)']));
        expect(result.signFlg, false);
        expect(result.passwordLength, 6);
      });

      test('should generate rules with minimum length', () {
        // 边界情况：最小长度规则
        // Act
        final result = useCase.initPasswordRules(mustContain: 'number', passwordLength: 1);

        // Assert
        expect(result, isA<PasswordVerificationRulesModel>());
        expect(result.regexText, '^(?=.*[0-9])\{1,\}');
        expect(result.mustContainArray, contains('数字を1文字以上含む(0-9)'));
        expect(result.signFlg, false);
        expect(result.passwordLength, 1);
      });

      test('should generate rules with empty mustContain', () {
        // 空规则：不包含任何规则
        // Act
        final result = useCase.initPasswordRules(mustContain: '', passwordLength: 8);

        // Assert
        expect(result, isA<PasswordVerificationRulesModel>());
        expect(result.regexText, '^\{8,\}');
        expect(result.mustContainArray, isEmpty);
        expect(result.signFlg, false);
        expect(result.passwordLength, 8);
      });

      test('should generate rules with mixed requirements', () {
        // 混合规则：包含多种规则组合
        // Act
        final result = useCase.initPasswordRules(mustContain: 'upper,sign', passwordLength: 12);

        // Assert
        expect(result, isA<PasswordVerificationRulesModel>());
        expect(result.regexText, '^(?=.*[A-Z])\{12,\}');
        expect(result.mustContainArray, containsAll(['英大文字を1文字以上含む(A-Z)']));
        expect(result.mustContainArray.any((item) => item.contains('记号を1文字以上含む')), isTrue);
        expect(result.signFlg, true);
        expect(result.passwordLength, 12);
      });
    });

    group('passwordLegitimacyCheck() 方法测试', () {
      test('should return error when password is empty', () {
        // 密码为空：空字符串检查
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: '');

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '新しいパスワードは必須項目です');
      });

      test('should return error when password contains spaces', () {
        // 包含空格：空格检查
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'Pass word123');

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return error when password length is insufficient', () {
        // 长度不足：密码长度小于要求
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'Pass1');

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return error when password does not match regex pattern', () {
        // 正则匹配失败：不符合正则表达式规则
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(
          pvr: rules,
          password: 'password123', // 缺少大写字母
        );

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return error when special characters are required but not included', () {
        // 特殊字符检查失败：需要特殊字符但未包含
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number,sign', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(
          pvr: rules,
          password: 'Password123', // 缺少特殊字符
        );

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return success when special characters are required and included', () {
        // 特殊字符检查成功：需要特殊字符且包含
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number,sign', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'Password@123');

        // Assert
        expect(result.hasNewPasswordError, false);
        expect(result.newPasswordTip, '');
      });

      test('should return error when uppercase letter is missing', () {
        // 大写字母缺失：需要大写字母但未包含
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'password123');

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return error when lowercase letter is missing', () {
        // 小写字母缺失：需要小写字母但未包含
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'PASSWORD123');

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return error when number is missing', () {
        // 数字缺失：需要数字但未包含
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'Password');

        // Assert
        expect(result.hasNewPasswordError, true);
        expect(result.newPasswordTip, '以下のパスワード条件を満たすパスワードを設定してください');
      });

      test('should return success when password is valid', () {
        // 密码有效：所有规则都满足
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'Password123');

        // Assert
        expect(result.hasNewPasswordError, false);
        expect(result.newPasswordTip, '');
      });

      test('should return success with complex rule combination', () {
        // 复杂规则组合：多种规则组合的验证
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number,sign', passwordLength: 10);

        // Act
        final result = useCase.passwordLegitimacyCheck(pvr: rules, password: 'Password@123');

        // Assert
        expect(result.hasNewPasswordError, false);
        expect(result.newPasswordTip, '');
      });

      test('should return success with boundary length', () {
        // 边界长度：刚好满足长度要求
        // Arrange
        final rules = useCase.initPasswordRules(mustContain: 'upper,lower,number', passwordLength: 8);

        // Act
        final result = useCase.passwordLegitimacyCheck(
          pvr: rules,
          password: 'Pass1234', // 刚好8位
        );

        // Assert
        expect(result.hasNewPasswordError, false);
        expect(result.newPasswordTip, '');
      });
    });

    group('passwordConsistenceCheck() 方法测试', () {
      test('should return error when confirm password is empty', () {
        // 确认密码为空：空字符串检查
        // Act
        final result = useCase.passwordConsistenceCheck(confirmPassword: '', newPassword: 'Password123');

        // Assert
        expect(result.hasConfirmPasswordError, true);
        expect(result.passwordConfirmTip, '新しいパスワード（確認用）は必須項目です');
      });

      test('should return error when passwords do not match', () {
        // 密码不匹配：两次输入不一致
        // Act
        final result = useCase.passwordConsistenceCheck(confirmPassword: 'Password123', newPassword: 'Password456');

        // Assert
        expect(result.hasConfirmPasswordError, true);
        expect(result.passwordConfirmTip, '入力したパスワードが一致しません');
      });

      test('should return success when passwords match', () {
        // 密码匹配：两次输入一致
        // Act
        final result = useCase.passwordConsistenceCheck(confirmPassword: 'Password123', newPassword: 'Password123');

        // Assert
        expect(result.hasConfirmPasswordError, false);
        expect(result.passwordConfirmTip, '');
      });

      test('should return success when both passwords are empty strings', () {
        // 边界情况：空字符串与空字符串比较
        // Act
        final result = useCase.passwordConsistenceCheck(confirmPassword: '', newPassword: '');

        // Assert
        expect(result.hasConfirmPasswordError, true);
        expect(result.passwordConfirmTip, '新しいパスワード（確認用）は必須項目です');
      });
    });
  });
}
