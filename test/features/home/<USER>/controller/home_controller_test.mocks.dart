// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/home/<USER>/controller/home_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i9;
import 'dart:ui' as _i15;

import 'package:asset_force_mobile_v2/core/event_bus/event_bus_core.dart'
    as _i18;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i16;
import 'package:asset_force_mobile_v2/core/services/action_scan_service.dart'
    as _i19;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/app_tabs/domain/usecase/action_sheet_module_usecase.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i21;
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_model_and_unread_hint_usecase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_home_page_list_usecase.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_list_model.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_arinfo.dart'
    as _i20;
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart'
    as _i14;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/get_authority_repository.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/domain/usecase/action/action_temporary_usecase.dart'
    as _i8;
import 'package:get/get.dart' as _i5;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i17;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeHomeRepository_0 extends _i1.SmartFake
    implements _i2.HomeRepository {
  _FakeHomeRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetAuthorityRepository_1 extends _i1.SmartFake
    implements _i3.GetAuthorityRepository {
  _FakeGetAuthorityRepository_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetHomeModelAndUnreadHintParams_2 extends _i1.SmartFake
    implements _i4.GetHomeModelAndUnreadHintParams {
  _FakeGetHomeModelAndUnreadHintParams_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeRxInt_3 extends _i1.SmartFake implements _i5.RxInt {
  _FakeRxInt_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_4 extends _i1.SmartFake implements _i5.RxBool {
  _FakeRxBool_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeModuleUseCase_5 extends _i1.SmartFake implements _i6.ModuleUseCase {
  _FakeModuleUseCase_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_6 extends _i1.SmartFake
    implements _i7.NavigationService {
  _FakeNavigationService_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_7<T> extends _i1.SmartFake
    implements _i5.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeObject_8 extends _i1.SmartFake implements Object {
  _FakeObject_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeActionTemporaryUsecase_9 extends _i1.SmartFake
    implements _i8.ActionTemporaryUsecase {
  _FakeActionTemporaryUsecase_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetHomeModelAndUnreadHintUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetHomeModelAndUnreadHintUseCase extends _i1.Mock
    implements _i4.GetHomeModelAndUnreadHintUseCase {
  MockGetHomeModelAndUnreadHintUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.HomeRepository get homeRepository =>
      (super.noSuchMethod(
            Invocation.getter(#homeRepository),
            returnValue: _FakeHomeRepository_0(
              this,
              Invocation.getter(#homeRepository),
            ),
          )
          as _i2.HomeRepository);

  @override
  _i3.GetAuthorityRepository get getAuthorityRepository =>
      (super.noSuchMethod(
            Invocation.getter(#getAuthorityRepository),
            returnValue: _FakeGetAuthorityRepository_1(
              this,
              Invocation.getter(#getAuthorityRepository),
            ),
          )
          as _i3.GetAuthorityRepository);

  @override
  _i9.Future<_i4.GetHomeModelAndUnreadHintParams> call(dynamic params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i9.Future<_i4.GetHomeModelAndUnreadHintParams>.value(
              _FakeGetHomeModelAndUnreadHintParams_2(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i9.Future<_i4.GetHomeModelAndUnreadHintParams>);
}

/// A class which mocks [GetHomePageListUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetHomePageListUseCase extends _i1.Mock
    implements _i10.GetHomePageListUseCase {
  MockGetHomePageListUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.HomeRepository get homeRepository =>
      (super.noSuchMethod(
            Invocation.getter(#homeRepository),
            returnValue: _FakeHomeRepository_0(
              this,
              Invocation.getter(#homeRepository),
            ),
          )
          as _i2.HomeRepository);

  @override
  _i9.Future<List<dynamic>> call(dynamic params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i9.Future<List<dynamic>>.value(<dynamic>[]),
          )
          as _i9.Future<List<dynamic>>);

  @override
  List<dynamic> homeActionAndWFList(_i11.HomeListModel? homeModelInfo) =>
      (super.noSuchMethod(
            Invocation.method(#homeActionAndWFList, [homeModelInfo]),
            returnValue: <dynamic>[],
          )
          as List<dynamic>);
}

/// A class which mocks [GetRoleListUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetRoleListUseCase extends _i1.Mock
    implements _i12.GetRoleListUseCase {
  MockGetRoleListUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.HomeRepository get homeRepository =>
      (super.noSuchMethod(
            Invocation.getter(#homeRepository),
            returnValue: _FakeHomeRepository_0(
              this,
              Invocation.getter(#homeRepository),
            ),
          )
          as _i2.HomeRepository);

  @override
  _i9.Future<dynamic> call(dynamic params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i9.Future<dynamic>.value(),
          )
          as _i9.Future<dynamic>);
}

/// A class which mocks [TabsController].
///
/// See the documentation for Mockito's code generation for more information.
class MockTabsController extends _i1.Mock implements _i13.TabsController {
  MockTabsController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.RxInt get currentIndex =>
      (super.noSuchMethod(
            Invocation.getter(#currentIndex),
            returnValue: _FakeRxInt_3(this, Invocation.getter(#currentIndex)),
          )
          as _i5.RxInt);

  @override
  _i5.RxBool get showWorkflowUnreadHint =>
      (super.noSuchMethod(
            Invocation.getter(#showWorkflowUnreadHint),
            returnValue: _FakeRxBool_4(
              this,
              Invocation.getter(#showWorkflowUnreadHint),
            ),
          )
          as _i5.RxBool);

  @override
  _i5.RxBool get showActionTaskUnreadHint =>
      (super.noSuchMethod(
            Invocation.getter(#showActionTaskUnreadHint),
            returnValue: _FakeRxBool_4(
              this,
              Invocation.getter(#showActionTaskUnreadHint),
            ),
          )
          as _i5.RxBool);

  @override
  _i5.RxBool get showBottomNavBar =>
      (super.noSuchMethod(
            Invocation.getter(#showBottomNavBar),
            returnValue: _FakeRxBool_4(
              this,
              Invocation.getter(#showBottomNavBar),
            ),
          )
          as _i5.RxBool);

  @override
  _i6.ModuleUseCase get moduleUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#moduleUseCase),
            returnValue: _FakeModuleUseCase_5(
              this,
              Invocation.getter(#moduleUseCase),
            ),
          )
          as _i6.ModuleUseCase);

  @override
  _i7.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_6(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i7.NavigationService);

  @override
  set navigationService(_i7.NavigationService? _navigationService) =>
      super.noSuchMethod(
        Invocation.setter(#navigationService, _navigationService),
        returnValueForMissingStub: null,
      );

  @override
  _i5.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i5.InternalFinalCallback<void>);

  @override
  _i5.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_7<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i5.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(Invocation.getter(#listeners), returnValue: 0)
          as int);

  @override
  Object get eventBusScopeKey =>
      (super.noSuchMethod(
            Invocation.getter(#eventBusScopeKey),
            returnValue: _FakeObject_8(
              this,
              Invocation.getter(#eventBusScopeKey),
            ),
          )
          as Object);

  @override
  set eventBusScopeKey(Object? _eventBusScopeKey) => super.noSuchMethod(
    Invocation.setter(#eventBusScopeKey, _eventBusScopeKey),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void handleTabTap(
    _i14.SharedNavBarEnum? tab, {
    bool? forceSwitch = false,
    _i15.VoidCallback? onTabSwitched,
  }) => super.noSuchMethod(
    Invocation.method(
      #handleTabTap,
      [tab],
      {#forceSwitch: forceSwitch, #onTabSwitched: onTabSwitched},
    ),
    returnValueForMissingStub: null,
  );

  @override
  _i9.Future<void> handleScanTap() =>
      (super.noSuchMethod(
            Invocation.method(#handleScanTap, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void updateUnreadHints({bool? workflow, bool? actionTask}) =>
      super.noSuchMethod(
        Invocation.method(#updateUnreadHints, [], {
          #workflow: workflow,
          #actionTask: actionTask,
        }),
        returnValueForMissingStub: null,
      );

  @override
  void showBar() => super.noSuchMethod(
    Invocation.method(#showBar, []),
    returnValueForMissingStub: null,
  );

  @override
  void hideBar() => super.noSuchMethod(
    Invocation.method(#hideBar, []),
    returnValueForMissingStub: null,
  );

  @override
  _i9.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i16.ErrorHandlingMode? mode = _i16.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i17.Disposer addListener(_i17.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
          )
          as _i17.Disposer);

  @override
  void removeListener(_i15.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i15.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i17.Disposer addListenerId(Object? key, _i17.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
          )
          as _i17.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void onEvent<T>(_i18.AsyncEventHandler<T>? handler) => super.noSuchMethod(
    Invocation.method(#onEvent, [handler]),
    returnValueForMissingStub: null,
  );

  @override
  void onceEvent<T>(_i18.AsyncEventHandler<T>? handler) => super.noSuchMethod(
    Invocation.method(#onceEvent, [handler]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [ActionScanService].
///
/// See the documentation for Mockito's code generation for more information.
class MockActionScanService extends _i1.Mock implements _i19.ActionScanService {
  MockActionScanService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.ActionTemporaryUsecase get actionTemporaryUsecase =>
      (super.noSuchMethod(
            Invocation.getter(#actionTemporaryUsecase),
            returnValue: _FakeActionTemporaryUsecase_9(
              this,
              Invocation.getter(#actionTemporaryUsecase),
            ),
          )
          as _i8.ActionTemporaryUsecase);

  @override
  set actionTemporaryUsecase(
    _i8.ActionTemporaryUsecase? _actionTemporaryUsecase,
  ) => super.noSuchMethod(
    Invocation.setter(#actionTemporaryUsecase, _actionTemporaryUsecase),
    returnValueForMissingStub: null,
  );

  @override
  _i7.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_6(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i7.NavigationService);

  @override
  set navigationService(_i7.NavigationService? _navigationService) =>
      super.noSuchMethod(
        Invocation.setter(#navigationService, _navigationService),
        returnValueForMissingStub: null,
      );

  @override
  _i9.Future<List<_i20.SharedArInfo>?> scanStartNextStep({
    required _i19.ClickNextStepDataModel? clickNextStepData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scanStartNextStep, [], {
              #clickNextStepData: clickNextStepData,
            }),
            returnValue: _i9.Future<List<_i20.SharedArInfo>?>.value(),
          )
          as _i9.Future<List<_i20.SharedArInfo>?>);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i7.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i9.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i9.Future<dynamic>.value(),
          )
          as _i9.Future<dynamic>);

  @override
  _i9.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i9.Future<dynamic>.value(),
          )
          as _i9.Future<dynamic>);

  @override
  _i9.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i9.Future<bool>.value(false),
          )
          as _i9.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i9.Future<dynamic> toAssetDetail(_i21.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i9.Future<dynamic>.value(),
          )
          as _i9.Future<dynamic>);
}
