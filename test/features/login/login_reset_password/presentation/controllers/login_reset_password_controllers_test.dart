import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'dart:async';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/models/reset_password_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/usecases/reset_password_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/controllers/login_reset_password_controllers.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/states/reset_password_ui_state.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/password_policy_model.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 Mock 类
@GenerateNiceMocks([
  MockSpec<DialogService>(),
  MockSpec<NavigationService>(),
  MockSpec<BiometricsSwitchUtil>(),
  MockSpec<ResetPasswordUsecase>(),
])
import 'login_reset_password_controllers_test.mocks.dart';

// 测试专用的 LoginResetPasswordControllers，避免 Get.arguments 依赖
class TestLoginResetPasswordController extends LoginResetPasswordControllers {
  TestLoginResetPasswordController({
    required super.biometricsSwitchUtil,
    required super.resetPasswordUsecase,
    required super.dialogService,
    required super.navigationService,
  });

  // 测试辅助字段
  bool testInitCalled = false;

  // 重写 onInit 避免 Get.arguments 调用，但保持其他逻辑不变
  @override
  void onInit() {
    // 不调用 super.onInit()，避免 Get.arguments 依赖
    // 不调用 initStateFromParam()，由测试手动控制时机
    testInitCalled = true;
  }

  // 手动设置测试参数（不重写业务逻辑）
  void setTestParam(ResetPasswordArgumentsModel testParam) {
    param = testParam;
  }

  // 测试辅助方法：暴露真实的业务逻辑供测试
  void callInitStateFromParam() {
    // 调用真实的业务逻辑方法，不重写任何逻辑
    super.initStateFromParam();
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockDialogService mockDialogService;
  late MockNavigationService mockNavigationService;
  late MockBiometricsSwitchUtil mockBiometricsSwitchUtil;
  late MockResetPasswordUsecase mockResetPasswordUsecase;
  late TestLoginResetPasswordController controller;

  // 测试数据准备
  late PasswordPolicyModel testPasswordPolicy;
  late ResetPasswordArgumentsModel testValidParams;
  late ResetPasswordArgumentsModel testNoBiometricsParams;
  late PasswordVerificationRulesModel testPasswordVerificationRules;
  late ResetPasswordResultModel testSuccessResult;
  late ResetPasswordResultModel testFailureResult;
  late ResetPasswordArgumentsModel testEmptyParams;

  setUp(() {
    // 初始化 Mock 对象
    mockDialogService = MockDialogService();
    mockNavigationService = MockNavigationService();
    mockBiometricsSwitchUtil = MockBiometricsSwitchUtil();
    mockResetPasswordUsecase = MockResetPasswordUsecase();

    // 初始化 LogUtil 以避免测试中的 LateInitializationError
    LogUtil.initialize();

    // 设置 Get 测试模式
    Get.testMode = true;

    // 准备测试数据
    testPasswordPolicy = PasswordPolicyModel(length: 8, mustContain: 'upperlowernumber');

    testValidParams = ResetPasswordArgumentsModel(
      userName: '<EMAIL>',
      tenantName: 'Test Tenant',
      tenantId: 'tenant123',
      zoneId: 'zone001',
      oldPassword: 'oldPassword123',
      isBiometrics: true,
      policy: testPasswordPolicy,
    );

    testNoBiometricsParams = ResetPasswordArgumentsModel(
      userName: '<EMAIL>',
      tenantName: 'Test Tenant',
      tenantId: 'tenant123',
      zoneId: 'zone001',
      oldPassword: 'oldPassword123',
      isBiometrics: false,
      policy: testPasswordPolicy,
    );

    testPasswordVerificationRules = PasswordVerificationRulesModel(
      regexText: '^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9]){8,}',
      mustContainArray: ['大文字', '小文字', '数字'],
      signFlg: false,
      passwordLength: 8,
    );

    testSuccessResult = ResetPasswordResultModel(code: 0, msg: 'success');

    testFailureResult = ResetPasswordResultModel(code: 1, msg: 'error')..updatePwdResult = '旧密码错误';

    testEmptyParams = ResetPasswordArgumentsModel(
      userName: '',
      tenantName: '',
      tenantId: '',
      zoneId: '',
      oldPassword: '',
      isBiometrics: false,
      policy: PasswordPolicyModel(length: 0, mustContain: ''),
    );

    // 创建测试控制器
    controller = TestLoginResetPasswordController(
      biometricsSwitchUtil: mockBiometricsSwitchUtil,
      resetPasswordUsecase: mockResetPasswordUsecase,
      dialogService: mockDialogService,
      navigationService: mockNavigationService,
    );
  });

  tearDown(() {
    // 清理测试环境
    Get.reset();
    // 安全地销毁控制器（可能在某些测试中已经被销毁）
    try {
      controller.onClose();
    } catch (e) {
      // 忽略清理时的异常
    }
  });

  // Mock 配置工具方法
  void setupMocksForSuccess() {
    // 配置密码验证成功
    when(
      mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
    ).thenReturn(PasswordVerificationResult(hasNewPasswordError: false, newPasswordTip: ''));

    when(
      mockResetPasswordUsecase.passwordConsistenceCheck(
        newPassword: anyNamed('newPassword'),
        confirmPassword: anyNamed('confirmPassword'),
      ),
    ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));

    // 配置更新密码成功
    when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testSuccessResult);

    // 配置生物识别禁用成功
    when(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen'))).thenAnswer((_) async => {});

    // 配置对话框服务
    when(
      mockDialogService.show(
        title: anyNamed('title'),
        content: anyNamed('content'),
        confirmText: anyNamed('confirmText'),
        cancelText: anyNamed('cancelText'),
        onConfirm: anyNamed('onConfirm'),
        onCancel: anyNamed('onCancel'),
        barrierDismissible: anyNamed('barrierDismissible'),
        type: anyNamed('type'),
      ),
    ).thenAnswer((_) async => {});
  }

  void setupMocksForValidationFailure() {
    // 配置密码验证失败
    when(
      mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
    ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

    when(
      mockResetPasswordUsecase.passwordConsistenceCheck(
        newPassword: anyNamed('newPassword'),
        confirmPassword: anyNamed('confirmPassword'),
      ),
    ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: true, passwordConfirmTip: '密码不一致'));
  }

  void setupMocksForInitPasswordRules() {
    // 配置初始化密码规则
    when(
      mockResetPasswordUsecase.initPasswordRules(
        mustContain: anyNamed('mustContain'),
        passwordLength: anyNamed('passwordLength'),
      ),
    ).thenReturn(testPasswordVerificationRules);
  }

  void setupMocksForPasswordValidationFailure() {
    // 配置密码验证失败的情况
    when(
      mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
    ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合要求'));

    // 配置密码一致性检查
    when(
      mockResetPasswordUsecase.passwordConsistenceCheck(
        newPassword: anyNamed('newPassword'),
        confirmPassword: anyNamed('confirmPassword'),
      ),
    ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));
  }

  // 验证工具方法
  void verifyDialogShown({
    String? expectedTitle,
    String? expectedContent,
    DialogType? expectedType,
    int expectedCallCount = 1,
  }) {
    final captured = verify(
      mockDialogService.show(
        title: expectedTitle != null ? captureAnyNamed('title') : anyNamed('title'),
        content: expectedContent != null ? captureAnyNamed('content') : anyNamed('content'),
        confirmText: anyNamed('confirmText'),
        cancelText: anyNamed('cancelText'),
        onConfirm: anyNamed('onConfirm'),
        onCancel: anyNamed('onCancel'),
        barrierDismissible: anyNamed('barrierDismissible'),
        type: expectedType != null ? captureAnyNamed('type') : anyNamed('type'),
      ),
    );

    captured.called(expectedCallCount);

    // 验证具体参数
    if (expectedTitle != null || expectedContent != null || expectedType != null) {
      final List<dynamic> capturedArgs = captured.captured;
      if (expectedTitle != null && capturedArgs.isNotEmpty) {
        expect(capturedArgs.first, expectedTitle);
      }
    }
  }

  void verifyNavigationCalled({int expectedCallCount = 1}) {
    if (expectedCallCount > 0) {
      verify(mockNavigationService.goBack()).called(expectedCallCount);
    } else {
      verifyNever(mockNavigationService.goBack());
    }
  }

  void verifyBiometricsDisabled({int expectedCallCount = 1}) {
    if (expectedCallCount > 0) {
      verify(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: false)).called(expectedCallCount);
    } else {
      verifyNever(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')));
    }
  }

  /// ----------------------------
  /// Phase 0: 测试基础设施验证
  /// ----------------------------

  group('Phase 0: 测试基础设施验证', () {
    test('应该能正常创建测试控制器', () {
      // Assert
      expect(controller, isNotNull);
      expect(controller.biometricsSwitchUtil, mockBiometricsSwitchUtil);
      expect(controller.resetPasswordUsecase, mockResetPasswordUsecase);
      expect(controller.dialogService, mockDialogService);
      expect(controller.navigationService, mockNavigationService);
    });

    test('测试专用 onInit 应该被调用', () {
      // Arrange - 创建新的控制器
      final testController = TestLoginResetPasswordController(
        biometricsSwitchUtil: mockBiometricsSwitchUtil,
        resetPasswordUsecase: mockResetPasswordUsecase,
        dialogService: mockDialogService,
        navigationService: mockNavigationService,
      );

      // Act - 手动调用 onInit（在测试环境中不会自动调用）
      testController.onInit();

      // Assert
      expect(testController.testInitCalled, true);

      // 清理
      testController.onClose();
    });

    test('测试数据应正确准备', () {
      // Assert - 验证测试数据
      expect(testPasswordPolicy.length, 8);
      expect(testPasswordPolicy.mustContain, 'upperlowernumber');

      expect(testValidParams.userName, '<EMAIL>');
      expect(testValidParams.isBiometrics, true);

      expect(testNoBiometricsParams.isBiometrics, false);

      expect(testPasswordVerificationRules.passwordLength, 8);
      expect(testPasswordVerificationRules.mustContainArray.length, 3);

      expect(testSuccessResult.code, 0);
      expect(testFailureResult.code, 1);
      expect(testFailureResult.updatePwdResult, '旧密码错误');
    });

    test('Mock 对象应正确配置', () {
      // Arrange
      setupMocksForSuccess();

      // Act & Assert - 验证 Mock 配置不会抛出异常
      expect(() => mockDialogService.show(content: 'test'), returnsNormally);
      expect(() => mockNavigationService.goBack(), returnsNormally);
      expect(() => mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: false), returnsNormally);
    });

    test('initStateFromParam 应正确初始化状态（测试真实业务逻辑）', () {
      // Arrange
      setupMocksForInitPasswordRules();
      controller.setTestParam(testValidParams);

      // Act - 调用真实的业务逻辑方法
      controller.callInitStateFromParam();

      // Assert
      expect(controller.param, testValidParams);
      expect(controller.state.userEmail.value, '<EMAIL>');
      expect(controller.state.tenantName.value, 'Test Tenant');
      expect(controller.state.isBiometrics.value, true);
      expect(controller.state.passwordLength.value, 8);

      // 验证 usecase 被正确调用
      verify(mockResetPasswordUsecase.initPasswordRules(mustContain: 'upperlowernumber', passwordLength: 8)).called(1);
    });

    test('TextEditingController 应正确初始化', () {
      // Assert
      expect(controller.newPasswordController, isNotNull);
      expect(controller.newPasswordConfirmController, isNotNull);
      expect(controller.newPasswordController.text, '');
      expect(controller.newPasswordConfirmController.text, '');
    });

    test('状态对象应正确初始化', () {
      // Assert
      expect(controller.state, isNotNull);
      expect(controller.state.hasNewPasswordError.value, false);
      expect(controller.state.hasConfirmPasswordError.value, false);
      expect(controller.state.isUpdating.value, false);
      expect(controller.state.isReturning.value, false);
      expect(controller.state.userEmail.value, '');
      expect(controller.state.tenantName.value, '');
      expect(controller.state.isBiometrics.value, false);
      expect(controller.state.passwordLength.value, 0);
    });

    test('验证工具方法应正常工作', () {
      // Arrange
      setupMocksForSuccess();

      // Act - 调用 dialogService.show
      mockDialogService.show(title: 'Test Title', content: 'Test Content', type: DialogType.info);

      // Assert - 验证工具方法能正确验证调用
      expect(
        () => verifyDialogShown(
          expectedTitle: 'Test Title',
          expectedContent: 'Test Content',
          expectedType: DialogType.info,
        ),
        returnsNormally,
      );
    });

    test('onClose 应正确释放资源', () {
      // Arrange - 创建新的控制器专门用于测试 onClose
      final testController = TestLoginResetPasswordController(
        biometricsSwitchUtil: mockBiometricsSwitchUtil,
        resetPasswordUsecase: mockResetPasswordUsecase,
        dialogService: mockDialogService,
        navigationService: mockNavigationService,
      );

      // Act & Assert - 验证 onClose 不抛出异常
      expect(() => testController.onClose(), returnsNormally);
    });

    test('Mock 配置工具方法应正常工作', () {
      // Act & Assert - 验证各种 Mock 配置方法不抛出异常
      expect(() => setupMocksForSuccess(), returnsNormally);
      expect(() => setupMocksForValidationFailure(), returnsNormally);
      expect(() => setupMocksForInitPasswordRules(), returnsNormally);
    });
  });

  /// ----------------------------
  /// Phase 1: 核心业务逻辑测试
  /// ----------------------------

  group('Phase 1: 核心业务逻辑测试', () {
    group('initStateFromParam 方法测试', () {
      test('应正确初始化完整的状态', () {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);

        // Act
        controller.callInitStateFromParam();

        // Assert - 验证状态正确初始化
        expect(controller.param, testValidParams);
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.isBiometrics.value, true);
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.mustContainArray.value, testPasswordVerificationRules.mustContainArray);

        // Assert - 验证 usecase 被正确调用
        verify(
          mockResetPasswordUsecase.initPasswordRules(mustContain: 'upperlowernumber', passwordLength: 8),
        ).called(1);
      });

      test('应处理空参数情况', () {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(
          ResetPasswordArgumentsModel(
            userName: '',
            tenantName: '',
            tenantId: '',
            zoneId: '',
            oldPassword: '',
            isBiometrics: false,
            policy: PasswordPolicyModel(length: null, mustContain: null),
          ),
        );

        // Act
        controller.callInitStateFromParam();

        // Assert - 验证默认值处理
        expect(controller.state.userEmail.value, '');
        expect(controller.state.tenantName.value, '');
        expect(controller.state.isBiometrics.value, false);
        expect(controller.state.passwordLength.value, 0);

        // Assert - 验证使用默认值调用 usecase
        verify(mockResetPasswordUsecase.initPasswordRules(mustContain: '', passwordLength: 0)).called(1);
      });

      test('应处理无生物识别场景', () {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testNoBiometricsParams);

        // Act
        controller.callInitStateFromParam();

        // Assert
        expect(controller.state.isBiometrics.value, false);
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.passwordLength.value, 8);
      });

      test('应正确设置密码验证规则', () {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);

        // Act
        controller.callInitStateFromParam();

        // Assert - 验证密码规则被正确设置
        expect(controller.state.mustContainArray.value, isNotEmpty);
        expect(controller.state.mustContainArray.value.length, 3);
        expect(controller.state.mustContainArray.value.first, '大文字');
      });
    });

    group('updatePassword 方法 - 验证逻辑测试', () {
      setUp(() {
        // 为密码更新测试设置基础参数
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('密码合规性验证失败时应显示错误', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));

        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            confirmText: anyNamed('confirmText'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
            onCancel: anyNamed('onCancel'),
            barrierDismissible: anyNamed('barrierDismissible'),
            type: anyNamed('type'),
          ),
        ).thenAnswer((_) async => {});

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'invalid';

        // Act
        await controller.updatePassword();

        // Assert - 验证状态更新
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.newPasswordTip.value, '密码不符合规则');
        expect(controller.state.hasConfirmPasswordError.value, false);
        expect(controller.state.isUpdating.value, false);

        // Assert - 验证错误对话框显示
        verify(
          mockDialogService.show(
            type: DialogType.error,
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
          ),
        ).called(1);

        // Assert - 验证不调用更新密码
        verifyNever(mockResetPasswordUsecase.call(any));
      });

      test('密码一致性验证失败时应显示错误', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: false, newPasswordTip: ''));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: true, passwordConfirmTip: '密码不一致'));

        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            confirmText: anyNamed('confirmText'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
            onCancel: anyNamed('onCancel'),
            barrierDismissible: anyNamed('barrierDismissible'),
            type: anyNamed('type'),
          ),
        ).thenAnswer((_) async => {});

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'DifferentPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证状态更新
        expect(controller.state.hasNewPasswordError.value, false);
        expect(controller.state.hasConfirmPasswordError.value, true);
        expect(controller.state.passwordConfirmTip.value, '密码不一致');
        expect(controller.state.isUpdating.value, false);

        // Assert - 验证错误对话框显示
        verify(
          mockDialogService.show(
            type: DialogType.error,
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
          ),
        ).called(1);

        // Assert - 验证不调用更新密码
        verifyNever(mockResetPasswordUsecase.call(any));
      });

      test('两种验证都失败时应显示错误', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: true, passwordConfirmTip: '密码不一致'));

        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            confirmText: anyNamed('confirmText'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
            onCancel: anyNamed('onCancel'),
            barrierDismissible: anyNamed('barrierDismissible'),
            type: anyNamed('type'),
          ),
        ).thenAnswer((_) async => {});

        controller.newPasswordController.text = 'bad';
        controller.newPasswordConfirmController.text = 'different';

        // Act
        await controller.updatePassword();

        // Assert - 验证状态更新
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.newPasswordTip.value, '密码不符合规则');
        expect(controller.state.hasConfirmPasswordError.value, true);
        expect(controller.state.passwordConfirmTip.value, '密码不一致');
        expect(controller.state.isUpdating.value, false);

        // Assert - 验证错误对话框显示
        verify(
          mockDialogService.show(
            type: DialogType.error,
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
          ),
        ).called(1);

        // Assert - 验证不调用更新密码
        verifyNever(mockResetPasswordUsecase.call(any));
      });

      test('验证通过时应继续执行更新', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: false, newPasswordTip: ''));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));

        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testSuccessResult);

        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            confirmText: anyNamed('confirmText'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
            onCancel: anyNamed('onCancel'),
            barrierDismissible: anyNamed('barrierDismissible'),
            type: anyNamed('type'),
          ),
        ).thenAnswer((_) async => {});

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证状态清除
        expect(controller.state.hasNewPasswordError.value, false);
        expect(controller.state.hasConfirmPasswordError.value, false);
        expect(controller.state.isUpdating.value, false);

        // Assert - 验证调用更新密码
        verify(mockResetPasswordUsecase.call(any)).called(1);
      });
    });

    group('updatePassword 方法 - 更新逻辑测试', () {
      setUp(() {
        // 为密码更新测试设置基础参数和成功验证
        setupMocksForInitPasswordRules();
        setupMocksForSuccess();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';
      });

      test('更新成功（无生物识别）应显示成功消息', () async {
        // Arrange
        controller.setTestParam(testNoBiometricsParams);
        controller.callInitStateFromParam();

        // Act
        await controller.updatePassword();

        // Assert - 验证成功对话框显示
        verify(mockDialogService.show(content: '新しいパスワードを設定しました', onConfirm: anyNamed('onConfirm'))).called(1);

        // Assert - 验证不调用生物识别禁用
        verifyNever(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')));

        // Assert - 验证更新密码被调用
        verify(mockResetPasswordUsecase.call(any)).called(1);
      });

      test('更新成功（有生物识别）应禁用生物识别并显示提示', () async {
        // Arrange
        when(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen'))).thenAnswer((_) async => {});

        // Act
        await controller.updatePassword();

        // Assert - 验证生物识别被禁用
        verify(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: false)).called(1);

        // Assert - 验证特殊成功对话框显示
        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // Assert - 验证更新密码被调用
        verify(mockResetPasswordUsecase.call(any)).called(1);
      });

      test('更新失败（后台错误）应显示错误消息', () async {
        // Arrange
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testFailureResult);

        // Act
        await controller.updatePassword();

        // Assert - 验证错误对话框显示
        verify(mockDialogService.show(title: '内容に不備があります', type: DialogType.error, content: '旧密码错误')).called(1);

        // Assert - 验证不调用生物识别禁用
        verifyNever(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')));

        // Assert - 验证更新密码被调用
        verify(mockResetPasswordUsecase.call(any)).called(1);
      });

      test('更新失败（空错误信息）应显示系统错误', () async {
        // Arrange
        final emptyErrorResult = ResetPasswordResultModel(code: 1, msg: 'error')..updatePwdResult = '';
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => emptyErrorResult);

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误对话框显示
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证更新密码被调用
        verify(mockResetPasswordUsecase.call(any)).called(1);
      });

      test('更新失败（null错误信息）应显示系统错误', () async {
        // Arrange
        final nullErrorResult = ResetPasswordResultModel(code: 1, msg: 'error')..updatePwdResult = null;
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => nullErrorResult);

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误对话框显示
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证更新密码被调用
        verify(mockResetPasswordUsecase.call(any)).called(1);
      });

      test('更新过程中应正确管理 isUpdating 状态', () async {
        // Arrange
        bool isUpdatingDuringCall = false;
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async {
          isUpdatingDuringCall = controller.state.isUpdating.value;
          return testSuccessResult;
        });

        // Act
        final future = controller.updatePassword();

        // Assert - 验证开始时状态为 true
        expect(controller.state.isUpdating.value, true);

        await future;

        // Assert - 验证调用期间状态为 true
        expect(isUpdatingDuringCall, true);

        // Assert - 验证完成后状态为 false
        expect(controller.state.isUpdating.value, false);
      });

      test('更新异常时应显示系统错误', () async {
        // Arrange
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Network error'));

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误对话框显示
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });
    });
  });

  /// ----------------------------
  /// Phase 2: 用户交互测试
  /// ----------------------------

  group('Phase 2: 用户交互测试', () {
    group('onCancel 方法测试', () {
      setUp(() {
        // 为取消操作测试设置基础参数
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('输入框为空时应直接返回', () async {
        // Arrange
        controller.newPasswordController.clear();
        controller.newPasswordConfirmController.clear();

        // Act
        await controller.onCancel();

        // Assert - 验证直接导航返回
        verifyNavigationCalled(expectedCallCount: 1);

        // Assert - 验证不显示确认对话框
        verifyNever(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
          ),
        );

        // Assert - 验证状态恢复
        expect(controller.state.isReturning.value, true);
      });

      test('输入框有内容时应显示确认对话框', () async {
        // Arrange
        controller.newPasswordController.text = 'some input';
        controller.newPasswordConfirmController.text = 'some input';

        // Act
        await controller.onCancel();

        // Assert - 验证显示确认对话框
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // Assert - 验证不直接导航
        verifyNavigationCalled(expectedCallCount: 0);

        // Assert - 验证状态恢复
        expect(controller.state.isReturning.value, false);
      });

      test('只有新密码输入时应显示确认对话框', () async {
        // Arrange
        controller.newPasswordController.text = 'new password';
        controller.newPasswordConfirmController.clear();

        // Act
        await controller.onCancel();

        // Assert - 验证显示确认对话框
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isReturning.value, false);
      });

      test('只有确认密码输入时应显示确认对话框', () async {
        // Arrange
        controller.newPasswordController.clear();
        controller.newPasswordConfirmController.text = 'confirm password';

        // Act
        await controller.onCancel();

        // Assert - 验证显示确认对话框
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isReturning.value, false);
      });

      test('确认对话框的确认操作应导航返回', () async {
        // Arrange
        controller.newPasswordController.text = 'some input';
        controller.newPasswordConfirmController.text = 'some input';

        VoidCallback? capturedOnConfirm;
        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
          ),
        ).thenAnswer((invocation) async {
          capturedOnConfirm = invocation.namedArguments[#onConfirm] as VoidCallback?;
        });

        // Act
        await controller.onCancel();

        // Assert - 验证捕获了 onConfirm 回调
        expect(capturedOnConfirm, isNotNull);

        // Act - 执行确认操作
        capturedOnConfirm!();

        // Assert - 验证导航返回
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('应正确管理 isReturning 状态', () async {
        // Arrange
        controller.newPasswordController.text = 'some input';
        expect(controller.state.isReturning.value, false);

        // Act
        final future = controller.onCancel();

        // Assert - 验证开始时状态设置为 true
        expect(controller.state.isReturning.value, true);

        await future;

        // Assert - 验证完成后状态恢复为 false
        expect(controller.state.isReturning.value, false);
      });

      test('空白字符输入应被视为有内容', () async {
        // Arrange
        controller.newPasswordController.text = '   ';
        controller.newPasswordConfirmController.text = '\t\n';

        // Act
        await controller.onCancel();

        // Assert - 由于这里是检查 isEmpty，空白字符不被视为空
        // 实际行为：应该显示确认对话框
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });
    });

    group('用户输入处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('TextEditingController 应正确存储用户输入', () {
        // Arrange
        const testPassword = 'TestPassword123';
        const testConfirmPassword = 'TestPassword123';

        // Act
        controller.newPasswordController.text = testPassword;
        controller.newPasswordConfirmController.text = testConfirmPassword;

        // Assert
        expect(controller.newPasswordController.text, testPassword);
        expect(controller.newPasswordConfirmController.text, testConfirmPassword);
      });

      test('应能清空输入内容', () {
        // Arrange
        controller.newPasswordController.text = 'Some content';
        controller.newPasswordConfirmController.text = 'Some content';

        // Act
        controller.newPasswordController.clear();
        controller.newPasswordConfirmController.clear();

        // Assert
        expect(controller.newPasswordController.text, '');
        expect(controller.newPasswordConfirmController.text, '');
      });

      test('应能处理特殊字符输入', () {
        // Arrange
        const specialChars = '!@#\$%^&*()_+-=[]{}|;:,.<>?';

        // Act
        controller.newPasswordController.text = specialChars;
        controller.newPasswordConfirmController.text = specialChars;

        // Assert
        expect(controller.newPasswordController.text, specialChars);
        expect(controller.newPasswordConfirmController.text, specialChars);
      });

      test('应能处理Unicode字符输入', () {
        // Arrange
        const unicodeChars = '测试密码123パスワード🔐';

        // Act
        controller.newPasswordController.text = unicodeChars;
        controller.newPasswordConfirmController.text = unicodeChars;

        // Assert
        expect(controller.newPasswordController.text, unicodeChars);
        expect(controller.newPasswordConfirmController.text, unicodeChars);
      });

      test('应能处理长字符串输入', () {
        // Arrange
        final longString = 'A' * 1000;

        // Act
        controller.newPasswordController.text = longString;
        controller.newPasswordConfirmController.text = longString;

        // Assert
        expect(controller.newPasswordController.text, longString);
        expect(controller.newPasswordConfirmController.text, longString);
        expect(controller.newPasswordController.text.length, 1000);
        expect(controller.newPasswordConfirmController.text.length, 1000);
      });
    });

    group('对话框交互测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('成功对话框应包含正确的内容和回调', () async {
        // Arrange
        setupMocksForSuccess();
        controller.setTestParam(testNoBiometricsParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        VoidCallback? capturedOnConfirm;
        when(mockDialogService.show(content: anyNamed('content'), onConfirm: anyNamed('onConfirm'))).thenAnswer((
          invocation,
        ) async {
          capturedOnConfirm = invocation.namedArguments[#onConfirm] as VoidCallback?;
        });

        // Act
        await controller.updatePassword();

        // Assert - 验证对话框内容
        verify(mockDialogService.show(content: '新しいパスワードを設定しました', onConfirm: anyNamed('onConfirm'))).called(1);

        // Assert - 验证回调存在
        expect(capturedOnConfirm, isNotNull);

        // Act - 执行确认回调
        capturedOnConfirm!();

        // Assert - 验证导航返回
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('生物识别成功对话框应包含正确的内容和回调', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        VoidCallback? capturedOnConfirm;
        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            onConfirm: anyNamed('onConfirm'),
          ),
        ).thenAnswer((invocation) async {
          capturedOnConfirm = invocation.namedArguments[#onConfirm] as VoidCallback?;
        });

        // Act
        await controller.updatePassword();

        // Assert - 验证对话框内容
        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // Assert - 验证回调存在
        expect(capturedOnConfirm, isNotNull);

        // Act - 执行确认回调
        capturedOnConfirm!();

        // Assert - 验证导航返回
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('错误对话框应包含正确的类型和内容', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'invalid';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误对话框
        verify(
          mockDialogService.show(
            type: DialogType.error,
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
          ),
        ).called(1);
      });

      test('后台错误对话框应包含具体错误信息', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testFailureResult);

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误对话框包含具体错误
        verify(mockDialogService.show(title: '内容に不備があります', type: DialogType.error, content: '旧密码错误')).called(1);
      });

      test('系统错误对话框应显示通用错误信息', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Network error'));

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误对话框
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);
      });
    });

    group('导航行为测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('取消操作（无输入）应直接导航返回', () async {
        // Arrange
        controller.newPasswordController.clear();
        controller.newPasswordConfirmController.clear();

        // Act
        await controller.onCancel();

        // Assert
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('取消操作（有输入）确认后应导航返回', () async {
        // Arrange
        controller.newPasswordController.text = 'some input';

        VoidCallback? capturedOnConfirm;
        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
          ),
        ).thenAnswer((invocation) async {
          capturedOnConfirm = invocation.namedArguments[#onConfirm] as VoidCallback?;
        });

        // Act
        await controller.onCancel();
        capturedOnConfirm!();

        // Assert
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('密码更新成功应导航返回', () async {
        // Arrange
        setupMocksForSuccess();
        controller.setTestParam(testNoBiometricsParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        VoidCallback? capturedOnConfirm;
        when(mockDialogService.show(content: anyNamed('content'), onConfirm: anyNamed('onConfirm'))).thenAnswer((
          invocation,
        ) async {
          capturedOnConfirm = invocation.namedArguments[#onConfirm] as VoidCallback?;
        });

        // Act
        await controller.updatePassword();
        capturedOnConfirm!();

        // Assert
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('生物识别密码更新成功应导航返回', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        VoidCallback? capturedOnConfirm;
        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            onConfirm: anyNamed('onConfirm'),
          ),
        ).thenAnswer((invocation) async {
          capturedOnConfirm = invocation.namedArguments[#onConfirm] as VoidCallback?;
        });

        // Act
        await controller.updatePassword();
        capturedOnConfirm!();

        // Assert
        verifyNavigationCalled(expectedCallCount: 1);
      });

      test('密码验证失败不应导航返回', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'invalid';

        // Act
        await controller.updatePassword();

        // Assert
        verifyNavigationCalled(expectedCallCount: 0);
      });

      test('密码更新失败不应导航返回', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testFailureResult);

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert
        verifyNavigationCalled(expectedCallCount: 0);
      });

      test('应能正确处理多次导航调用', () async {
        // Arrange
        controller.newPasswordController.clear();
        controller.newPasswordConfirmController.clear();

        // Act - 多次调用取消操作
        await controller.onCancel();
        await controller.onCancel();
        await controller.onCancel();

        // Assert - 验证导航被调用多次
        verifyNavigationCalled(expectedCallCount: 3);
      });
    });

    group('状态响应测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('操作过程中状态应正确响应', () async {
        // Arrange
        controller.newPasswordController.text = 'some input';
        expect(controller.state.isReturning.value, false);

        // Act
        final future = controller.onCancel();

        // Assert - 验证状态立即改变
        expect(controller.state.isReturning.value, true);

        await future;

        // Assert - 验证状态恢复
        expect(controller.state.isReturning.value, false);
      });

      test('密码更新状态应正确响应', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        expect(controller.state.isUpdating.value, false);

        // Act
        final future = controller.updatePassword();

        // Assert - 验证状态立即改变
        expect(controller.state.isUpdating.value, true);

        await future;

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('错误状态应正确设置', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: true, passwordConfirmTip: '密码不一致'));

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'different';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误状态设置
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.newPasswordTip.value, '密码不符合规则');
        expect(controller.state.hasConfirmPasswordError.value, true);
        expect(controller.state.passwordConfirmTip.value, '密码不一致');
      });

      test('成功状态不会自动清除错误状态（符合实际业务逻辑）', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // 预先设置错误状态
        controller.state.hasNewPasswordError.value = true;
        controller.state.newPasswordTip.value = 'Previous error';
        controller.state.hasConfirmPasswordError.value = true;
        controller.state.passwordConfirmTip.value = 'Previous error';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误状态保持不变（这是实际的业务行为）
        // 业务逻辑中，成功操作不会主动清除之前的错误状态
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.hasConfirmPasswordError.value, true);
        expect(controller.state.newPasswordTip.value, 'Previous error');
        expect(controller.state.passwordConfirmTip.value, 'Previous error');
      });
    });
  });

  /// ----------------------------
  /// Phase 3: 状态管理测试
  /// ----------------------------

  group('Phase 3: 状态管理测试', () {
    group('状态初始化测试', () {
      test('状态对象应有正确的初始值', () {
        // Arrange & Act
        final state = ResetPasswordUiState();

        // Assert - 验证所有状态的初始值
        expect(state.isUpdating.value, false);
        expect(state.isReturning.value, false);
        expect(state.hasNewPasswordError.value, false);
        expect(state.hasConfirmPasswordError.value, false);
        expect(state.newPasswordTip.value, '');
        expect(state.passwordConfirmTip.value, '');
        expect(state.passwordLength.value, 0);
        expect(state.userEmail.value, '');
        expect(state.tenantName.value, '');
        expect(state.isBiometrics.value, false);
        expect(state.mustContainArray.value, <String>[]);
      });

      test('控制器初始化后状态应正确设置', () {
        // Arrange & Act
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Assert - 验证初始化后的状态
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.isBiometrics.value, true);
        expect(controller.state.mustContainArray.value, ['大文字', '小文字', '数字']);

        // Assert - 验证错误状态为初始值
        expect(controller.state.hasNewPasswordError.value, false);
        expect(controller.state.hasConfirmPasswordError.value, false);
        expect(controller.state.newPasswordTip.value, '');
        expect(controller.state.passwordConfirmTip.value, '');

        // Assert - 验证操作状态为初始值
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.isReturning.value, false);
      });

      test('空参数初始化后状态应有默认值', () {
        // Arrange & Act
        setupMocksForInitPasswordRules();
        controller.setTestParam(testEmptyParams);
        controller.callInitStateFromParam();

        // Assert - 验证默认值
        expect(controller.state.passwordLength.value, 0);
        expect(controller.state.userEmail.value, '');
        expect(controller.state.tenantName.value, '');
        expect(controller.state.isBiometrics.value, false);
        expect(controller.state.mustContainArray.value, isNotEmpty);
      });
    });

    group('状态变更测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('isUpdating 状态应正确变更', () async {
        // Arrange
        setupMocksForSuccess();
        expect(controller.state.isUpdating.value, false);

        // Act & Assert - 验证状态变更序列
        final future = controller.updatePassword();
        expect(controller.state.isUpdating.value, true);

        await future;
        expect(controller.state.isUpdating.value, false);
      });

      test('isReturning 状态应正确变更', () async {
        // Arrange
        controller.newPasswordController.text = 'some input';
        expect(controller.state.isReturning.value, false);

        // Act & Assert - 验证状态变更序列
        final future = controller.onCancel();
        expect(controller.state.isReturning.value, true);

        await future;
        expect(controller.state.isReturning.value, false);
      });

      test('错误状态应正确设置和保持', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '新密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: true, passwordConfirmTip: '密码不一致'));

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'different';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误状态设置
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.newPasswordTip.value, '新密码不符合规则');
        expect(controller.state.hasConfirmPasswordError.value, true);
        expect(controller.state.passwordConfirmTip.value, '密码不一致');

        // Assert - 验证状态持久性
        await Future.delayed(Duration(milliseconds: 10));
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.hasConfirmPasswordError.value, true);
      });

      test('状态变更应保持其他状态不变', () async {
        // Arrange
        final initialPasswordLength = controller.state.passwordLength.value;
        final initialUserEmail = controller.state.userEmail.value;
        final initialTenantName = controller.state.tenantName.value;
        final initialIsBiometrics = controller.state.isBiometrics.value;
        final initialMustContainArray = List<String>.from(controller.state.mustContainArray.value);

        // Act - 执行状态变更操作
        controller.newPasswordController.text = 'some input';
        final future = controller.onCancel();
        expect(controller.state.isReturning.value, true);
        await future;

        // Assert - 验证其他状态保持不变
        expect(controller.state.passwordLength.value, initialPasswordLength);
        expect(controller.state.userEmail.value, initialUserEmail);
        expect(controller.state.tenantName.value, initialTenantName);
        expect(controller.state.isBiometrics.value, initialIsBiometrics);
        expect(controller.state.mustContainArray.value, initialMustContainArray);
      });
    });

    group('状态生命周期测试', () {
      test('控制器创建到销毁的状态生命周期', () {
        // Arrange - 创建新的控制器实例
        final testController = TestLoginResetPasswordController(
          biometricsSwitchUtil: mockBiometricsSwitchUtil,
          resetPasswordUsecase: mockResetPasswordUsecase,
          dialogService: mockDialogService,
          navigationService: mockNavigationService,
        );

        // Act & Assert - 验证初始状态
        expect(testController.state.isUpdating.value, false);
        expect(testController.state.isReturning.value, false);

        // Act - 初始化状态
        setupMocksForInitPasswordRules();
        testController.setTestParam(testValidParams);
        testController.callInitStateFromParam();

        // Assert - 验证初始化后状态
        expect(testController.state.passwordLength.value, 8);
        expect(testController.state.userEmail.value, '<EMAIL>');

        // Act - 销毁控制器
        testController.onClose();

        // Assert - 验证资源释放后状态仍然保持
        expect(testController.state.passwordLength.value, 8);
        expect(testController.state.userEmail.value, '<EMAIL>');
      });

      test('状态在多次初始化中的行为', () {
        // Arrange
        setupMocksForInitPasswordRules();

        // Act & Assert - 第一次初始化
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.userEmail.value, '<EMAIL>');

        // Act & Assert - 第二次初始化（不同参数）
        controller.setTestParam(testNoBiometricsParams);
        controller.callInitStateFromParam();
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.isBiometrics.value, false);

        // Act & Assert - 第三次初始化（空参数）
        controller.setTestParam(testEmptyParams);
        controller.callInitStateFromParam();
        expect(controller.state.passwordLength.value, 0);
        expect(controller.state.userEmail.value, '');
        expect(controller.state.isBiometrics.value, false);
      });
    });

    group('状态响应式测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('状态变化应能被观察到', () async {
        // Arrange
        bool isUpdatingChanged = false;
        bool isReturningChanged = false;

        // 监听状态变化
        controller.state.isUpdating.listen((value) {
          if (value) isUpdatingChanged = true;
        });

        controller.state.isReturning.listen((value) {
          if (value) isReturningChanged = true;
        });

        // Act
        controller.newPasswordController.text = 'some input';
        await controller.onCancel();

        // Assert
        expect(isReturningChanged, true);
        expect(isUpdatingChanged, false);
      });

      test('多个状态变化应能并行观察', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        bool isUpdatingBecameTrue = false;
        bool isUpdatingBecameFalse = false;

        // 监听状态变化
        controller.state.isUpdating.listen((value) {
          if (value) {
            isUpdatingBecameTrue = true;
          } else if (isUpdatingBecameTrue) {
            isUpdatingBecameFalse = true;
          }
        });

        // Act
        await controller.updatePassword();

        // Assert
        expect(isUpdatingBecameTrue, true);
        expect(isUpdatingBecameFalse, true);
      });

      test('状态变化应保持数据一致性', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        List<bool> isUpdatingValues = [];

        // 监听状态变化
        controller.state.isUpdating.listen((value) {
          isUpdatingValues.add(value);
        });

        // Act
        await controller.updatePassword();

        // Assert - 验证状态变化序列
        expect(isUpdatingValues, [true, false]);
      });
    });

    group('状态一致性测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('同时执行多个操作时状态应保持一致', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act - 同时触发多个操作
        final updateFuture = controller.updatePassword();

        // Assert - 验证状态一致性
        expect(controller.state.isUpdating.value, true);
        expect(controller.state.isReturning.value, false);

        await updateFuture;

        // Assert - 验证最终状态
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.isReturning.value, false);
      });

      test('错误状态和操作状态应保持独立', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: false, passwordConfirmTip: ''));

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'invalid';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误状态和操作状态的独立性
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.hasConfirmPasswordError.value, false);
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.isReturning.value, false);
      });

      test('状态更新应保持原子性', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        List<Map<String, dynamic>> stateSnapshots = [];

        // 监听状态变化，记录快照
        controller.state.isUpdating.listen((value) {
          stateSnapshots.add({
            'isUpdating': controller.state.isUpdating.value,
            'isReturning': controller.state.isReturning.value,
            'hasNewPasswordError': controller.state.hasNewPasswordError.value,
            'hasConfirmPasswordError': controller.state.hasConfirmPasswordError.value,
          });
        });

        // Act
        await controller.updatePassword();

        // Assert - 验证状态更新的原子性
        expect(stateSnapshots.length, 2);

        // 第一个快照：isUpdating = true, 其他状态不变
        expect(stateSnapshots[0]['isUpdating'], true);
        expect(stateSnapshots[0]['isReturning'], false);
        expect(stateSnapshots[0]['hasNewPasswordError'], false);
        expect(stateSnapshots[0]['hasConfirmPasswordError'], false);

        // 第二个快照：isUpdating = false, 其他状态不变
        expect(stateSnapshots[1]['isUpdating'], false);
        expect(stateSnapshots[1]['isReturning'], false);
        expect(stateSnapshots[1]['hasNewPasswordError'], false);
        expect(stateSnapshots[1]['hasConfirmPasswordError'], false);
      });
    });

    group('状态持久性测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('状态应在操作完成后保持正确值', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证状态持久性
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.isBiometrics.value, true);

        // Act - 等待一段时间
        await Future.delayed(Duration(milliseconds: 100));

        // Assert - 验证状态仍然保持
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.isBiometrics.value, true);
      });

      test('错误状态应持续到被显式清除', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: true, newPasswordTip: '密码不符合规则'));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenReturn(ConfirmPasswordVerificationResult(hasConfirmPasswordError: true, passwordConfirmTip: '密码不一致'));

        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'different';

        // Act
        await controller.updatePassword();

        // Assert - 验证错误状态持续
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.hasConfirmPasswordError.value, true);

        // Act - 等待一段时间
        await Future.delayed(Duration(milliseconds: 100));

        // Assert - 验证错误状态仍然保持
        expect(controller.state.hasNewPasswordError.value, true);
        expect(controller.state.hasConfirmPasswordError.value, true);
      });

      test('状态应在控制器销毁后仍可访问', () {
        // Arrange
        final testController = TestLoginResetPasswordController(
          biometricsSwitchUtil: mockBiometricsSwitchUtil,
          resetPasswordUsecase: mockResetPasswordUsecase,
          dialogService: mockDialogService,
          navigationService: mockNavigationService,
        );

        setupMocksForInitPasswordRules();
        testController.setTestParam(testValidParams);
        testController.callInitStateFromParam();

        // Act - 销毁控制器
        testController.onClose();

        // Assert - 验证状态仍可访问
        expect(testController.state.passwordLength.value, 8);
        expect(testController.state.userEmail.value, '<EMAIL>');
        expect(testController.state.tenantName.value, 'Test Tenant');
        expect(testController.state.isBiometrics.value, true);
      });
    });

    group('状态边界测试', () {
      test('状态应正确处理极端值', () {
        // Arrange
        final extremeParams = ResetPasswordArgumentsModel(
          userName: 'x' * 1000, // 极长的用户名
          tenantName: '', // 空租户名
          tenantId: 'test-tenant-id',
          zoneId: 'test-zone-id',
          oldPassword: 'old-password',
          isBiometrics: true,
          policy: PasswordPolicyModel(
            length: 999999, // 极大的密码长度
            mustContain: 'ABC' * 100, // 极长的必需字符
          ),
        );

        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(extremeParams);
        controller.callInitStateFromParam();

        // Assert - 验证状态能处理极端值
        expect(controller.state.passwordLength.value, 999999);
        expect(controller.state.userEmail.value, 'x' * 1000);
        expect(controller.state.tenantName.value, '');
        expect(controller.state.isBiometrics.value, true);
        expect(controller.state.mustContainArray.value, isNotEmpty);
      });

      test('状态应处理null和空值', () {
        // Arrange
        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(testEmptyParams);
        controller.callInitStateFromParam();

        // Assert - 验证默认值处理
        expect(controller.state.passwordLength.value, 0);
        expect(controller.state.userEmail.value, '');
        expect(controller.state.tenantName.value, '');
        expect(controller.state.isBiometrics.value, false);
        expect(controller.state.mustContainArray.value, isNotEmpty);
      });
    });
  });

  /// ----------------------------
  /// Phase 4: 异常处理测试
  /// ----------------------------

  group('Phase 4: 异常处理测试', () {
    group('网络异常处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';
      });

      test('网络超时异常应显示系统错误', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Connection timeout'));

        // Act
        await controller.updatePassword();

        // Assert - 验证显示系统错误对话框
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('网络连接异常应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Network unreachable'));

        // Act
        await controller.updatePassword();

        // Assert - 验证异常被捕获并处理
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证不会导航返回
        verifyNavigationCalled(expectedCallCount: 0);
      });

      test('HTTP错误应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('HTTP 500 Internal Server Error'));

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误处理
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证状态正确恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('JSON解析异常应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(FormatException('Invalid JSON format'));

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误处理
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);
      });
    });

    group('系统异常处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';
      });

      test('SystemException应显示系统错误', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(SystemException());

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误对话框
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);
      });

      test('内存不足异常应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(OutOfMemoryError());

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误处理
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);
      });

      test('空指针异常应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        when(
          mockResetPasswordUsecase.call(any),
        ).thenThrow(NoSuchMethodError.withInvocation(null, Invocation.method(#call, [])));

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误处理
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);
      });

      test('类型转换异常应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(TypeError());

        // Act
        await controller.updatePassword();

        // Assert - 验证系统错误处理
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);
      });
    });

    group('业务异常处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('密码验证服务异常应正确处理', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenThrow(Exception('Password validation service error'));

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act & Assert - 验证异常被正确传播
        expect(() async => await controller.updatePassword(), throwsA(isA<Exception>()));

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('密码一致性验证异常应正确处理', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: anyNamed('password')),
        ).thenReturn(PasswordVerificationResult(hasNewPasswordError: false, newPasswordTip: ''));

        when(
          mockResetPasswordUsecase.passwordConsistenceCheck(
            newPassword: anyNamed('newPassword'),
            confirmPassword: anyNamed('confirmPassword'),
          ),
        ).thenThrow(Exception('Password consistency check error'));

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act & Assert - 验证异常被正确传播
        expect(() async => await controller.updatePassword(), throwsA(isA<Exception>()));

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('生物识别服务异常应显示系统错误', () async {
        // Arrange
        setupMocksForSuccess();
        when(
          mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')),
        ).thenThrow(Exception('Biometrics service error'));

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证显示系统错误对话框
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('初始化密码规则异常应使用默认规则', () async {
        // Arrange
        when(
          mockResetPasswordUsecase.initPasswordRules(
            mustContain: anyNamed('mustContain'),
            passwordLength: anyNamed('passwordLength'),
          ),
        ).thenThrow(Exception('Password rules initialization error'));

        // Act & Assert - 验证异常不会阻止控制器创建
        expect(() => controller.callInitStateFromParam(), throwsA(isA<Exception>()));
      });
    });

    group('参数异常处理测试', () {
      test('空参数应正确处理', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testEmptyParams);

        // Act
        controller.callInitStateFromParam();

        // Assert - 验证使用默认值
        expect(controller.state.passwordLength.value, 0);
        expect(controller.state.userEmail.value, '');
        expect(controller.state.tenantName.value, '');
        expect(controller.state.isBiometrics.value, false);
      });

      test('updatePassword时空参数实际能正常工作', () async {
        // Arrange
        setupMocksForSuccess();
        // 为空参数设置专门的 Mock 配置
        when(
          mockResetPasswordUsecase.initPasswordRules(
            mustContain: anyNamed('mustContain'),
            passwordLength: anyNamed('passwordLength'),
          ),
        ).thenReturn(
          PasswordVerificationRulesModel(regexText: '', mustContainArray: [], signFlg: false, passwordLength: 0),
        );

        controller.setTestParam(testEmptyParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证成功更新（空参数实际上能正常工作）
        verify(
          mockDialogService.show(title: null, content: '新しいパスワードを設定しました', onConfirm: anyNamed('onConfirm')),
        ).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });

      test('无效的密码策略应正确处理', () async {
        // Arrange
        final invalidParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'Test Tenant',
          tenantId: 'tenant123',
          zoneId: 'zone001',
          oldPassword: 'oldPassword123',
          isBiometrics: true,
          policy: PasswordPolicyModel(length: -1, mustContain: ''), // 无效长度
        );

        setupMocksForInitPasswordRules();
        controller.setTestParam(invalidParams);

        // Act
        controller.callInitStateFromParam();

        // Assert - 验证能处理无效参数
        expect(controller.state.passwordLength.value, -1);
        expect(controller.state.mustContainArray.value, isNotEmpty);
      });
    });

    group('依赖服务异常处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('对话框服务异常不应阻止业务流程', () async {
        // Arrange
        setupMocksForSuccess();
        when(
          mockDialogService.show(
            title: anyNamed('title'),
            content: anyNamed('content'),
            confirmText: anyNamed('confirmText'),
            cancelText: anyNamed('cancelText'),
            onConfirm: anyNamed('onConfirm'),
            onCancel: anyNamed('onCancel'),
            barrierDismissible: anyNamed('barrierDismissible'),
            type: anyNamed('type'),
          ),
        ).thenThrow(Exception('Dialog service error'));

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act & Assert - 验证异常被正确传播
        expect(() async => await controller.updatePassword(), throwsA(isA<Exception>()));

        // Assert - 验证状态仍然为 true（因为异常在 finally 块之前抛出）
        expect(controller.state.isUpdating.value, true);
      });

      test('导航服务异常应正确处理', () async {
        // Arrange
        when(mockNavigationService.goBack()).thenThrow(Exception('Navigation service error'));

        controller.newPasswordController.clear();
        controller.newPasswordConfirmController.clear();

        // Act & Assert - 验证异常被正确传播
        expect(() async => await controller.onCancel(), throwsA(isA<Exception>()));

        // Assert - 验证状态仍然为 true（因为异常在 finally 块之前抛出）
        expect(controller.state.isReturning.value, true);
      });

      test('生物识别服务未初始化应显示系统错误', () async {
        // Arrange
        setupMocksForSuccess();
        when(
          mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')),
        ).thenThrow(StateError('Service not initialized'));

        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act
        await controller.updatePassword();

        // Assert - 验证显示系统错误对话框
        verify(mockDialogService.show(type: DialogType.error, content: '内容に不備があります')).called(1);

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);
      });
    });

    group('资源异常处理测试', () {
      test('控制器销毁后的操作应正确处理', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Act - 销毁控制器
        controller.onClose();

        // Assert - 验证控制器已被销毁（TextEditingController 被释放）
        expect(controller.newPasswordController.text, '');
        expect(controller.newPasswordConfirmController.text, '');

        // Act & Assert - 尝试在销毁后执行操作应抛出异常
        expect(() => controller.newPasswordController.text = 'SomePassword', throwsA(isA<AssertionError>()));

        expect(() => controller.newPasswordConfirmController.text = 'SomePassword', throwsA(isA<AssertionError>()));
      });

      test('内存泄漏预防 - 多次创建销毁控制器', () {
        // Arrange & Act - 创建和销毁多个控制器实例
        for (int i = 0; i < 10; i++) {
          final testController = TestLoginResetPasswordController(
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            resetPasswordUsecase: mockResetPasswordUsecase,
            dialogService: mockDialogService,
            navigationService: mockNavigationService,
          );

          setupMocksForInitPasswordRules();
          testController.setTestParam(testValidParams);
          testController.callInitStateFromParam();

          // Assert - 验证正常初始化
          expect(testController.state.passwordLength.value, 8);

          // Act - 销毁控制器
          testController.onClose();
        }

        // Assert - 测试完成表示无内存泄漏或严重异常
        expect(true, true);
      });
    });

    group('并发异常处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';
      });

      test('同时执行多个updatePassword应正确处理', () async {
        // Arrange
        setupMocksForSuccess();

        // Act - 同时触发多个更新密码操作
        final future1 = controller.updatePassword();
        final future2 = controller.updatePassword();
        final future3 = controller.updatePassword();

        // Assert - 验证所有操作都能完成（即使可能有竞态条件）
        await Future.wait([future1, future2, future3]);

        // Assert - 验证最终状态正确
        expect(controller.state.isUpdating.value, false);
      });

      test('updatePassword和onCancel同时执行应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'some input';

        // Act - 同时触发更新密码和取消操作
        final updateFuture = controller.updatePassword();
        final cancelFuture = controller.onCancel();

        // Assert - 验证所有操作都能完成
        await Future.wait([updateFuture, cancelFuture]);

        // Assert - 验证最终状态一致
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.isReturning.value, false);
      });

      test('多线程状态访问应保持一致性', () async {
        // Arrange
        setupMocksForSuccess();
        final states = <bool>[];

        // Act - 在多个异步操作中访问状态
        final futures = List.generate(5, (index) async {
          await controller.updatePassword();
          states.add(controller.state.isUpdating.value);
        });

        await Future.wait(futures);

        // Assert - 验证所有状态都是 false（操作完成后）
        expect(states.every((state) => state == false), true);
      });
    });

    group('异常恢复测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';
      });

      test('异常后状态应正确恢复', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Network error'));

        // Act - 执行失败的操作
        await controller.updatePassword();

        // Assert - 验证状态已恢复
        expect(controller.state.isUpdating.value, false);

        // Arrange - 配置成功的模拟
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testSuccessResult);

        // Act - 重新执行操作
        await controller.updatePassword();

        // Assert - 验证能够成功执行
        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });

      test('多次异常后仍能正常工作', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Error'));

        // Act - 执行多次失败的操作
        for (int i = 0; i < 3; i++) {
          await controller.updatePassword();
          expect(controller.state.isUpdating.value, false);
        }

        // Arrange - 配置成功的模拟
        when(mockResetPasswordUsecase.call(any)).thenAnswer((_) async => testSuccessResult);

        // Act - 最终成功的操作
        await controller.updatePassword();

        // Assert - 验证最终成功
        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });

      test('异常恢复后用户交互应正常', () async {
        // Arrange
        setupMocksForSuccess();
        when(mockResetPasswordUsecase.call(any)).thenThrow(Exception('Error'));

        // Act - 执行失败的更新密码操作
        await controller.updatePassword();

        // Assert - 验证状态恢复
        expect(controller.state.isUpdating.value, false);

        // Act - 执行取消操作
        controller.newPasswordController.text = 'some input';
        await controller.onCancel();

        // Assert - 验证取消操作正常工作
        expect(controller.state.isReturning.value, false);
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });
    });
  });

  /// ----------------------------
  /// Phase 5: 边界情况测试
  /// ----------------------------

  group('Phase 5: 边界情况测试', () {
    group('输入长度边界测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('密码输入长度为0应正确处理', () async {
        // Arrange
        setupMocksForPasswordValidationFailure();
        controller.newPasswordController.text = '';
        controller.newPasswordConfirmController.text = '';

        // Act
        await controller.updatePassword();

        // Assert - 验证显示密码验证错误
        verify(
          mockDialogService.show(
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
            type: DialogType.error,
          ),
        ).called(1);

        // Assert - 验证不调用后端
        verifyNever(mockResetPasswordUsecase.call(any));
      });

      test('密码输入长度为1应正确处理', () async {
        // Arrange
        setupMocksForPasswordValidationFailure();
        controller.newPasswordController.text = 'a';
        controller.newPasswordConfirmController.text = 'a';

        // Act
        await controller.updatePassword();

        // Assert - 验证显示密码验证错误
        verify(
          mockDialogService.show(
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
            type: DialogType.error,
          ),
        ).called(1);
      });

      test('极长密码输入应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        final longPassword = 'A' * 1000; // 1000字符的密码
        controller.newPasswordController.text = longPassword;
        controller.newPasswordConfirmController.text = longPassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证能正常处理长密码
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: longPassword),
        ).called(1);
      });

      test('密码长度刚好达到要求应成功', () async {
        // Arrange
        setupMocksForSuccess();
        // 假设最小长度要求是8位
        final minLengthPassword = 'Abcd123!';
        controller.newPasswordController.text = minLengthPassword;
        controller.newPasswordConfirmController.text = minLengthPassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证成功更新
        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });

      test('用户邮箱为空字符串应使用默认值', () {
        // Arrange
        final emptyEmailParams = ResetPasswordArgumentsModel(
          userName: '',
          tenantName: 'TestTenant',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: 8, mustContain: 'uppercase'),
        );

        // Act
        controller.setTestParam(emptyEmailParams);
        controller.callInitStateFromParam();

        // Assert - 验证使用空字符串
        expect(controller.state.userEmail.value, '');
      });
    });

    group('特殊字符处理测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('密码包含emoji字符应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        final emojiPassword = 'Pass123!😀🎉🔐';
        controller.newPasswordController.text = emojiPassword;
        controller.newPasswordConfirmController.text = emojiPassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证能处理emoji
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: emojiPassword),
        ).called(1);
      });

      test('密码包含特殊Unicode字符应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        final unicodePassword = 'Pαss123!中文日本語한국어';
        controller.newPasswordController.text = unicodePassword;
        controller.newPasswordConfirmController.text = unicodePassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证能处理Unicode
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: unicodePassword),
        ).called(1);
      });

      test('密码包含HTML/XML标签应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        final htmlPassword = 'Pass<script>123!</script>';
        controller.newPasswordController.text = htmlPassword;
        controller.newPasswordConfirmController.text = htmlPassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证能处理HTML标签
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: htmlPassword),
        ).called(1);
      });

      test('密码包含SQL注入字符应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        final sqlPassword = "Pass123'; DROP TABLE users; --";
        controller.newPasswordController.text = sqlPassword;
        controller.newPasswordConfirmController.text = sqlPassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证能处理SQL注入字符
        verify(mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: sqlPassword)).called(1);
      });

      test('密码包含换行符应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        final newlinePassword = 'Pass123!\n\r\t';
        controller.newPasswordController.text = newlinePassword;
        controller.newPasswordConfirmController.text = newlinePassword;

        // Act
        await controller.updatePassword();

        // Assert - 验证能处理换行符
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: newlinePassword),
        ).called(1);
      });
    });

    group('数据类型边界测试', () {
      test('passwordLength为0应正确处理', () {
        // Arrange
        final zeroLengthParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'TestTenant',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: 0, mustContain: 'uppercase'),
        );

        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(zeroLengthParams);
        controller.callInitStateFromParam();

        // Assert - 验证使用0长度
        expect(controller.state.passwordLength.value, 0);
      });

      test('passwordLength为负数应正确处理', () {
        // Arrange
        final negativeLengthParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'TestTenant',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: -5, mustContain: 'uppercase'),
        );

        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(negativeLengthParams);
        controller.callInitStateFromParam();

        // Assert - 验证使用负数长度
        expect(controller.state.passwordLength.value, -5);
      });

      test('passwordLength为最大整数应正确处理', () {
        // Arrange
        final maxIntParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'TestTenant',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: 2147483647, mustContain: 'uppercase'),
        );

        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(maxIntParams);
        controller.callInitStateFromParam();

        // Assert - 验证使用最大整数
        expect(controller.state.passwordLength.value, 2147483647);
      });

      test('mustContain为空数组应正确处理', () {
        // Arrange
        final emptyMustContainParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'TestTenant',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: 8, mustContain: ''), // 空字符串
        );

        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(emptyMustContainParams);
        controller.callInitStateFromParam();

        // Assert - 验证空数组处理
        expect(controller.state, isNotNull);
      });

      test('tenantName包含特殊字符应正确处理', () {
        // Arrange
        final specialTenantParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'Test!@#\$%^&*()Tenant租户',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: 8, mustContain: 'uppercase'),
        );

        setupMocksForInitPasswordRules();

        // Act
        controller.setTestParam(specialTenantParams);
        controller.callInitStateFromParam();

        // Assert - 验证特殊字符处理
        expect(controller.state.tenantName.value, 'Test!@#\$%^&*()Tenant租户');
      });
    });

    group('快速操作边界测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('快速连续调用updatePassword应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act - 快速连续调用
        final future1 = controller.updatePassword();
        final future2 = controller.updatePassword();
        final future3 = controller.updatePassword();

        await Future.wait([future1, future2, future3]);

        // Assert - 验证状态管理正确
        expect(controller.state.isUpdating.value, false);
      });

      test('快速连续调用onCancel应正确处理', () async {
        // Arrange
        controller.newPasswordController.text = 'sometext';
        controller.newPasswordConfirmController.text = 'sometext';

        // Act - 快速连续调用
        final future1 = controller.onCancel();
        final future2 = controller.onCancel();
        final future3 = controller.onCancel();

        await Future.wait([future1, future2, future3]);

        // Assert - 验证状态管理正确
        expect(controller.state.isReturning.value, false);
      });

      test('updatePassword和onCancel同时调用应正确处理', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act - 同时调用不同操作
        final updateFuture = controller.updatePassword();
        final cancelFuture = controller.onCancel();

        await Future.wait([updateFuture, cancelFuture]);

        // Assert - 验证两个操作都完成
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.isReturning.value, false);
      });

      test('快速输入变更应正确处理', () {
        // Arrange & Act - 快速变更输入
        for (int i = 0; i < 100; i++) {
          controller.newPasswordController.text = 'password$i';
          controller.newPasswordConfirmController.text = 'password$i';
        }

        // Assert - 验证最终状态正确
        expect(controller.newPasswordController.text, 'password99');
        expect(controller.newPasswordConfirmController.text, 'password99');
      });
    });

    group('状态转换边界测试', () {
      setUp(() {
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
      });

      test('isUpdating状态快速切换应保持一致性', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        List<bool> stateChanges = [];

        // Act - 监听状态变化
        controller.state.isUpdating.listen((value) {
          stateChanges.add(value);
        });

        await controller.updatePassword();

        // Assert - 验证状态转换序列
        expect(stateChanges, contains(true));
        expect(stateChanges.last, false);
      });

      test('isReturning状态快速切换应保持一致性', () async {
        // Arrange
        controller.newPasswordController.text = 'sometext';
        List<bool> stateChanges = [];

        // Act - 监听状态变化
        controller.state.isReturning.listen((value) {
          stateChanges.add(value);
        });

        await controller.onCancel();

        // Assert - 验证状态转换序列
        expect(stateChanges, contains(true));
        expect(stateChanges.last, false);
      });

      test('多个状态同时变化应保持独立性', () async {
        // Arrange
        setupMocksForSuccess();
        controller.newPasswordController.text = 'ValidPass123';
        controller.newPasswordConfirmController.text = 'ValidPass123';

        // Act - 同时触发多个状态变化
        final updateFuture = controller.updatePassword();

        // 在更新过程中设置错误状态
        controller.state.newPasswordTip.value = 'Test error';

        await updateFuture;

        // Assert - 验证状态独立性
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.newPasswordTip.value, 'Test error'); // 错误状态不应被自动清除
      });
    });

    group('初始化边界测试', () {
      test('重复初始化应保持最后一次的值', () {
        // Arrange
        setupMocksForInitPasswordRules();
        final params1 = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'FirstTenant',
          tenantId: 'tenant123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false,
          policy: PasswordPolicyModel(length: 8, mustContain: 'uppercase'),
        );
        final params2 = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'SecondTenant',
          tenantId: 'tenant456',
          oldPassword: 'oldpass',
          zoneId: 'zone2',
          isBiometrics: true,
          policy: PasswordPolicyModel(length: 12, mustContain: 'lowercase'),
        );

        // Act - 多次初始化
        controller.setTestParam(params1);
        controller.callInitStateFromParam();
        final firstEmail = controller.state.userEmail.value;

        controller.setTestParam(params2);
        controller.callInitStateFromParam();
        final secondEmail = controller.state.userEmail.value;

        // Assert - 验证保持最后一次的值
        expect(firstEmail, '<EMAIL>');
        expect(secondEmail, '<EMAIL>');
        expect(controller.state.passwordLength.value, 12);
        expect(controller.state.tenantName.value, 'SecondTenant');
        expect(controller.state.isBiometrics.value, true);
      });

      test('初始化过程中修改输入应不影响初始化', () {
        // Arrange
        setupMocksForInitPasswordRules();

        // Act - 在初始化过程中修改输入
        controller.newPasswordController.text = 'before';
        controller.setTestParam(testValidParams);
        controller.newPasswordController.text = 'during';
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'after';

        // Assert - 验证输入保持最后设置的值
        expect(controller.newPasswordController.text, 'after');
        expect(controller.state.userEmail.value, '<EMAIL>');
      });
    });

    group('内存和性能边界测试', () {
      test('大量状态监听器应正确管理', () {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        List<StreamSubscription> subscriptions = [];

        // Act - 创建大量监听器
        for (int i = 0; i < 100; i++) {
          subscriptions.add(controller.state.isUpdating.listen((_) {}));
          subscriptions.add(controller.state.isReturning.listen((_) {}));
          subscriptions.add(controller.state.newPasswordTip.listen((_) {}));
        }

        // Assert - 验证能正常工作
        expect(subscriptions.length, 300);

        // Cleanup
        for (var subscription in subscriptions) {
          subscription.cancel();
        }
      });

      test('长时间运行应保持状态一致性', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Act - 模拟长时间使用
        for (int i = 0; i < 50; i++) {
          controller.newPasswordController.text = 'password$i';
          controller.newPasswordConfirmController.text = 'password$i';

          // 模拟用户快速输入
          await Future.delayed(Duration(milliseconds: 1));
        }

        // Assert - 验证状态一致性
        expect(controller.newPasswordController.text, 'password49');
        expect(controller.newPasswordConfirmController.text, 'password49');
        expect(controller.state.isUpdating.value, false);
        expect(controller.state.isReturning.value, false);
      });
    });
  });

  /// ----------------------------
  /// Phase 6: 集成测试
  /// ----------------------------

  group('Phase 6: 集成测试', () {
    group('完整密码重置流程测试', () {
      test('从初始化到成功完成的完整流程', () async {
        // Arrange - 模拟完整的用户场景
        setupMocksForInitPasswordRules();
        setupMocksForSuccess();

        // Act 1: 初始化
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Assert 1: 初始化状态正确
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.passwordLength.value, 8);
        expect(controller.state.isBiometrics.value, true);

        // Act 2: 用户输入密码
        controller.newPasswordController.text = 'NewPass123!';
        controller.newPasswordConfirmController.text = 'NewPass123!';

        // Act 3: 执行密码更新
        await controller.updatePassword();

        // Assert 2: 验证完整的调用链
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: 'NewPass123!'),
        ).called(1);

        verify(
          mockResetPasswordUsecase.passwordConsistenceCheck(newPassword: 'NewPass123!', confirmPassword: 'NewPass123!'),
        ).called(1);

        verify(mockResetPasswordUsecase.call(any)).called(1);

        verify(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: false)).called(1);

        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // Assert 3: 最终状态正确
        expect(controller.state.isUpdating.value, false);
      });

      test('从初始化到验证失败的完整流程', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        setupMocksForPasswordValidationFailure();

        // Act 1: 初始化
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Act 2: 用户输入不合规密码
        controller.newPasswordController.text = '123'; // 不合规密码
        controller.newPasswordConfirmController.text = '456'; // 不一致密码

        // Act 3: 尝试更新密码
        await controller.updatePassword();

        // Assert: 验证调用链在验证阶段停止
        verify(mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: '123')).called(1);

        // 密码验证失败，不应该调用后续步骤
        verifyNever(mockResetPasswordUsecase.call(any));
        verifyNever(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')));

        // 应该显示错误对话框
        verify(
          mockDialogService.show(
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
            type: DialogType.error,
          ),
        ).called(1);
      });

      test('取消操作的完整流程', () async {
        // Arrange
        setupMocksForInitPasswordRules();

        // Act 1: 初始化
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Act 2: 用户输入一些内容
        controller.newPasswordController.text = 'SomePassword';
        controller.newPasswordConfirmController.text = 'SomePassword';

        // Act 3: 用户点击取消
        await controller.onCancel();

        // Assert: 验证取消流程 - 有输入内容时显示确认对话框，还未实际导航
        expect(controller.state.isReturning.value, false); // 还未确认，所以还是 false

        // 验证确认对话框被显示
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });
    });

    group('跨组件状态同步测试', () {
      test('密码输入与状态同步', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Act: 模拟用户逐步输入
        controller.newPasswordController.text = 'N';
        expect(controller.newPasswordController.text, 'N');

        controller.newPasswordController.text = 'Ne';
        expect(controller.newPasswordController.text, 'Ne');

        controller.newPasswordController.text = 'NewPass123!';
        expect(controller.newPasswordController.text, 'NewPass123!');

        controller.newPasswordConfirmController.text = 'NewPass123!';
        expect(controller.newPasswordConfirmController.text, 'NewPass123!');

        // Assert: 状态保持一致
        expect(controller.newPasswordController.text, 'NewPass123!');
        expect(controller.newPasswordConfirmController.text, 'NewPass123!');
      });

      test('操作状态与UI状态同步', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        setupMocksForSuccess();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'NewPass123!';
        controller.newPasswordConfirmController.text = 'NewPass123!';

        // Act & Assert: 追踪状态变化
        expect(controller.state.isUpdating.value, false);

        final updateFuture = controller.updatePassword();
        expect(controller.state.isUpdating.value, true);

        await updateFuture;
        expect(controller.state.isUpdating.value, false);
      });

      test('错误状态与UI反馈同步', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        setupMocksForPasswordValidationFailure();
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();
        controller.newPasswordController.text = 'invalid';
        controller.newPasswordConfirmController.text = 'invalid';

        // Act
        await controller.updatePassword();

        // Assert: 错误状态正确设置
        expect(controller.state.newPasswordTip.value, '密码不符合要求');

        // 验证UI反馈（对话框）
        verify(
          mockDialogService.show(
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
            type: DialogType.error,
          ),
        ).called(1);
      });
    });

    group('真实场景模拟测试', () {
      test('无生物识别用户的完整使用场景', () async {
        // Arrange - 无生物识别的参数
        final noBiometricsParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'CompanyTenant',
          tenantId: 'company123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: false, // 无生物识别
          policy: PasswordPolicyModel(length: 10, mustContain: 'uppercase,lowercase,number'),
        );

        setupMocksForInitPasswordRules();
        setupMocksForSuccess();

        // Act 1: 初始化（无生物识别场景）
        controller.setTestParam(noBiometricsParams);
        controller.callInitStateFromParam();

        // Assert 1: 初始化状态
        expect(controller.state.isBiometrics.value, false);

        // Act 2: 输入符合策略的密码
        controller.newPasswordController.text = 'NewPassword123!';
        controller.newPasswordConfirmController.text = 'NewPassword123!';

        // Act 3: 更新密码
        await controller.updatePassword();

        // Assert 2: 验证无生物识别流程
        verify(mockResetPasswordUsecase.call(any)).called(1);
        verifyNever(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: anyNamed('isOpen')));

        verify(
          mockDialogService.show(title: null, content: '新しいパスワードを設定しました', onConfirm: anyNamed('onConfirm')),
        ).called(1);
      });

      test('有生物识别用户的完整使用场景', () async {
        // Arrange - 有生物识别的参数
        setupMocksForInitPasswordRules();
        setupMocksForSuccess();

        // Act 1: 初始化（有生物识别场景）
        controller.setTestParam(testValidParams); // 包含生物识别
        controller.callInitStateFromParam();

        // Assert 1: 初始化状态
        expect(controller.state.isBiometrics.value, true);

        // Act 2: 输入密码
        controller.newPasswordController.text = 'NewPassword123!';
        controller.newPasswordConfirmController.text = 'NewPassword123!';

        // Act 3: 更新密码
        await controller.updatePassword();

        // Assert 2: 验证生物识别流程
        verify(mockResetPasswordUsecase.call(any)).called(1);
        verify(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: false)).called(1);

        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });

      test('密码策略复杂度高的企业用户场景', () async {
        // Arrange - 高复杂度密码策略
        final enterpriseParams = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'Enterprise Corp',
          tenantId: 'enterprise123',
          oldPassword: 'oldpass',
          zoneId: 'zone1',
          isBiometrics: true,
          policy: PasswordPolicyModel(
            length: 16, // 高要求长度
            mustContain: 'uppercase,lowercase,number,special', // 复杂要求
          ),
        );

        setupMocksForInitPasswordRules();
        setupMocksForSuccess();

        // Act 1: 初始化
        controller.setTestParam(enterpriseParams);
        controller.callInitStateFromParam();

        // Assert 1: 高要求策略设置
        expect(controller.state.passwordLength.value, 16);

        // Act 2: 输入符合企业级要求的密码
        final enterprisePassword = 'EnterprisePass123!@#';
        controller.newPasswordController.text = enterprisePassword;
        controller.newPasswordConfirmController.text = enterprisePassword;

        // Act 3: 更新密码
        await controller.updatePassword();

        // Assert 2: 验证企业级流程
        verify(
          mockResetPasswordUsecase.passwordLegitimacyCheck(pvr: anyNamed('pvr'), password: enterprisePassword),
        ).called(1);

        verify(mockResetPasswordUsecase.call(any)).called(1);
      });
    });

    group('数据流完整性测试', () {
      test('参数传递的完整数据流', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        setupMocksForSuccess();

        final testParam = ResetPasswordArgumentsModel(
          userName: '<EMAIL>',
          tenantName: 'DataFlowTenant',
          tenantId: 'dataflow123',
          oldPassword: 'oldDataPass',
          zoneId: 'datazone1',
          isBiometrics: true,
          policy: PasswordPolicyModel(length: 12, mustContain: 'all'),
        );

        // Act 1: 数据流入
        controller.setTestParam(testParam);
        controller.callInitStateFromParam();

        // Assert 1: 数据正确映射到状态
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'DataFlowTenant');
        expect(controller.state.passwordLength.value, 12);
        expect(controller.state.isBiometrics.value, true);

        // Act 2: 数据流出（到后端）
        controller.newPasswordController.text = 'DataFlowPass123!';
        controller.newPasswordConfirmController.text = 'DataFlowPass123!';
        await controller.updatePassword();

        // Assert 2: 验证数据正确传递给后端
        final capturedRequest = verify(mockResetPasswordUsecase.call(captureAny)).captured.single;
        expect(capturedRequest.userName, '<EMAIL>');
        expect(capturedRequest.tenantId, 'dataflow123');
        expect(capturedRequest.oldPassword, 'oldDataPass');
        expect(capturedRequest.newPassword, 'DataFlowPass123!');
        expect(capturedRequest.zoneId, 'datazone1');
      });

      test('状态变化的数据流追踪', () async {
        // Arrange
        setupMocksForInitPasswordRules();
        final List<bool> isUpdatingHistory = [];
        final List<bool> isReturningHistory = [];

        // 监听状态变化
        controller.state.isUpdating.listen((value) {
          isUpdatingHistory.add(value);
        });

        controller.state.isReturning.listen((value) {
          isReturningHistory.add(value);
        });

        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Act 1: 更新操作
        setupMocksForSuccess();
        controller.newPasswordController.text = 'TrackPass123!';
        controller.newPasswordConfirmController.text = 'TrackPass123!';

        await controller.updatePassword();

        // Act 2: 取消操作
        controller.newPasswordController.text = 'SomeInput';
        await controller.onCancel();

        // Assert: 验证状态变化历史
        expect(isUpdatingHistory, contains(false)); // 初始状态
        expect(isUpdatingHistory, contains(true)); // 更新过程中
        expect(isUpdatingHistory.last, false); // 最终状态

        expect(isReturningHistory, contains(false)); // 初始状态
        expect(isReturningHistory, contains(true)); // 返回过程中
      });
    });

    group('端到端用户体验测试', () {
      test('快乐路径 - 用户成功重置密码', () async {
        // Arrange - 模拟真实用户操作序列
        setupMocksForInitPasswordRules();
        setupMocksForSuccess();

        // Step 1: 用户进入页面
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // 验证页面初始状态
        expect(controller.state.userEmail.value, '<EMAIL>');
        expect(controller.state.tenantName.value, 'Test Tenant');
        expect(controller.state.isUpdating.value, false);
        expect(controller.newPasswordController.text, '');
        expect(controller.newPasswordConfirmController.text, '');

        // Step 2: 用户输入新密码
        controller.newPasswordController.text = 'MyNewSecurePass123!';

        // Step 3: 用户输入确认密码
        controller.newPasswordConfirmController.text = 'MyNewSecurePass123!';

        // Step 4: 用户点击确认按钮
        final updateFuture = controller.updatePassword();

        // 验证加载状态
        expect(controller.state.isUpdating.value, true);

        // Step 5: 等待处理完成
        await updateFuture;

        // Step 6: 验证成功状态和用户反馈 - 集成测试关注整体状态
        expect(controller.state.isUpdating.value, false);

        // Step 7: 验证密码已成功更新（通过验证后端调用确认）
        verify(mockResetPasswordUsecase.call(any)).called(1);
        verify(mockBiometricsSwitchUtil.closeOrOpenBiometrics(isOpen: false)).called(1);
      });

      test('错误恢复路径 - 用户从错误中恢复', () async {
        // Arrange
        setupMocksForInitPasswordRules();

        // Step 1: 用户进入页面
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Step 2: 用户输入无效密码
        controller.newPasswordController.text = '123';
        controller.newPasswordConfirmController.text = '456';

        // Step 3: 用户点击确认，验证失败
        setupMocksForPasswordValidationFailure();
        await controller.updatePassword();

        // 验证错误状态
        verify(
          mockDialogService.show(
            title: '内容に不備があります',
            content: '表示されたメッセージをご確認の上、もう一度設定してください。',
            type: DialogType.error,
          ),
        ).called(1);

        // Step 4: 用户修正输入
        controller.newPasswordController.text = 'ValidPassword123!';
        controller.newPasswordConfirmController.text = 'ValidPassword123!';

        // Step 5: 用户重新点击确认，成功
        setupMocksForSuccess();
        await controller.updatePassword();

        // 验证恢复后的成功状态
        verify(
          mockDialogService.show(
            title: '新しいパスワードを設定しました',
            content: '引き続き生体認証をご利用される方は、マイページからもう一度設定してください。',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);
      });

      test('中断路径 - 用户中途取消操作', () async {
        // Arrange
        setupMocksForInitPasswordRules();

        // Step 1: 用户进入页面
        controller.setTestParam(testValidParams);
        controller.callInitStateFromParam();

        // Step 2: 用户开始输入
        controller.newPasswordController.text = 'StartedTyping';

        // Step 3: 用户中途决定取消
        await controller.onCancel();

        // Step 4: 验证取消确认流程启动 - 有输入内容时显示确认对话框
        expect(controller.state.isReturning.value, false); // 还未确认，所以还是 false

        // 验证确认对话框被显示
        verify(
          mockDialogService.show(
            title: 'キャンセルします',
            content: '入力中のデータは破棄されますが、よろしいですか？',
            cancelText: 'キャンセル',
            onConfirm: anyNamed('onConfirm'),
          ),
        ).called(1);

        // 验证没有调用密码更新
        verifyNever(mockResetPasswordUsecase.call(any));
      });
    });
  });
}
