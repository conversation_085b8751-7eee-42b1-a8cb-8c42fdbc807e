import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../../../mocks/storage_utils_mock.dart';
import 'deeplink_usecase_test.mocks.dart';

@GenerateMocks([TenantRepository, UserRepository, LoginSsoUseCase])
void main() {
  group('DeepLinkUseCase', () {
    late DeepLinkUseCase useCase;
    late MockTenantRepository mockTenantRepository;
    late MockUserRepository mockUserRepository;
    late MockLoginSsoUseCase mockLoginSsoUseCase;
    late MockStorageUtils mockStorageUtils;

    setUp(() {
      LogUtil.initialize();
      mockTenantRepository = MockTenantRepository();
      mockUserRepository = MockUserRepository();
      mockLoginSsoUseCase = MockLoginSsoUseCase();
      mockStorageUtils = MockStorageUtils();

      useCase = DeepLinkUseCase(
        loginSsoUseCase: mockLoginSsoUseCase,
        tenantRepository: mockTenantRepository,
        userRepository: mockUserRepository,
        storageUtils: mockStorageUtils,
      );

      // Mock设置：直接使用mockStorageUtils

      // 设置环境
      EnvHelper.environment = 'dev';
    });

    tearDown(() {
      // 测试后清理
    });

    group('getBasicInfo', () {
      test('should throw SystemException when token is null', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealDeeplink: DealDeeplinkModel(token: ''),
          dealTenant: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
      });

      test('should throw SystemException when token is empty', () async {
        // Arrange
        final params = SelectTenantModel(
          zoneId: 'test-zone',
          dealDeeplink: DealDeeplinkModel(token: ''),
          dealTenant: null,
          optTenant: null,
        );

        // Act & Assert
        expect(() => useCase.getBasicInfo(params), throwsA(isA<SystemException>()));
      });

      test('should successfully process valid token and return result', () async {
        // Arrange
        const token = 'valid-token';
        const zoneId = 'test-zone';
        const tenantId = 'test-tenant';
        const tenantName = 'Test Tenant';

        final params = SelectTenantModel(
          zoneId: zoneId,
          dealDeeplink: DealDeeplinkModel(token: token),
          dealTenant: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');
        mockResult.tenant = SharedTenantModel(tenantId: tenantId, tenantName: tenantName);

        when(mockTenantRepository.getFullUserInfo()).thenAnswer((_) async => mockResult);

        // Act
        final result = await useCase.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));
        expect(result.mapToken?.accessToken, equals(token));

        // 验证 token 被存储 - 使用 MockStorageUtils 的方法验证
        expect(mockStorageUtils.setCalls, contains(StorageKeys.token));
        expect(mockStorageUtils.getValue<String>(StorageKeys.token), equals(token));
      });

      test('should handle SSO data when ssoUrl exists', () async {
        // Arrange
        const token = 'valid-token';
        const zoneId = 'test-zone';
        const tenantId = 'test-tenant';
        const tenantName = 'Test Tenant';
        const ssoUrl = 'https://sso.example.com';
        const appModel = 'dev';

        final params = SelectTenantModel(
          zoneId: zoneId,
          dealDeeplink: DealDeeplinkModel(token: token),
          dealTenant: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');
        mockResult.tenant = SharedTenantModel(tenantId: tenantId, tenantName: tenantName);

        // 设置 mock 存储数据
        mockStorageUtils.setupStorage({
          '$appModel${StorageKeys.ssoTenantUrl}$tenantId$zoneId': ssoUrl,
          '$appModel${StorageKeys.ssoTenantUrl}': '[]',
        });

        when(mockTenantRepository.getFullUserInfo()).thenAnswer((_) async => mockResult);
        when(mockLoginSsoUseCase.fromJsonList(jsonString: '[]')).thenReturn([]);
        when(mockLoginSsoUseCase.processOrderTenants(tenants: anyNamed('tenants'))).thenReturn('processed-json');

        // Act
        final result = await useCase.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));

        // 验证 SSO 相关操作
        verify(mockLoginSsoUseCase.fromJsonList(jsonString: '[]')).called(1);
        verify(mockLoginSsoUseCase.processOrderTenants(tenants: anyNamed('tenants'))).called(1);

        // 验证存储操作 - 使用 MockStorageUtils 的方法验证
        expect(mockStorageUtils.setCalls, contains('$appModel${StorageKeys.ssoTenantUrl}'));
        expect(mockStorageUtils.getValue<String>('$appModel${StorageKeys.ssoTenantUrl}'), equals('processed-json'));
      });

      test('should not process SSO data when ssoUrl is null', () async {
        // Arrange
        const token = 'valid-token';
        const zoneId = 'test-zone';
        const tenantId = 'test-tenant';
        const tenantName = 'Test Tenant';

        final params = SelectTenantModel(
          zoneId: zoneId,
          dealDeeplink: DealDeeplinkModel(token: token),
          dealTenant: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');
        mockResult.tenant = SharedTenantModel(tenantId: tenantId, tenantName: tenantName);

        when(mockTenantRepository.getFullUserInfo()).thenAnswer((_) async => mockResult);

        // Act
        final result = await useCase.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));

        // 验证没有调用 SSO 相关方法
        verifyNever(mockLoginSsoUseCase.fromJsonList(jsonString: anyNamed('jsonString')));
        verifyNever(mockLoginSsoUseCase.processOrderTenants(tenants: anyNamed('tenants')));
      });

      test('should not process SSO data when loginSsoUseCase is null', () async {
        // Arrange
        const token = 'valid-token';
        const zoneId = 'test-zone';
        const tenantId = 'test-tenant';
        const tenantName = 'Test Tenant';
        const ssoUrl = 'https://sso.example.com';
        const appModel = 'dev';

        final params = SelectTenantModel(
          zoneId: zoneId,
          dealDeeplink: DealDeeplinkModel(token: token),
          dealTenant: null,
          optTenant: null,
        );

        final mockResult = GetTokenResultModel(code: 0, msg: 'Success');
        mockResult.tenant = SharedTenantModel(tenantId: tenantId, tenantName: tenantName);

        // 创建没有 loginSsoUseCase 的 useCase
        final useCaseWithoutSso = DeepLinkUseCase(
          loginSsoUseCase: null,
          tenantRepository: mockTenantRepository,
          userRepository: mockUserRepository,
          storageUtils: mockStorageUtils,
        );

        // 设置 mock 存储数据
        mockStorageUtils.setupStorage({
          '$appModel${StorageKeys.ssoTenantUrl}$tenantId$zoneId': ssoUrl,
          '$appModel${StorageKeys.ssoTenantUrl}': '[]',
        });

        when(mockTenantRepository.getFullUserInfo()).thenAnswer((_) async => mockResult);

        // Act
        final result = await useCaseWithoutSso.getBasicInfo(params);

        // Assert
        expect(result, equals(mockResult));

        // 验证没有调用 SSO 相关方法
        verifyNever(mockLoginSsoUseCase.fromJsonList(jsonString: anyNamed('jsonString')));
        verifyNever(mockLoginSsoUseCase.processOrderTenants(tenants: anyNamed('tenants')));
      });
    });
  });
}
