import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/controllers/home_controller.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_list_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/states/home_ui_state.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/home_item_placeholder_widget.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/widgets/menu_section_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 只模拟控制器，不模拟状态
@GenerateNiceMocks([MockSpec<MyHomeController>()])
import 'menu_section_widget_test.mocks.dart';

// 创建自定义状态类以避免模拟嵌套属性
class TestHomeUIState extends HomeUIState {
  @override
  final RxBool homePageListLoading = false.obs;

  @override
  final RxList<dynamic> homePageList = <dynamic>[].obs;
}

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();
  late MockMyHomeController mockController;
  late TestHomeUIState testState;

  // 创建测试模型数据
  final assetActionList = SharedAssetAction(
    assetActionId: 1,
    assetActionName: '更新资产',
    tenantId: 'tenant1',
    assetTypeId: 2,
    assetTypeGroupIds: '1,2,3',
    displayFlg: 'Y',
    assetTypeName: '设备',
    assetActionItem: 'item1',
    quantityFlg: 'Y',
    scanCondition: 'condition1',
    userIds: '101,102',
    groupIds: '201,202',
    isUpdateAmount: 'N',
    autoFetchAsset: 'N',
    assetConditionSearchName: 'search1',
    createdById: 'user1',
    createdByName: 'Admin User',
    createdDate: '2023-01-01',
    actionLabel: '新規',
    actionIcon: 'star',
  );

  final workflowList = WorkflowList(
    processDefinitionId: 'proc1',
    workflowId: 1,
    workflowName: '申请流程',
    version: 1,
    description: '流程描述',
    assetActionList: [],
    taskDefinitions: [],
    sequenceFlows: [],
    assetTypeIdWithFirstWf: 1,
    createdDate: '2023-01-01',
    workflowType: 'type1',
    workflowTypeCode: 'code1',
    autoFetchAsset: 'N',
    autoSetItemDefaultValue: 'N',
    timerStartUserId: 'user1',
  );

  // 创建测试 Widget 环境的辅助方法
  Widget createWidgetUnderTest() {
    return const GetMaterialApp(home: Scaffold(body: MenuSectionWidget()));
  }

  setUp(() {
    LogUtil.initialize();
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    // 创建真实的测试状态
    testState = TestHomeUIState();

    // 初始化模拟控制器并设置状态
    mockController = MockMyHomeController();
    when(mockController.state).thenReturn(testState);
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);

    Get.put<MyHomeController>(mockController);
  });

  tearDown(() {
    Get.reset();
  });

  group('MenuSectionWidget UI 测试', () {
    testWidgets('当列表为空且不在加载状态时，应该不显示任何内容', (WidgetTester tester) async {
      // 设置状态
      testState.homePageListLoading.value = false;
      testState.homePageList.clear();

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证不显示任何内容
      expect(find.text('よく使うメニュー'), findsNothing);
    });

    testWidgets('当列表为空但在加载状态时，应该显示加载占位符', (WidgetTester tester) async {
      // 设置状态
      testState.homePageListLoading.value = true;
      testState.homePageList.clear();

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证显示标题和加载占位符
      expect(find.text('よく使うメニュー'), findsOneWidget);
      expect(find.byType(HomeItemPlaceholderWidget), findsNWidgets(4));
    });

    testWidgets('当列表有数据时，应该显示菜单项结构', (WidgetTester tester) async {
      // 设置状态
      testState.homePageListLoading.value = false;
      testState.homePageList.assignAll([assetActionList, workflowList]);

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 验证显示标题
      expect(find.text('よく使うメニュー'), findsOneWidget);

      // 验证基本结构
      expect(find.byType(Column), findsWidgets);
    });

    testWidgets('标题应该有正确的样式', (WidgetTester tester) async {
      // 设置状态
      testState.homePageListLoading.value = true;
      testState.homePageList.clear();

      // 执行
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump(const Duration(milliseconds: 50));

      // 找到标题文本
      final titleFinder = find.text('よく使うメニュー');
      expect(titleFinder, findsOneWidget);

      final titleText = tester.widget<Text>(titleFinder);

      // 验证文本样式
      expect(titleText.style!.fontSize, 20);
      expect(titleText.style!.fontWeight, FontWeight.normal);
      expect(titleText.style!.color, Colors.white);
    });
  });
}
