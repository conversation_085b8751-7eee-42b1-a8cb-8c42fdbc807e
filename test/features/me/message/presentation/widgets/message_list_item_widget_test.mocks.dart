// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/message/presentation/widgets/message_list_item_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i12;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_readstatus_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/me/message/presentation/controllers/message_controller.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_list_ui_model.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_ui_model.dart'
    as _i9;
import 'package:get/get.dart' as _i5;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i11;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeMessageListLoadUseCase_0 extends _i1.SmartFake
    implements _i2.MessageListLoadUseCase {
  _FakeMessageListLoadUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMessageReadStatusUsecase_1 extends _i1.SmartFake
    implements _i3.MessageReadStatusUsecase {
  _FakeMessageReadStatusUsecase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_2 extends _i1.SmartFake
    implements _i4.NavigationService {
  _FakeNavigationService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRx_3<T> extends _i1.SmartFake implements _i5.Rx<T> {
  _FakeRx_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_4 extends _i1.SmartFake implements _i5.RxBool {
  _FakeRxBool_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_5<T> extends _i1.SmartFake
    implements _i5.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MessageController].
///
/// See the documentation for Mockito's code generation for more information.
class MockMessageController extends _i1.Mock implements _i6.MessageController {
  @override
  _i2.MessageListLoadUseCase get messageListLoadUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#messageListLoadUseCase),
            returnValue: _FakeMessageListLoadUseCase_0(
              this,
              Invocation.getter(#messageListLoadUseCase),
            ),
            returnValueForMissingStub: _FakeMessageListLoadUseCase_0(
              this,
              Invocation.getter(#messageListLoadUseCase),
            ),
          )
          as _i2.MessageListLoadUseCase);

  @override
  _i3.MessageReadStatusUsecase get messageReadstatusUsecase =>
      (super.noSuchMethod(
            Invocation.getter(#messageReadstatusUsecase),
            returnValue: _FakeMessageReadStatusUsecase_1(
              this,
              Invocation.getter(#messageReadstatusUsecase),
            ),
            returnValueForMissingStub: _FakeMessageReadStatusUsecase_1(
              this,
              Invocation.getter(#messageReadstatusUsecase),
            ),
          )
          as _i3.MessageReadStatusUsecase);

  @override
  _i4.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_2(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_2(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i4.NavigationService);

  @override
  _i5.Rx<_i7.MessageListUIModel> get uiModel =>
      (super.noSuchMethod(
            Invocation.getter(#uiModel),
            returnValue: _FakeRx_3<_i7.MessageListUIModel>(
              this,
              Invocation.getter(#uiModel),
            ),
            returnValueForMissingStub: _FakeRx_3<_i7.MessageListUIModel>(
              this,
              Invocation.getter(#uiModel),
            ),
          )
          as _i5.Rx<_i7.MessageListUIModel>);

  @override
  set uiModel(_i5.Rx<_i7.MessageListUIModel>? _uiModel) => super.noSuchMethod(
    Invocation.setter(#uiModel, _uiModel),
    returnValueForMissingStub: null,
  );

  @override
  _i5.RxBool get noMoreData =>
      (super.noSuchMethod(
            Invocation.getter(#noMoreData),
            returnValue: _FakeRxBool_4(this, Invocation.getter(#noMoreData)),
            returnValueForMissingStub: _FakeRxBool_4(
              this,
              Invocation.getter(#noMoreData),
            ),
          )
          as _i5.RxBool);

  @override
  set noMoreData(_i5.RxBool? _noMoreData) => super.noSuchMethod(
    Invocation.setter(#noMoreData, _noMoreData),
    returnValueForMissingStub: null,
  );

  @override
  int get row =>
      (super.noSuchMethod(
            Invocation.getter(#row),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  int get currentPage =>
      (super.noSuchMethod(
            Invocation.getter(#currentPage),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  set currentPage(int? _currentPage) => super.noSuchMethod(
    Invocation.setter(#currentPage, _currentPage),
    returnValueForMissingStub: null,
  );

  @override
  _i5.RxBool get isLoadingError =>
      (super.noSuchMethod(
            Invocation.getter(#isLoadingError),
            returnValue: _FakeRxBool_4(
              this,
              Invocation.getter(#isLoadingError),
            ),
            returnValueForMissingStub: _FakeRxBool_4(
              this,
              Invocation.getter(#isLoadingError),
            ),
          )
          as _i5.RxBool);

  @override
  set isLoadingError(_i5.RxBool? _isLoadingError) => super.noSuchMethod(
    Invocation.setter(#isLoadingError, _isLoadingError),
    returnValueForMissingStub: null,
  );

  @override
  _i5.RxBool get isLoading =>
      (super.noSuchMethod(
            Invocation.getter(#isLoading),
            returnValue: _FakeRxBool_4(this, Invocation.getter(#isLoading)),
            returnValueForMissingStub: _FakeRxBool_4(
              this,
              Invocation.getter(#isLoading),
            ),
          )
          as _i5.RxBool);

  @override
  set isLoading(_i5.RxBool? _isLoading) => super.noSuchMethod(
    Invocation.setter(#isLoading, _isLoading),
    returnValueForMissingStub: null,
  );

  @override
  _i5.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i5.InternalFinalCallback<void>);

  @override
  _i5.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_5<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i5.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  _i8.Future<void> refreshData() =>
      (super.noSuchMethod(
            Invocation.method(#refreshData, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> loadMore() =>
      (super.noSuchMethod(
            Invocation.method(#loadMore, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> fetchData(dynamic params) =>
      (super.noSuchMethod(
            Invocation.method(#fetchData, [params]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void goBack() => super.noSuchMethod(
    Invocation.method(#goBack, []),
    returnValueForMissingStub: null,
  );

  @override
  void toMsgContent(_i9.MessageUIModel? item) => super.noSuchMethod(
    Invocation.method(#toMsgContent, [item]),
    returnValueForMissingStub: null,
  );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void loadDataOnInit() => super.noSuchMethod(
    Invocation.method(#loadDataOnInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Future<void> loadDataWithLoadingStatus({dynamic params = null}) =>
      (super.noSuchMethod(
            Invocation.method(#loadDataWithLoadingStatus, [], {
              #params: params,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i10.ErrorHandlingMode? mode = _i10.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListener(_i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i11.Disposer);

  @override
  void removeListener(_i12.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i12.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListenerId(Object? key, _i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i11.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
