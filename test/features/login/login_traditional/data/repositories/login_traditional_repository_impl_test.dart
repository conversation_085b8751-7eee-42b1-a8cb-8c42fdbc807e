import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/system_mobile_deploy_manage_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/repositories/login_traditional_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
import '../../../../../core/utils/dio_utils_test.mocks.dart';

@GenerateMocks([DioUtil])
void main() {
  group('LoginTraditionalRepositoryImpl', () {
    late LoginTraditionalRepositoryImpl repository;
    late MockDioUtil mockDioUtil;

    setUp(() {
      LogUtil.initialize();
      mockDioUtil = MockDioUtil();
      repository = LoginTraditionalRepositoryImpl(dioUtil: mockDioUtil);
    });

    group('基础功能测试', () {
      test('应该能够创建 LoginTraditionalRepositoryImpl 实例', () {
        expect(repository, isNotNull);
        expect(repository, isA<LoginTraditionalRepositoryImpl>());
      });

      test('应该实现 LoginTraditionalRepository 接口', () {
        expect(repository, isA<LoginTraditionalRepository>());
      });

      test('应该混入 RepositoryErrorHandler', () {
        expect(repository, isA<RepositoryErrorHandler>());
      });
    });

    group('login() 方法测试', () {
      test('应该成功处理正常登录请求', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';
        const successResponse = {
          'tenants': [],
          'mapToken': {'accessToken': 'test_token'},
        };

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试
        final result = await repository.login(userName: userName, password: password);

        // 验证结果
        expect(result, isA<LoginResultModel>());
        expect(result.tenants, isNotNull);
        expect(result.mapToken, isNotNull);

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理用户名和密码的空格', () async {
        // 准备测试数据
        const userNameWithSpaces = '  <EMAIL>  ';
        const passwordWithSpaces = '  password123  ';
        const successResponse = {
          'tenants': [],
          'mapToken': {'accessToken': 'test_token'},
        };

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试
        final result = await repository.login(userName: userNameWithSpaces, password: passwordWithSpaces);

        // 验证结果
        expect(result, isA<LoginResultModel>());

        // 验证 Mock 调用时参数已去除空格
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': '<EMAIL>', 'password': 'password123'},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';

        // 设置 Mock 行为 - API 调用失败
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: password), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理账户过期异常', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';
        const accountExpirationResponse = {'message': 'userApiAccountExpiration'};

        // 设置 Mock 行为 - 账户过期响应
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: accountExpirationResponse,
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: password), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理空用户名', () async {
        // 准备测试数据
        const emptyUserName = '';
        const password = 'password123';

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: {'message': 'Username is required'},
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: emptyUserName, password: password), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': '', 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理空密码', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const emptyPassword = '';

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: {'message': 'Password is required'},
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: emptyPassword), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': ''},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      // 第三阶段：边界测试和特殊业务逻辑
      test('应该正确处理特殊字符用户名', () async {
        // 准备测试数据
        const specialUserName = '<EMAIL>';
        const password = 'password123';
        const successResponse = {
          'tenants': [],
          'mapToken': {'accessToken': 'test_token'},
        };

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试
        final result = await repository.login(userName: specialUserName, password: password);

        // 验证结果
        expect(result, isA<LoginResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': specialUserName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理特殊字符密码', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const specialPassword = 'p@ssw0rd!@#';
        const successResponse = {
          'tenants': [],
          'mapToken': {'accessToken': 'test_token'},
        };

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试
        final result = await repository.login(userName: userName, password: specialPassword);

        // 验证结果
        expect(result, isA<LoginResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': specialPassword},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理网络超时异常', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';

        // 设置 Mock 行为 - 网络超时
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
            type: DioExceptionType.connectionTimeout,
            message: 'Connection timeout',
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: password), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理数据解析异常', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';

        // 设置 Mock 行为 - 返回无效数据格式
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: 'invalid json string',
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: password), throwsA(isA<SystemException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理空响应数据', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';

        // 设置 Mock 行为 - 返回空数据
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async =>
              Response(data: null, statusCode: 200, requestOptions: RequestOptions(path: GlobalVariable.secureSignIn)),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: password), throwsA(isA<SystemException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });

      test('应该正确处理无消息的网络异常', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';

        // 设置 Mock 行为 - 网络异常但无消息
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // 执行测试并验证异常
        expect(() => repository.login(userName: userName, password: password), throwsA(isA<BusinessException>()));

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);
      });
    });

    group('getMobileDeployManageApiHostVersion() 方法测试', () {
      test('应该成功获取版本信息 - 客户端类型0', () async {
        // 准备测试数据
        const appClientType = 0;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '0', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployManageApiHostVersion(appClientType: appClientType);

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());
        expect(result.mobileVersion, equals('1.0.0'));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该成功获取版本信息 - 客户端类型1', () async {
        // 准备测试数据
        const appClientType = 1;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '1', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployManageApiHostVersion(appClientType: appClientType);

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());
        expect(result.mobileVersion, equals('1.0.0'));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该成功获取版本信息 - 客户端类型2', () async {
        // 准备测试数据
        const appClientType = 2;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '2', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployManageApiHostVersion(appClientType: appClientType);

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());
        expect(result.mobileVersion, equals('1.0.0'));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const appClientType = 1;

        // 设置 Mock 行为 - API 调用失败
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getMobileDeployManageApiHostVersion(appClientType: appClientType),
          throwsA(isA<BusinessException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理无效客户端类型', () async {
        // 准备测试数据
        const invalidAppClientType = 3;

        // 执行测试并验证断言异常
        expect(
          () => repository.getMobileDeployManageApiHostVersion(appClientType: invalidAppClientType),
          throwsA(isA<AssertionError>()),
        );
      });

      // 第三阶段：边界测试
      test('应该正确处理网络连接异常', () async {
        // 准备测试数据
        const appClientType = 1;

        // 设置 Mock 行为 - 网络连接异常
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
            type: DioExceptionType.connectionError,
            message: 'Connection error',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getMobileDeployManageApiHostVersion(appClientType: appClientType),
          throwsA(isA<BusinessException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理数据解析异常', () async {
        // 准备测试数据
        const appClientType = 1;

        // 设置 Mock 行为 - 返回无效数据格式
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: 'invalid json string',
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getMobileDeployManageApiHostVersion(appClientType: appClientType),
          throwsA(isA<SystemException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });
    });

    group('getMobileDeployMobileVersion() 方法测试', () {
      test('应该成功获取版本信息 - 客户端类型0', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const appClientType = 0;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '0', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployMobileVersion(
          mobileVersion: mobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());
        expect(result?.mobileVersion, equals('1.0.0'));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该成功获取版本信息 - 客户端类型1', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const appClientType = 1;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '1', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployMobileVersion(
          mobileVersion: mobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());
        expect(result?.mobileVersion, equals('1.0.0'));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该成功获取版本信息 - 客户端类型2', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const appClientType = 2;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '2', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployMobileVersion(
          mobileVersion: mobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());
        expect(result?.mobileVersion, equals('1.0.0'));

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理返回null的情况', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const appClientType = 1;

        // 设置 Mock 行为 - 返回null
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: null,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployMobileVersion(
          mobileVersion: mobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(result, isNull);

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理API调用失败', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const appClientType = 1;

        // 设置 Mock 行为 - API 调用失败
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: {'message': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getMobileDeployMobileVersion(mobileVersion: mobileVersion, appClientType: appClientType),
          throwsA(isA<BusinessException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理无效客户端类型', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const invalidAppClientType = 3;

        // 执行测试并验证断言异常
        expect(
          () => repository.getMobileDeployMobileVersion(
            mobileVersion: mobileVersion,
            appClientType: invalidAppClientType,
          ),
          throwsA(isA<AssertionError>()),
        );
      });

      // 第三阶段：边界测试
      test('应该正确处理空版本号', () async {
        // 准备测试数据
        const emptyMobileVersion = '';
        const appClientType = 1;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '1', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployMobileVersion(
          mobileVersion: emptyMobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理特殊字符版本号', () async {
        // 准备测试数据
        const specialMobileVersion = '1.0.0-beta+123';
        const appClientType = 1;
        const successResponse = {'mobileVersion': '1.0.0', 'appClientType': '1', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: successResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final result = await repository.getMobileDeployMobileVersion(
          mobileVersion: specialMobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(result, isA<SystemMobileDeployManageResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });

      test('应该正确处理网络超时异常', () async {
        // 准备测试数据
        const mobileVersion = '1.0.0';
        const appClientType = 1;

        // 设置 Mock 行为 - 网络超时
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
            type: DioExceptionType.receiveTimeout,
            message: 'Receive timeout',
          ),
        );

        // 执行测试并验证异常
        expect(
          () => repository.getMobileDeployMobileVersion(mobileVersion: mobileVersion, appClientType: appClientType),
          throwsA(isA<BusinessException>()),
        );

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(1);
      });
    });

    // 第三阶段：集成测试
    group('集成测试', () {
      test('应该能够连续调用多个方法', () async {
        // 准备测试数据
        const userName = '<EMAIL>';
        const password = 'password123';
        const mobileVersion = '1.0.0';
        const appClientType = 1;

        const loginResponse = {
          'tenants': [],
          'mapToken': {'accessToken': 'test_token'},
        };
        const versionResponse = {'mobileVersion': '1.0.0', 'appClientType': '1', 'env': 'production'};

        // 设置 Mock 行为
        when(mockDioUtil.post(GlobalVariable.secureSignIn, data: anyNamed('data'), useFormUrlEncoded: true)).thenAnswer(
          (_) async => Response(
            data: loginResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureSignIn),
          ),
        );

        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: versionResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行测试
        final loginResult = await repository.login(userName: userName, password: password);
        final versionResult = await repository.getMobileDeployManageApiHostVersion(appClientType: appClientType);
        final mobileVersionResult = await repository.getMobileDeployMobileVersion(
          mobileVersion: mobileVersion,
          appClientType: appClientType,
        );

        // 验证结果
        expect(loginResult, isA<LoginResultModel>());
        expect(versionResult, isA<SystemMobileDeployManageResultModel>());
        expect(mobileVersionResult, isA<SystemMobileDeployManageResultModel>());

        // 验证 Mock 调用
        verify(
          mockDioUtil.post(
            GlobalVariable.secureSignIn,
            data: {'userName': userName, 'password': password},
            useFormUrlEncoded: true,
          ),
        ).called(1);

        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(2);
      });

      test('应该正确处理并发调用', () async {
        // 准备测试数据
        const appClientType = 1;
        const versionResponse = {'mobileVersion': '1.0.0', 'appClientType': '1', 'env': 'production'};

        // 设置 Mock 行为
        when(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).thenAnswer(
          (_) async => Response(
            data: versionResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureGetLastedApi),
          ),
        );

        // 执行并发测试
        final futures = [
          repository.getMobileDeployManageApiHostVersion(appClientType: appClientType),
          repository.getMobileDeployManageApiHostVersion(appClientType: appClientType),
          repository.getMobileDeployManageApiHostVersion(appClientType: appClientType),
        ];

        final results = await Future.wait(futures);

        // 验证结果
        expect(results.length, equals(3));
        for (final result in results) {
          expect(result, isA<SystemMobileDeployManageResultModel>());
          expect(result.mobileVersion, equals('1.0.0'));
        }

        // 验证 Mock 调用
        verify(
          mockDioUtil.get(GlobalVariable.secureGetLastedApi, queryParams: {'appClientType': appClientType}),
        ).called(3);
      });
    });
  });
}
