import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/models/scan_setting_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/controllers/scan_setting_controller.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/widgets/vibration_setting_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'vibration_setting_widget_test.mocks.dart';

// ==========================================
// 🧪 VibrationSettingWidget 单元测试
// ==========================================

@GenerateNiceMocks([MockSpec<ScanSettingsController>()])
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  group('🧪 VibrationSettingWidget 单元测试', () {
    final mockInternalFinalCallback = MockInternalFinalCallback<void>();
    late MockScanSettingsController mockController;
    late ScanSettingUIModel testSettingsModel;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    // Set up Mock Controller helper method (moved to top-level scope)
    void setupMockController() {
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onInit()).thenAnswer((_) async {});
      when(mockController.onClose()).thenAnswer((_) async {});
      when(mockController.onReady()).thenAnswer((_) async {});
    }

    setUp(() {
      Get.reset();
      mockController = MockScanSettingsController();
      setupMockController();
      Get.put<ScanSettingsController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    Widget createWidgetUnderTest() {
      return GetMaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(body: const VibrationSettingWidget()),
      );
    }

    // Helper method to create test data model with enhanced vibration support
    ScanSettingUIModel createTestModel({
      bool useSound = true,
      double soundValue = 75.0,
      int scanMusicList = 1,
      bool useVibration = true,
      double vibrationValue = 50.0,
    }) {
      return ScanSettingUIModel(
        useSound: useSound,
        soundValue: soundValue,
        scanMusicList: scanMusicList,
        useVibration: useVibration,
        vibrationValue: vibrationValue,
      );
    }

    // Helper method to update mock controller data
    void updateMockControllerData(ScanSettingUIModel model) {
      when(mockController.model).thenReturn(model.obs);
    }

    // ==========================================
    // 📋 Phase 0: 测试基础设施建设
    // ==========================================

    group('📋 Phase 0: 测试基础设施建设', () {
      group('0.1 Mock Controller 设置验证', () {
        testWidgets('应该成功创建 MockScanSettingsController', (tester) async {
          // Assert
          expect(mockController, isNotNull);
          expect(mockController, isA<MockScanSettingsController>());
        });

        testWidgets('应该正确设置 Mock Controller 的基础方法', (tester) async {
          // Act
          mockController.onInit();
          mockController.onReady();
          mockController.onClose();

          // Assert - 验证生命周期方法可以被调用
          verify(mockController.onInit()).called(1);
          verify(mockController.onReady()).called(1);
          verify(mockController.onClose()).called(1);
        });

        testWidgets('应该正确 Mock Controller 的振动相关业务方法', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act - 测试振动开关方法
          mockController.vibrationEventChange(false);

          // Act - 测试振动值调节方法
          mockController.vibrationValueChange(75.0);

          // Assert
          verify(mockController.vibrationEventChange(false)).called(1);
          verify(mockController.vibrationValueChange(75.0)).called(1);
        });

        testWidgets('应该正确处理 GetX 生命周期', (tester) async {
          // Assert
          expect(mockController.onStart, isNotNull);
          expect(mockController.onStart, same(mockInternalFinalCallback));
        });

        testWidgets('应该能够 Mock 所有振动相关的交互方法', (tester) async {
          // Arrange
          const testVibrationStates = [true, false, true];
          const testVibrationValues = [0.0, 50.0, 100.0];

          // Act & Assert - 测试多种振动开关状态
          for (final state in testVibrationStates) {
            mockController.vibrationEventChange(state);
            verify(mockController.vibrationEventChange(state)).called(1);
          }

          // Act & Assert - 测试多种振动值
          for (final value in testVibrationValues) {
            mockController.vibrationValueChange(value);
            verify(mockController.vibrationValueChange(value)).called(1);
          }
        });
      });

      group('0.2 测试数据模型验证', () {
        testWidgets('应该能够创建默认振动测试模型', (tester) async {
          // Act
          final model = createTestModel();

          // Assert
          expect(model.useSound, true);
          expect(model.soundValue, 75.0);
          expect(model.scanMusicList, 1);
          expect(model.useVibration, true);
          expect(model.vibrationValue, 50.0);
        });

        testWidgets('应该能够创建自定义振动测试模型', (tester) async {
          // Act
          final model = createTestModel(useSound: false, useVibration: false, vibrationValue: 25.0);

          // Assert
          expect(model.useSound, false);
          expect(model.useVibration, false);
          expect(model.vibrationValue, 25.0);
        });

        testWidgets('应该支持振动值的边界测试数据', (tester) async {
          // Test minimum value
          final minModel = createTestModel(vibrationValue: 0.0);
          expect(minModel.vibrationValue, 0.0);

          // Test maximum value
          final maxModel = createTestModel(vibrationValue: 100.0);
          expect(maxModel.vibrationValue, 100.0);

          // Test middle values
          final midModel = createTestModel(vibrationValue: 50.0);
          expect(midModel.vibrationValue, 50.0);
        });

        testWidgets('应该支持复杂的状态组合测试数据', (tester) async {
          // State combination 1: useSound=false (component hidden)
          final hiddenModel = createTestModel(useSound: false, useVibration: true);
          expect(hiddenModel.useSound, false);
          expect(hiddenModel.useVibration, true);

          // State combination 2: useSound=true, useVibration=false (switch visible, slider hidden)
          final switchOnlyModel = createTestModel(useSound: true, useVibration: false);
          expect(switchOnlyModel.useSound, true);
          expect(switchOnlyModel.useVibration, false);

          // State combination 3: useSound=true, useVibration=true (all visible)
          final fullVisibleModel = createTestModel(useSound: true, useVibration: true);
          expect(fullVisibleModel.useSound, true);
          expect(fullVisibleModel.useVibration, true);
        });

        testWidgets('应该能够动态更新 Mock Controller 振动数据', (tester) async {
          // Arrange
          final model = createTestModel(useVibration: false, vibrationValue: 25.0);

          // Act
          updateMockControllerData(model);

          // Trigger a call to model to verify mocking
          final _ = mockController.model;

          // Assert
          verify(mockController.model).called(1);
        });
      });

      group('0.3 GetX 依赖注入验证', () {
        testWidgets('应该成功注入 Mock Controller', (tester) async {
          // Assert
          expect(Get.isRegistered<ScanSettingsController>(), true, reason: 'Controller 应该已注册到 GetX');

          final injectedController = Get.find<ScanSettingsController>();
          expect(injectedController, same(mockController), reason: '注入的 Controller 应该是我们的 Mock');
        });

        testWidgets('setUp 和 tearDown 应该正确管理 GetX 状态', (tester) async {
          // Arrange - 在 setUp 中已经注册了 Controller
          expect(Get.isRegistered<ScanSettingsController>(), true);

          // Act - 模拟 tearDown 行为
          Get.reset();

          // Assert
          expect(Get.isRegistered<ScanSettingsController>(), false, reason: 'tearDown 后 Controller 应该被清除');
        });

        testWidgets('应该能够重复注册和清理 Controller', (tester) async {
          // Test multiple setup/teardown cycles
          for (int i = 0; i < 3; i++) {
            Get.reset();
            final testController = MockScanSettingsController();

            // 为每个新的 controller 实例设置 Mock
            when(testController.onStart).thenReturn(mockInternalFinalCallback);
            when(testController.onInit()).thenAnswer((_) async {});
            when(testController.onClose()).thenAnswer((_) async {});
            when(testController.onReady()).thenAnswer((_) async {});

            Get.put<ScanSettingsController>(testController);

            expect(Get.isRegistered<ScanSettingsController>(), true, reason: 'Cycle $i: Controller 应该成功注册');

            final foundController = Get.find<ScanSettingsController>();
            expect(foundController, same(testController), reason: 'Cycle $i: 找到的 Controller 应该是注册的实例');
          }
        });
      });

      group('0.4 Widget 测试环境验证', () {
        testWidgets('应该能够创建振动设置测试 Widget 环境', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          final widget = createWidgetUnderTest();

          // Assert
          expect(widget, isNotNull);
          expect(widget, isA<GetMaterialApp>());
        });

        testWidgets('应该能够渲染振动设置 Widget 而不报错', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act & Assert - 应该不会抛出异常
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();
        });

        testWidgets('应该包含 VibrationSettingWidget', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: '应该找到 VibrationSettingWidget');
        });

        testWidgets('应该正确应用 AppTheme', (tester) async {
          // Arrange
          testSettingsModel = createTestModel();
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final materialApp = tester.widget<GetMaterialApp>(find.byType(GetMaterialApp));
          expect(materialApp.theme, AppTheme.lightTheme, reason: '应该使用正确的主题');
        });

        testWidgets('应该能够在不同状态组合下正确构建', (tester) async {
          // Test different state combinations
          final testStates = [
            {'useSound': true, 'useVibration': true},
            {'useSound': true, 'useVibration': false},
            {'useSound': false, 'useVibration': true},
            {'useSound': false, 'useVibration': false},
          ];

          for (int i = 0; i < testStates.length; i++) {
            final state = testStates[i];

            // Setup new environment for each state
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(
              useSound: state['useSound'] as bool,
              useVibration: state['useVibration'] as bool,
            );
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            expect(
              find.byType(VibrationSettingWidget),
              findsOneWidget,
              reason: 'State $i: VibrationSettingWidget 应该存在',
            );
          }
        });

        testWidgets('基础设施应该支持振动组件的Key识别', (tester) async {
          // Arrange - useSound=true, useVibration=true 确保所有组件都显示
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证关键的Key组件能够被找到（基础设施验证）
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '应该能够找到主容器');
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '应该能够找到振动开关');
          expect(find.byKey(const Key('vibration_slider')), findsOneWidget, reason: '应该能够找到振动滑块');
        });
      });

      group('0.5 测试隔离和清理验证', () {
        testWidgets('测试之间应该正确隔离振动状态', (tester) async {
          // Arrange - 确保这是一个干净的测试环境
          expect(Get.isRegistered<ScanSettingsController>(), true, reason: '每个测试开始时应该有注册的 Controller');

          // Act
          testSettingsModel = createTestModel(useVibration: false, vibrationValue: 25.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.byType(VibrationSettingWidget), findsOneWidget);
        });

        testWidgets('应该能够创建独立的振动测试数据', (tester) async {
          // Arrange - 创建特定的振动测试数据
          final customModel = createTestModel(useSound: true, useVibration: false, vibrationValue: 75.0);
          updateMockControllerData(customModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 基础渲染成功即可，具体状态验证在后续Phase
          expect(find.byType(VibrationSettingWidget), findsOneWidget);
        });

        testWidgets('Mock Controller 状态应该在测试间独立', (tester) async {
          // Arrange & Act - 调用一些方法
          mockController.vibrationEventChange(true);
          mockController.vibrationValueChange(80.0);

          // Assert - 验证方法被调用
          verify(mockController.vibrationEventChange(true)).called(1);
          verify(mockController.vibrationValueChange(80.0)).called(1);

          // 注意：实际的状态独立性由 setUp/tearDown 保证
          // 这里只验证Mock机制正常工作
        });
      });
    });

    // ==========================================
    // 🎯 Phase 1: 条件渲染逻辑测试
    // ==========================================

    group('🎯 Phase 1: 条件渲染逻辑测试', () {
      group('1.1 外层条件渲染测试 (useSound)', () {
        testWidgets('useSound = false 时整个组件应该隐藏', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 整个振动组件都不应该显示
          expect(find.byKey(const Key('vibration_widget_column')), findsNothing, reason: 'useSound=false 时主容器应该隐藏');
          expect(find.byKey(const Key('vibration_switch')), findsNothing, reason: 'useSound=false 时振动开关应该隐藏');
          expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: 'useSound=false 时振动滑块应该隐藏');
          expect(find.text('バイブレーション'), findsNothing, reason: 'useSound=false 时振动标签应该隐藏');
        });

        testWidgets('useSound = true 时组件应该显示', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 主要组件应该显示
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: 'useSound=true 时主容器应该显示');
          expect(find.byKey(const Key('vibration_switch_row')), findsOneWidget, reason: 'useSound=true 时开关行应该显示');
          expect(find.text('バイブレーション'), findsOneWidget, reason: 'useSound=true 时振动标签应该显示');
        });

        testWidgets('useSound 状态变化应该触发正确的响应式更新', (tester) async {
          // Test useSound: false -> true
          testSettingsModel = createTestModel(useSound: false, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证初始隐藏状态
          expect(find.byKey(const Key('vibration_widget_column')), findsNothing);

          // 切换到显示状态
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证切换后显示状态
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '切换到 useSound=true 后应该显示组件');
        });

        testWidgets('useSound = false 时不应该影响内层状态的保存', (tester) async {
          // Arrange - useSound=false, 但 useVibration=true, vibrationValue=75.0
          testSettingsModel = createTestModel(useSound: false, useVibration: true, vibrationValue: 75.0);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 组件隐藏但数据状态应该保持
          expect(find.byKey(const Key('vibration_widget_column')), findsNothing);

          // 验证 Mock 中的数据状态仍然正确
          expect(testSettingsModel.useVibration, true, reason: '内层状态应该保持');
          expect(testSettingsModel.vibrationValue, 75.0, reason: '振动值应该保持');
        });
      });

      group('1.2 内层条件渲染测试 (useVibration)', () {
        testWidgets('useVibration = false 时 Slider 区域应该隐藏', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 开关区域显示，Slider 区域隐藏
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '主容器应该显示');
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '振动开关应该显示');
          expect(
            find.byKey(const Key('vibration_slider_row')),
            findsNothing,
            reason: 'useVibration=false 时 Slider 行应该隐藏',
          );
          expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: 'useVibration=false 时 Slider 应该隐藏');
          expect(find.byKey(const Key('vibration_icon_left')), findsNothing, reason: 'useVibration=false 时左侧图标应该隐藏');
          expect(find.byKey(const Key('vibration_icon_right')), findsNothing, reason: 'useVibration=false 时右侧图标应该隐藏');
        });

        testWidgets('useVibration = true 时 Slider 区域应该显示', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 所有组件都应该显示
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '主容器应该显示');
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '振动开关应该显示');
          expect(
            find.byKey(const Key('vibration_slider_row')),
            findsOneWidget,
            reason: 'useVibration=true 时 Slider 行应该显示',
          );
          expect(find.byKey(const Key('vibration_slider')), findsOneWidget, reason: 'useVibration=true 时 Slider 应该显示');
          expect(find.byKey(const Key('vibration_icon_left')), findsOneWidget, reason: 'useVibration=true 时左侧图标应该显示');
          expect(find.byKey(const Key('vibration_icon_right')), findsOneWidget, reason: 'useVibration=true 时右侧图标应该显示');
        });

        testWidgets('useVibration 状态变化应该只影响 Slider 区域', (tester) async {
          // Test useVibration: true -> false
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证初始完整显示状态
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget);
          expect(find.byKey(const Key('vibration_slider')), findsOneWidget);

          // 切换到隐藏 Slider 状态
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证切换后状态：开关仍显示，Slider 隐藏
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '开关区域不应该受 useVibration 影响');
          expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: 'useVibration=false 后 Slider 应该隐藏');
        });

        testWidgets('标签行应该始终显示不受 useVibration 影响', (tester) async {
          // Test both useVibration states
          final vibrationStates = [true, false];

          for (final vibrationState in vibrationStates) {
            // Setup new environment for each test
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(useSound: true, useVibration: vibrationState);
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // 验证标签行始终显示
            expect(
              find.byKey(const Key('vibration_label_row')),
              findsOneWidget,
              reason: 'useVibration=$vibrationState 时标签行应该显示',
            );
            expect(find.text('振動調節'), findsOneWidget, reason: 'useVibration=$vibrationState 时调节标签应该显示');
          }
        });
      });

      group('1.3 状态组合测试', () {
        testWidgets('State 1: useSound=false, useVibration=any → 完全隐藏', (tester) async {
          final vibrationStates = [true, false];

          for (final vibrationState in vibrationStates) {
            // Setup new environment for each test
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(useSound: false, useVibration: vibrationState);
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // Assert - 完全隐藏
            expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: 'Widget 本身应该存在');
            expect(
              find.byKey(const Key('vibration_widget_column')),
              findsNothing,
              reason: 'useSound=false, useVibration=$vibrationState: 主容器应该隐藏',
            );
            expect(find.text('バイブレーション'), findsNothing, reason: 'useSound=false: 所有内容都应该隐藏');
          }
        });

        testWidgets('State 2: useSound=true, useVibration=false → 显示开关，隐藏Slider', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 部分显示
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '主容器应该显示');
          expect(find.byKey(const Key('vibration_switch_row')), findsOneWidget, reason: '开关行应该显示');
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '振动开关应该显示');
          expect(find.byKey(const Key('vibration_label_row')), findsOneWidget, reason: '标签行应该显示');

          // Slider 区域应该隐藏
          expect(find.byKey(const Key('vibration_slider_row')), findsNothing, reason: 'Slider 行应该隐藏');
          expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: 'Slider 应该隐藏');
        });

        testWidgets('State 3: useSound=true, useVibration=true → 完全显示', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 完全显示
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '主容器应该显示');
          expect(find.byKey(const Key('vibration_switch_row')), findsOneWidget, reason: '开关行应该显示');
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '振动开关应该显示');
          expect(find.byKey(const Key('vibration_label_row')), findsOneWidget, reason: '标签行应该显示');
          expect(find.byKey(const Key('vibration_slider_row')), findsOneWidget, reason: 'Slider 行应该显示');
          expect(find.byKey(const Key('vibration_slider')), findsOneWidget, reason: 'Slider 应该显示');
          expect(find.byKey(const Key('vibration_icon_left')), findsOneWidget, reason: '左侧图标应该显示');
          expect(find.byKey(const Key('vibration_icon_right')), findsOneWidget, reason: '右侧图标应该显示');
        });

        testWidgets('状态组合切换应该保持一致性', (tester) async {
          // 测试状态切换序列: State 3 -> State 2 -> State 1
          final stateSequence = [
            {'useSound': true, 'useVibration': true, 'name': 'State 3'},
            {'useSound': true, 'useVibration': false, 'name': 'State 2'},
            {'useSound': false, 'useVibration': false, 'name': 'State 1'},
          ];

          for (int i = 0; i < stateSequence.length; i++) {
            final state = stateSequence[i];

            // Setup new environment for each state
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(
              useSound: state['useSound'] as bool,
              useVibration: state['useVibration'] as bool,
            );
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // 验证每个状态的一致性
            final stateName = state['name'] as String;
            if (stateName == 'State 1') {
              expect(find.byKey(const Key('vibration_widget_column')), findsNothing, reason: '$stateName: 应该完全隐藏');
            } else {
              expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '$stateName: 主容器应该显示');

              if (stateName == 'State 2') {
                expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: '$stateName: Slider 应该隐藏');
              } else {
                expect(find.byKey(const Key('vibration_slider')), findsOneWidget, reason: '$stateName: Slider 应该显示');
              }
            }
          }
        });
      });

      group('1.4 SizedBox.shrink() 验证', () {
        testWidgets('外层 SizedBox.shrink() 应该正确处理完全隐藏', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证外层隐藏逻辑
          expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: 'Widget 本身应该存在');
          expect(find.byType(SizedBox), findsOneWidget, reason: '应该渲染 SizedBox.shrink()');

          // 验证没有实际内容
          expect(
            find.byKey(const Key('vibration_widget_column')),
            findsNothing,
            reason: '外层 SizedBox.shrink() 应该隐藏所有内容',
          );
        });

        testWidgets('内层 SizedBox.shrink() 应该正确处理 Slider 隐藏', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证内层隐藏逻辑
          expect(find.byKey(const Key('vibration_widget_column')), findsOneWidget, reason: '主容器应该显示');
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '开关应该显示');

          // 验证 Slider 区域被 SizedBox.shrink() 隐藏
          expect(
            find.byKey(const Key('vibration_slider_row')),
            findsNothing,
            reason: '内层 SizedBox.shrink() 应该隐藏 Slider 区域',
          );
          expect(find.byType(SizedBox), findsWidgets, reason: '应该有 SizedBox 组件（包括 shrink）');
        });

        testWidgets('SizedBox.shrink() 不应该影响布局结构', (tester) async {
          // Test layout consistency with and without shrink
          final testStates = [
            {'useSound': true, 'useVibration': true, 'name': '完全显示'},
            {'useSound': true, 'useVibration': false, 'name': '部分显示'},
            {'useSound': false, 'useVibration': true, 'name': '完全隐藏'},
          ];

          for (final state in testStates) {
            // Setup new environment
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(
              useSound: state['useSound'] as bool,
              useVibration: state['useVibration'] as bool,
            );
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // 验证 Widget 本身总是存在，不影响上层布局
            expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: '${state['name']}: Widget 本身应该始终存在');

            // 验证不会抛出布局异常
            expect(tester.takeException(), isNull, reason: '${state['name']}: 不应该有布局异常');
          }
        });

        testWidgets('多重嵌套的 SizedBox.shrink() 应该协调工作', (tester) async {
          // Arrange - 测试最复杂的嵌套隐藏场景
          testSettingsModel = createTestModel(useSound: false, useVibration: false);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证嵌套隐藏的正确性
          expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: 'Widget 本身应该存在');
          expect(find.byKey(const Key('vibration_widget_column')), findsNothing, reason: '外层条件应该优先，隐藏整个组件');

          // 验证内层组件确实不存在（被外层隐藏）
          expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: '外层隐藏时内层组件不应该存在');
          expect(find.text('バイブレーション'), findsNothing, reason: '外层隐藏时所有文本都不应该显示');
        });
      });
    });

    // ==========================================
    // 🎨 Phase 2: 静态 UI 组件测试
    // ==========================================

    group('🎨 Phase 2: 静态 UI 组件测试', () {
      group('2.1 布局结构测试', () {
        testWidgets('Column 主容器应该具有正确的布局属性', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final columnWidget = tester.widget<Column>(find.byKey(const Key('vibration_widget_column')));
          expect(columnWidget, isNotNull, reason: 'Column 主容器应该存在');
          expect(columnWidget.children.length, equals(3), reason: 'Column 应该包含3个子组件：开关行、标签行、Slider区域');
        });

        testWidgets('振动开关行应该具有正确的 MainAxisAlignment', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchRowWidget = tester.widget<Row>(find.byKey(const Key('vibration_switch_row')));
          expect(switchRowWidget.mainAxisAlignment, MainAxisAlignment.spaceBetween, reason: '开关行应该使用 spaceBetween 对齐');
        });

        testWidgets('Slider 行应该具有正确的 MainAxisAlignment', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderRowWidget = tester.widget<Row>(find.byKey(const Key('vibration_slider_row')));
          expect(
            sliderRowWidget.mainAxisAlignment,
            MainAxisAlignment.spaceBetween,
            reason: 'Slider 行应该使用 spaceBetween 对齐',
          );
        });

        testWidgets('Slider 应该被 Expanded 包装以占据可用空间', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderRowFinder = find.byKey(const Key('vibration_slider_row'));
          final expandedFinder = find.descendant(of: sliderRowFinder, matching: find.byType(Expanded));
          expect(expandedFinder, findsOneWidget, reason: 'Slider 应该被 Expanded 包装');

          final sliderInExpanded = find.descendant(
            of: expandedFinder,
            matching: find.byKey(const Key('vibration_slider')),
          );
          expect(sliderInExpanded, findsOneWidget, reason: 'Slider 应该在 Expanded 内部');
        });

        testWidgets('标签行应该具有正确的子组件结构', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final labelRowWidget = tester.widget<Row>(find.byKey(const Key('vibration_label_row')));
          expect(labelRowWidget.children.length, equals(2), reason: '标签行应该包含2个子组件：Text 和 SizedBox.shrink()');
        });

        testWidgets('布局结构在不同状态组合下应该保持一致', (tester) async {
          // Test layout consistency across different states
          final testStates = [
            {'useSound': true, 'useVibration': true, 'name': 'State 3'},
            {'useSound': true, 'useVibration': false, 'name': 'State 2'},
          ];

          for (final state in testStates) {
            // Setup new environment
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(
              useSound: state['useSound'] as bool,
              useVibration: state['useVibration'] as bool,
            );
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final stateName = state['name'] as String;

            // 验证主要布局结构的一致性
            final columnWidget = tester.widget<Column>(find.byKey(const Key('vibration_widget_column')));
            expect(columnWidget.children.length, equals(3), reason: '$stateName: Column 应该始终包含3个子组件');

            final switchRowWidget = tester.widget<Row>(find.byKey(const Key('vibration_switch_row')));
            expect(switchRowWidget.mainAxisAlignment, MainAxisAlignment.spaceBetween, reason: '$stateName: 开关行对齐应该一致');
          }
        });
      });

      group('2.2 文本组件测试', () {
        testWidgets('应该显示正确的日文振动标签', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('バイブレーション'), findsOneWidget, reason: '应该显示日文振动标签');

          final vibrationLabelWidget = tester.widget<Text>(find.byKey(const Key('vibration_switch_label')));
          expect(vibrationLabelWidget.data, 'バイブレーション', reason: '振动标签内容应该正确');
        });

        testWidgets('应该显示正确的振动调节标签', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('振動調節'), findsOneWidget, reason: '应该显示振动调节标签');

          final adjustLabelWidget = tester.widget<Text>(find.byKey(const Key('vibration_adjust_label')));
          expect(adjustLabelWidget.data, '振動調節', reason: '调节标签内容应该正确');
        });

        testWidgets('文本标签应该在正确的布局位置', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证振动标签在开关行中
          final switchRowFinder = find.byKey(const Key('vibration_switch_row'));
          expect(
            find.descendant(of: switchRowFinder, matching: find.byKey(const Key('vibration_switch_label'))),
            findsOneWidget,
            reason: '振动标签应该在开关行内',
          );

          // 验证调节标签在标签行中
          final labelRowFinder = find.byKey(const Key('vibration_label_row'));
          expect(
            find.descendant(of: labelRowFinder, matching: find.byKey(const Key('vibration_adjust_label'))),
            findsOneWidget,
            reason: '调节标签应该在标签行内',
          );
        });

        testWidgets('文本组件应该具有正确的默认样式', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final vibrationLabelWidget = tester.widget<Text>(find.byKey(const Key('vibration_switch_label')));
          final adjustLabelWidget = tester.widget<Text>(find.byKey(const Key('vibration_adjust_label')));

          // 验证文本内容
          expect(vibrationLabelWidget.data, 'バイブレーション');
          expect(adjustLabelWidget.data, '振動調節');

          // Text 组件的 style 默认为 null，由主题决定
          expect(vibrationLabelWidget, isNotNull);
          expect(adjustLabelWidget, isNotNull);
        });

        testWidgets('文本标签在不同状态下应该保持一致', (tester) async {
          // Test text consistency across different vibration states
          final vibrationStates = [true, false];

          for (final vibrationState in vibrationStates) {
            // Setup new environment
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(useSound: true, useVibration: vibrationState);
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            // 验证文本始终存在且内容正确
            expect(find.text('バイブレーション'), findsOneWidget, reason: 'useVibration=$vibrationState: 振动标签应该始终显示');
            expect(find.text('振動調節'), findsOneWidget, reason: 'useVibration=$vibrationState: 调节标签应该始终显示');
          }
        });
      });

      group('2.3 图标组件测试', () {
        testWidgets('左侧图标应该是 phone_android 类型', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final leftIconWidget = tester.widget<Icon>(find.byKey(const Key('vibration_icon_left')));
          expect(leftIconWidget.icon, Icons.phone_android, reason: '左侧图标应该是 phone_android');
        });

        testWidgets('右侧图标应该是 vibration 类型', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final rightIconWidget = tester.widget<Icon>(find.byKey(const Key('vibration_icon_right')));
          expect(rightIconWidget.icon, Icons.vibration, reason: '右侧图标应该是 vibration');
        });

        testWidgets('两个图标都应该使用灰色', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final leftIconWidget = tester.widget<Icon>(find.byKey(const Key('vibration_icon_left')));
          final rightIconWidget = tester.widget<Icon>(find.byKey(const Key('vibration_icon_right')));

          expect(leftIconWidget.color, Colors.grey, reason: '左侧图标应该是灰色');
          expect(rightIconWidget.color, Colors.grey, reason: '右侧图标应该是灰色');
        });

        testWidgets('图标应该在 Slider 行的正确位置', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderRowFinder = find.byKey(const Key('vibration_slider_row'));

          // 验证左侧图标在 Slider 行中
          expect(
            find.descendant(of: sliderRowFinder, matching: find.byKey(const Key('vibration_icon_left'))),
            findsOneWidget,
            reason: '左侧图标应该在 Slider 行内',
          );

          // 验证右侧图标在 Slider 行中
          expect(
            find.descendant(of: sliderRowFinder, matching: find.byKey(const Key('vibration_icon_right'))),
            findsOneWidget,
            reason: '右侧图标应该在 Slider 行内',
          );
        });

        testWidgets('图标应该只在 useVibration = true 时显示', (tester) async {
          // Test when useVibration = false
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 图标应该隐藏
          expect(find.byKey(const Key('vibration_icon_left')), findsNothing, reason: 'useVibration=false 时左侧图标应该隐藏');
          expect(find.byKey(const Key('vibration_icon_right')), findsNothing, reason: 'useVibration=false 时右侧图标应该隐藏');

          // Test when useVibration = true
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 图标应该显示
          expect(find.byKey(const Key('vibration_icon_left')), findsOneWidget, reason: 'useVibration=true 时左侧图标应该显示');
          expect(find.byKey(const Key('vibration_icon_right')), findsOneWidget, reason: 'useVibration=true 时右侧图标应该显示');
        });

        testWidgets('图标样式应该保持一致性', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final leftIconWidget = tester.widget<Icon>(find.byKey(const Key('vibration_icon_left')));
          final rightIconWidget = tester.widget<Icon>(find.byKey(const Key('vibration_icon_right')));

          // 验证图标属性一致性
          expect(leftIconWidget.color, rightIconWidget.color, reason: '两个图标应该使用相同的颜色');
          expect(leftIconWidget.size, rightIconWidget.size, reason: '两个图标应该使用相同的大小');
        });
      });

      group('2.4 组件层次结构测试', () {
        testWidgets('应该具有正确的组件嵌套层次', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证完整的层次结构
          // VibrationSettingWidget > Obx > Column > [Row, Row, Obx]

          final widgetFinder = find.byType(VibrationSettingWidget);
          expect(widgetFinder, findsOneWidget, reason: 'VibrationSettingWidget 应该存在');

          final columnFinder = find.byKey(const Key('vibration_widget_column'));
          expect(columnFinder, findsOneWidget, reason: 'Column 主容器应该存在');

          // 验证 Column 的直接子组件
          final switchRowFinder = find.byKey(const Key('vibration_switch_row'));
          final labelRowFinder = find.byKey(const Key('vibration_label_row'));
          final sliderObxFinder = find.descendant(of: columnFinder, matching: find.byType(Obx));

          expect(switchRowFinder, findsOneWidget, reason: '开关行应该在 Column 中');
          expect(labelRowFinder, findsOneWidget, reason: '标签行应该在 Column 中');
          expect(sliderObxFinder, findsWidgets, reason: 'Obx 应该在 Column 中');
        });

        testWidgets('开关行应该包含正确的子组件', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchRowFinder = find.byKey(const Key('vibration_switch_row'));

          // 验证开关行包含文本和 Obx(Switch)
          expect(
            find.descendant(of: switchRowFinder, matching: find.byKey(const Key('vibration_switch_label'))),
            findsOneWidget,
            reason: '开关行应该包含振动标签',
          );

          expect(
            find.descendant(of: switchRowFinder, matching: find.byKey(const Key('vibration_switch'))),
            findsOneWidget,
            reason: '开关行应该包含振动开关',
          );
        });

        testWidgets('Slider 行应该包含正确的子组件顺序', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderRowFinder = find.byKey(const Key('vibration_slider_row'));
          final sliderRowWidget = tester.widget<Row>(sliderRowFinder);

          // 验证 Slider 行包含：左图标 + Expanded(Slider) + 右图标
          expect(sliderRowWidget.children.length, equals(3), reason: 'Slider 行应该包含3个子组件');

          // 验证子组件存在
          expect(
            find.descendant(of: sliderRowFinder, matching: find.byKey(const Key('vibration_icon_left'))),
            findsOneWidget,
            reason: 'Slider 行应该包含左侧图标',
          );

          expect(
            find.descendant(of: sliderRowFinder, matching: find.byType(Expanded)),
            findsOneWidget,
            reason: 'Slider 行应该包含 Expanded',
          );

          expect(
            find.descendant(of: sliderRowFinder, matching: find.byKey(const Key('vibration_icon_right'))),
            findsOneWidget,
            reason: 'Slider 行应该包含右侧图标',
          );
        });

        testWidgets('Obx 组件应该正确包装响应式组件', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final obxFinders = find.byType(Obx);
          expect(obxFinders, findsAtLeastNWidgets(2), reason: '应该有至少2个 Obx 组件');

          // 验证开关的 Obx 包装
          final switchRowFinder = find.byKey(const Key('vibration_switch_row'));
          final switchObxFinder = find.descendant(of: switchRowFinder, matching: find.byType(Obx));
          expect(switchObxFinder, findsOneWidget, reason: '开关应该被 Obx 包装');

          final switchInObx = find.descendant(of: switchObxFinder, matching: find.byKey(const Key('vibration_switch')));
          expect(switchInObx, findsOneWidget, reason: 'Switch 应该在 Obx 内部');
        });

        testWidgets('层次结构应该在不同状态下保持一致', (tester) async {
          // Test hierarchy consistency across states
          final testStates = [
            {'useVibration': true, 'name': 'useVibration=true'},
            {'useVibration': false, 'name': 'useVibration=false'},
          ];

          for (final state in testStates) {
            // Setup new environment
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(useSound: true, useVibration: state['useVibration'] as bool);
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final stateName = state['name'] as String;

            // 验证基础层次结构的一致性
            expect(find.byType(VibrationSettingWidget), findsOneWidget, reason: '$stateName: Widget 本身应该存在');
            expect(
              find.byKey(const Key('vibration_widget_column')),
              findsOneWidget,
              reason: '$stateName: Column 主容器应该存在',
            );
            expect(find.byKey(const Key('vibration_switch_row')), findsOneWidget, reason: '$stateName: 开关行应该存在');
            expect(find.byKey(const Key('vibration_label_row')), findsOneWidget, reason: '$stateName: 标签行应该存在');
          }
        });

        testWidgets('Key 标识的组件应该能够被精确定位', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证所有 Key 组件都能被找到
          final allKeys = [
            'vibration_widget_column',
            'vibration_switch_row',
            'vibration_switch_label',
            'vibration_switch',
            'vibration_label_row',
            'vibration_adjust_label',
            'vibration_slider_row',
            'vibration_slider',
            'vibration_icon_left',
            'vibration_icon_right',
          ];

          for (final keyName in allKeys) {
            expect(find.byKey(Key(keyName)), findsOneWidget, reason: 'Key "$keyName" 的组件应该能够被找到');
          }
        });
      });
    });

    // ==========================================
    // 🎮 Phase 3: 交互组件功能测试
    // ==========================================

    group('🎮 Phase 3: 交互组件功能测试', () {
      group('3.1 Switch 交互测试', () {
        testWidgets('应该能够点击 Switch 切换振动状态', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击 Switch
          final switchFinder = find.byKey(const Key('vibration_switch'));
          expect(switchFinder, findsOneWidget, reason: 'Switch 应该存在');

          await tester.tap(switchFinder);
          await tester.pumpAndSettle();

          // Assert - 验证回调被调用
          verify(mockController.vibrationEventChange(true)).called(1);
        });

        testWidgets('Switch 应该反映当前的 useVibration 状态', (tester) async {
          // Test useVibration = true
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          Switch switchWidget = tester.widget<Switch>(find.byKey(const Key('vibration_switch')));
          expect(switchWidget.value, true, reason: 'useVibration=true 时 Switch 应该为开启状态');

          // Test useVibration = false
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          switchWidget = tester.widget<Switch>(find.byKey(const Key('vibration_switch')));
          expect(switchWidget.value, false, reason: 'useVibration=false 时 Switch 应该为关闭状态');
        });

        testWidgets('Switch 交互应该传递正确的布尔值', (tester) async {
          // Test switching from false to true
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          verify(mockController.vibrationEventChange(true)).called(1);

          // Test switching from true to false
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          verify(mockController.vibrationEventChange(false)).called(1);
        });

        testWidgets('Switch 在 useSound=false 时应该隐藏且无法交互', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: false, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - Switch 应该不存在
          expect(find.byKey(const Key('vibration_switch')), findsNothing, reason: 'useSound=false 时 Switch 应该隐藏');

          // 尝试交互应该抛出 FlutterError（因为 Switch 不存在）
          expect(() async {
            await tester.tap(find.byKey(const Key('vibration_switch')));
          }, throwsA(isA<FlutterError>()));
        });

        testWidgets('Switch 应该具有正确的可访问性属性', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final switchWidget = tester.widget<Switch>(find.byKey(const Key('vibration_switch')));
          expect(switchWidget.onChanged, isNotNull, reason: 'Switch 应该有 onChanged 回调');
          expect(switchWidget.value, isA<bool>(), reason: 'Switch value 应该是布尔类型');
        });

        testWidgets('多次点击 Switch 应该触发多次回调', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 多次点击
          for (int i = 0; i < 3; i++) {
            await tester.tap(find.byKey(const Key('vibration_switch')));
            await tester.pumpAndSettle();
          }

          // Assert - 验证多次调用
          verify(mockController.vibrationEventChange(true)).called(3);
        });
      });

      group('3.2 Slider 交互测试', () {
        testWidgets('应该能够拖动 Slider 改变振动值', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 25.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 拖动 Slider 到新位置
          final sliderFinder = find.byKey(const Key('vibration_slider'));
          expect(sliderFinder, findsOneWidget, reason: 'Slider 应该存在');

          // 模拟拖动到中间位置 (50.0)
          await tester.drag(sliderFinder, const Offset(100, 0));
          await tester.pumpAndSettle();

          // Assert - 验证回调被调用（具体值取决于拖动距离）
          verify(mockController.vibrationValueChange(any)).called(greaterThan(0));
        });

        testWidgets('Slider 应该反映当前的 vibrationValue', (tester) async {
          // Test different values
          final testValues = [0.0, 25.0, 50.0, 75.0, 100.0];

          for (final value in testValues) {
            // Setup new environment
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: value);
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final sliderWidget = tester.widget<Slider>(find.byKey(const Key('vibration_slider')));
            expect(sliderWidget.value, value, reason: 'Slider 应该反映 vibrationValue=$value');
          }
        });

        testWidgets('Slider 应该具有正确的属性配置', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 50.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('vibration_slider')));
          expect(sliderWidget.min, 0.0, reason: 'Slider 最小值应该是 0.0');
          expect(sliderWidget.max, 100.0, reason: 'Slider 最大值应该是 100.0');
          expect(sliderWidget.divisions, 4, reason: 'Slider 应该有 4 个分割点');
          expect(sliderWidget.onChanged, isNotNull, reason: 'Slider 应该有 onChanged 回调');
        });

        testWidgets('Slider 应该只在 useVibration=true 时可交互', (tester) async {
          // Test when useVibration = false
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - Slider 应该不存在
          expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: 'useVibration=false 时 Slider 应该隐藏');

          // Test when useVibration = true
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - Slider 应该存在且可交互
          expect(find.byKey(const Key('vibration_slider')), findsOneWidget, reason: 'useVibration=true 时 Slider 应该显示');

          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('vibration_slider')));
          expect(sliderWidget.onChanged, isNotNull, reason: 'Slider 应该是可交互的');
        });

        testWidgets('Slider 应该在分割点上正确对齐', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 25.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert
          final sliderWidget = tester.widget<Slider>(find.byKey(const Key('vibration_slider')));

          // 验证分割点计算：divisions=4，范围0-100，所以分割点应该是 [0, 25, 50, 75, 100]
          expect(sliderWidget.divisions, 4);
          expect(sliderWidget.value, 25.0); // 应该正好在分割点上

          // 验证范围
          expect(sliderWidget.min, 0.0);
          expect(sliderWidget.max, 100.0);
        });

        testWidgets('Slider 拖动应该在边界值内工作', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 50.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          final sliderFinder = find.byKey(const Key('vibration_slider'));

          // Act - 尝试拖动到极值
          // 向右拖动很远（尝试超过最大值）
          await tester.drag(sliderFinder, const Offset(1000, 0));
          await tester.pumpAndSettle();

          // 向左拖动很远（尝试低于最小值）
          await tester.drag(sliderFinder, const Offset(-1000, 0));
          await tester.pumpAndSettle();

          // Assert - 验证回调被调用（Slider 会自动限制在边界内）
          verify(mockController.vibrationValueChange(any)).called(greaterThan(0));
        });
      });

      group('3.3 回调函数验证', () {
        testWidgets('vibrationEventChange 应该接收正确的布尔值', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act & Assert - 测试从 false 切换到 true
          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          verify(mockController.vibrationEventChange(true)).called(1);
          verifyNever(mockController.vibrationEventChange(false));
        });

        testWidgets('vibrationValueChange 应该接收数值类型参数', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 25.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 拖动 Slider
          await tester.drag(find.byKey(const Key('vibration_slider')), const Offset(50, 0));
          await tester.pumpAndSettle();

          // Assert - 验证调用了数值变化回调
          final captured = verify(mockController.vibrationValueChange(captureAny)).captured;
          expect(captured, isNotEmpty, reason: '应该调用了 vibrationValueChange');
          expect(captured.first, isA<double>(), reason: '参数应该是 double 类型');
          expect(captured.first, greaterThanOrEqualTo(0.0), reason: '数值应该 >= 0.0');
          expect(captured.first, lessThanOrEqualTo(100.0), reason: '数值应该 <= 100.0');
        });

        testWidgets('回调函数不应该在组件隐藏时被调用', (tester) async {
          // Arrange - useSound = false，整个组件隐藏
          testSettingsModel = createTestModel(useSound: false, useVibration: true);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证组件隐藏
          expect(find.byKey(const Key('vibration_switch')), findsNothing);
          expect(find.byKey(const Key('vibration_slider')), findsNothing);

          // 由于组件不存在，无法触发回调
          verifyNever(mockController.vibrationEventChange(any));
          verifyNever(mockController.vibrationValueChange(any));
        });

        testWidgets('每次交互都应该独立触发回调', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 50.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Clear any setup calls
          clearInteractions(mockController);

          // Act - 先操作 Switch
          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          // 再操作 Slider（确保拖动距离足够大以触发回调）
          await tester.drag(find.byKey(const Key('vibration_slider')), const Offset(100, 0));
          await tester.pumpAndSettle();

          // Assert - 验证回调被调用
          verify(mockController.vibrationEventChange(false)).called(1);
          verify(mockController.vibrationValueChange(any)).called(greaterThan(0));
        });

        testWidgets('Mock 设置应该正确拦截所有回调', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 0.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Clear any setup calls
          clearInteractions(mockController);

          // Act - 执行多种交互
          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          await tester.drag(find.byKey(const Key('vibration_slider')), const Offset(100, 0));
          await tester.pumpAndSettle();

          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          // Assert - 验证所有调用都被正确 Mock
          // 注意：连续点击可能会导致多次调用，主要验证回调机制正常工作
          verify(mockController.vibrationEventChange(false)).called(greaterThanOrEqualTo(1));
          verify(mockController.vibrationValueChange(any)).called(greaterThan(0));
          // 验证 Mock 机制成功拦截了回调
        });
      });

      group('3.4 交互组合测试', () {
        testWidgets('Switch 和 Slider 应该协调工作', (tester) async {
          // Arrange - 开始时 useVibration = false
          testSettingsModel = createTestModel(useSound: true, useVibration: false);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 初始状态：Switch 存在，Slider 隐藏
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget);
          expect(find.byKey(const Key('vibration_slider')), findsNothing);

          // 模拟状态更新：useVibration 变为 true（而不是实际点击）
          Get.reset();
          mockController = MockScanSettingsController();
          setupMockController();
          Get.put<ScanSettingsController>(mockController);

          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 50.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 更新后状态：Switch 和 Slider 都存在
          expect(find.byKey(const Key('vibration_switch')), findsOneWidget);
          expect(find.byKey(const Key('vibration_slider')), findsOneWidget);

          // Act - 现在可以操作 Slider（使用更大的拖动距离）
          await tester.drag(find.byKey(const Key('vibration_slider')), const Offset(150, 0));
          await tester.pumpAndSettle();

          // Assert - Slider 交互成功（只验证 Slider 的交互）
          verify(mockController.vibrationValueChange(any)).called(greaterThan(0));
        });

        testWidgets('复杂交互序列应该按预期工作', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 25.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Clear any setup calls
          clearInteractions(mockController);

          // Act - 执行复杂的交互序列
          // 1. 调节 Slider
          await tester.drag(find.byKey(const Key('vibration_slider')), const Offset(25, 0));
          await tester.pumpAndSettle();

          // 2. 关闭 Switch (true → false)
          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          // 3. 再次开启 Switch (false → true)
          await tester.tap(find.byKey(const Key('vibration_switch')));
          await tester.pumpAndSettle();

          // 4. 再次调节 Slider
          await tester.drag(find.byKey(const Key('vibration_slider')), const Offset(-10, 0));
          await tester.pumpAndSettle();

          // Assert - 验证所有交互都被记录
          verify(mockController.vibrationValueChange(any)).called(greaterThan(1));
          verify(mockController.vibrationEventChange(false)).called(greaterThanOrEqualTo(1));
          // 验证复杂序列的交互都被正确处理
        });

        testWidgets('状态同步应该保持一致性', (tester) async {
          // Test different state combinations
          final testScenarios = [
            {
              'name': 'Scenario 1: useVibration=false → 只有 Switch',
              'useVibration': false,
              'expectSwitch': true,
              'expectSlider': false,
            },
            {
              'name': 'Scenario 2: useVibration=true → Switch + Slider',
              'useVibration': true,
              'expectSwitch': true,
              'expectSlider': true,
            },
          ];

          for (final scenario in testScenarios) {
            // Setup new environment
            Get.reset();
            mockController = MockScanSettingsController();
            setupMockController();
            Get.put<ScanSettingsController>(mockController);

            testSettingsModel = createTestModel(
              useSound: true,
              useVibration: scenario['useVibration'] as bool,
              vibrationValue: 50.0,
            );
            updateMockControllerData(testSettingsModel);

            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pumpAndSettle();

            final scenarioName = scenario['name'] as String;

            // Assert component visibility
            if (scenario['expectSwitch'] as bool) {
              expect(find.byKey(const Key('vibration_switch')), findsOneWidget, reason: '$scenarioName: Switch 应该显示');
            } else {
              expect(find.byKey(const Key('vibration_switch')), findsNothing, reason: '$scenarioName: Switch 应该隐藏');
            }

            if (scenario['expectSlider'] as bool) {
              expect(find.byKey(const Key('vibration_slider')), findsOneWidget, reason: '$scenarioName: Slider 应该显示');
            } else {
              expect(find.byKey(const Key('vibration_slider')), findsNothing, reason: '$scenarioName: Slider 应该隐藏');
            }
          }
        });

        testWidgets('嵌套交互不应该产生意外副作用', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 50.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Clear any setup calls
          clearInteractions(mockController);

          // Act - 同时进行多种交互（虽然在实际中很少发生）
          final switchFinder = find.byKey(const Key('vibration_switch'));
          final sliderFinder = find.byKey(const Key('vibration_slider'));

          // 快速连续交互（增加拖动距离以确保触发回调）
          await tester.tap(switchFinder); // true → false
          await tester.pumpAndSettle();
          await tester.drag(sliderFinder, const Offset(100, 0)); // 使用更大的拖动距离
          await tester.pumpAndSettle();
          await tester.tap(switchFinder); // false → true
          await tester.pumpAndSettle();

          // Assert - 验证所有交互都被正确处理，没有异常
          verify(mockController.vibrationEventChange(false)).called(greaterThanOrEqualTo(1));
          verify(mockController.vibrationValueChange(any)).called(greaterThan(0));
          // 验证快速连续交互没有产生异常

          // 验证没有抛出异常
          expect(tester.takeException(), isNull, reason: '不应该有未处理的异常');
        });

        testWidgets('交互响应速度应该满足用户体验要求', (tester) async {
          // Arrange
          testSettingsModel = createTestModel(useSound: true, useVibration: true, vibrationValue: 25.0);
          updateMockControllerData(testSettingsModel);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act & Assert - 快速连续交互应该都被响应
          for (int i = 0; i < 5; i++) {
            await tester.tap(find.byKey(const Key('vibration_switch')));
            await tester.pump(); // 只等待一帧，测试响应速度
          }

          await tester.pumpAndSettle(); // 等待所有动画完成

          // 验证所有交互都被处理
          verify(mockController.vibrationEventChange(any)).called(5);
        });
      });
    });
  });
}
