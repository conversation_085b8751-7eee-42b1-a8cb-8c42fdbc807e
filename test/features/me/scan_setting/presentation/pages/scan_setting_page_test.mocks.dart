// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/scan_setting/presentation/pages/scan_setting_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i11;

import 'package:asset_force_mobile_v2/core/platform/method_channel.dart' as _i3;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i9;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/controllers/scan_setting_controller.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/models/scan_setting_ui_model.dart'
    as _i7;
import 'package:get/get.dart' as _i4;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeIStorageUtils_0 extends _i1.SmartFake implements _i2.IStorageUtils {
  _FakeIStorageUtils_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePlatformChannel_1 extends _i1.SmartFake
    implements _i3.PlatformChannel {
  _FakePlatformChannel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRx_2<T> extends _i1.SmartFake implements _i4.Rx<T> {
  _FakeRx_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_3 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_4<T> extends _i1.SmartFake
    implements _i4.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ScanSettingsController].
///
/// See the documentation for Mockito's code generation for more information.
class MockScanSettingsController extends _i1.Mock
    implements _i6.ScanSettingsController {
  @override
  _i2.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_0(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_0(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i2.IStorageUtils);

  @override
  _i3.PlatformChannel get platformChannel =>
      (super.noSuchMethod(
            Invocation.getter(#platformChannel),
            returnValue: _FakePlatformChannel_1(
              this,
              Invocation.getter(#platformChannel),
            ),
            returnValueForMissingStub: _FakePlatformChannel_1(
              this,
              Invocation.getter(#platformChannel),
            ),
          )
          as _i3.PlatformChannel);

  @override
  _i4.Rx<_i7.ScanSettingUIModel> get model =>
      (super.noSuchMethod(
            Invocation.getter(#model),
            returnValue: _FakeRx_2<_i7.ScanSettingUIModel>(
              this,
              Invocation.getter(#model),
            ),
            returnValueForMissingStub: _FakeRx_2<_i7.ScanSettingUIModel>(
              this,
              Invocation.getter(#model),
            ),
          )
          as _i4.Rx<_i7.ScanSettingUIModel>);

  @override
  set model(_i4.Rx<_i7.ScanSettingUIModel>? _model) => super.noSuchMethod(
    Invocation.setter(#model, _model),
    returnValueForMissingStub: null,
  );

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  _i4.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_4<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_4<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i4.InternalFinalCallback<void>);

  @override
  _i4.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_4<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_4<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i4.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Future<void> soundEventChange(bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#soundEventChange, [value]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> vibrationEventChange(bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#vibrationEventChange, [value]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> musicChange(int? currentMusic) =>
      (super.noSuchMethod(
            Invocation.method(#musicChange, [currentMusic]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> soundValueChange(double? value) =>
      (super.noSuchMethod(
            Invocation.method(#soundValueChange, [value]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> vibrationValueChange(double? value) =>
      (super.noSuchMethod(
            Invocation.method(#vibrationValueChange, [value]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i9.ErrorHandlingMode? mode = _i9.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Disposer addListener(_i10.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i10.Disposer);

  @override
  void removeListener(_i11.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i11.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Disposer addListenerId(Object? key, _i10.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i10.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
