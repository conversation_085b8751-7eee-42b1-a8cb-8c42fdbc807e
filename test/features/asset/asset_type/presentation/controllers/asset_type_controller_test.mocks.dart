// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_type/presentation/controllers/asset_type_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart' as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_type/domain/usecase/get_asset_type_list_usecase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAssetTypeRepository_0 extends _i1.SmartFake
    implements _i2.AssetTypeRepository {
  _FakeAssetTypeRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetAssetTypeListResult_1 extends _i1.SmartFake
    implements _i3.GetAssetTypeListResult {
  _FakeGetAssetTypeListResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetAssetTypeListUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAssetTypeListUseCase extends _i1.Mock
    implements _i3.GetAssetTypeListUseCase {
  @override
  _i2.AssetTypeRepository get assetTypeRepository =>
      (super.noSuchMethod(
            Invocation.getter(#assetTypeRepository),
            returnValue: _FakeAssetTypeRepository_0(
              this,
              Invocation.getter(#assetTypeRepository),
            ),
            returnValueForMissingStub: _FakeAssetTypeRepository_0(
              this,
              Invocation.getter(#assetTypeRepository),
            ),
          )
          as _i2.AssetTypeRepository);

  @override
  _i4.Future<_i3.GetAssetTypeListResult> call(_i5.NoParams? noParams) =>
      (super.noSuchMethod(
            Invocation.method(#call, [noParams]),
            returnValue: _i4.Future<_i3.GetAssetTypeListResult>.value(
              _FakeGetAssetTypeListResult_1(
                this,
                Invocation.method(#call, [noParams]),
              ),
            ),
            returnValueForMissingStub:
                _i4.Future<_i3.GetAssetTypeListResult>.value(
                  _FakeGetAssetTypeListResult_1(
                    this,
                    Invocation.method(#call, [noParams]),
                  ),
                ),
          )
          as _i4.Future<_i3.GetAssetTypeListResult>);

  @override
  _i4.Future<bool> checkAuthority({required int? assetTypeId}) =>
      (super.noSuchMethod(
            Invocation.method(#checkAuthority, [], {#assetTypeId: assetTypeId}),
            returnValue: _i4.Future<bool>.value(false),
            returnValueForMissingStub: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);
}
