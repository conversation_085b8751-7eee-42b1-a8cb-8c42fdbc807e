import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_list_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/pages/login_sso_list_page.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateNiceMocks([MockSpec<LoginSsoListController>()])
import 'login_sso_list_page_test.mocks.dart';

// Mock回调函数
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

// 测试用的TenantSsoModel数据
class TestData {
  static List<TenantSsoModel> createTestSsoList() {
    return [
      TenantSsoModel(
        tenantId: 'tenant1',
        tenantName: 'テナント1',
        ssoUrl: 'https://test1.example.com/sso',
        zoneId: 'zone1',
      ),
      TenantSsoModel(
        tenantId: 'tenant2',
        tenantName: '長いテナント名前の例です。これは複数行に渡る可能性があります。',
        ssoUrl: 'https://test2.example.com/sso/very/long/url/path',
        zoneId: 'zone2',
      ),
      TenantSsoModel(
        tenantId: 'tenant3',
        tenantName: 'Test Tenant 3',
        ssoUrl: 'https://test3.example.com',
        zoneId: 'zone3',
      ),
    ];
  }

  static TenantSsoModel createSingleSsoItem() {
    return TenantSsoModel(
      tenantId: 'single_tenant',
      tenantName: 'シングルテナント',
      ssoUrl: 'https://single.example.com',
      zoneId: 'single_zone',
    );
  }
}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();

  late MockLoginSsoListController mockController;
  late RxList<TenantSsoModel> testSsoItemList;
  late RxBool testIsSsoListEdit;

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset();

    // 创建Mock控制器
    mockController = MockLoginSsoListController();

    // 创建响应式测试数据
    testSsoItemList = <TenantSsoModel>[].obs;
    testIsSsoListEdit = false.obs;

    // 设置Mock控制器的基本属性
    when(mockController.ssoItemList).thenReturn(testSsoItemList);
    when(mockController.isSsoListEdit).thenReturn(testIsSsoListEdit);

    // 设置Mock控制器的方法
    when(mockController.goBackPage()).thenAnswer((_) async {});
    when(mockController.ssoListEditOnClick()).thenAnswer((_) async {});
    when(mockController.ssoListAddOnClick()).thenAnswer((_) async {});
    when(mockController.ssoListGoToWebViewOnClick(ssoItem: anyNamed('ssoItem'))).thenAnswer((_) async {});
    when(mockController.ssoListDeleteOnClick(ssoItem: anyNamed('ssoItem'))).thenAnswer((_) async {});

    // 设置生命周期方法的stub
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);
    when(mockController.onDelete).thenReturn(mockInternalFinalCallback);

    // 注册控制器到GetX
    Get.put<LoginSsoListController>(mockController);
  });

  tearDown(() {
    Get.reset();
    reset(mockController);
    clearInteractions(mockController);
  });

  /// 创建测试用的Widget
  Widget createWidgetUnderTest() {
    return MediaQuery(
      data: const MediaQueryData(size: Size(400, 900)),
      child: GetMaterialApp(
        home: const LoginSsoListPage(),
        theme: ThemeData(colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.darkBlueColor), useMaterial3: true),
      ),
    );
  }

  // ================================
  // 第一阶段：基础环境搭建完成
  // ================================

  group('第一阶段：基础环境搭建验证', () {
    testWidgets('测试环境应该正确设置Mock控制器', (WidgetTester tester) async {
      // 验证Mock控制器是否正确注册
      expect(Get.find<LoginSsoListController>(), equals(mockController));

      // 验证响应式变量是否正确设置
      expect(mockController.ssoItemList, equals(testSsoItemList));
      expect(mockController.isSsoListEdit, equals(testIsSsoListEdit));
      expect(testIsSsoListEdit.value, isFalse);
      expect(testSsoItemList.isEmpty, isTrue);
    });

    testWidgets('测试数据应该正确创建', (WidgetTester tester) async {
      // 验证测试数据创建
      final testList = TestData.createTestSsoList();
      expect(testList.length, equals(3));
      expect(testList[0].tenantName, equals('テナント1'));
      expect(testList[1].tenantName, contains('長いテナント名前'));

      final singleItem = TestData.createSingleSsoItem();
      expect(singleItem.tenantName, equals('シングルテナント'));
    });

    testWidgets('Widget应该能够成功创建', (WidgetTester tester) async {
      // 基础Widget创建测试
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pump();

      // 验证基础结构存在
      expect(find.byType(MaterialApp), findsOneWidget);
      expect(find.byType(LoginSsoListPage), findsOneWidget);
    });
  });

  // ================================
  // 第二阶段：静态渲染测试
  // ================================

  group('第二阶段：静态渲染测试', () {
    group('基础Widget渲染测试', () {
      testWidgets('应该显示Scaffold基础结构', (WidgetTester tester) async {
        // 设置空列表状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证基础容器结构
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(SafeArea), findsWidgets); // 可能有多个SafeArea
        expect(
          find.byWidgetPredicate((widget) => widget is Padding && widget.padding == const EdgeInsets.all(10.0)),
          findsOneWidget,
        );
      });

      testWidgets('应该显示AppBar的基本元素', (WidgetTester tester) async {
        // 设置初始状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证AppBar基本元素
        expect(find.byType(AppBar), findsOneWidget);

        // 验证标题
        expect(find.text('SSOテナント選択'), findsOneWidget);

        // 验证返回按钮（左箭头图标）
        expect(find.byIcon(Icons.chevron_left), findsOneWidget);

        // 验证编辑按钮
        expect(find.text('編集'), findsOneWidget);
      });

      testWidgets('返回按钮应该有正确的样式', (WidgetTester tester) async {
        // 设置初始状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找返回按钮
        final backButtonFinder = find.byWidgetPredicate(
          (widget) =>
              widget is IconButton &&
              widget.icon is Icon &&
              (widget.icon as Icon).icon == Icons.chevron_left &&
              (widget.icon as Icon).color == AppTheme.whiteColor &&
              (widget.icon as Icon).size == 30,
        );

        expect(backButtonFinder, findsOneWidget);
      });

      testWidgets('标题应该有正确的样式', (WidgetTester tester) async {
        // 设置初始状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找标题Text Widget
        final titleFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              widget.data == 'SSOテナント選択' &&
              widget.style != null &&
              widget.style!.fontSize == 18 &&
              widget.style!.fontWeight == FontWeight.bold &&
              widget.style!.color == AppTheme.whiteColor,
        );

        expect(titleFinder, findsOneWidget);
      });

      testWidgets('编辑按钮应该有正确的样式', (WidgetTester tester) async {
        // 设置初始状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找编辑按钮 - 应该有多个TextButton（编辑按钮和添加按钮）
        final editButtonFinder = find.byWidgetPredicate((widget) => widget is TextButton);

        expect(editButtonFinder, findsWidgets); // 期望找到多个TextButton

        // 验证编辑按钮文本
        final editTextFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              widget.data == '編集' &&
              widget.style != null &&
              widget.style!.color == AppTheme.whiteColor,
        );

        expect(editTextFinder, findsOneWidget);
      });
    });

    group('空状态测试', () {
      testWidgets('空列表应该只显示添加租户按钮', (WidgetTester tester) async {
        // 设置空列表状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证ListView存在
        expect(find.byType(ListView), findsOneWidget);

        // 验证添加租户按钮存在
        expect(find.text('テナントを追加'), findsOneWidget);

        // 验证没有租户项的Container显示（空状态不应该有租户项的Container）
        final tenantContainerFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Container &&
              widget.decoration is BoxDecoration &&
              (widget.decoration as BoxDecoration).color == AppTheme.white85Color,
        );
        expect(tenantContainerFinder, findsNothing);
      });

      testWidgets('添加租户按钮应该有正确的样式', (WidgetTester tester) async {
        // 设置空列表状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找添加租户按钮
        final addButtonFinder = find.byWidgetPredicate((widget) => widget is TextButton);

        expect(addButtonFinder, findsWidgets); // 应该找到多个TextButton（包括编辑按钮）

        // 验证添加按钮文本样式
        final addTextFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              widget.data == 'テナントを追加' &&
              widget.style != null &&
              widget.style!.fontSize == 16 &&
              widget.style!.fontWeight == FontWeight.bold &&
              widget.style!.color == AppTheme.whiteColor,
        );

        expect(addTextFinder, findsOneWidget);
      });

      testWidgets('添加租户按钮应该有正确的Padding', (WidgetTester tester) async {
        // 设置空列表状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证添加按钮的Padding
        final paddingFinder = find.byWidgetPredicate(
          (widget) => widget is Padding && widget.padding == const EdgeInsets.symmetric(vertical: 30.0),
        );

        expect(paddingFinder, findsOneWidget);
      });

      testWidgets('空状态下应该显示正确的ListView itemCount', (WidgetTester tester) async {
        // 设置空列表状态
        testSsoItemList.clear();
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找ListView.builder
        final listViewFinder = find.byType(ListView);
        expect(listViewFinder, findsOneWidget);

        // 在空状态下，itemCount应该是1（只有添加按钮）
        // 这里我们通过验证只有一个子元素来间接验证
        expect(find.text('テナントを追加'), findsOneWidget);
        expect(find.byType(Container), findsNothing); // 空状态下没有租户项容器
      });
    });
  });

  // ================================
  // 第三阶段：数据驱动渲染测试
  // ================================

  group('第三阶段：数据驱动渲染测试', () {
    group('列表渲染测试', () {
      testWidgets('应该正确渲染单个租户项', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证ListView存在
        expect(find.byType(ListView), findsOneWidget);

        // 验证租户项存在
        expect(find.text(singleSsoItem.tenantName), findsOneWidget);

        // 验证添加租户按钮仍然存在
        expect(find.text('テナントを追加'), findsOneWidget);

        // 验证租户项容器
        final tenantContainerFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Container &&
              widget.decoration is BoxDecoration &&
              (widget.decoration as BoxDecoration).color == AppTheme.white85Color,
        );
        expect(tenantContainerFinder, findsOneWidget);
      });

      testWidgets('应该正确渲染多个租户项', (WidgetTester tester) async {
        // 设置多个租户数据
        final testSsoList = TestData.createTestSsoList();
        testSsoItemList.clear();
        testSsoItemList.addAll(testSsoList);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证所有租户名称都显示
        for (final ssoItem in testSsoList) {
          expect(find.text(ssoItem.tenantName), findsOneWidget);
        }

        // 验证添加租户按钮存在
        expect(find.text('テナントを追加'), findsOneWidget);

        // 验证有正确数量的租户容器
        final tenantContainerFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Container &&
              widget.decoration is BoxDecoration &&
              (widget.decoration as BoxDecoration).color == AppTheme.white85Color,
        );
        expect(tenantContainerFinder, findsNWidgets(testSsoList.length));
      });

      testWidgets('ListView.builder应该有正确的itemCount', (WidgetTester tester) async {
        // 设置测试数据
        final testSsoList = TestData.createTestSsoList();
        testSsoItemList.clear();
        testSsoItemList.addAll(testSsoList);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // itemCount应该是列表长度 + 1（添加按钮）
        // 我们通过验证所有项目都存在来间接验证itemCount
        expect(find.text('テナント1'), findsOneWidget);
        expect(find.textContaining('長いテナント名前'), findsOneWidget);
        expect(find.text('Test Tenant 3'), findsOneWidget);
        expect(find.text('テナントを追加'), findsOneWidget);
      });
    });

    group('普通模式租户项UI测试', () {
      testWidgets('_ssoView应该显示正确的基础结构', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证InkWell存在（可点击）- 期望找到多个InkWell（租户项和按钮）
        expect(find.byType(InkWell), findsWidgets);

        // 验证Row布局
        expect(find.byType(Row), findsWidgets);

        // 验证右箭头图标
        expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      });

      testWidgets('租户名应该有正确的显示样式', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找租户名Text Widget
        final tenantNameFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              widget.data == singleSsoItem.tenantName &&
              widget.style != null &&
              widget.style!.fontSize == 16.0 &&
              widget.style!.fontWeight == FontWeight.w500 &&
              widget.style!.color == AppTheme.blackColor,
        );

        expect(tenantNameFinder, findsOneWidget);
      });

      testWidgets('租户名Text应该有正确的文本处理属性', (WidgetTester tester) async {
        // 使用长文本租户名
        final longNameSsoItem = TestData.createTestSsoList()[1]; // 长文本项
        testSsoItemList.clear();
        testSsoItemList.add(longNameSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找租户名Text Widget并验证文本处理属性
        final tenantNameFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              widget.data == longNameSsoItem.tenantName &&
              widget.maxLines == 3 &&
              widget.overflow == TextOverflow.ellipsis &&
              widget.softWrap == true,
        );

        expect(tenantNameFinder, findsOneWidget);
      });

      testWidgets('右箭头图标应该有正确的样式', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 查找右箭头图标
        final chevronIconFinder = find.byWidgetPredicate(
          (widget) => widget is Icon && widget.icon == Icons.chevron_right && widget.color == AppTheme.blackColor,
        );

        expect(chevronIconFinder, findsOneWidget);
      });

      testWidgets('租户项容器应该有正确的样式', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证容器装饰
        final containerFinder = find.byWidgetPredicate(
          (widget) =>
              widget is Container &&
              widget.decoration is BoxDecoration &&
              (widget.decoration as BoxDecoration).color == AppTheme.white85Color &&
              (widget.decoration as BoxDecoration).borderRadius != null,
        );

        expect(containerFinder, findsOneWidget);
      });

      testWidgets('租户项应该有正确的Padding设置', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证租户项外部Padding
        final outerPaddingFinder = find.byWidgetPredicate(
          (widget) => widget is Padding && widget.padding == const EdgeInsets.symmetric(vertical: 8.0),
        );

        expect(outerPaddingFinder, findsWidgets); // 可能有多个

        // 验证租户项内部Padding
        final innerPaddingFinder = find.byWidgetPredicate(
          (widget) => widget is Padding && widget.padding == const EdgeInsets.all(16.0),
        );

        expect(innerPaddingFinder, findsWidgets); // 可能有多个
      });
    });

    group('布局结构测试', () {
      testWidgets('租户项应该使用Expanded来处理文本溢出', (WidgetTester tester) async {
        // 设置长文本租户名
        final longNameSsoItem = TestData.createTestSsoList()[1];
        testSsoItemList.clear();
        testSsoItemList.add(longNameSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证Expanded Widget存在
        expect(find.byType(Expanded), findsWidgets);

        // 验证长文本能够正确显示（不会导致溢出错误）
        expect(find.textContaining('長いテナント名前'), findsOneWidget);
      });

      testWidgets('Row布局应该正确分配空间', (WidgetTester tester) async {
        // 设置单个租户数据
        final singleSsoItem = TestData.createSingleSsoItem();
        testSsoItemList.clear();
        testSsoItemList.add(singleSsoItem);
        testIsSsoListEdit.value = false;

        // 执行
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pump();

        // 验证Row的MainAxisAlignment
        final rowFinder = find.byWidgetPredicate(
          (widget) => widget is Row && widget.mainAxisAlignment == MainAxisAlignment.spaceBetween,
        );

        expect(rowFinder, findsWidgets); // 可能找到多个Row
      });
    });
  });
}
