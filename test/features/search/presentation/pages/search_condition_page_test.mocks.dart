// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/search/presentation/pages/search_condition_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i11;
import 'dart:ui' as _i14;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i12;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/search/domain/usecases/get_search_conditions_usecase.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/search/presentation/states/search_condition_ui_state.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart'
    as _i9;
import 'package:flutter/cupertino.dart' as _i5;
import 'package:get/get.dart' as _i7;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i13;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGetSearchConditionsUseCase_0 extends _i1.SmartFake
    implements _i2.GetSearchConditionsUseCase {
  _FakeGetSearchConditionsUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_1 extends _i1.SmartFake
    implements _i3.NavigationService {
  _FakeNavigationService_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_2 extends _i1.SmartFake implements _i4.DialogService {
  _FakeDialogService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTextEditingController_3 extends _i1.SmartFake
    implements _i5.TextEditingController {
  _FakeTextEditingController_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetSearchConditionParams_4 extends _i1.SmartFake
    implements _i2.GetSearchConditionParams {
  _FakeGetSearchConditionParams_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSearchConditionUIState_5 extends _i1.SmartFake
    implements _i6.SearchConditionUIState {
  _FakeSearchConditionUIState_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_6<T> extends _i1.SmartFake
    implements _i7.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SearchConditionController].
///
/// See the documentation for Mockito's code generation for more information.
class MockSearchConditionController extends _i1.Mock
    implements _i8.SearchConditionController {
  @override
  _i2.GetSearchConditionsUseCase get getSearchConditionsUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#getSearchConditionsUseCase),
            returnValue: _FakeGetSearchConditionsUseCase_0(
              this,
              Invocation.getter(#getSearchConditionsUseCase),
            ),
            returnValueForMissingStub: _FakeGetSearchConditionsUseCase_0(
              this,
              Invocation.getter(#getSearchConditionsUseCase),
            ),
          )
          as _i2.GetSearchConditionsUseCase);

  @override
  _i3.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_1(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i3.NavigationService);

  @override
  _i4.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i4.DialogService);

  @override
  _i5.TextEditingController get textEditingController =>
      (super.noSuchMethod(
            Invocation.getter(#textEditingController),
            returnValue: _FakeTextEditingController_3(
              this,
              Invocation.getter(#textEditingController),
            ),
            returnValueForMissingStub: _FakeTextEditingController_3(
              this,
              Invocation.getter(#textEditingController),
            ),
          )
          as _i5.TextEditingController);

  @override
  _i2.GetSearchConditionParams get params =>
      (super.noSuchMethod(
            Invocation.getter(#params),
            returnValue: _FakeGetSearchConditionParams_4(
              this,
              Invocation.getter(#params),
            ),
            returnValueForMissingStub: _FakeGetSearchConditionParams_4(
              this,
              Invocation.getter(#params),
            ),
          )
          as _i2.GetSearchConditionParams);

  @override
  set params(_i2.GetSearchConditionParams? _params) => super.noSuchMethod(
    Invocation.setter(#params, _params),
    returnValueForMissingStub: null,
  );

  @override
  _i6.SearchConditionUIState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeSearchConditionUIState_5(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeSearchConditionUIState_5(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i6.SearchConditionUIState);

  @override
  _i7.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i7.InternalFinalCallback<void>);

  @override
  _i7.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i7.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClickSearch() => super.noSuchMethod(
    Invocation.method(#onClickSearch, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClickSearchConditionItemToSearch(
    _i9.SearchConditionModel? searchCondition,
  ) => super.noSuchMethod(
    Invocation.method(#onClickSearchConditionItemToSearch, [searchCondition]),
    returnValueForMissingStub: null,
  );

  @override
  void onClickClearAllRecordList() => super.noSuchMethod(
    Invocation.method(#onClickClearAllRecordList, []),
    returnValueForMissingStub: null,
  );

  @override
  void onChangeUserSearchInput(String? search) => super.noSuchMethod(
    Invocation.method(#onChangeUserSearchInput, [search]),
    returnValueForMissingStub: null,
  );

  @override
  void onClickClearSearchInput() => super.noSuchMethod(
    Invocation.method(#onClickClearSearchInput, []),
    returnValueForMissingStub: null,
  );

  @override
  void goBackPage() => super.noSuchMethod(
    Invocation.method(#goBackPage, []),
    returnValueForMissingStub: null,
  );

  @override
  String getTitle() =>
      (super.noSuchMethod(
            Invocation.method(#getTitle, []),
            returnValue: _i10.dummyValue<String>(
              this,
              Invocation.method(#getTitle, []),
            ),
            returnValueForMissingStub: _i10.dummyValue<String>(
              this,
              Invocation.method(#getTitle, []),
            ),
          )
          as String);

  @override
  bool isFromAssetList() =>
      (super.noSuchMethod(
            Invocation.method(#isFromAssetList, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void onClickClearTheConditions() => super.noSuchMethod(
    Invocation.method(#onClickClearTheConditions, []),
    returnValueForMissingStub: null,
  );

  @override
  bool commonSearchConditionsDialog() =>
      (super.noSuchMethod(
            Invocation.method(#commonSearchConditionsDialog, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i11.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i12.ErrorHandlingMode? mode = _i12.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Disposer addListener(_i13.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i13.Disposer);

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i14.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Disposer addListenerId(Object? key, _i13.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i13.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}
