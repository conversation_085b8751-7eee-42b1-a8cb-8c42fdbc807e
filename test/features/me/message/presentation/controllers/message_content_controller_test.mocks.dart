// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/message/presentation/controllers/message_content_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i12;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i15;
import 'package:asset_force_mobile_v2/features/me/message/domain/repositories/message_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/me/message/domain/usecase/message_detail_usecase.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/me/message/presentation/models/message_ui_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_s3_upload_url_response.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/shared/presentation/controllers/download_controller.dart'
    as _i9;
import 'package:get/get.dart' as _i4;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i11;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i14;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeMessageRepository_0 extends _i1.SmartFake
    implements _i2.MessageRepository {
  _FakeMessageRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMessageUIModel_1 extends _i1.SmartFake
    implements _i3.MessageUIModel {
  _FakeMessageUIModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxDouble_2 extends _i1.SmartFake implements _i4.RxDouble {
  _FakeRxDouble_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_3 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_4<T> extends _i1.SmartFake
    implements _i4.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedS3UploadUrlResponse_5 extends _i1.SmartFake
    implements _i6.SharedS3UploadUrlResponse {
  _FakeSharedS3UploadUrlResponse_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MessageDetailUsecase].
///
/// See the documentation for Mockito's code generation for more information.
class MockMessageDetailUsecase extends _i1.Mock
    implements _i7.MessageDetailUsecase {
  MockMessageDetailUsecase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.MessageRepository get messageRepository =>
      (super.noSuchMethod(
            Invocation.getter(#messageRepository),
            returnValue: _FakeMessageRepository_0(
              this,
              Invocation.getter(#messageRepository),
            ),
          )
          as _i2.MessageRepository);

  @override
  _i8.Future<_i3.MessageUIModel> call(int? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#call, [notificationId]),
            returnValue: _i8.Future<_i3.MessageUIModel>.value(
              _FakeMessageUIModel_1(
                this,
                Invocation.method(#call, [notificationId]),
              ),
            ),
          )
          as _i8.Future<_i3.MessageUIModel>);
}

/// A class which mocks [DownloadController].
///
/// See the documentation for Mockito's code generation for more information.
class MockDownloadController extends _i1.Mock
    implements _i9.DownloadController {
  MockDownloadController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.RxDouble get downloadProgress =>
      (super.noSuchMethod(
            Invocation.getter(#downloadProgress),
            returnValue: _FakeRxDouble_2(
              this,
              Invocation.getter(#downloadProgress),
            ),
          )
          as _i4.RxDouble);

  @override
  set downloadProgress(_i4.RxDouble? _downloadProgress) => super.noSuchMethod(
    Invocation.setter(#downloadProgress, _downloadProgress),
    returnValueForMissingStub: null,
  );

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  _i4.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_4<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i4.InternalFinalCallback<void>);

  @override
  _i4.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_4<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i4.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(Invocation.getter(#listeners), returnValue: 0)
          as int);

  @override
  _i8.Future<void> downloadFile(String? url, String? fileName) =>
      (super.noSuchMethod(
            Invocation.method(#downloadFile, [url, fileName]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i10.ErrorHandlingMode? mode = _i10.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListener(_i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
          )
          as _i11.Disposer);

  @override
  void removeListener(_i12.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i12.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListenerId(Object? key, _i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
          )
          as _i11.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [S3Repository].
///
/// See the documentation for Mockito's code generation for more information.
class MockS3Repository extends _i1.Mock implements _i13.S3Repository {
  MockS3Repository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.Future<String> getTurl(String? filePath) =>
      (super.noSuchMethod(
            Invocation.method(#getTurl, [filePath]),
            returnValue: _i8.Future<String>.value(
              _i14.dummyValue<String>(
                this,
                Invocation.method(#getTurl, [filePath]),
              ),
            ),
          )
          as _i8.Future<String>);

  @override
  _i8.Future<_i6.SharedS3UploadUrlResponse> getS3UploadUrl(String? fileName) =>
      (super.noSuchMethod(
            Invocation.method(#getS3UploadUrl, [fileName]),
            returnValue: _i8.Future<_i6.SharedS3UploadUrlResponse>.value(
              _FakeSharedS3UploadUrlResponse_5(
                this,
                Invocation.method(#getS3UploadUrl, [fileName]),
              ),
            ),
          )
          as _i8.Future<_i6.SharedS3UploadUrlResponse>);

  @override
  _i8.Future<bool> deleteFileFromS3(List<String>? fileToDelete) =>
      (super.noSuchMethod(
            Invocation.method(#deleteFileFromS3, [fileToDelete]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i5.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i8.Future<dynamic>.value(),
          )
          as _i8.Future<dynamic>);

  @override
  _i8.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i8.Future<dynamic>.value(),
          )
          as _i8.Future<dynamic>);

  @override
  _i8.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i8.Future<dynamic> toAssetDetail(_i15.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i8.Future<dynamic>.value(),
          )
          as _i8.Future<dynamic>);
}
