import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_all_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_assemble_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_count_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/enums/navigation_action_enum.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'category_controller_test.mocks.dart';

// 创建一个测试专用的子类，重写UI相关方法
class TestCategoryController extends CategoryController {
  TestCategoryController({
    required super.getCategoryCountUseCase,
    required super.getCategoryAssembleListUseCase,
    required super.getCategoryAllListUseCase,
    required super.navigationService,
  });

  // Already existing overrides
  @override
  Future<void> showLoading() async => Future.value();

  @override
  void hideLoading() {}

  // Add this new override to mock exception handling
  @override
  Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    ErrorHandlingMode mode = ErrorHandlingMode.dialog,
  ]) async {
    // Just log the exception or do nothing in tests
    // You could also set a flag to verify the exception was handled
    // return Future.value();

    // Alternatively, if you want to verify the exception was called:
    exceptionHandled = true;
    exceptionType = exception.runtimeType;
  }

  // Add these properties to track exception handling
  bool exceptionHandled = false;
  Type? exceptionType;
}

@GenerateMocks([GetCategoryCountUseCase, GetCategoryAssembleListUseCase, GetCategoryAllListUseCase, NavigationService])
void main() {
  late TestCategoryController controller;
  late MockGetCategoryCountUseCase mockGetCategoryCountUseCase;
  late MockGetCategoryAssembleListUseCase mockGetCategoryAssembleListUseCase;
  late MockGetCategoryAllListUseCase mockGetCategoryAllListUseCase;
  late MockNavigationService mockNavigationService;

  setUp(() {
    LogUtil.initialize();
    TestWidgetsFlutterBinding.ensureInitialized();
    mockGetCategoryCountUseCase = MockGetCategoryCountUseCase();
    mockGetCategoryAssembleListUseCase = MockGetCategoryAssembleListUseCase();
    mockGetCategoryAllListUseCase = MockGetCategoryAllListUseCase();
    mockNavigationService = MockNavigationService();

    controller = TestCategoryController(
      getCategoryCountUseCase: mockGetCategoryCountUseCase,
      getCategoryAssembleListUseCase: mockGetCategoryAssembleListUseCase,
      getCategoryAllListUseCase: mockGetCategoryAllListUseCase,
      navigationService: mockNavigationService,
    );

    Get.testMode = true;
  });

  tearDown(() {
    controller.onClose();
    Get.delete<CategoryController>();
  });

  group('Lifecycle Methods Tests', () {
    test('onClose should clear categoryList and categoryCountList', () {
      // Set some initial data
      controller.state.categoryList.value = [
        CategoryData(
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemType: 'normal',
            itemSubId: 10,
            itemDisplayName: 'Test',
            itemName: 'Test',
          ),
        ),
      ];

      controller.state.categoryCountList.value = [
        CategoryItemValueModel(categoryItemValue: 'Test', count: 10, homeImageMobileDisplayFlg: false),
      ];

      expect(controller.state.categoryList.isNotEmpty, true);
      expect(controller.state.categoryCountList.isNotEmpty, true);

      // Call onClose method
      controller.onClose();

      // Verify lists are cleared
      expect(controller.state.categoryList.isEmpty, true);
      expect(controller.state.categoryCountList.isEmpty, true);
    });
  });

  group('Navigation Methods Tests', () {
    test('backBtnClick should call handleNavigation with back action when categoryList is empty', () {
      // Prepare empty categoryList
      controller.state.categoryList.clear();

      // Call the method
      controller.backBtnClick();

      // Verify handleNavigation was called with correct params
      verify(
        mockNavigationService.goBack(result: {'assetSearchConditionList': [], 'selectedCategoryTitle': []}),
      ).called(1);
    });

    test(
      'backBtnClick should call handleNavigation with back action and payload when categoryList has more than one category',
      () async {
        // Setup mock for getCategoryAllListUseCase methods
        when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
          (_) async =>
              GetCategoryAllListResult(categoryList: [], categoryCountList: [], isCompleted: false, isEmpty: false),
        );

        // Mock isClickCurrentLevel method - this was missing
        when(mockGetCategoryAllListUseCase.isClickCurrentLevel(any, any)).thenReturn(false);

        // Prepare categoryList with multiple items
        controller.state.categoryList.value = [
          CategoryData(
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemType: 'normal',
              itemSubId: 10,
              itemDisplayName: 'Level 1',
              itemName: 'Level 1',
            ),
          ),
          CategoryData(
            categoryInfo: const CategoryModel(
              itemId: 2,
              itemType: 'normal',
              itemSubId: 20,
              itemDisplayName: 'Level 2',
              itemName: 'Level 2',
            ),
          ),
        ];

        // Call the method
        controller.backBtnClick();

        // 等待异步操作执行完毕
        await Future.delayed(Duration.zero);

        // Verify that isClickCurrentLevel was called
        verify(mockGetCategoryAllListUseCase.isClickCurrentLevel(any, any)).called(1);

        // Verify that _getCategoryAllList was called through the call to the useCase
        verify(mockGetCategoryAllListUseCase.call(any)).called(1);
      },
    );

    test('handleNavigation should call appropriate handler methods based on action', () async {
      // Setup mock for getCategoryAllListUseCase to use in _handleForward
      when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
        (_) async =>
            GetCategoryAllListResult(categoryList: [], categoryCountList: [], isCompleted: false, isEmpty: false),
      );

      // Test forward action
      final forwardPayload = CategoryItemValueModel(
        categoryItemValue: 'Test',
        count: 10,
        homeImageMobileDisplayFlg: false,
      );
      controller.handleNavigation(NavigationActionEnum.forward, payload: forwardPayload);

      // 等待异步操作执行完毕
      await Future.delayed(Duration.zero);

      // Verify getCategoryAllListUseCase was called (indicating _handleForward was executed)
      verify(mockGetCategoryAllListUseCase.call(any)).called(1);

      // Reset mocks for next test
      reset(mockGetCategoryAllListUseCase);
      when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
        (_) async =>
            GetCategoryAllListResult(categoryList: [], categoryCountList: [], isCompleted: false, isEmpty: false),
      );

      // Test back action with null payload
      controller.handleNavigation(NavigationActionEnum.back, payload: null);

      // 等待异步操作执行完毕
      await Future.delayed(Duration.zero);

      // Verify navigationService.goBack was called (indicating _handleBack was executed)
      verify(
        mockNavigationService.goBack(result: {'assetSearchConditionList': [], 'selectedCategoryTitle': []}),
      ).called(1);

      // Test none action (should do nothing)
      clearInteractions(mockNavigationService);
      clearInteractions(mockGetCategoryAllListUseCase);

      controller.handleNavigation(NavigationActionEnum.none);

      // Verify no methods were called
      verifyZeroInteractions(mockNavigationService);
      verifyZeroInteractions(mockGetCategoryAllListUseCase);
    });
  });

  group('Forward Navigation Tests', () {
    test('handleNavigation with forward action should process category selection', () async {
      // Setup mock for getCategoryAllListUseCase
      when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
        (_) async =>
            GetCategoryAllListResult(categoryList: [], categoryCountList: [], isCompleted: false, isEmpty: false),
      );

      // Prepare payload
      final payload = CategoryItemValueModel(
        categoryItemValue: 'Test Category',
        count: 10,
        homeImageMobileDisplayFlg: false,
      );

      // Call the method
      controller.handleNavigation(NavigationActionEnum.forward, payload: payload);

      // 等待异步操作执行完毕
      await Future.delayed(Duration.zero);

      // Verify getCategoryAllListUseCase was called with correct Info object
      final captured = verify(mockGetCategoryAllListUseCase.call(captureAny)).captured.first;
      expect(captured.info.titleValue, 'Test Category');
    });

    test('handleNavigation with forward action should handle completed category selection', () async {
      // Setup mock for getCategoryAllListUseCase to return completed result
      when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
        (_) async => GetCategoryAllListResult(
          categoryList: [],
          categoryCountList: [],
          isCompleted: true, // Mark as completed
          isEmpty: false,
        ),
      );

      // Prepare payload
      final payload = CategoryItemValueModel(
        categoryItemValue: 'Test Category',
        count: 10,
        homeImageMobileDisplayFlg: false,
      );

      // Call the method
      controller.handleNavigation(NavigationActionEnum.forward, payload: payload);

      // 等待异步操作执行完毕
      await Future.delayed(Duration.zero);

      verify(
        mockNavigationService.goBack(result: {'assetSearchConditionList': [], 'selectedCategoryTitle': []}),
      ).called(1);
    });

    test('handleNavigation with invalid payload type should handle exception', () {
      // Setup getCategoryAllListUseCase mock
      when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
        (_) async =>
            GetCategoryAllListResult(categoryList: [], categoryCountList: [], isCompleted: false, isEmpty: false),
      );

      // Reset the tracking properties before the test
      controller.exceptionHandled = false;
      controller.exceptionType = null;

      // Call with invalid payload type
      controller.handleNavigation(NavigationActionEnum.forward, payload: 'invalid-payload');

      // Verify getCategoryAllListUseCase was not called
      verifyNever(mockGetCategoryAllListUseCase.call(any));

      // Verify the exception was handled
      expect(controller.exceptionHandled, true);
      expect(controller.exceptionType, equals(SystemException));
    });
  });

  group('Back Navigation Tests', () {
    test('handleNavigation with back action and null payload should navigate back', () {
      // Call the method with null payload
      controller.handleNavigation(NavigationActionEnum.back, payload: null);

      // Verify navigation.goBack was called
      verify(
        mockNavigationService.goBack(result: {'assetSearchConditionList': [], 'selectedCategoryTitle': []}),
      ).called(1);
    });

    test('handleNavigation with back action and valid payload should process navigation', () async {
      // Setup mock for getCategoryAllListUseCase
      when(mockGetCategoryAllListUseCase.call(any)).thenAnswer(
        (_) async =>
            GetCategoryAllListResult(categoryList: [], categoryCountList: [], isCompleted: false, isEmpty: false),
      );

      // Setup mock for isClickCurrentLevel
      when(mockGetCategoryAllListUseCase.isClickCurrentLevel(any, any)).thenReturn(false);

      // Prepare payload
      final payload = CategoryData(
        categoryInfo: const CategoryModel(
          itemId: 1,
          itemType: 'normal',
          itemSubId: 10,
          itemDisplayName: 'Test',
          itemName: 'Test',
        ),
      );

      // Call the method
      controller.handleNavigation(NavigationActionEnum.back, payload: payload);

      await Future.delayed(Duration.zero);

      // Verify getCategoryAllListUseCase was called
      verify(mockGetCategoryAllListUseCase.call(any)).called(1);
    });

    test('handleNavigation with back action and current level should do nothing', () async {
      // Setup mock for isClickCurrentLevel to return true (same level)
      when(mockGetCategoryAllListUseCase.isClickCurrentLevel(any, any)).thenReturn(true);

      // Prepare current category list
      controller.state.categoryList.value = [
        CategoryData(
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemType: 'normal',
            itemSubId: 10,
            itemDisplayName: 'Current',
            itemName: 'Current',
          ),
        ),
      ];

      // Prepare payload with same category info
      final payload = CategoryData(
        categoryInfo: const CategoryModel(
          itemId: 1,
          itemType: 'normal',
          itemSubId: 10,
          itemDisplayName: 'Current',
          itemName: 'Current',
        ),
      );

      // Call the method
      controller.handleNavigation(NavigationActionEnum.back, payload: payload);

      await Future.delayed(Duration.zero);

      // Verify getCategoryAllListUseCase was not called
      verifyNever(mockGetCategoryAllListUseCase.call(any));
    });
  });
}
