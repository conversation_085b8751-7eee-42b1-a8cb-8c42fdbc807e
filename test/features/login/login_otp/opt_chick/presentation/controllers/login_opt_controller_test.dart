import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/countdown_timer_service.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/controllers/login_add_new_sms_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/domain/usecases/opt_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/controllers/login_opt_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'login_opt_controller_test.mocks.dart';

// 生成所有需要的Mock类
@GenerateNiceMocks([
  MockSpec<OptUsecase>(),
  MockSpec<TenantUseCase>(),
  MockSpec<NavigationService>(),
  MockSpec<DialogService>(),
  MockSpec<CountdownTimerService>(),
])
void main() {
  group('LoginOptController Tests', () {
    late LoginOptControllerTest testHelper;

    setUp(() {
      testHelper = LoginOptControllerTest();
      testHelper.setUp();
    });

    tearDown(() {
      testHelper.tearDown();
    });

    group('基础结构测试', () {
      test('控制器创建时应初始化为正确的初始状态', () {
        testHelper.verifyInitialState();
      });

      test('应正确注入所有依赖', () {
        expect(testHelper.controller.optUsecase, equals(testHelper.mockOptUsecase));
        expect(testHelper.controller.tenantUseCase, equals(testHelper.mockTenantUseCase));
        expect(testHelper.controller.navigationService, equals(testHelper.mockNavigationService));
        expect(testHelper.controller.dialogService, equals(testHelper.mockDialogService));
        expect(testHelper.controller.countdownTimerService, equals(testHelper.mockCountdownTimerService));
      });
    });

    group('initParams()方法测试', () {
      test('当传入SMS场景数据时应正确初始化状态', () {
        // Arrange
        final testData = TestDataFactory.createSmsScenario();

        // Act
        testHelper.controller.initParams(testData);

        // Assert
        expect(testHelper.controller.state.showPhoneNumber.value, equals('1234567890'));
        expect(testHelper.controller.state.nationCode.value, equals('+81'));
        expect(testHelper.controller.state.userUnCode.value, equals(4)); // codeSms
        expect(testHelper.controller.state.userEmail.value, equals('<EMAIL>'));
        expect(testHelper.controller.state.showOptWidget.value, equals(ShowOptWidgetEnum.sms));
      });

      test('当传入新SMS场景数据时应正确初始化状态', () {
        // Arrange
        final testData = TestDataFactory.createNewSmsScenario();

        // Act
        testHelper.controller.initParams(testData);

        // Assert
        expect(testHelper.controller.state.showOptWidget.value, equals(ShowOptWidgetEnum.newSms));
        expect(testHelper.controller.state.userUnCode.value, equals(3)); // codeNewSms
      });

      test('当传入Email场景数据时应正确初始化状态', () {
        // Arrange
        final testData = TestDataFactory.createEmailScenario();

        // Act
        testHelper.controller.initParams(testData);

        // Assert
        expect(testHelper.controller.state.showOptWidget.value, equals(ShowOptWidgetEnum.email));
        expect(testHelper.controller.state.userUnCode.value, equals(6)); // codeEmail
      });

      test('当传入null optTenant时应抛出SystemException', () {
        // Arrange
        final testData = TestDataFactory.createSelectTenant(optTenant: null, useDefaultOptTenant: false);

        // Act & Assert
        expect(() => testHelper.controller.initParams(testData), throwsA(isA<SystemException>()));
      });

      test('当传入非SelectTenantModel数据时应不做任何操作', () {
        // Arrange
        final invalidData = 'invalid_data';

        // Act
        testHelper.controller.initParams(invalidData);

        // Assert - 状态应保持初始值
        testHelper.verifyInitialState();
      });
    });

    group('sendSMS()方法测试', () {
      setUp(() {
        // 为sendSMS测试准备测试数据
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('成功发送短信应启动倒计时', () async {
        // Arrange
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {});

        // Act
        await testHelper.controller.sendSMS();

        // Assert
        expect(testHelper.controller.state.isCountingDown.value, isTrue);
        expect(testHelper.controller.state.countdownSeconds.value, equals(60));
        verify(
          testHelper.mockCountdownTimerService.start(
            durationSeconds: 60,
            onTick: anyNamed('onTick'),
            onEnd: anyNamed('onEnd'),
          ),
        ).called(1);
        verify(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: ShowOptWidgetEnum.sms),
        ).called(1);
      });

      test('当正在倒计时时应防止重复点击', () async {
        // Arrange
        testHelper.controller.state.isCountingDown.value = true;

        // Act
        await testHelper.controller.sendSMS();

        // Assert
        verifyNever(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        );
      });

      test('发送短信失败应停止倒计时并处理异常', () async {
        // Arrange
        final testException = Exception('网络错误');
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenThrow(testException);

        // Act & Assert
        expect(() => testHelper.controller.sendSMS(), throwsA(equals(testException)));

        // 验证倒计时被停止
        verify(testHelper.mockCountdownTimerService.stop()).called(1);
      });
    });

    group('sendEmail()方法测试', () {
      setUp(() {
        // 为sendEmail测试准备Email场景数据
        final testData = TestDataFactory.createEmailScenario();
        testHelper.controller.initParams(testData);
      });

      test('成功发送邮件验证码', () async {
        // Arrange
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {});

        // Act
        await testHelper.controller.sendEmail();

        // Assert
        verify(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: ShowOptWidgetEnum.email),
        ).called(1);
      });

      test('当optTenant为null时应验证异常处理', () async {
        // Arrange - 创建新的控制器实例确保未初始化
        final newTestHelper = LoginOptControllerTest();
        newTestHelper.setUp();

        // Act & Assert - 测试未初始化的情况
        try {
          await newTestHelper.controller.sendEmail();
          fail('应该抛出SystemException');
        } catch (e) {
          expect(e, isA<SystemException>());
        } finally {
          newTestHelper.tearDown();
        }
      });

      test('发送邮件失败应处理异常', () async {
        // Arrange
        final testException = Exception('邮件发送失败');
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenThrow(testException);

        // Act & Assert
        expect(() => testHelper.controller.sendEmail(), throwsA(equals(testException)));
      });
    });

    group('login()方法测试', () {
      setUp(() {
        // 为login测试准备SMS场景数据
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
        testHelper.controller.controllerNCode.text = '123456'; // 设置验证码
      });

      test('验证码为空时应处理BusinessException', () async {
        // Arrange
        testHelper.controller.controllerNCode.text = '';

        // Act & Assert
        expect(() => testHelper.controller.login(), throwsA(isA<BusinessException>()));
      });

      test('SMS场景应调用tenantUseCase并处理结果', () async {
        // Arrange
        when(testHelper.mockTenantUseCase.call(any)).thenAnswer((_) async => throw Exception('Test complete'));

        // Act & Assert
        expect(() => testHelper.controller.login(), throwsA(isA<Exception>()));
      });

      test('Email场景应调用tenantUseCase', () async {
        // Arrange
        final emailData = TestDataFactory.createEmailScenario();
        testHelper.controller.initParams(emailData);
        testHelper.controller.controllerNCode.text = '123456';

        when(testHelper.mockTenantUseCase.call(any)).thenAnswer((_) async => throw Exception('Test complete'));

        // Act & Assert
        expect(() => testHelper.controller.login(), throwsA(isA<Exception>()));
      });
    });

    group('emailLogin()方法测试', () {
      setUp(() {
        // 为emailLogin测试准备Email场景数据
        final testData = TestDataFactory.createEmailScenario();
        testHelper.controller.initParams(testData);
      });

      test('应调用optUsecase进行邮件登录', () async {
        // Arrange
        when(testHelper.mockOptUsecase.call(any)).thenAnswer((_) async => throw Exception('Test complete'));

        // Act & Assert
        expect(() => testHelper.controller.emailLogin(), throwsA(isA<Exception>()));
      });

      test('当未初始化时应处理SystemException', () async {
        // Arrange - 使用新的控制器实例，未初始化
        final newTestHelper = LoginOptControllerTest();
        newTestHelper.setUp();

        // Act & Assert
        expect(() => newTestHelper.controller.emailLogin(), throwsA(isA<SystemException>()));

        // Cleanup
        newTestHelper.tearDown();
      });
    });

    group('导航功能集成测试', () {
      setUp(() {
        // 为导航测试准备基础数据
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('settingCellPhoneNumberOnClick应导航到设置手机号页面', () async {
        // Arrange
        final mockPhoneModel = TestDataFactory.createPhoneModel();
        when(
          testHelper.mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => mockPhoneModel);

        // Act
        await testHelper.controller.settingCellPhoneNumberOnClick();

        // Assert
        verify(testHelper.mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).called(1);

        // 验证状态更新
        expect(testHelper.controller.state.showPhoneNumber.value, equals('9876543210'));
        expect(testHelper.controller.state.nationCode.value, equals('+86'));
      });

      test('settingCellPhoneNumberOnClick返回空值时不应更新状态', () async {
        // Arrange
        when(
          testHelper.mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => null);

        final originalPhone = testHelper.controller.state.showPhoneNumber.value;
        final originalCode = testHelper.controller.state.nationCode.value;

        // Act
        await testHelper.controller.settingCellPhoneNumberOnClick();

        // Assert
        expect(testHelper.controller.state.showPhoneNumber.value, equals(originalPhone));
        expect(testHelper.controller.state.nationCode.value, equals(originalCode));
      });

      test('goBackPage应调用navigationService.goBack', () {
        // Act
        testHelper.controller.goBackPage();

        // Assert
        verify(testHelper.mockNavigationService.goBack()).called(1);
      });
    });

    group('倒计时功能集成测试', () {
      setUp(() {
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('倒计时回调函数应正确更新状态', () async {
        // Arrange
        late Function(int) onTickCallback;
        late Function() onEndCallback;

        when(
          testHelper.mockCountdownTimerService.start(
            durationSeconds: anyNamed('durationSeconds'),
            onTick: anyNamed('onTick'),
            onEnd: anyNamed('onEnd'),
          ),
        ).thenAnswer((invocation) {
          onTickCallback = invocation.namedArguments[#onTick];
          onEndCallback = invocation.namedArguments[#onEnd];
        });

        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {});

        // Act
        await testHelper.controller.sendSMS();

        // Simulate countdown tick
        onTickCallback(30);
        expect(testHelper.controller.state.countdownSeconds.value, equals(30));

        // Simulate countdown end
        onEndCallback();
        expect(testHelper.controller.state.isCountingDown.value, isFalse);
      });
    });

    group('辅助功能测试', () {
      setUp(() {
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('addNewSMS应验证手机号并更新状态', () async {
        // Arrange
        testHelper.controller.state.showPhoneNumber.value = '1234567890';
        testHelper.controller.state.nationCode.value = '+81';

        // Act
        await testHelper.controller.addNewSMS();

        // Assert
        expect(testHelper.controller.state.showOptWidget.value, equals(ShowOptWidgetEnum.sms));
      });

      test('addNewSMS手机号验证失败应显示错误对话框', () async {
        // Arrange
        testHelper.controller.state.showPhoneNumber.value = ''; // 无效手机号
        testHelper.controller.state.nationCode.value = '';

        // Act
        await testHelper.controller.addNewSMS();

        // Assert
        verify(testHelper.mockDialogService.show(content: anyNamed('content'), type: anyNamed('type'))).called(1);
      });

      test('hiddenPhoneNumber应返回隐藏后的手机号', () {
        // Act
        final result = testHelper.controller.hiddenPhoneNumber(phoneNo: '1234567890');

        // Assert
        expect(result, isNotNull);
        // 这里我们只验证方法被调用，具体的隐藏逻辑由IpnService处理
      });
    });

    group('生命周期管理测试', () {
      test('onClose应正确清理资源', () {
        // Arrange - 使用独立的测试实例避免重复dispose
        final isolatedTestHelper = LoginOptControllerTest();
        isolatedTestHelper.setUp();

        final testData = TestDataFactory.createSmsScenario();
        isolatedTestHelper.controller.initParams(testData);
        isolatedTestHelper.controller.controllerNCode.text = 'test';

        // Act
        isolatedTestHelper.controller.onClose();

        // Assert
        verify(isolatedTestHelper.mockCountdownTimerService.stop()).called(1);
        expect(isolatedTestHelper.controller.state.isCountingDown.value, isFalse);

        // 不需要调用tearDown，因为已经通过onClose清理了
      });
    });

    group('异常处理集成测试', () {
      setUp(() {
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('addNewSMS业务异常应被正确处理', () async {
        // Arrange - 设置会导致业务异常的手机号
        testHelper.controller.state.showPhoneNumber.value = 'invalid';
        testHelper.controller.state.nationCode.value = 'invalid';

        // Act
        await testHelper.controller.addNewSMS();

        // Assert - 验证异常处理
        verify(testHelper.mockDialogService.show(content: anyNamed('content'), type: anyNamed('type'))).called(1);
      });

      test('导航异常应不影响状态', () async {
        // Arrange
        when(
          testHelper.mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenThrow(Exception('导航错误'));

        // Act & Assert
        expect(() => testHelper.controller.settingCellPhoneNumberOnClick(), throwsA(isA<Exception>()));
      });
    });

    group('边界情况测试', () {
      setUp(() {
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('验证码输入边界情况 - 空字符串', () async {
        // Arrange
        testHelper.controller.controllerNCode.text = '';

        // Act & Assert
        expect(() => testHelper.controller.login(), throwsA(isA<BusinessException>()));
      });

      test('验证码输入边界情况 - 超长字符串', () async {
        // Arrange
        final veryLongCode = 'a' * 1000; // 1000个字符
        testHelper.controller.controllerNCode.text = veryLongCode;

        when(testHelper.mockTenantUseCase.call(any)).thenAnswer((_) async => TestDataFactory.createGetTokenResult());

        // Act
        await testHelper.controller.login();

        // Assert - 应该正常处理，不抛出异常
        verify(testHelper.mockTenantUseCase.call(any)).called(1);
      });

      test('验证码输入边界情况 - 特殊字符', () async {
        // Arrange
        testHelper.controller.controllerNCode.text = '!@#\$%^&*()';

        when(testHelper.mockTenantUseCase.call(any)).thenAnswer((_) async => TestDataFactory.createGetTokenResult());

        // Act
        await testHelper.controller.login();

        // Assert - 应该正常处理特殊字符
        verify(testHelper.mockTenantUseCase.call(any)).called(1);
      });

      test('手机号边界情况 - 空字符串', () async {
        // Arrange
        testHelper.controller.state.showPhoneNumber.value = '';
        testHelper.controller.state.nationCode.value = '';

        // Act
        await testHelper.controller.addNewSMS();

        // Assert
        verify(testHelper.mockDialogService.show(content: anyNamed('content'), type: anyNamed('type'))).called(1);
      });

      test('手机号边界情况 - 超长字符串', () async {
        // Arrange
        final veryLongPhone = '1' * 50; // 50位数字
        testHelper.controller.state.showPhoneNumber.value = veryLongPhone;
        testHelper.controller.state.nationCode.value = '+86';

        // Act
        await testHelper.controller.addNewSMS();

        // Assert - 应该被验证逻辑处理
        expect(testHelper.controller.state.showOptWidget.value, equals(ShowOptWidgetEnum.sms));
      });

      test('倒计时边界情况 - 0秒初始值', () async {
        // Arrange
        late Function(int) onTickCallback;
        late Function() onEndCallback;

        when(
          testHelper.mockCountdownTimerService.start(
            durationSeconds: anyNamed('durationSeconds'),
            onTick: anyNamed('onTick'),
            onEnd: anyNamed('onEnd'),
          ),
        ).thenAnswer((invocation) {
          onTickCallback = invocation.namedArguments[#onTick];
          onEndCallback = invocation.namedArguments[#onEnd];
        });

        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {});

        // Act
        await testHelper.controller.sendSMS();

        // Simulate countdown reaching 0
        onTickCallback(0);

        // Assert
        expect(testHelper.controller.state.countdownSeconds.value, equals(0));

        // Simulate end callback
        onEndCallback();
        expect(testHelper.controller.state.isCountingDown.value, isFalse);
      });
    });

    group('并发操作安全性测试', () {
      setUp(() {
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('快速连续点击sendSMS应被防抖保护', () async {
        // Arrange
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {
          // 模拟网络延迟
          await Future.delayed(Duration(milliseconds: 100));
        });

        // Act - 快速连续调用3次
        final List<Future<dynamic>> futures = [
          testHelper.controller.sendSMS(),
          testHelper.controller.sendSMS(),
          testHelper.controller.sendSMS(),
        ];

        await Future.wait(futures);

        // Assert - 只应该调用一次，其他被防抖保护
        verify(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).called(1);
      });

      test('sendEmail不受倒计时状态影响，但会被正常调用', () async {
        // Arrange
        testHelper.controller.state.isCountingDown.value = true;

        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {});

        // Act - 连续调用多次
        await testHelper.controller.sendEmail();
        await testHelper.controller.sendEmail();
        await testHelper.controller.sendEmail();

        // Assert - sendEmail不检查倒计时状态，会被正常调用
        // 注意：根据实际运行结果，sendEmail内部传递的是sms参数
        verify(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: ShowOptWidgetEnum.sms),
        ).called(3);
      });

      test('同时进行login和sendSMS操作的安全性', () async {
        // Arrange
        testHelper.controller.controllerNCode.text = '123456';

        when(testHelper.mockTenantUseCase.call(any)).thenAnswer((_) async {
          await Future.delayed(Duration(milliseconds: 50));
          return TestDataFactory.createGetTokenResult();
        });

        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {
          await Future.delayed(Duration(milliseconds: 50));
        });

        // Act - 同时执行login和sendSMS
        final List<Future<dynamic>> futures = [testHelper.controller.login(), testHelper.controller.sendSMS()];

        await Future.wait(futures);

        // Assert - 两个操作都应该执行
        verify(testHelper.mockTenantUseCase.call(any)).called(1);
        verify(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).called(1);
      });
    });

    group('异常恢复机制测试', () {
      setUp(() {
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);
      });

      test('网络异常后状态恢复', () async {
        // Arrange
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenThrow(Exception('网络连接失败'));

        // Act - 第一次失败
        try {
          await testHelper.controller.sendSMS();
        } catch (e) {
          // 期望的异常
        }

        // 验证倒计时被停止
        expect(testHelper.controller.state.isCountingDown.value, isFalse);

        // Arrange - 网络恢复
        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenAnswer((_) async {});

        // Act - 第二次成功
        await testHelper.controller.sendSMS();

        // Assert - 应该能正常工作
        expect(testHelper.controller.state.isCountingDown.value, isTrue);
      });

      test('多重异常场景处理', () async {
        // Arrange
        testHelper.controller.controllerNCode.text = ''; // 这会导致BusinessException

        when(testHelper.mockTenantUseCase.call(any)).thenThrow(Exception('服务器错误'));

        // Act & Assert - 应该抛出BusinessException（验证码为空）
        expect(() => testHelper.controller.login(), throwsA(isA<BusinessException>()));
      });

      test('异常后状态一致性验证', () async {
        // Arrange
        final initialState = {
          'showPhoneNumber': testHelper.controller.state.showPhoneNumber.value,
          'nationCode': testHelper.controller.state.nationCode.value,
          'userEmail': testHelper.controller.state.userEmail.value,
        };

        when(
          testHelper.mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
        ).thenThrow(Exception('测试异常'));

        // Act
        try {
          await testHelper.controller.sendSMS();
        } catch (e) {
          // 期望的异常
        }

        // Assert - 验证核心状态没有被破坏
        expect(testHelper.controller.state.showPhoneNumber.value, equals(initialState['showPhoneNumber']));
        expect(testHelper.controller.state.nationCode.value, equals(initialState['nationCode']));
        expect(testHelper.controller.state.userEmail.value, equals(initialState['userEmail']));
      });
    });

    group('生命周期压力测试', () {
      test('多次初始化和销毁的稳定性', () {
        // Act & Assert - 多次创建和销毁控制器
        for (int i = 0; i < 5; i++) {
          final tempTestHelper = LoginOptControllerTest();
          tempTestHelper.setUp();

          final testData = TestDataFactory.createSmsScenario();
          tempTestHelper.controller.initParams(testData);

          // 验证状态正确初始化
          expect(tempTestHelper.controller.state.showPhoneNumber.value, isNotEmpty);
          expect(tempTestHelper.controller.state.nationCode.value, isNotEmpty);

          // 清理
          tempTestHelper.controller.onClose();
        }
      });

      test('状态数据完整性验证', () {
        // Arrange
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);

        // 记录初始状态
        final originalPhone = testHelper.controller.state.showPhoneNumber.value;
        final originalCode = testHelper.controller.state.nationCode.value;
        final originalEmail = testHelper.controller.state.userEmail.value;

        // Act - 进行多种操作
        testHelper.controller.controllerNCode.text = 'test123';
        testHelper.controller.state.countdownSeconds.value = 30;
        testHelper.controller.state.isCountingDown.value = true;

        // Assert - 验证数据完整性
        expect(testHelper.controller.state.showPhoneNumber.value, equals(originalPhone));
        expect(testHelper.controller.state.nationCode.value, equals(originalCode));
        expect(testHelper.controller.state.userEmail.value, equals(originalEmail));
        expect(testHelper.controller.controllerNCode.text, equals('test123'));
        expect(testHelper.controller.state.countdownSeconds.value, equals(30));
        expect(testHelper.controller.state.isCountingDown.value, isTrue);
      });

      test('大量数据处理的性能稳定性', () {
        // Arrange - 创建大量测试数据
        final testData = TestDataFactory.createSmsScenario();
        testHelper.controller.initParams(testData);

        // Act - 大量快速操作
        for (int i = 0; i < 100; i++) {
          testHelper.controller.controllerNCode.text = 'code$i';
          testHelper.controller.state.countdownSeconds.value = i;

          // 验证每次操作后状态正确
          expect(testHelper.controller.controllerNCode.text, equals('code$i'));
          expect(testHelper.controller.state.countdownSeconds.value, equals(i));
        }

        // Assert - 最终状态应该正确
        expect(testHelper.controller.controllerNCode.text, equals('code99'));
        expect(testHelper.controller.state.countdownSeconds.value, equals(99));
      });
    });
  });
}

/// 测试数据工厂类
class TestDataFactory {
  /// 创建测试用的OptTenantModel
  static OptTenantModel createOptTenant({
    int reCode = 4, // LoginCodeStatus.codeSms.value
    String tel = '1234567890',
    String nationCode = '+81',
    String email = '<EMAIL>',
    String ticket = 'test-ticket',
    String tenantId = 'test-tenant-id',
    int userId = 123,
    String userName = 'testuser',
    String password = 'testpass',
  }) {
    return OptTenantModel(
      reCode: reCode,
      tel: tel,
      nationCode: nationCode,
      email: email,
      ticket: ticket,
      tenantId: tenantId,
      userId: userId,
      userName: userName,
      password: password,
    );
  }

  /// 创建测试用的SelectTenantModel
  static SelectTenantModel createSelectTenant({
    String zoneId = 'test-zone-id',
    OptTenantModel? optTenant,
    DealTenantModel? dealTenant,
    bool useDefaultOptTenant = true,
  }) {
    return SelectTenantModel(
      zoneId: zoneId,
      optTenant: useDefaultOptTenant ? (optTenant ?? createOptTenant()) : optTenant,
      dealTenant: dealTenant,
      dealDeeplink: null,
    );
  }

  /// 创建SMS场景的测试数据
  static SelectTenantModel createSmsScenario() {
    return createSelectTenant(optTenant: createOptTenant(reCode: LoginCodeStatus.codeSms.value));
  }

  /// 创建新SMS场景的测试数据
  static SelectTenantModel createNewSmsScenario() {
    return createSelectTenant(optTenant: createOptTenant(reCode: LoginCodeStatus.codeNewSms.value));
  }

  /// 创建Email场景的测试数据
  static SelectTenantModel createEmailScenario() {
    return createSelectTenant(optTenant: createOptTenant(reCode: LoginCodeStatus.codeEmail.value));
  }

  /// 创建RePhoneAndIpnCodeModel
  static RePhoneAndIpnCodeModel createPhoneModel({String phoneNum = '9876543210', String nationCode = '+86'}) {
    return RePhoneAndIpnCodeModel(phoneNum: phoneNum, nationCode: nationCode);
  }

  /// 创建GetTokenResultModel（用于TenantUseCase.call()返回值）
  static GetTokenResultModel createGetTokenResult({
    int code = 200,
    String msg = 'success',
    String? signInResult = 'test-signin-result',
  }) {
    return GetTokenResultModel(code: code, msg: msg, signInResult: signInResult);
  }
}

/// 测试用的Controller封装类
class TestLoginOptController extends LoginOptController {
  TestLoginOptController({
    required super.optUsecase,
    required super.tenantUseCase,
    required super.navigationService,
    required super.dialogService,
    required super.countdownTimerService,
  });

  // 重写加载相关方法以避免实际的UI操作
  @override
  Future<void> showLoading() async {
    // 测试中不显示实际的Loading
  }

  @override
  void hideLoading() {
    // 测试中不隐藏实际的Loading
  }

  @override
  Future<void> handleException(dynamic exception, [StackTrace? stackTrace, ErrorHandlingMode? mode]) async {
    // 测试中不处理实际的异常UI
    throw exception;
  }
}

/// 主测试类
class LoginOptControllerTest {
  late MockOptUsecase mockOptUsecase;
  late MockTenantUseCase mockTenantUseCase;
  late MockNavigationService mockNavigationService;
  late MockDialogService mockDialogService;
  late MockCountdownTimerService mockCountdownTimerService;
  late TestLoginOptController controller;

  /// 设置测试环境
  void setUp() {
    // 创建所有Mock对象
    mockOptUsecase = MockOptUsecase();
    mockTenantUseCase = MockTenantUseCase();
    mockNavigationService = MockNavigationService();
    mockDialogService = MockDialogService();
    mockCountdownTimerService = MockCountdownTimerService();

    // 创建测试控制器
    controller = TestLoginOptController(
      optUsecase: mockOptUsecase,
      tenantUseCase: mockTenantUseCase,
      navigationService: mockNavigationService,
      dialogService: mockDialogService,
      countdownTimerService: mockCountdownTimerService,
    );

    // 设置默认的Mock行为
    _setupDefaultMockBehavior();
  }

  /// 清理测试环境
  void tearDown() {
    controller.onClose();
    reset(mockOptUsecase);
    reset(mockTenantUseCase);
    reset(mockNavigationService);
    reset(mockDialogService);
    reset(mockCountdownTimerService);
  }

  /// 设置默认的Mock行为
  void _setupDefaultMockBehavior() {
    // OptUsecase默认行为
    when(
      mockOptUsecase.sendSMSCall(params: anyNamed('params'), showOptWidget: anyNamed('showOptWidget')),
    ).thenAnswer((_) async {});

    // TenantUseCase和OptUsecase的call方法暂不设置默认行为
    // 在具体测试中按需配置

    // NavigationService默认行为
    when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => null);

    // DialogService默认行为
    when(mockDialogService.show(content: anyNamed('content'), type: anyNamed('type'))).thenAnswer((_) async {});

    // CountdownTimerService默认行为
    when(mockCountdownTimerService.isRunning).thenReturn(false);
  }

  /// 验证所有Mock对象没有未预期的交互
  void verifyNoMoreInteractionsOnMocks() {
    verifyNoMoreInteractions(mockOptUsecase);
    verifyNoMoreInteractions(mockTenantUseCase);
    verifyNoMoreInteractions(mockNavigationService);
    verifyNoMoreInteractions(mockDialogService);
    verifyNoMoreInteractions(mockCountdownTimerService);
  }

  /// 验证初始状态
  void verifyInitialState() {
    expect(controller.state.showPhoneNumber.value, isEmpty);
    expect(controller.state.nationCode.value, isEmpty);
    expect(controller.state.userUnCode.value, equals(0)); // int类型，检查初始值0
    expect(controller.state.userEmail.value, isEmpty);
    expect(controller.state.isCountingDown.value, isFalse);
    expect(controller.state.countdownSeconds.value, equals(0)); // int类型，检查初始值0
    expect(controller.state.showOptWidget.value, isNull); // 初始为null
    expect(controller.controllerNCode.text, isEmpty);
  }
}

// ==================== 占位符 ====================
// 具体的测试用例将在Phase 3中添加
// Phase 3将添加：
// - initParams()方法测试
// - sendSMS()方法测试
// - sendEmail()方法测试
// - login()方法测试
// - emailLogin()方法测试
//
// Phase 4将添加：
// - 导航流程集成测试
// - 倒计时功能集成测试
// - 异常处理集成测试
//
// Phase 5将添加：
// - 边界情况测试
// - 生命周期测试
// - 资源清理测试
