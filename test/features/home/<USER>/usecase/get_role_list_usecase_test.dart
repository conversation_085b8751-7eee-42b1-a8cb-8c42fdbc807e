import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// Generate mocks for the HomeRepository
@GenerateMocks([HomeRepository])
import 'get_role_list_usecase_test.mocks.dart';

void main() {
  LogUtil.initialize();
  late GetRoleListUseCase useCase;
  late MockHomeRepository mockHomeRepository;

  setUp(() {
    mockHomeRepository = MockHomeRepository();
    useCase = GetRoleListUseCase(homeRepository: mockHomeRepository);
  });

  final tRoleList = [SharedRoleModel(roleId: 1, roleName: 'Admin'), SharedRoleModel(roleId: 2, roleName: 'User')];

  group('GetRoleListUseCase', () {
    test('should return role list when repository call is successful', () async {
      // arrange
      when(mockHomeRepository.getRoleList()).thenAnswer((_) async => tRoleList);

      // act
      final result = await useCase(null);

      // assert
      expect(result, equals(tRoleList));
      verify(mockHomeRepository.getRoleList());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should propagate BusinessException when repository throws it', () async {
      // arrange
      final tException = BusinessException('現在のグループ権限制限の取得に失敗しました');
      when(mockHomeRepository.getRoleList()).thenThrow(tException);

      // act & assert
      expect(
        () => useCase(null),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', '現在のグループ権限制限の取得に失敗しました')),
      );
      verify(mockHomeRepository.getRoleList());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should rethrow SystemException when repository throws it', () async {
      // arrange
      final tException = SystemException();
      when(mockHomeRepository.getRoleList()).thenThrow(tException);

      // act & assert
      expect(
        () => useCase(null),
        throwsA(same(tException)), // We expect the exact same exception object to be rethrown
      );
      verify(mockHomeRepository.getRoleList());
      verifyNoMoreInteractions(mockHomeRepository);
    });

    test('should handle empty role list properly', () async {
      // arrange
      when(mockHomeRepository.getRoleList()).thenAnswer((_) async => []);

      // act
      final result = await useCase(null);

      // assert
      expect(result, equals([]));
      verify(mockHomeRepository.getRoleList());
      verifyNoMoreInteractions(mockHomeRepository);
    });
  });
}
