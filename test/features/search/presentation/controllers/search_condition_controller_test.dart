import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/search/domain/enums/search_type.dart';
import 'package:asset_force_mobile_v2/features/search/domain/usecases/get_search_conditions_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'search_condition_controller_test.mocks.dart';

// Define constants consistent with the original code
const int searchIdConditionZero = 0;
const int searchIdConditionTwo = 2;

// Create a test-specific subclass that overrides UI-related methods
class TestSearchConditionController extends SearchConditionController {
  TestSearchConditionController({
    required super.getSearchConditionsUseCase,
    required super.navigationService,
    required super.dialogService,
  });

  // Override UI-related methods
  @override
  Future<void> showLoading() async => Future.value();

  @override
  void hideLoading() {}

  // Override exception handling methods
  @override
  Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    ErrorHandlingMode mode = ErrorHandlingMode.dialog,
  ]) async {
    exceptionHandled = true;
    exceptionType = exception.runtimeType;
  }

  // Add these properties to track exception handling
  bool exceptionHandled = false;
  Type? exceptionType;

  // Override onInit to avoid calling _loadData during initialization
  @override
  void onInit() {
    super.onInit();

    // Skip super.onInit() to avoid automatic _loadData call
  }

  // Method to set test parameters
  void setTestParams(GetSearchConditionParams testParams) {
    // Directly set the params field, which is annotated with @visibleForTesting
    params = testParams;
  }
}

@GenerateMocks([GetSearchConditionsUseCase, NavigationService, DialogService])
void main() {
  late TestSearchConditionController controller;
  late MockGetSearchConditionsUseCase mockGetSearchConditionsUseCase;
  late MockNavigationService mockNavigationService;
  late MockDialogService mockDialogService;

  setUp(() {
    LogUtil.initialize();
    TestWidgetsFlutterBinding.ensureInitialized();
    mockGetSearchConditionsUseCase = MockGetSearchConditionsUseCase();
    mockNavigationService = MockNavigationService();
    mockDialogService = MockDialogService();

    controller = TestSearchConditionController(
      getSearchConditionsUseCase: mockGetSearchConditionsUseCase,
      navigationService: mockNavigationService,
      dialogService: mockDialogService,
    );

    Get.testMode = true;
  });

  tearDown(() {
    controller.onClose();
    Get.delete<SearchConditionController>();
  });

  group('UI State and User Interaction Tests', () {
    test('onChangeUserSearchInput should update search input state', () {
      // Prepare test data
      const testInput = 'Test search keyword';

      // Call the method
      controller.onChangeUserSearchInput(testInput);

      // Verify the state is correctly updated
      expect(controller.state.searchInput.value, equals(testInput));
      expect(controller.textEditingController.text, equals(testInput));
    });

    test('onClickClearSearchInput should clear search input', () {
      // Set initial values
      controller.state.searchInput.value = 'Initial search keyword';
      controller.textEditingController.text = 'Initial search keyword';

      // Call the method
      controller.onClickClearSearchInput();

      // Verify the state is correctly cleared
      expect(controller.state.searchInput.value, equals(''));
      expect(controller.textEditingController.text, equals(''));
    });

    test('onClickSearchConditionItemToSearch should update selected search condition', () {
      // Prepare test data
      final testCondition = SearchConditionModel(searchId: 123, searchName: 'Test search condition');

      // Call the method
      controller.onClickSearchConditionItemToSearch(testCondition);

      // Verify the state is correctly updated
      expect(controller.state.selectedRadioCondition.value, equals(testCondition));
    });
  });

  group('Navigation Method Tests', () {
    test('goBackPage should call navigationService.goBack', () {
      // Call the method
      controller.goBackPage();

      // Verify navigationService.goBack is called
      verify(mockNavigationService.goBack()).called(1);
    });

    test('getTitle should return correct title based on SearchPageType', () {
      // Test for asset list page scenario
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.assetListPage,
          searchId: null,
          searchKey: null,
          assetTypeId: null,
        ),
      );
      expect(controller.getTitle(), equals('検索条件設定'));
    });

    test('getTitle should return correct title based on SearchPageType', () {
      // Test for other page scenario
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.actionListPage,
          searchId: null,
          searchKey: null,
          assetTypeId: null,
        ),
      );
      expect(controller.getTitle(), equals('検索'));
    });

    test('isFromAssetList should return correct value based on SearchPageType', () {
      // Test for other page scenario
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.actionListPage,
          searchId: null,
          searchKey: null,
          assetTypeId: null,
        ),
      );
      expect(controller.isFromAssetList(), isFalse);
    });

    test('isFromAssetList should return correct value based on SearchPageType', () {
      // Test for asset list page scenario
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.assetListPage,
          searchId: null,
          searchKey: null,
          assetTypeId: null,
        ),
      );
      expect(controller.isFromAssetList(), isTrue);
    });
  });

  group('Search History Record Tests', () {
    test('onClickClearAllRecordList should clear history search records', () async {
      // Mock dialogService.show to call onConfirm callback
      when(
        mockDialogService.show(
          type: DialogType.confirm,
          content: argThat(isA<String>(), named: 'content'),
          confirmText: argThat(isA<String>(), named: 'confirmText'),
          cancelText: argThat(isA<String>(), named: 'cancelText'),
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((invocation) {
        // Get and call onConfirm callback
        final onConfirm = invocation.namedArguments[const Symbol('onConfirm')] as Function;
        onConfirm();
        return Future.value();
      });

      // Prepare test data
      when(mockGetSearchConditionsUseCase.clearAllHistorySearchInput()).thenAnswer((_) async => Future.value());

      // Set initial history records
      controller.state.searchHistoryList.value = ['History 1', 'History 2'];

      // Call the method
      controller.onClickClearAllRecordList();

      // Wait for async operations to complete
      await Future.delayed(Duration.zero);

      // Verify dialogService.show was called
      verify(
        mockDialogService.show(
          type: DialogType.confirm,
          content: '過去に検索したキーワードをクリアしますか？',
          confirmText: 'OK',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).called(1);

      // Verify useCase method was called
      verify(mockGetSearchConditionsUseCase.clearAllHistorySearchInput()).called(1);

      // Verify state was properly updated
      expect(controller.state.searchHistoryList, isEmpty);
    });
  });

  group('Search Operation Tests', () {
    test('onClickSearch should show prompt dialog when keyword is empty and using general search condition', () async {
      // Initialize test parameters
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.assetListPage,
          searchId: searchIdConditionTwo,
          searchKey: '',
          assetTypeId: null,
        ),
      );

      // Mock commonSearchConditionsDialog to return false
      // Setup search condition as general search (searchId is 0)
      controller.state.selectedRadioCondition.value = SearchConditionModel(
        searchId: searchIdConditionZero,
        searchName: 'General Search',
      );

      // Set empty search keyword
      controller.state.searchInput.value = '';

      // Call the method
      controller.onClickSearch();

      // Verify dialogService.show was called
      verify(mockDialogService.show(content: 'キーワードを入力ください')).called(1);

      // Verify navigationService.goBack was not called
      verifyNever(mockNavigationService.goBack(result: {}));
    });

    test('onClickSearch should save search history and return search result', () async {
      // Initialize test parameters
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.assetListPage,
          searchId: searchIdConditionTwo,
          searchKey: '',
          assetTypeId: null,
        ),
      );

      // Initialize selected condition
      controller.state.selectedRadioCondition.value = SearchConditionModel(
        searchId: searchIdConditionTwo,
        searchName: 'All Data (Group Viewable)',
      );

      // Prepare test data
      const testInput = 'Test search keyword';
      controller.state.searchInput.value = testInput;

      when(
        mockGetSearchConditionsUseCase.saveHistorySearchInput(inputKey: testInput),
      ).thenAnswer((_) async => ['Test search keyword', 'History 1']);

      // Call the method
      controller.onClickSearch();

      // Wait for async operations to complete
      await Future.delayed(Duration.zero);

      // Verify method was called
      verify(mockGetSearchConditionsUseCase.saveHistorySearchInput(inputKey: testInput)).called(1);

      // Verify result was returned
      verify(mockNavigationService.goBack(result: isA<SearchConditionResult>())).called(1);
    });

    test('commonSearchConditionsDialog should return true when no conditions are set', () {
      // Setup scenario with no conditions
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.assetListPage,
          searchId: searchIdConditionTwo,
          searchKey: '',
          assetTypeId: null,
        ),
      );
      controller.state.searchInput.value = '';
      controller.state.selectedRadioCondition.value = SearchConditionModel(
        searchId: searchIdConditionTwo,
        searchName: 'All Data (Group Viewable)',
      );

      // Call the method
      final result = controller.commonSearchConditionsDialog();

      // Verify dialogService.show was called
      verify(mockDialogService.show(content: '条件が設定されていません')).called(1);

      // Verify result
      expect(result, isTrue);
    });

    test('commonSearchConditionsDialog should return false when conditions are set', () {
      // Setup scenario with conditions (set search keyword)
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.assetListPage,
          searchId: searchIdConditionTwo,
          searchKey: '',
          assetTypeId: null,
        ),
      );
      controller.state.searchInput.value = 'Set keyword';
      controller.state.selectedRadioCondition.value = SearchConditionModel(
        searchId: searchIdConditionTwo,
        searchName: 'All Data (Group Viewable)',
      );

      // Call the method
      final result = controller.commonSearchConditionsDialog();

      // Verify dialogService.show was not called
      verifyNever(mockDialogService.show(content: argThat(isA<String>(), named: 'content')));

      // Verify result
      expect(result, isFalse);
    });
  });

  group('Clear Condition Tests', () {
    test('onClickClearTheConditions should reset search conditions', () async {
      // Initialize test parameters for non-general search condition
      // Set searchKey to null so _loadData won't reset the search input
      controller.setTestParams(
        GetSearchConditionParams(
          searchEnum: SearchPageType.actionListPage,
          searchId: 123,
          searchKey: null, // Changed to null
          assetTypeId: null,
        ),
      );

      // Set initial state
      controller.state.searchInput.value = 'Initial keyword';

      // Mock getSearchConditionsUseCase response
      when(mockGetSearchConditionsUseCase.call(any)).thenAnswer(
        (_) async =>
            GetSearchConditionResult(searchConditionList: [], historySearchList: [], keyRecordSearch: 'test_key'),
      );

      // Mock dialogService.show specifically for onClickClearTheConditions
      when(
        mockDialogService.show(
          content: '検索条件をクリアしますか？',
          confirmText: 'OK',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).thenAnswer((invocation) {
        // Get and call onConfirm callback
        final onConfirm = invocation.namedArguments[const Symbol('onConfirm')] as Function;
        onConfirm();
        return Future.value();
      });

      // Call the method
      controller.onClickClearTheConditions();

      // Wait for async operations to complete
      await Future.delayed(Duration.zero);

      // Verify dialogService.show was called
      verify(
        mockDialogService.show(
          content: '検索条件をクリアしますか？',
          confirmText: 'OK',
          cancelText: 'キャンセル',
          onConfirm: anyNamed('onConfirm'),
        ),
      ).called(1);

      // Verify search input was cleared
      expect(controller.state.searchInput.value, equals(''));
    });
  });
}
