import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/models/asset_search_category_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/customize_asset_list_model_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/master_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_list_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_functional_processing_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../mock/asset_list_mock_data.dart';
import 'load_data_usecase_test.mocks.dart';

@GenerateMocks([AssetRepository, AssetTypeRepository, AssetFunctionalProcessingHelperRepository])
void main() {
  late LoadDataUseCase useCase;
  late MockAssetRepository mockAssetRepository;
  late MockAssetTypeRepository mockAssetTypeRepository;
  late MockAssetFunctionalProcessingHelperRepository mockAssetHelper;

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    LogUtil.initialize();
    mockAssetRepository = MockAssetRepository();
    mockAssetTypeRepository = MockAssetTypeRepository();
    mockAssetHelper = MockAssetFunctionalProcessingHelperRepository();
    useCase = LoadDataUseCase(
      assetRepository: mockAssetRepository,
      assetTypeRepository: mockAssetTypeRepository,
      assetHelper: mockAssetHelper,
    );
  });
  group('LoadDataUseCase', () {
    final assetList = [
      Asset(assetId: 1, assetText: '{"name": "Asset 1"}'),
      Asset(assetId: 2, assetText: '{"name": "Asset 2"}'),
    ];
    final searchResults = CustomizeAssetListModelResponse(
      assetList: assetList,
      searchAssetCount: 2,
      sumAssetCount: 2,
      moreThenLimit: false,
      code: 1,
      msg: '',
    );

    test('should return valid data when asset list is successfully retrieved', () async {
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => AssetMobileResponse.fromJson(AssetListMockData.assetMobileResponse));
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => CustomizeAssetListModelResponse.fromJson(AssetListMockData.searchResults));
      when(
        mockAssetRepository.getAllMasterLayoutSetting(typeId: anyNamed('typeId')),
      ).thenAnswer((_) async => MasterLayoutSettingResponse.fromJson(AssetListMockData.layoutSettingResults));
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');
      when(
        mockAssetHelper.assembledNumberTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'number');
      when(
        mockAssetHelper.assembledCurrencyTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'currency');
      when(mockAssetHelper.itemValueToString(value: anyNamed('value'))).thenAnswer((_) => 'itemValue');
      when(
        mockAssetHelper.assembledAppurInfoSummaryTypeValue(
          layoutSetting: anyNamed('layoutSetting'),
          value: anyNamed('value'),
        ),
      ).thenAnswer((_) => 'appurInfoSummary');
      when(
        mockAssetHelper.assembledCalculateTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'calculate');
      when(
        mockAssetHelper.assembledDateTimeTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'date');
      when(
        mockAssetHelper.assembledCheckboxTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'checkbox');
      // Act
      final result = await useCase(
        LoadDataParams(
          searchId: 1,
          assetSearchConditionList: [
            AssetSearchCategoryUIModel(
              searchData: '-10000.00001',
              itemId: 12493079,
              itemName: '数字',
              searchLogic: 'AND',
              method: 'numberEqu',
            ),
            AssetSearchCategoryUIModel(
              searchData: '1234.008900',
              itemId: 12493080,
              itemName: '通貨',
              searchLogic: 'AND',
              method: 'numberEqu',
            ),
            AssetSearchCategoryUIModel(
              searchData: '-9999.00001',
              itemId: 12493081,
              itemName: '計算',
              searchLogic: 'AND',
              method: 'equeue',
            ),
            AssetSearchCategoryUIModel(
              subItemId: 962060,
              subItemName: '数字666',
              searchData: '1231.4561237',
              itemId: 12493082,
              itemName: 'FXH_Master_All',
              searchLogic: 'AND',
              method: 'listInclude',
            ),
          ],
          keyword: '',
          currentPage: 1,
          assetTypeId: 1,
        ),
      );
      final resultToJson = result.toJson();
      final resultFromJson = LoadDataResult.fromJson(resultToJson);
      expect(resultFromJson, isA<LoadDataResult>());
      final resultAssetUIModel = result.assetUIModelList?[0].toJson();
      expect(result.assetUIModelList?[0].toString(), isA<String>());
      expect(result.assetUIModelList?[0].assetDisplayList?[0].toString(), isA<String>());
      final resultAssetUIModelFromJson = AssetUIModel.fromJson(resultAssetUIModel ?? {});
      expect(resultAssetUIModelFromJson, isA<AssetUIModel>());
      // Assert
      expect(result.assetUIModelList, isNotNull);
      expect(result.assetUIModelList!.length, 20);
      expect(result.assetUIModelList![0].assetId, 1622791192);
      expect(result.assetUIModelList![1].assetId, 1622791191);
      verify(mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId'))).called(1);
      verify(mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams'))).called(1);
    });
    test('test for SharedItemTypeEnum.appurInfoSummary and SharedItemTypeEnum.date', () async {
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => AssetMobileResponse.fromJson(AssetListMockData.assetMobileResponse2));
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => CustomizeAssetListModelResponse.fromJson(AssetListMockData.searchResults));
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');
      when(mockAssetHelper.itemValueToString(value: anyNamed('value'))).thenAnswer((_) => 'itemValue');
      when(
        mockAssetHelper.assembledCalculateTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'calculate');
      when(
        mockAssetHelper.assembledAppurInfoSummaryTypeValue(
          layoutSetting: anyNamed('layoutSetting'),
          value: anyNamed('value'),
        ),
      ).thenAnswer((_) => 'appurInfoSummary');
      when(
        mockAssetHelper.assembledDateTimeTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'date');
      final result = await useCase(
        LoadDataParams(searchId: 1, assetSearchConditionList: [], keyword: '', currentPage: 1, assetTypeId: 1),
      );
      expect(result.assetUIModelList, isNotNull);
    });

    test('test for assetLevelItemId is 0', () async {
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => AssetMobileResponse.fromJson(AssetListMockData.assetMobileResponse3));
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => CustomizeAssetListModelResponse.fromJson(AssetListMockData.searchResults));
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');
      when(mockAssetHelper.itemValueToString(value: anyNamed('value'))).thenAnswer((_) => 'itemValue');
      when(
        mockAssetHelper.assembledCalculateTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'calculate');
      when(
        mockAssetHelper.assembledAppurInfoSummaryTypeValue(
          layoutSetting: anyNamed('layoutSetting'),
          value: anyNamed('value'),
        ),
      ).thenAnswer((_) => 'appurInfoSummary');
      when(
        mockAssetHelper.assembledDateTimeTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'date');
      final result = await useCase(
        LoadDataParams(searchId: 1, assetSearchConditionList: [], keyword: '', currentPage: 1, assetTypeId: 1),
      );
      expect(result.assetUIModelList, isNotNull);
    });

    test('test for assetInfo is null', () async {
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => AssetMobileResponse.fromJson(AssetListMockData.assetMobileResponse4));
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => CustomizeAssetListModelResponse.fromJson(AssetListMockData.searchResults));
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');
      when(mockAssetHelper.itemValueToString(value: anyNamed('value'))).thenAnswer((_) => 'itemValue');
      when(
        mockAssetHelper.assembledCalculateTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'calculate');
      when(
        mockAssetHelper.assembledAppurInfoSummaryTypeValue(
          layoutSetting: anyNamed('layoutSetting'),
          value: anyNamed('value'),
        ),
      ).thenAnswer((_) => 'appurInfoSummary');
      when(
        mockAssetHelper.assembledDateTimeTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'date');
      final result = await useCase(
        LoadDataParams(searchId: 1, assetSearchConditionList: [], keyword: '', currentPage: 1, assetTypeId: 1),
      );
      expect(result.assetUIModelList, isNotNull);
    });

    test('test for assetItemTypeSettingList isEmpty', () async {
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => AssetMobileResponse.fromJson(AssetListMockData.assetMobileResponse5));
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => CustomizeAssetListModelResponse.fromJson(AssetListMockData.searchResults));
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');
      when(mockAssetHelper.itemValueToString(value: anyNamed('value'))).thenAnswer((_) => 'itemValue');
      when(
        mockAssetHelper.assembledCalculateTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'calculate');
      when(
        mockAssetHelper.assembledAppurInfoSummaryTypeValue(
          layoutSetting: anyNamed('layoutSetting'),
          value: anyNamed('value'),
        ),
      ).thenAnswer((_) => 'appurInfoSummary');
      when(
        mockAssetHelper.assembledDateTimeTypeValue(layoutSetting: anyNamed('layoutSetting'), value: anyNamed('value')),
      ).thenAnswer((_) => 'date');
      final result = await useCase(
        LoadDataParams(searchId: 1, assetSearchConditionList: [], keyword: '', currentPage: 1, assetTypeId: 1),
      );
      expect(result.assetUIModelList, isNotNull);
    });

    test('_dataReorganization() params all null', () async {
      // Arrange
      final assetList = [
        Asset(assetId: 1, assetText: '{"name": "Asset 1"}'),
        Asset(assetId: 2, assetText: '{"name": "Asset 2"}'),
      ];
      final assetMobileResponse = AssetMobileResponse(
        assetMobileSettingForMobile: AssetMobileSettingForMobile(),
        assetItemList: null,
        code: 1,
        msg: '',
      );
      final searchResults = CustomizeAssetListModelResponse(
        assetList: assetList,
        searchAssetCount: 2,
        sumAssetCount: 2,
        moreThenLimit: false,
        code: 1,
        msg: '',
      );

      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => assetMobileResponse);
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => searchResults);
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');

      // Act
      final result = await useCase(
        LoadDataParams(
          searchId: 1,
          assetSearchConditionList: [
            AssetSearchCategoryUIModel(
              searchData: '-10000.00001',
              itemId: 12493079,
              itemName: '数字',
              searchLogic: 'AND',
              method: 'numberEqu',
            ),
            AssetSearchCategoryUIModel(
              searchData: '1234.008900',
              itemId: 12493080,
              itemName: '通貨',
              searchLogic: 'AND',
              method: 'numberEqu',
            ),
            AssetSearchCategoryUIModel(
              searchData: '-9999.00001',
              itemId: 12493081,
              itemName: '計算',
              searchLogic: 'AND',
              method: 'equeue',
            ),
            AssetSearchCategoryUIModel(
              subItemId: 962060,
              subItemName: '数字666',
              searchData: '1231.4561237',
              itemId: 12493082,
              itemName: 'FXH_Master_All',
              searchLogic: 'AND',
              method: 'listInclude',
            ),
          ],
          keyword: '',
          currentPage: 1,
          assetTypeId: 1,
        ),
      );
      expect(result.assetUIModelList, isNotNull);
    });
    test('should return empty data when no assets are found', () async {
      // Arrange
      final assetMobileResponse = AssetMobileResponse(
        assetMobileSettingForMobile: AssetMobileSettingForMobile(assetInfo: AssetInfo()),
        assetItemList: [SharedLayoutSetting(itemId: 1, itemName: 'Name', itemType: 'text')],
        code: 1,
        msg: '',
      );
      final searchResults = CustomizeAssetListModelResponse(
        assetList: [],
        searchAssetCount: 0,
        sumAssetCount: 0,
        moreThenLimit: false,
        code: 1,
        msg: '',
      );

      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => assetMobileResponse);
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => searchResults);

      // Act
      final result = await useCase(
        LoadDataParams(searchId: 1, assetSearchConditionList: [], keyword: '', currentPage: 1, assetTypeId: 1),
      );

      // Assert
      expect(result.assetUIModelList, null);
      expect(result.searchAssetCount, 0);
      expect(result.sumAssetCount, 0);
      verify(mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId'))).called(1);
      verify(mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams'))).called(1);
    });

    test('should throw BusinessException when asset mobile settings are not found', () async {
      // Arrange
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenThrow(BusinessException('Asset mobile settings not found'));
      verifyNever(mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId'))).called(0);
      // Act & Assert
      expect(
        () async => await useCase(
          LoadDataParams(searchId: 1, assetSearchConditionList: [], keyword: '123', currentPage: 0, assetTypeId: 1),
        ),
        throwsA(isA<BusinessException>()),
      );
    });

    test('searchId is zero and isNotKeyWord', () async {
      final result = await useCase(
        LoadDataParams(searchId: 0, assetSearchConditionList: [], keyword: '', currentPage: 1, assetTypeId: 1),
      );
      // Act & Assert
      expect(result, isA<LoadDataResult>());
    });

    test('web has not set up layers 1 to 5', () async {
      final assetMobileResponse = AssetMobileResponse(
        assetMobileSettingForMobile: AssetMobileSettingForMobile(assetInfo: AssetInfo(assetLevel1ItemId: null)),
        assetItemList: [
          SharedLayoutSetting(
            itemId: 3,
            tenantId: '9900004',
            itemName: 'assetName',
            itemDisplayName: '資産名',
            itemType: 'input',
            option:
                '{\"readonly\": \"0\", \"maxlength\": \"255\", \"sectionPrivateGroups\": \"\", \"sectionPrivateEditGroups\": \"\"}',
            defaultData: '',
            inputFlg: '1',
            mobileFlg: '1',
            sectionName: '基本情報',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 2,
            tenantId: '9900004',
            itemName: 'identityCode',
            itemDisplayName: '識別コード',
            itemType: 'input',
            option:
                '{"readonly": "1", "automatic": true, "maxlength": "30", "ruleUseFlg": "0", "resetTiming": "", "sectionPrivateGroups": "", "autoNumberingRuleList": [], "sectionPrivateEditGroups": ""}',
            defaultData: '',
            inputFlg: '0',
            mobileFlg: '1',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 12493079,
            tenantId: '9900004',
            itemName: '数字',
            itemDisplayName: '数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12数字_小数点桁数12',
            itemType: 'number',
            option:
                '{"check": "/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証代码\\n    return {\\n      valid: true,\\n      data: {\\n        message:\\n      }\\n    };\\n  };", "unitFlg": "0", "readonly": "0", "percentage": "0", "numberDecimalPoint": "12", "sectionPrivateGroups": "", "numberCommaDecimalPoint": "3", "sectionPrivateEditGroups": ""}',
            defaultData: '1234.1567',
            inputFlg: '',
            mobileFlg: '1',
            sectionName: '基本情報',
            sectionSort: 1.0,
            createdById: '1044758',
            createdDate: '2024-12-19 16:47:08',
            modifiedById: '1044758',
            modifiedDate: '2024-12-26 18:06:37',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 962060,
            tenantId: '9900004',
            itemName: 'createdDate',
            itemDisplayName: '登録日',
            itemType: 'date',
            option:
                '{\\"dateType\\": \\"date\\", \\"readonly\\": \\"0\\", \\"sectionPrivateGroups\\": \\"\\", \\"sectionPrivateEditGroups\\": \\"\\"}',
            defaultData: 'today',
            inputFlg: '0',
            mobileFlg: '1',
            sectionName: '基本情報',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 12493080,
            tenantId: '9900004',
            itemName: '通貨',
            itemDisplayName: '通貨_小数点桁数12',
            itemType: 'currency',
            option:
                '{"check": "/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証代码\\n    return {\\n      valid: true,\\n      data: {\\n        message:\\n      }\\n    };\\n  };", "readonly": "0", "currencyType": "JPY(￥)", "currencyDecimalPoint": "12", "sectionPrivateGroups": "", "sectionPrivateEditGroups": ""}',
            defaultData: '11234.12345',
            inputFlg: '',
            mobileFlg: '1',
            sectionName: '基本情報',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 12493079,
            tenantId: '9900004',
            itemName: 'appurInfoSummary',
            itemDisplayName: 'appurInfoSummary',
            itemType: 'appurInfoSummary',
            defaultData: '',
            inputFlg: '',
            mobileFlg: '1',
            sectionName: '基本情報',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 12493081,
            tenantId: '9900004',
            itemName: '計算',
            itemDisplayName: '計算_小数点桁数12',
            itemType: 'calculate',
            option:
                '{"check": "/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証代码\\n    return {\\n      valid: true,\\n      data: {\\n        message:\\n      }\\n    };\\n  };", "unitFlg": "0", "readonly": "0", "calculate": "`数字`+1", "percentage": "0", "calculateType": "digital", "sectionPrivateGroups": "", "calculateDecimalPoint": "12", "sectionPrivateEditGroups": "", "calculateCommaDecimalPoint": "3"}',
            defaultData: '',
            inputFlg: '',
            mobileFlg: '1',
            sectionName: '基本情報',
            assetTypeId: 111111855,
          ),
          SharedLayoutSetting(
            itemId: 12493082,
            tenantId: '9900004',
            itemName: 'FXH_Master_All',
            itemDisplayName: 'FXH_Master_All',
            itemType: 'master',
            option:
                '{"masterDisplayItems":[{"itemDisplayName":"数字_小数点桁数12","itemId":962060,"itemName":"数字666","itemType":"number","option":"{\\"check\\": \\"/* jshint esversion: 8 */\\\\n  //value => 該当項目の値\\\\n  var validation = function(value,instance) {\\\\n    // TODO 検証代码\\\\n    return {\\\\n      valid: true,\\\\n      data: {\\\\n        message:\\\\n      }\\\\n    };\\\\n  };\\", \\"unitFlg\\": \\"0\\", \\"readonly\\": \\"0\\", \\"percentage\\": \\"0\\", \\"numberDecimalPoint\\": \\"12\\", \\"numberCommaDecimalPoint\\": \\"3\\"}"}],"readonly":"0","sectionPrivateGroups":"","sectionPrivateEditGroups":"","check":"/* jshint esversion: 8 */\\n  //value => 該当項目の値\\n  var validation = function(value,instance) {\\n    // TODO 検証代码\\n    return {\\n      valid: true,\\n      data: {\\n        message:\\n      }\\n    };\\n  };","masterTypeId":124043,"masterChainFlg":"0"}',
            defaultData: '7794626',
            inputFlg: '',
            mobileFlg: '1',
            sectionName: '基本情報',
            assetTypeId: 111111855,
          ),
        ],
        code: 1,
        msg: '',
      );
      when(
        mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId')),
      ).thenAnswer((_) async => assetMobileResponse);
      when(
        mockAssetRepository.getCustomizeSearchForMobile(searchParams: anyNamed('searchParams')),
      ).thenAnswer((_) async => searchResults);
      when(
        mockAssetHelper.getHomeImageFun(
          assetItemTypeSettingList: anyNamed('assetItemTypeSettingList'),
          assetItem: anyNamed('assetItem'),
        ),
      ).thenAnswer((_) async => 'http://example.com/image.jpg');
      when(mockAssetHelper.itemValueToString(value: anyNamed('value'))).thenAnswer((_) => 'mockValue');
      final result = await useCase(
        LoadDataParams(searchId: 1, keyword: '', currentPage: 1, assetTypeId: 1, assetSearchConditionList: []),
      );
      expect(result.assetUIModelList, isNotNull);
      verify(mockAssetRepository.getAssetMobileSettings(assetTypeId: anyNamed('assetTypeId'))).called(1);
    });

    group('CustomizeSearchForMobileRequestQuery', () {
      test('should convert to and from map correctly', () {
        final query = CustomizeSearchForMobileRequestQuery(
          assetTypeId: 1,
          rows: 10,
          skip: 0,
          assetSearchConditionList: [],
          searchId: 2,
          keyword: 'test',
        );

        final map = query.toMap();
        expect(map['assetTypeId'], 1);
        expect(map['rows'], 10);
        expect(map['skip'], 0);
        expect(map['assetSearchConditionList'], []);
        expect(map['searchId'], 2);
        expect(map['keyword'], 'test');
      });
    });

    group('FixedSearching', () {
      test('should convert to and from map correctly', () {
        final searching = FixedSearching(searchUserInput: 'input', isUserInput: true);

        final map = searching.toMap();
        expect(map['searchUserInput'], 'input');
        expect(map['isUserInput'], true);

        final fromMap = FixedSearching.fromMap(map);
        expect(fromMap.searchUserInput, 'input');
        expect(fromMap.isUserInput, true);
      });
    });

    group('AssetItemLevelInfo', () {
      test('should convert to and from map correctly', () {
        final levelInfo = AssetItemLevelInfo(
          assetLevelItemId: 1,
          assetLevelItemName: 'name',
          assetLevelItemType: 'type',
          assetLevelItemOption: 'option',
          assetLevelSubItemId: 2,
        );

        final map = levelInfo.toMap();
        expect(map['assetLevelItemId'], 1);
        expect(map['assetLevelItemName'], 'name');
        expect(map['assetLevelItemType'], 'type');
        expect(map['assetLevelItemOption'], 'option');
        expect(map['assetLevelSubItemId'], 2);

        final fromMap = AssetItemLevelInfo.fromMap(map);
        expect(fromMap.assetLevelItemId, 1);
        expect(fromMap.assetLevelItemName, 'name');
        expect(fromMap.assetLevelItemType, 'type');
        expect(fromMap.assetLevelItemOption, 'option');
        expect(fromMap.assetLevelSubItemId, 2);
      });
    });

    group('AssetItemInfo', () {
      test('should convert to and from map correctly', () {
        final itemInfo = AssetItemInfo(assetItemDisplayTitle: 'title', assetItemDisplayValue: 'value');

        final map = itemInfo.toMap();
        expect(map['assetItemDisplayTitle'], 'title');
        expect(map['assetItemDisplayValue'], 'value');

        final fromMap = AssetItemInfo.fromMap(map);
        expect(fromMap.assetItemDisplayTitle, 'title');
        expect(fromMap.assetItemDisplayValue, 'value');
      });
    });
  });
}
