import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/models/home_page_list_api_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:dio/dio.dart'; // 导入dio包以使用Response类
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// Generate Mockito classes
@GenerateMocks([DioUtil])
// Import the mocks after the annotation
import 'home_repositories_impl_test.mocks.dart';

void main() {
  late MockDioUtil mockDioUtil;
  late HomeRepositoriesImpl homeRepositoriesImpl;

  LogUtil.initialize();

  setUp(() {
    mockDioUtil = MockDioUtil();
    homeRepositoriesImpl = HomeRepositoriesImpl(dioUtil: mockDioUtil);
  });

  group('HomeRepositoriesImpl', () {
    group('getHomePageListByMobileHomeSetting', () {
      test('should return APIHomePageListByMobileHomeSettingRoot when API call is successful', () async {
        // Arrange
        final mockResponseData = {
          'success': true,
          'msg': 'Success',
          'code': 200,
          'mobileHomeList': {
            'assetActionList': [
              {
                'assetActionId': 1,
                'assetActionName': 'Test Action',
                'tenantId': '123',
                'assetTypeId': 1,
                'assetTypeGroupIds': '1,2,3',
                'displayFlg': 'Y',
                'assetTypeName': 'Asset Type 1',
                'assetActionItem': 'Item 1',
                'quantityFlg': 'Y',
                'scanCondition': 'condition',
                'userIds': '1,2',
                'groupIds': '1,2',
                'isUpdateAmount': 'Y',
                'autoFetchAsset': 'N',
                'assetConditionSearchName': null,
                'createdById': '1',
                'createdByName': 'Admin',
                'createdDate': '2023-01-01',
                'actionLabel': 'Test Label',
                'actionIcon': 'test_icon',
              },
            ],
            'workflowList': [
              {
                'processDefinitionId': 'proc1',
                'workflowId': 1,
                'workflowName': 'Test Workflow',
                'version': 1,
                'description': 'Test Description',
                'assetActionList': null,
                'taskDefinitions': null,
                'sequenceFlows': null,
                'assetTypeIdWithFirstWf': 1,
                'createdDate': '2023-01-01',
                'workflowType': 'Type 1',
                'workflowTypeCode': 'TYPE1',
                'autoFetchAsset': 'N',
                'autoSetItemDefaultValue': 'N',
                'timerStartUserId': '1',
              },
            ],
          },
        };

        when(mockDioUtil.get(GlobalVariable.getHomePageListByMobileHomeSetting)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getHomePageListByMobileHomeSetting),
          ),
        );

        // Act
        final result = await homeRepositoriesImpl.getHomePageListByMobileHomeSetting();

        // Assert
        expect(result, isA<APIHomePageListByMobileHomeSettingRoot>());
        expect(result.mobileHomeList, isNotNull);
        expect(result.msg, 'Success');
        expect(result.code, 200);
        verify(mockDioUtil.get(GlobalVariable.getHomePageListByMobileHomeSetting)).called(1);
      });

      test('should throw Exception when API call is not successful', () async {
        // Arrange
        final mockResponseData = {'success': false};

        when(mockDioUtil.get(GlobalVariable.getHomePageListByMobileHomeSetting)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.getHomePageListByMobileHomeSetting),
          ),
        );

        // Act & Assert
        expect(() => homeRepositoriesImpl.getHomePageListByMobileHomeSetting(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getHomePageListByMobileHomeSetting)).called(1);
      });

      test('should throw Exception when an error occurs', () async {
        // Arrange
        when(mockDioUtil.get(GlobalVariable.getHomePageListByMobileHomeSetting)).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => homeRepositoriesImpl.getHomePageListByMobileHomeSetting(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getHomePageListByMobileHomeSetting)).called(1);
      });
    });

    group('getHomePageProcessSummary', () {
      test('should return HomeModel when API call is successful', () async {
        // Arrange
        final mockResponseData = {
          'success': true,
          'mobileHomeProcessSummary': {
            'talkBadgeNum': 0,
            'saveTemporaryActionCount': 5,
            'saveTemporaryProcessCount': 10,
            'unApproveProcessCount': 2,
            'saveTemporaryActionDisplayCount': '5',
            'saveTemporaryProcessDisplayCount': '10',
            'unApproveProcessDisplayCount': '2',
            'saveTemporaryActionLatestDate': '2023-01-01',
            'saveTemporaryProcessLatestDate': '2023-01-01',
            'unApproveProcessLatestDate': '2023-01-01',
            'assetActionList': [],
            'workflowList': [],
          },
        };

        when(mockDioUtil.get(GlobalVariable.getHomePageProcessSummary)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getHomePageProcessSummary),
          ),
        );

        // Act
        final result = await homeRepositoriesImpl.getHomePageProcessSummary();

        // Assert
        expect(result, isA<HomeModel>());
        expect(result?.saveTemporaryActionCount, 5);
        expect(result?.saveTemporaryProcessCount, 10);
        expect(result?.unApproveProcessCount, 2);
        verify(mockDioUtil.get(GlobalVariable.getHomePageProcessSummary)).called(1);
      });

      test('should throw Exception when API call is not successful', () async {
        // Arrange
        final mockResponseData = {'success': false};

        when(mockDioUtil.get(GlobalVariable.getHomePageProcessSummary)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.getHomePageProcessSummary),
          ),
        );

        // Act & Assert
        expect(() => homeRepositoriesImpl.getHomePageProcessSummary(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getHomePageProcessSummary)).called(1);
      });

      test('should throw Exception when an error occurs', () async {
        // Arrange
        when(mockDioUtil.get(GlobalVariable.getHomePageProcessSummary)).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => homeRepositoriesImpl.getHomePageProcessSummary(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getHomePageProcessSummary)).called(1);
      });
    });

    group('httpGetNotificationAnyNew', () {
      test('should return data when API call is successful', () async {
        // Arrange
        final mockResponseData = {
          'success': true,
          'data': {'hasNew': true},
        };

        when(mockDioUtil.get(GlobalVariable.getNotificationAnyNew)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getNotificationAnyNew),
          ),
        );

        // Act
        final result = await homeRepositoriesImpl.httpGetNotificationAnyNew();

        // Assert
        expect(result['data'], mockResponseData['data']);
        verify(mockDioUtil.get(GlobalVariable.getNotificationAnyNew)).called(1);
      });

      test('should throw Exception when API call is not successful', () async {
        // Arrange
        final mockResponseData = {'success': false};

        when(mockDioUtil.get(GlobalVariable.getNotificationAnyNew)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.getNotificationAnyNew),
          ),
        );

        // Act & Assert
        expect(() => homeRepositoriesImpl.httpGetNotificationAnyNew(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getNotificationAnyNew)).called(1);
      });

      test('should throw Exception when an error occurs', () async {
        // Arrange
        when(mockDioUtil.get(GlobalVariable.getNotificationAnyNew)).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => homeRepositoriesImpl.httpGetNotificationAnyNew(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getNotificationAnyNew)).called(1);
      });
    });

    group('getRoleList', () {
      test('should return data when API call is successful', () async {
        // Arrange
        final mockData = [
          {'roleId': 1, 'roleName': 'Admin'},
          {'roleId': 2, 'roleName': 'User'},
        ];
        final mockResponseData = {'success': true, 'roleList': mockData};

        when(mockDioUtil.get(GlobalVariable.getRoleList)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.getRoleList),
          ),
        );

        // Act
        final result = await homeRepositoriesImpl.getRoleList();

        // Assert
        expect(result, isA<List<SharedRoleModel>>());
        expect(result.length, 2);
        expect(result[0].roleId, 1);
        expect(result[0].roleName, 'Admin');
        expect(result[1].roleId, 2);
        expect(result[1].roleName, 'User');
        verify(mockDioUtil.get(GlobalVariable.getRoleList)).called(1);
      });

      test('should throw Exception when API call is not successful', () async {
        // Arrange
        final mockResponseData = {'success': false};

        when(mockDioUtil.get(GlobalVariable.getRoleList)).thenAnswer(
          (_) async => Response(
            data: mockResponseData,
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.getRoleList),
          ),
        );

        // Act & Assert
        expect(() => homeRepositoriesImpl.getRoleList(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getRoleList)).called(1);
      });

      test('should throw Exception when an error occurs', () async {
        // Arrange
        when(mockDioUtil.get(GlobalVariable.getRoleList)).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => homeRepositoriesImpl.getRoleList(), throwsA(isA<Exception>()));
        verify(mockDioUtil.get(GlobalVariable.getRoleList)).called(1);
      });
    });
  });
}

// 在底部添加一个注释，说明我们在这个文件中使用了dio包的Response类
// 注意：这个测试使用dio包中的Response类来模拟HTTP响应
