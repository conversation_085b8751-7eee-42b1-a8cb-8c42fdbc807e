// test/features/asset/asset_category/domain/usecases/get_category_assemble_list_usecase_test.dart

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/usecases/get_category_assemble_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late GetCategoryAssembleListUseCase useCase;

  setUp(() {
    LogUtil.initialize();
    useCase = GetCategoryAssembleListUseCase();
  });

  group('GetCategoryAssembleListUseCase', () {
    test('should assemble category list correctly when not at final layer', () async {
      // Arrange
      final testParams = GetCategoryAssembleListParams(
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
          Info(
            titleValue: 'Category 2',
            goBackLevelInfo: const CategoryModel(
              itemId: 2,
              itemDisplayName: 'Display Name 2',
              itemName: 'Name2',
              itemType: 'master',
              optionObj: {'key2': 'value2'},
              itemSubName: 'SubName2',
              itemSubId: 102,
              itemSubType: 'subType2',
              subOptionObj: SubOptionObjModel(),
              value: 'Value2',
              selectedCategoryValue: 'SelectedValue2',
              subOption: 'SubOption2',
              itemSubDisplayName: 'SubDisplayName2',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
        lastValue: 'Value2',
      );

      final expectedCategoryList = [
        CategoryData(
          titleVal: 'Existing Category 1',
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 1,
            categorySubItemId: 101,
            categoryItemValue: 'Value2',
            itemType: 'type1',
            index: 0,
            itFinished: false,
          ),
        ),
        CategoryData(
          titleVal: 'Category 2',
          categoryInfo: const CategoryModel(
            itemId: 2,
            itemDisplayName: 'Display Name 2',
            itemName: 'Name2',
            itemType: 'master',
            optionObj: {'key2': 'value2'},
            itemSubName: 'SubName2',
            itemSubId: 102,
            itemSubType: 'subType2',
            subOptionObj: SubOptionObjModel(),
            value: 'Value2',
            selectedCategoryValue: 'SelectedValue2',
            subOption: 'SubOption2',
            itemSubDisplayName: 'SubDisplayName2',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 2,
            categorySubItemId: 102,
            categoryItemValue: 'Value2',
            itemType: 'master',
            index: 1,
            itFinished: false,
          ),
        ),
      ];

      // Act
      final result = await useCase.call(testParams);

      // Assert
      expect(result.categoryList, isA<List<CategoryData>>());
      expect(result.categoryList.length, expectedCategoryList.length);

      for (int i = 0; i < result.categoryList.length; i++) {
        final resultItem = result.categoryList[i];
        final expectedItem = expectedCategoryList[i];

        // 比较 CategoryData 的 titleVal
        expect(resultItem.titleVal, expectedItem.titleVal);

        // 比较 CategoryModel 的部分字段
        expect(resultItem.categoryInfo?.itemId, expectedItem.categoryInfo?.itemId);
        expect(resultItem.categoryInfo?.itemDisplayName, expectedItem.categoryInfo?.itemDisplayName);
        expect(resultItem.categoryInfo?.itemName, expectedItem.categoryInfo?.itemName);
        expect(resultItem.categoryInfo?.itemType, expectedItem.categoryInfo?.itemType);
        // 根据需要添加更多字段的比较

        // 比较 CategorySelectedData 的部分字段
        expect(resultItem.categorySelectedData?.categoryItemId, expectedItem.categorySelectedData?.categoryItemId);
        expect(
          resultItem.categorySelectedData?.categorySubItemId,
          expectedItem.categorySelectedData?.categorySubItemId,
        );
        expect(resultItem.categorySelectedData?.itemType, expectedItem.categorySelectedData?.itemType);
        expect(resultItem.categorySelectedData?.index, expectedItem.categorySelectedData?.index);
        expect(resultItem.categorySelectedData?.itFinished, expectedItem.categorySelectedData?.itFinished);
      }
    });

    test('should finalize category selection when at final layer', () async {
      // Arrange
      final testParams = GetCategoryAssembleListParams(
        categoryOriginalList: [
          Info(
            titleValue: 'Category 1',
            goBackLevelInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
          ),
        ],
        categoryList: [
          CategoryData(
            titleVal: 'Existing Category 1',
            categoryInfo: const CategoryModel(
              itemId: 1,
              itemDisplayName: 'Display Name 1',
              itemName: 'Name1',
              itemType: 'type1',
              optionObj: {'key1': 'value1'},
              itemSubName: 'SubName1',
              itemSubId: 101,
              itemSubType: 'subType1',
              subOptionObj: SubOptionObjModel(),
              value: 'Value1',
              selectedCategoryValue: 'SelectedValue1',
              subOption: 'SubOption1',
              itemSubDisplayName: 'SubDisplayName1',
            ),
            categorySelectedData: CategorySelectedData(
              categoryItemId: 1,
              categorySubItemId: 101,
              categoryItemValue: 'Value1',
              itemType: 'type1',
              index: 0,
              itFinished: false,
            ),
          ),
        ],
        lastValue: 'すべて', // Indicates 'All', which should finalize selection
      );

      final expectedCategoryList = [
        CategoryData(
          titleVal: 'Existing Category 1',
          categoryInfo: const CategoryModel(
            itemId: 1,
            itemDisplayName: 'Display Name 1',
            itemName: 'Name1',
            itemType: 'type1',
            optionObj: {'key1': 'value1'},
            itemSubName: 'SubName1',
            itemSubId: 101,
            itemSubType: 'subType1',
            subOptionObj: SubOptionObjModel(),
            value: 'Value1',
            selectedCategoryValue: 'SelectedValue1',
            subOption: 'SubOption1',
            itemSubDisplayName: 'SubDisplayName1',
          ),
          categorySelectedData: CategorySelectedData(
            categoryItemId: 1,
            categorySubItemId: 101,
            categoryItemValue: 'すべて',
            itemType: 'type1',
            index: 0,
            itFinished: true,
          ),
        ),
      ];

      // Act
      final result = await useCase.call(testParams);

      // Assert
      expect(result.categoryList, isA<List<CategoryData>>());
      expect(result.categoryList.length, expectedCategoryList.length);

      for (int i = 0; i < result.categoryList.length; i++) {
        final resultItem = result.categoryList[i];
        final expectedItem = expectedCategoryList[i];

        // 比较 CategoryData 的 titleVal
        expect(resultItem.titleVal, expectedItem.titleVal);

        // 比较 CategoryModel 的部分字段
        expect(resultItem.categoryInfo?.itemId, expectedItem.categoryInfo?.itemId);
        expect(resultItem.categoryInfo?.itemDisplayName, expectedItem.categoryInfo?.itemDisplayName);
        expect(resultItem.categoryInfo?.itemName, expectedItem.categoryInfo?.itemName);
        expect(resultItem.categoryInfo?.itemType, expectedItem.categoryInfo?.itemType);
        // 根据需要添加更多字段的比较

        // 比较 CategorySelectedData 的部分字段
        expect(resultItem.categorySelectedData?.categoryItemId, expectedItem.categorySelectedData?.categoryItemId);
        expect(
          resultItem.categorySelectedData?.categorySubItemId,
          expectedItem.categorySelectedData?.categorySubItemId,
        );
        expect(
          resultItem.categorySelectedData?.categoryItemValue,
          expectedItem.categorySelectedData?.categoryItemValue,
        );
        expect(resultItem.categorySelectedData?.itemType, expectedItem.categorySelectedData?.itemType);
        expect(resultItem.categorySelectedData?.index, expectedItem.categorySelectedData?.index);
        expect(resultItem.categorySelectedData?.itFinished, expectedItem.categorySelectedData?.itFinished);
      }
    });

    test('should throw BusinessException with proper message on BusinessException', () async {
      // Arrange
      // 假设当 categoryOriginalList 为空时，会抛出 BusinessException
      final testParams = GetCategoryAssembleListParams(categoryOriginalList: [], categoryList: [], lastValue: null);

      // Act & Assert
      expect(
        () => useCase.call(testParams),
        throwsA(isA<BusinessException>().having((e) => e.message, 'message', 'カテゴリの取得に失敗しました。もう一度お試しください。')),
      );
    });
  });
}
