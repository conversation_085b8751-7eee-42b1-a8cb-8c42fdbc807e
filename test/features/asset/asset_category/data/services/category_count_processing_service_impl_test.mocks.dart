// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/asset/asset_category/data/services/category_count_processing_service_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_item_value_list_response.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_category/domain/repositories/category_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCategoryItemValueListResponse_0 extends _i1.SmartFake
    implements _i2.CategoryItemValueListResponse {
  _FakeCategoryItemValueListResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [CategoryRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockCategoryRepository extends _i1.Mock
    implements _i3.CategoryRepository {
  MockCategoryRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.CategoryItemValueListResponse> getCategoryCount({
    required Map<String, dynamic>? params,
    required String? searchId,
    required String? keyword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getCategoryCount, [], {
              #params: params,
              #searchId: searchId,
              #keyword: keyword,
            }),
            returnValue: _i4.Future<_i2.CategoryItemValueListResponse>.value(
              _FakeCategoryItemValueListResponse_0(
                this,
                Invocation.method(#getCategoryCount, [], {
                  #params: params,
                  #searchId: searchId,
                  #keyword: keyword,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.CategoryItemValueListResponse>);
}
